{"ast": null, "code": "import { createUpdateEffect } from './createUpdateEffect';\nimport useAntdTable from './useAntdTable';\nimport useAsyncEffect from './useAsyncEffect';\nimport useBoolean from './useBoolean';\nimport useClickAway from './useClickAway';\nimport useControllableValue from './useControllableValue';\nimport useCookieState from './useCookieState';\nimport useCountDown from './useCountDown';\nimport useCounter from './useCounter';\nimport useCreation from './useCreation';\nimport useDebounce from './useDebounce';\nimport useDebounceEffect from './useDebounceEffect';\nimport useDebounceFn from './useDebounceFn';\nimport useDeepCompareEffect from './useDeepCompareEffect';\nimport useDeepCompareLayoutEffect from './useDeepCompareLayoutEffect';\nimport useDocumentVisibility from './useDocumentVisibility';\nimport useDrag from './useDrag';\nimport useDrop from './useDrop';\nimport useDynamicList from './useDynamicList';\nimport useEventEmitter from './useEventEmitter';\nimport useEventListener from './useEventListener';\nimport useEventTarget from './useEventTarget';\nimport useExternal from './useExternal';\nimport useFavicon from './useFavicon';\nimport useFocusWithin from './useFocusWithin';\nimport useFullscreen from './useFullscreen';\nimport useFusionTable from './useFusionTable';\nimport useGetState from './useGetState';\nimport useHistoryTravel from './useHistoryTravel';\nimport useHover from './useHover';\nimport useInfiniteScroll from './useInfiniteScroll';\nimport useInterval from './useInterval';\nimport useInViewport from './useInViewport';\nimport useIsomorphicLayoutEffect from './useIsomorphicLayoutEffect';\nimport useKeyPress from './useKeyPress';\nimport useLatest from './useLatest';\nimport useLocalStorageState from './useLocalStorageState';\nimport useLockFn from './useLockFn';\nimport useLongPress from './useLongPress';\nimport useMap from './useMap';\nimport useMemoizedFn from './useMemoizedFn';\nimport useMount from './useMount';\nimport useMouse from './useMouse';\nimport useNetwork from './useNetwork';\nimport usePagination from './usePagination';\nimport usePrevious from './usePrevious';\nimport useRafInterval from './useRafInterval';\nimport useRafState from './useRafState';\nimport useRafTimeout from './useRafTimeout';\nimport useReactive from './useReactive';\nimport useRequest, { clearCache } from './useRequest';\nimport useResetState from './useResetState';\nimport useResponsive, { configResponsive } from './useResponsive';\nimport useSafeState from './useSafeState';\nimport useScroll from './useScroll';\nimport useSelections from './useSelections';\nimport useSessionStorageState from './useSessionStorageState';\nimport useSet from './useSet';\nimport useSetState from './useSetState';\nimport useSize from './useSize';\nimport useTextSelection from './useTextSelection';\nimport useThrottle from './useThrottle';\nimport useThrottleEffect from './useThrottleEffect';\nimport useThrottleFn from './useThrottleFn';\nimport useTimeout from './useTimeout';\nimport useTitle from './useTitle';\nimport useToggle from './useToggle';\nimport useTrackedEffect from './useTrackedEffect';\nimport useUnmount from './useUnmount';\nimport useUnmountedRef from './useUnmountedRef';\nimport useUpdate from './useUpdate';\nimport useUpdateEffect from './useUpdateEffect';\nimport useUpdateLayoutEffect from './useUpdateLayoutEffect';\nimport useVirtualList from './useVirtualList';\nimport useWebSocket from './useWebSocket';\nimport useWhyDidYouUpdate from './useWhyDidYouUpdate';\nimport useMutationObserver from './useMutationObserver';\nimport useTheme from './useTheme';\nexport { useRequest, useControllableValue, useDynamicList, useVirtualList, useResponsive, useEventEmitter, useLocalStorageState, useSessionStorageState, useSize, configResponsive, useUpdateEffect, useUpdateLayoutEffect, useBoolean, useToggle, useDocumentVisibility, useSelections, useThrottle, useThrottleFn, useThrottleEffect, useDebounce, useDebounceFn, useDebounceEffect, usePrevious, useMouse, useScroll, useClickAway, useFullscreen, useInViewport, useKeyPress, useEventListener, useHover, useUnmount, useSet, useMemoizedFn, useMap, useCreation, useDrag, useDrop, useMount, useCounter, useUpdate, useTextSelection, useEventTarget, useHistoryTravel, useCookieState, useSetState, useInterval, useWhyDidYouUpdate, useTitle, useNetwork, useTimeout, useReactive, useFavicon, useCountDown, useWebSocket, useLockFn, useUnmountedRef, useExternal, useSafeState, useLatest, useIsomorphicLayoutEffect, useDeepCompareEffect, useDeepCompareLayoutEffect, useAsyncEffect, useLongPress, useRafState, useTrackedEffect, usePagination, useAntdTable, useFusionTable, useInfiniteScroll, useGetState, clearCache, useFocusWithin, createUpdateEffect, useRafInterval, useRafTimeout, useResetState, useMutationObserver, useTheme };", "map": {"version": 3, "names": ["createUpdateEffect", "useAntdTable", "useAsyncEffect", "useBoolean", "useClickAway", "useControllableValue", "useCookieState", "useCountDown", "useCounter", "useCreation", "useDebounce", "useDebounceEffect", "useDebounceFn", "useDeepCompareEffect", "useDeepCompareLayoutEffect", "useDocumentVisibility", "useDrag", "useDrop", "useDynamicList", "useEventEmitter", "useEventListener", "useEventTarget", "useExternal", "useFavicon", "useFocusWithin", "useFullscreen", "useFusionTable", "useGetState", "useHistoryTravel", "useHover", "useInfiniteScroll", "useInterval", "useInViewport", "useIsomorphicLayoutEffect", "useKeyPress", "useLatest", "useLocalStorageState", "useLockFn", "useLongPress", "useMap", "useMemoizedFn", "useMount", "useMouse", "useNetwork", "usePagination", "usePrevious", "useRafInterval", "useRafState", "useRafTimeout", "useReactive", "useRequest", "clearCache", "useResetState", "useResponsive", "configResponsive", "useSafeState", "useScroll", "useSelections", "useSessionStorageState", "useSet", "useSetState", "useSize", "useTextSelection", "useThrottle", "useThrottleEffect", "useThrottleFn", "useTimeout", "useTitle", "useToggle", "useTrackedEffect", "useUnmount", "useUnmountedRef", "useUpdate", "useUpdateEffect", "useUpdateLayoutEffect", "useVirtualList", "useWebSocket", "useWhyDidYouUpdate", "useMutationObserver", "useTheme"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/index.js"], "sourcesContent": ["import { createUpdateEffect } from './createUpdateEffect';\nimport useAntdTable from './useAntdTable';\nimport useAsyncEffect from './useAsyncEffect';\nimport useBoolean from './useBoolean';\nimport useClickAway from './useClickAway';\nimport useControllableValue from './useControllableValue';\nimport useCookieState from './useCookieState';\nimport useCountDown from './useCountDown';\nimport useCounter from './useCounter';\nimport useCreation from './useCreation';\nimport useDebounce from './useDebounce';\nimport useDebounceEffect from './useDebounceEffect';\nimport useDebounceFn from './useDebounceFn';\nimport useDeepCompareEffect from './useDeepCompareEffect';\nimport useDeepCompareLayoutEffect from './useDeepCompareLayoutEffect';\nimport useDocumentVisibility from './useDocumentVisibility';\nimport useDrag from './useDrag';\nimport useDrop from './useDrop';\nimport useDynamicList from './useDynamicList';\nimport useEventEmitter from './useEventEmitter';\nimport useEventListener from './useEventListener';\nimport useEventTarget from './useEventTarget';\nimport useExternal from './useExternal';\nimport useFavicon from './useFavicon';\nimport useFocusWithin from './useFocusWithin';\nimport useFullscreen from './useFullscreen';\nimport useFusionTable from './useFusionTable';\nimport useGetState from './useGetState';\nimport useHistoryTravel from './useHistoryTravel';\nimport useHover from './useHover';\nimport useInfiniteScroll from './useInfiniteScroll';\nimport useInterval from './useInterval';\nimport useInViewport from './useInViewport';\nimport useIsomorphicLayoutEffect from './useIsomorphicLayoutEffect';\nimport useKeyPress from './useKeyPress';\nimport useLatest from './useLatest';\nimport useLocalStorageState from './useLocalStorageState';\nimport useLockFn from './useLockFn';\nimport useLongPress from './useLongPress';\nimport useMap from './useMap';\nimport useMemoizedFn from './useMemoizedFn';\nimport useMount from './useMount';\nimport useMouse from './useMouse';\nimport useNetwork from './useNetwork';\nimport usePagination from './usePagination';\nimport usePrevious from './usePrevious';\nimport useRafInterval from './useRafInterval';\nimport useRafState from './useRafState';\nimport useRafTimeout from './useRafTimeout';\nimport useReactive from './useReactive';\nimport useRequest, { clearCache } from './useRequest';\nimport useResetState from './useResetState';\nimport useResponsive, { configResponsive } from './useResponsive';\nimport useSafeState from './useSafeState';\nimport useScroll from './useScroll';\nimport useSelections from './useSelections';\nimport useSessionStorageState from './useSessionStorageState';\nimport useSet from './useSet';\nimport useSetState from './useSetState';\nimport useSize from './useSize';\nimport useTextSelection from './useTextSelection';\nimport useThrottle from './useThrottle';\nimport useThrottleEffect from './useThrottleEffect';\nimport useThrottleFn from './useThrottleFn';\nimport useTimeout from './useTimeout';\nimport useTitle from './useTitle';\nimport useToggle from './useToggle';\nimport useTrackedEffect from './useTrackedEffect';\nimport useUnmount from './useUnmount';\nimport useUnmountedRef from './useUnmountedRef';\nimport useUpdate from './useUpdate';\nimport useUpdateEffect from './useUpdateEffect';\nimport useUpdateLayoutEffect from './useUpdateLayoutEffect';\nimport useVirtualList from './useVirtualList';\nimport useWebSocket from './useWebSocket';\nimport useWhyDidYouUpdate from './useWhyDidYouUpdate';\nimport useMutationObserver from './useMutationObserver';\nimport useTheme from './useTheme';\nexport { useRequest, useControllableValue, useDynamicList, useVirtualList, useResponsive, useEventEmitter, useLocalStorageState, useSessionStorageState, useSize, configResponsive, useUpdateEffect, useUpdateLayoutEffect, useBoolean, useToggle, useDocumentVisibility, useSelections, useThrottle, useThrottleFn, useThrottleEffect, useDebounce, useDebounceFn, useDebounceEffect, usePrevious, useMouse, useScroll, useClickAway, useFullscreen, useInViewport, useKeyPress, useEventListener, useHover, useUnmount, useSet, useMemoizedFn, useMap, useCreation, useDrag, useDrop, useMount, useCounter, useUpdate, useTextSelection, useEventTarget, useHistoryTravel, useCookieState, useSetState, useInterval, useWhyDidYouUpdate, useTitle, useNetwork, useTimeout, useReactive, useFavicon, useCountDown, useWebSocket, useLockFn, useUnmountedRef, useExternal, useSafeState, useLatest, useIsomorphicLayoutEffect, useDeepCompareEffect, useDeepCompareLayoutEffect, useAsyncEffect, useLongPress, useRafState, useTrackedEffect, usePagination, useAntdTable, useFusionTable, useInfiniteScroll, useGetState, clearCache, useFocusWithin, createUpdateEffect, useRafInterval, useRafTimeout, useResetState, useMutationObserver, useTheme };"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,sBAAsB;AACzD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,0BAA0B,MAAM,8BAA8B;AACrE,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,yBAAyB,MAAM,6BAA6B;AACnE,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,UAAU,IAAIC,UAAU,QAAQ,cAAc;AACrD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,aAAa,IAAIC,gBAAgB,QAAQ,iBAAiB;AACjE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAAS7B,UAAU,EAAE7C,oBAAoB,EAAEa,cAAc,EAAEyD,cAAc,EAAEtB,aAAa,EAAElC,eAAe,EAAEiB,oBAAoB,EAAEsB,sBAAsB,EAAEG,OAAO,EAAEP,gBAAgB,EAAEmB,eAAe,EAAEC,qBAAqB,EAAEvE,UAAU,EAAEiE,SAAS,EAAErD,qBAAqB,EAAE0C,aAAa,EAAEM,WAAW,EAAEE,aAAa,EAAED,iBAAiB,EAAEtD,WAAW,EAAEE,aAAa,EAAED,iBAAiB,EAAEkC,WAAW,EAAEH,QAAQ,EAAEc,SAAS,EAAEpD,YAAY,EAAEqB,aAAa,EAAEO,aAAa,EAAEE,WAAW,EAAEd,gBAAgB,EAAES,QAAQ,EAAEyC,UAAU,EAAEX,MAAM,EAAEnB,aAAa,EAAED,MAAM,EAAE9B,WAAW,EAAEO,OAAO,EAAEC,OAAO,EAAEwB,QAAQ,EAAEjC,UAAU,EAAEgE,SAAS,EAAEV,gBAAgB,EAAEzC,cAAc,EAAEO,gBAAgB,EAAEtB,cAAc,EAAEsD,WAAW,EAAE7B,WAAW,EAAE8C,kBAAkB,EAAEV,QAAQ,EAAExB,UAAU,EAAEuB,UAAU,EAAEjB,WAAW,EAAE1B,UAAU,EAAEhB,YAAY,EAAEqE,YAAY,EAAEvC,SAAS,EAAEkC,eAAe,EAAEjD,WAAW,EAAEiC,YAAY,EAAEpB,SAAS,EAAEF,yBAAyB,EAAEpB,oBAAoB,EAAEC,0BAA0B,EAAEZ,cAAc,EAAEoC,YAAY,EAAES,WAAW,EAAEsB,gBAAgB,EAAEzB,aAAa,EAAE3C,YAAY,EAAEyB,cAAc,EAAEI,iBAAiB,EAAEH,WAAW,EAAEwB,UAAU,EAAE3B,cAAc,EAAExB,kBAAkB,EAAE8C,cAAc,EAAEE,aAAa,EAAEI,aAAa,EAAE0B,mBAAmB,EAAEC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}