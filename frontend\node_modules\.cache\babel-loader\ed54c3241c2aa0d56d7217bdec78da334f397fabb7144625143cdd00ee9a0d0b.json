{"ast": null, "code": "import React, { memo, useContext } from 'react';\nimport { FieldContext, useWatch } from 'rc-field-form';\nimport { useUpdate } from 'ahooks';\nimport { useIsomorphicUpdateLayoutEffect } from '../../utils/use-isomorphic-update-layout-effect';\nexport const FormSubscribe = props => {\n  const update = useUpdate();\n  const form = useContext(FieldContext);\n  const value = form.getFieldsValue(props.to);\n  // Memo to avoid useless render\n  const childNode = React.useMemo(() => props.children(value, form), [JSON.stringify(value), props.children]);\n  return React.createElement(React.Fragment, null, childNode, props.to.map(namePath => React.createElement(Watcher, {\n    key: namePath.toString(),\n    form: form,\n    namePath: namePath,\n    onChange: update\n  })));\n};\nexport const Watcher = memo(props => {\n  const value = useWatch(props.namePath, props.form);\n  useIsomorphicUpdateLayoutEffect(() => {\n    props.onChange();\n  }, [value]);\n  return null;\n});", "map": {"version": 3, "names": ["React", "memo", "useContext", "FieldContext", "useWatch", "useUpdate", "useIsomorphicUpdateLayoutEffect", "FormSubscribe", "props", "update", "form", "value", "getFieldsValue", "to", "childNode", "useMemo", "children", "JSON", "stringify", "createElement", "Fragment", "map", "namePath", "Watcher", "key", "toString", "onChange"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/form/form-subscribe.js"], "sourcesContent": ["import React, { memo, useContext } from 'react';\nimport { FieldContext, useWatch } from 'rc-field-form';\nimport { useUpdate } from 'ahooks';\nimport { useIsomorphicUpdateLayoutEffect } from '../../utils/use-isomorphic-update-layout-effect';\nexport const FormSubscribe = props => {\n  const update = useUpdate();\n  const form = useContext(FieldContext);\n  const value = form.getFieldsValue(props.to);\n  // Memo to avoid useless render\n  const childNode = React.useMemo(() => props.children(value, form), [JSON.stringify(value), props.children]);\n  return React.createElement(React.Fragment, null, childNode, props.to.map(namePath => React.createElement(Watcher, {\n    key: namePath.toString(),\n    form: form,\n    namePath: namePath,\n    onChange: update\n  })));\n};\nexport const Watcher = memo(props => {\n  const value = useWatch(props.namePath, props.form);\n  useIsomorphicUpdateLayoutEffect(() => {\n    props.onChange();\n  }, [value]);\n  return null;\n});"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,IAAI,EAAEC,UAAU,QAAQ,OAAO;AAC/C,SAASC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AACtD,SAASC,SAAS,QAAQ,QAAQ;AAClC,SAASC,+BAA+B,QAAQ,iDAAiD;AACjG,OAAO,MAAMC,aAAa,GAAGC,KAAK,IAAI;EACpC,MAAMC,MAAM,GAAGJ,SAAS,CAAC,CAAC;EAC1B,MAAMK,IAAI,GAAGR,UAAU,CAACC,YAAY,CAAC;EACrC,MAAMQ,KAAK,GAAGD,IAAI,CAACE,cAAc,CAACJ,KAAK,CAACK,EAAE,CAAC;EAC3C;EACA,MAAMC,SAAS,GAAGd,KAAK,CAACe,OAAO,CAAC,MAAMP,KAAK,CAACQ,QAAQ,CAACL,KAAK,EAAED,IAAI,CAAC,EAAE,CAACO,IAAI,CAACC,SAAS,CAACP,KAAK,CAAC,EAAEH,KAAK,CAACQ,QAAQ,CAAC,CAAC;EAC3G,OAAOhB,KAAK,CAACmB,aAAa,CAACnB,KAAK,CAACoB,QAAQ,EAAE,IAAI,EAAEN,SAAS,EAAEN,KAAK,CAACK,EAAE,CAACQ,GAAG,CAACC,QAAQ,IAAItB,KAAK,CAACmB,aAAa,CAACI,OAAO,EAAE;IAChHC,GAAG,EAAEF,QAAQ,CAACG,QAAQ,CAAC,CAAC;IACxBf,IAAI,EAAEA,IAAI;IACVY,QAAQ,EAAEA,QAAQ;IAClBI,QAAQ,EAAEjB;EACZ,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD,OAAO,MAAMc,OAAO,GAAGtB,IAAI,CAACO,KAAK,IAAI;EACnC,MAAMG,KAAK,GAAGP,QAAQ,CAACI,KAAK,CAACc,QAAQ,EAAEd,KAAK,CAACE,IAAI,CAAC;EAClDJ,+BAA+B,CAAC,MAAM;IACpCE,KAAK,CAACkB,QAAQ,CAAC,CAAC;EAClB,CAAC,EAAE,CAACf,KAAK,CAAC,CAAC;EACX,OAAO,IAAI;AACb,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}