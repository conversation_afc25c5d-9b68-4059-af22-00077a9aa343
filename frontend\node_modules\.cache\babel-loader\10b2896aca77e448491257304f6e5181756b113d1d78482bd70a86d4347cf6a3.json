{"ast": null, "code": "import { useEffect } from 'react';\nvar ImgTypeMap = {\n  SVG: 'image/svg+xml',\n  ICO: 'image/x-icon',\n  GIF: 'image/gif',\n  PNG: 'image/png'\n};\nvar useFavicon = function (href) {\n  useEffect(function () {\n    if (!href) return;\n    var cutUrl = href.split('.');\n    var imgSuffix = cutUrl[cutUrl.length - 1].toLocaleUpperCase();\n    var link = document.querySelector(\"link[rel*='icon']\") || document.createElement('link');\n    link.type = ImgTypeMap[imgSuffix];\n    link.href = href;\n    link.rel = 'shortcut icon';\n    document.getElementsByTagName('head')[0].appendChild(link);\n  }, [href]);\n};\nexport default useFavicon;", "map": {"version": 3, "names": ["useEffect", "ImgTypeMap", "SVG", "ICO", "GIF", "PNG", "useFavicon", "href", "cutUrl", "split", "imgSuffix", "length", "toLocaleUpperCase", "link", "document", "querySelector", "createElement", "type", "rel", "getElementsByTagName", "append<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useFavicon/index.js"], "sourcesContent": ["import { useEffect } from 'react';\nvar ImgTypeMap = {\n  SVG: 'image/svg+xml',\n  ICO: 'image/x-icon',\n  GIF: 'image/gif',\n  PNG: 'image/png'\n};\nvar useFavicon = function (href) {\n  useEffect(function () {\n    if (!href) return;\n    var cutUrl = href.split('.');\n    var imgSuffix = cutUrl[cutUrl.length - 1].toLocaleUpperCase();\n    var link = document.querySelector(\"link[rel*='icon']\") || document.createElement('link');\n    link.type = ImgTypeMap[imgSuffix];\n    link.href = href;\n    link.rel = 'shortcut icon';\n    document.getElementsByTagName('head')[0].appendChild(link);\n  }, [href]);\n};\nexport default useFavicon;"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,IAAIC,UAAU,GAAG;EACfC,GAAG,EAAE,eAAe;EACpBC,GAAG,EAAE,cAAc;EACnBC,GAAG,EAAE,WAAW;EAChBC,GAAG,EAAE;AACP,CAAC;AACD,IAAIC,UAAU,GAAG,SAAAA,CAAUC,IAAI,EAAE;EAC/BP,SAAS,CAAC,YAAY;IACpB,IAAI,CAACO,IAAI,EAAE;IACX,IAAIC,MAAM,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;IAC5B,IAAIC,SAAS,GAAGF,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;IAC7D,IAAIC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC,IAAID,QAAQ,CAACE,aAAa,CAAC,MAAM,CAAC;IACxFH,IAAI,CAACI,IAAI,GAAGhB,UAAU,CAACS,SAAS,CAAC;IACjCG,IAAI,CAACN,IAAI,GAAGA,IAAI;IAChBM,IAAI,CAACK,GAAG,GAAG,eAAe;IAC1BJ,QAAQ,CAACK,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAACP,IAAI,CAAC;EAC5D,CAAC,EAAE,CAACN,IAAI,CAAC,CAAC;AACZ,CAAC;AACD,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}