{"ast": null, "code": "import { canUseDom } from './can-use-dom';\nconst defaultRoot = canUseDom ? window : undefined;\nconst overflowStylePatterns = ['scroll', 'auto', 'overlay'];\nfunction isElement(node) {\n  const ELEMENT_NODE_TYPE = 1;\n  return node.nodeType === ELEMENT_NODE_TYPE;\n}\nexport function getScrollParent(el, root = defaultRoot) {\n  let node = el;\n  while (node && node !== root && isElement(node)) {\n    if (node === document.body) {\n      return root;\n    }\n    const {\n      overflowY\n    } = window.getComputedStyle(node);\n    if (overflowStylePatterns.includes(overflowY) && node.scrollHeight > node.clientHeight) {\n      return node;\n    }\n    node = node.parentNode;\n  }\n  return root;\n}", "map": {"version": 3, "names": ["canUseDom", "defaultRoot", "window", "undefined", "overflowStylePatterns", "isElement", "node", "ELEMENT_NODE_TYPE", "nodeType", "getScrollParent", "el", "root", "document", "body", "overflowY", "getComputedStyle", "includes", "scrollHeight", "clientHeight", "parentNode"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/get-scroll-parent.js"], "sourcesContent": ["import { canUseDom } from './can-use-dom';\nconst defaultRoot = canUseDom ? window : undefined;\nconst overflowStylePatterns = ['scroll', 'auto', 'overlay'];\nfunction isElement(node) {\n  const ELEMENT_NODE_TYPE = 1;\n  return node.nodeType === ELEMENT_NODE_TYPE;\n}\nexport function getScrollParent(el, root = defaultRoot) {\n  let node = el;\n  while (node && node !== root && isElement(node)) {\n    if (node === document.body) {\n      return root;\n    }\n    const {\n      overflowY\n    } = window.getComputedStyle(node);\n    if (overflowStylePatterns.includes(overflowY) && node.scrollHeight > node.clientHeight) {\n      return node;\n    }\n    node = node.parentNode;\n  }\n  return root;\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,MAAMC,WAAW,GAAGD,SAAS,GAAGE,MAAM,GAAGC,SAAS;AAClD,MAAMC,qBAAqB,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;AAC3D,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,MAAMC,iBAAiB,GAAG,CAAC;EAC3B,OAAOD,IAAI,CAACE,QAAQ,KAAKD,iBAAiB;AAC5C;AACA,OAAO,SAASE,eAAeA,CAACC,EAAE,EAAEC,IAAI,GAAGV,WAAW,EAAE;EACtD,IAAIK,IAAI,GAAGI,EAAE;EACb,OAAOJ,IAAI,IAAIA,IAAI,KAAKK,IAAI,IAAIN,SAAS,CAACC,IAAI,CAAC,EAAE;IAC/C,IAAIA,IAAI,KAAKM,QAAQ,CAACC,IAAI,EAAE;MAC1B,OAAOF,IAAI;IACb;IACA,MAAM;MACJG;IACF,CAAC,GAAGZ,MAAM,CAACa,gBAAgB,CAACT,IAAI,CAAC;IACjC,IAAIF,qBAAqB,CAACY,QAAQ,CAACF,SAAS,CAAC,IAAIR,IAAI,CAACW,YAAY,GAAGX,IAAI,CAACY,YAAY,EAAE;MACtF,OAAOZ,IAAI;IACb;IACAA,IAAI,GAAGA,IAAI,CAACa,UAAU;EACxB;EACA,OAAOR,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}