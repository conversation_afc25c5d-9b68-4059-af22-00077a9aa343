{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\pages\\\\ScanUploadPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Button, Space, Card, Input, Form, Toast, NavBar, Loading, Modal, TextArea, Divider } from 'antd-mobile';\nimport { CameraOutline, PictureOutline, CheckOutline, CloseOutline, EditSOutline, ScanningOutline, LoopOutline } from 'antd-mobile-icons';\nimport axios from 'axios';\nimport { getCameraManager } from '../utils/cameraManager';\nimport { getDeviceType } from '../utils/deviceDetector';\nimport MobileCameraModal from '../components/MobileCameraModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScanUploadPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [parseLoading, setParseLoading] = useState(false);\n\n  // 圖片管理狀態\n  const [frontImage, setFrontImage] = useState({\n    file: null,\n    preview: null,\n    ocrText: '',\n    parseStatus: null // 'success', 'error', 'parsing', null\n  });\n  const [backImage, setBackImage] = useState({\n    file: null,\n    preview: null,\n    ocrText: '',\n    parseStatus: null // 'success', 'error', 'parsing', null\n  });\n  const [cameraModalVisible, setCameraModalVisible] = useState(false);\n  const [currentCaptureTarget, setCurrentCaptureTarget] = useState('front'); // 'front' | 'back'\n  const [stream, setStream] = useState(null);\n\n  // 新增：相機管理器和設備類型狀態\n  const [cameraManager, setCameraManager] = useState(null);\n  const [isMobileCameraMode, setIsMobileCameraMode] = useState(false);\n\n  // 統一的名片資料狀態 - 標準化9個欄位\n  const [cardData, setCardData] = useState({\n    name: '',\n    // 姓名\n    company_name: '',\n    // 公司名稱\n    position: '',\n    // 職位\n    mobile_phone: '',\n    // 手機\n    office_phone: '',\n    // 公司電話\n    email: '',\n    // Email\n    line_id: '',\n    // Line ID\n    notes: '',\n    // 備註\n    company_address_1: '',\n    // 公司地址一\n    company_address_2: '' // 公司地址二\n  });\n  const fileInputRef = useRef(null);\n  const videoRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // 初始化相機管理器和設備檢測\n  useEffect(() => {\n    const initializeCamera = async () => {\n      try {\n        const manager = getCameraManager();\n        setCameraManager(manager);\n        const type = getDeviceType();\n        setIsMobileCameraMode(type === 'mobile' || type === 'tablet');\n        console.log(`設備類型: ${type}, 移動端相機模式: ${type === 'mobile' || type === 'tablet'}`);\n      } catch (error) {\n        console.error('初始化相機管理器失敗:', error);\n      }\n    };\n    initializeCamera();\n  }, []);\n\n  // 更新圖片解析狀態\n  const updateImageParseStatus = useCallback((side, status) => {\n    if (side === 'front') {\n      setFrontImage(prev => ({\n        ...prev,\n        parseStatus: status\n      }));\n    } else {\n      setBackImage(prev => ({\n        ...prev,\n        parseStatus: status\n      }));\n    }\n  }, []);\n\n  // 智能解析OCR文字並填充表單\n  const parseAndFillOCRData = useCallback(async (ocrText, side) => {\n    if (!ocrText) return;\n    try {\n      updateImageParseStatus(side, 'parsing');\n\n      // 調用後端智能解析API\n      const response = await axios.post('/api/v1/ocr/parse-fields', {\n        ocr_text: ocrText,\n        side: side\n      });\n      if (response.data.success) {\n        const parsedFields = response.data.parsed_fields;\n\n        // 更新表單數據，保留已有數據，只更新新解析到的欄位\n        setCardData(prevData => {\n          const updatedData = {\n            ...prevData\n          };\n\n          // 智能合併邏輯：如果欄位已有數據，則保留；如果沒有，則使用新解析的數據\n          Object.keys(parsedFields).forEach(field => {\n            if (parsedFields[field] && (!updatedData[field] || updatedData[field].trim() === '')) {\n              updatedData[field] = parsedFields[field];\n            }\n          });\n          return updatedData;\n        });\n        updateImageParseStatus(side, 'success');\n        Toast.show({\n          content: `${side === 'front' ? '正面' : '反面'}資料解析完成！已自動填入相關欄位`,\n          position: 'center'\n        });\n      } else {\n        updateImageParseStatus(side, 'error');\n        Toast.show({\n          content: 'OCR資料解析失敗，請手動編輯',\n          position: 'center'\n        });\n      }\n    } catch (error) {\n      console.error('OCR解析錯誤:', error);\n      updateImageParseStatus(side, 'error');\n      Toast.show({\n        content: 'OCR資料解析失敗，請檢查網絡連接',\n        position: 'center'\n      });\n    }\n  }, [updateImageParseStatus]);\n\n  // 執行OCR並智能解析\n  const performOCR = useCallback(async (file, side) => {\n    if (!file) return;\n    setLoading(true);\n    updateImageParseStatus(side, 'parsing');\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      const response = await axios.post('/api/v1/ocr/image', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data.success) {\n        const ocrText = response.data.text;\n\n        // 更新對應面的OCR結果\n        if (side === 'front') {\n          setFrontImage(prev => ({\n            ...prev,\n            ocrText\n          }));\n        } else {\n          setBackImage(prev => ({\n            ...prev,\n            ocrText\n          }));\n        }\n\n        // 執行智能解析並填充表單\n        await parseAndFillOCRData(ocrText, side);\n        Toast.show({\n          content: `${side === 'front' ? '正面' : '反面'}OCR識別完成！`,\n          position: 'center'\n        });\n      } else {\n        updateImageParseStatus(side, 'error');\n        Toast.show({\n          content: 'OCR識別失敗，請重試',\n          position: 'center'\n        });\n      }\n    } catch (error) {\n      console.error('OCR錯誤:', error);\n      updateImageParseStatus(side, 'error');\n      Toast.show({\n        content: 'OCR識別失敗，請檢查網絡連接並重試',\n        position: 'center'\n      });\n    } finally {\n      setLoading(false);\n    }\n  }, [parseAndFillOCRData, updateImageParseStatus]);\n\n  // 手動解析當前圖片\n  const handleManualParse = useCallback(async () => {\n    const currentImage = currentCaptureTarget === 'front' ? frontImage : backImage;\n    if (!currentImage.file) {\n      Toast.show({\n        content: '請先拍攝或選擇圖片',\n        position: 'center'\n      });\n      return;\n    }\n    if (currentImage.ocrText) {\n      // 如果已有OCR文本，直接解析\n      setParseLoading(true);\n      try {\n        await parseAndFillOCRData(currentImage.ocrText, currentCaptureTarget);\n      } finally {\n        setParseLoading(false);\n      }\n    } else {\n      // 如果沒有OCR文本，執行完整的OCR流程\n      await performOCR(currentImage.file, currentCaptureTarget);\n    }\n  }, [currentCaptureTarget, frontImage, backImage, parseAndFillOCRData, performOCR]);\n\n  // 處理圖片上傳\n  const handleImageUpload = useCallback(async (file, target = currentCaptureTarget) => {\n    const reader = new FileReader();\n    reader.onload = e => {\n      if (target === 'front') {\n        setFrontImage(prev => ({\n          ...prev,\n          file,\n          preview: e.target.result,\n          parseStatus: null // 重置解析狀態\n        }));\n      } else {\n        setBackImage(prev => ({\n          ...prev,\n          file,\n          preview: e.target.result,\n          parseStatus: null // 重置解析狀態\n        }));\n      }\n    };\n    reader.readAsDataURL(file);\n\n    // 自動執行OCR\n    await performOCR(file, target);\n  }, [performOCR, currentCaptureTarget]);\n\n  // 啟動攝像頭 - 使用新的相機管理器\n  const startCamera = async target => {\n    if (!cameraManager) {\n      Toast.show({\n        content: '相機管理器未初始化',\n        position: 'center'\n      });\n      return;\n    }\n    setCurrentCaptureTarget(target);\n    try {\n      if (isMobileCameraMode) {\n        // 移動端：使用全屏相機模式\n        setCameraModalVisible(true);\n      } else {\n        // Web端：使用傳統Modal模式\n        const mediaStream = await cameraManager.startCamera(target, {\n          videoElement: videoRef.current,\n          canvasElement: canvasRef.current\n        });\n        setStream(mediaStream);\n        setCameraModalVisible(true);\n      }\n    } catch (error) {\n      console.error('無法啟動攝像頭:', error);\n      Toast.show({\n        content: '無法啟動攝像頭，請檢查權限設置',\n        position: 'center'\n      });\n    }\n  };\n\n  // 停止攝像頭 - 使用新的相機管理器\n  const stopCamera = () => {\n    if (cameraManager) {\n      cameraManager.stopCamera();\n    }\n    if (stream) {\n      stream.getTracks().forEach(track => track.stop());\n      setStream(null);\n    }\n    setCameraModalVisible(false);\n  };\n\n  // 拍照 - 使用新的相機管理器\n  const takePhoto = async () => {\n    if (!cameraManager) return;\n    try {\n      const result = await cameraManager.takePhoto();\n      if (result && result.file) {\n        await handleImageUpload(result.file, currentCaptureTarget);\n        stopCamera();\n      }\n    } catch (error) {\n      console.error('拍照失敗:', error);\n      Toast.show({\n        content: '拍照失敗，請重試',\n        position: 'center'\n      });\n    }\n  };\n\n  // 移動端相機拍照完成回調\n  const handleMobilePhotoTaken = async data => {\n    if (data && data.file) {\n      await handleImageUpload(data.file, currentCaptureTarget);\n    }\n  };\n\n  // 從相冊選擇\n  const selectFromGallery = target => {\n    var _fileInputRef$current;\n    setCurrentCaptureTarget(target);\n    (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n  };\n\n  // 處理文件選擇\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      handleImageUpload(file, currentCaptureTarget);\n      // 清空input以便重複選擇同一文件\n      event.target.value = '';\n    }\n  };\n\n  // 保存名片資料\n  const handleSave = async () => {\n    // 驗證必填欄位\n    if (!cardData.name.trim()) {\n      Toast.show({\n        content: '請輸入姓名',\n        position: 'center'\n      });\n      return;\n    }\n    setLoading(true);\n    try {\n      const saveData = new FormData();\n\n      // 添加名片資料\n      Object.keys(cardData).forEach(key => {\n        if (cardData[key]) {\n          saveData.append(key, cardData[key]);\n        }\n      });\n\n      // 添加圖片文件\n      if (frontImage.file) {\n        saveData.append('front_image', frontImage.file);\n      }\n      if (backImage.file) {\n        saveData.append('back_image', backImage.file);\n      }\n\n      // 添加OCR原始文字\n      if (frontImage.ocrText) {\n        saveData.append('front_ocr_text', frontImage.ocrText);\n      }\n      if (backImage.ocrText) {\n        saveData.append('back_ocr_text', backImage.ocrText);\n      }\n      const response = await axios.post('/api/v1/cards', saveData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data.success) {\n        Toast.show({\n          content: '名片保存成功！',\n          position: 'center'\n        });\n        // 清空表單\n        setCardData({\n          name: '',\n          company_name: '',\n          position: '',\n          mobile_phone: '',\n          office_phone: '',\n          email: '',\n          line_id: '',\n          notes: '',\n          company_address_1: '',\n          company_address_2: ''\n        });\n        setFrontImage({\n          file: null,\n          preview: null,\n          ocrText: '',\n          parseStatus: null\n        });\n        setBackImage({\n          file: null,\n          preview: null,\n          ocrText: '',\n          parseStatus: null\n        });\n        // 導航到名片管理頁面\n        navigate('/cards');\n      }\n    } catch (error) {\n      console.error('保存失敗:', error);\n      Toast.show({\n        content: '保存失敗，請重試',\n        position: 'center'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 表單欄位更新處理\n  const handleFieldChange = (field, value) => {\n    setCardData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  // 獲取解析狀態圖標和顏色\n  const getParseStatusIcon = status => {\n    switch (status) {\n      case 'parsing':\n        return /*#__PURE__*/_jsxDEV(LoopOutline, {\n          style: {\n            color: '#1677ff',\n            animation: 'spin 1s linear infinite'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 16\n        }, this);\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(CheckOutline, {\n          style: {\n            color: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 16\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(CloseOutline, {\n          style: {\n            color: '#ff4d4f'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n\n  // 添加旋轉動畫樣式\n  React.useEffect(() => {\n    const style = document.createElement('style');\n    style.textContent = `\n      @keyframes spin {\n        from { transform: rotate(0deg); }\n        to { transform: rotate(360deg); }\n      }\n    `;\n    document.head.appendChild(style);\n    return () => document.head.removeChild(style);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"scan-upload-page\",\n    children: [/*#__PURE__*/_jsxDEV(NavBar, {\n      onBack: () => navigate(-1),\n      children: \"\\u540D\\u7247\\u8CC7\\u6599\\u8F38\\u5165\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 19\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content\",\n      style: {\n        padding: '16px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u62CD\\u651D\\u540D\\u7247\",\n        style: {\n          marginBottom: '16px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"image-capture-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"capture-mode-switch\",\n            style: {\n              marginBottom: '16px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                width: '100%',\n                justifyContent: 'center',\n                gap: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                color: currentCaptureTarget === 'front' ? 'primary' : 'default',\n                fill: currentCaptureTarget === 'front' ? 'solid' : 'outline',\n                onClick: () => setCurrentCaptureTarget('front'),\n                style: {\n                  flex: 1\n                },\n                children: \"\\u6B63\\u9762\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                color: currentCaptureTarget === 'back' ? 'primary' : 'default',\n                fill: currentCaptureTarget === 'back' ? 'solid' : 'outline',\n                onClick: () => setCurrentCaptureTarget('back'),\n                style: {\n                  flex: 1\n                },\n                children: \"\\u53CD\\u9762\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                color: \"success\",\n                fill: \"outline\",\n                onClick: handleManualParse,\n                disabled: (currentCaptureTarget === 'front' ? !frontImage.file : !backImage.file) || parseLoading,\n                style: {\n                  flex: 1\n                },\n                children: [parseLoading ? /*#__PURE__*/_jsxDEV(LoopOutline, {\n                  style: {\n                    animation: 'spin 1s linear infinite'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 35\n                }, this) : /*#__PURE__*/_jsxDEV(ScanningOutline, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 102\n                }, this), \" \\u89E3\\u6790\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"single-capture-frame\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"current-side-title\",\n              style: {\n                marginBottom: '8px',\n                fontSize: '14px',\n                fontWeight: 'bold',\n                textAlign: 'center'\n              },\n              children: [\"\\u7576\\u524D\\u62CD\\u651D: \", currentCaptureTarget === 'front' ? '正面' : '反面', getParseStatusIcon((currentCaptureTarget === 'front' ? frontImage : backImage).parseStatus)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this), (currentCaptureTarget === 'front' ? frontImage.preview : backImage.preview) ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: currentCaptureTarget === 'front' ? frontImage.preview : backImage.preview,\n              alt: `名片${currentCaptureTarget === 'front' ? '正面' : '反面'}`,\n              style: {\n                width: '100%',\n                height: 'clamp(280px, 40vw, 400px)',\n                objectFit: 'cover',\n                borderRadius: '8px',\n                marginBottom: '16px',\n                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n                transition: 'all 0.3s ease'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%',\n                height: 'clamp(280px, 40vw, 400px)',\n                border: '2px dashed #d9d9d9',\n                borderRadius: '8px',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: '#999',\n                marginBottom: '16px',\n                background: '#fafafa',\n                transition: 'all 0.3s ease'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CameraOutline, {\n                style: {\n                  fontSize: '64px',\n                  marginBottom: '12px',\n                  color: '#ccc'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '16px',\n                  textAlign: 'center'\n                },\n                children: [\"\\u8ACB\\u62CD\\u651D\\u540D\\u7247\", currentCaptureTarget === 'front' ? '正面' : '反面']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: '#bbb',\n                  marginTop: '8px'\n                },\n                children: \"\\u9EDE\\u64CA\\u4E0B\\u65B9\\u6309\\u9215\\u958B\\u59CB\\u62CD\\u7167\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '8px',\n                justifyContent: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                color: \"primary\",\n                onClick: () => startCamera(currentCaptureTarget),\n                style: {\n                  flex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(CameraOutline, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 19\n                }, this), \" \\u62CD\\u7167\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                color: \"primary\",\n                fill: \"outline\",\n                onClick: () => selectFromGallery(currentCaptureTarget),\n                style: {\n                  flex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(PictureOutline, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 19\n                }, this), \" \\u76F8\\u518A\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"capture-status\",\n              style: {\n                marginTop: '12px',\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '4px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '8px',\n                      height: '8px',\n                      borderRadius: '50%',\n                      backgroundColor: frontImage.preview ? '#52c41a' : '#d9d9d9'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: '12px',\n                      color: frontImage.preview ? '#52c41a' : '#8c8c8c'\n                    },\n                    children: [\"\\u6B63\\u9762 \", frontImage.preview ? '已拍攝' : '未拍攝']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 21\n                  }, this), frontImage.parseStatus && getParseStatusIcon(frontImage.parseStatus)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '4px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '8px',\n                      height: '8px',\n                      borderRadius: '50%',\n                      backgroundColor: backImage.preview ? '#52c41a' : '#d9d9d9'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: '12px',\n                      color: backImage.preview ? '#52c41a' : '#8c8c8c'\n                    },\n                    children: [\"\\u53CD\\u9762 \", backImage.preview ? '已拍攝' : '未拍攝']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 21\n                  }, this), backImage.parseStatus && getParseStatusIcon(backImage.parseStatus)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u540D\\u7247\\u8CC7\\u6599\",\n        extra: /*#__PURE__*/_jsxDEV(EditSOutline, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 35\n        }, this),\n        style: {\n          marginBottom: '16px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          layout: \"vertical\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-section\",\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              children: \"\\u57FA\\u672C\\u8CC7\\u8A0A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u59D3\\u540D *\",\n              required: true,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8ACB\\u8F38\\u5165\\u59D3\\u540D\",\n                value: cardData.name,\n                onChange: value => handleFieldChange('name', value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u516C\\u53F8\\u540D\\u7A31\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8ACB\\u8F38\\u5165\\u516C\\u53F8\\u540D\\u7A31\",\n                value: cardData.company_name,\n                onChange: value => handleFieldChange('company_name', value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u8077\\u4F4D\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8ACB\\u8F38\\u5165\\u8077\\u4F4D\",\n                value: cardData.position,\n                onChange: value => handleFieldChange('position', value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-section\",\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              children: \"\\u806F\\u7D61\\u8CC7\\u8A0A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u624B\\u6A5F\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8ACB\\u8F38\\u5165\\u624B\\u6A5F\\u865F\\u78BC\",\n                value: cardData.mobile_phone,\n                onChange: value => handleFieldChange('mobile_phone', value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u516C\\u53F8\\u96FB\\u8A71\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8ACB\\u8F38\\u5165\\u516C\\u53F8\\u96FB\\u8A71\",\n                value: cardData.office_phone,\n                onChange: value => handleFieldChange('office_phone', value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Email\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8ACB\\u8F38\\u5165Email\\u5730\\u5740\",\n                value: cardData.email,\n                onChange: value => handleFieldChange('email', value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Line ID\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8ACB\\u8F38\\u5165Line ID\",\n                value: cardData.line_id,\n                onChange: value => handleFieldChange('line_id', value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 665,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-section\",\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              children: \"\\u5730\\u5740\\u8CC7\\u8A0A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u516C\\u53F8\\u5730\\u5740\\u4E00\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8ACB\\u8F38\\u5165\\u516C\\u53F8\\u5730\\u5740\",\n                value: cardData.company_address_1,\n                onChange: value => handleFieldChange('company_address_1', value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u516C\\u53F8\\u5730\\u5740\\u4E8C\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8ACB\\u8F38\\u5165\\u516C\\u53F8\\u5730\\u5740\\uFF08\\u88DC\\u5145\\uFF09\",\n                value: cardData.company_address_2,\n                onChange: value => handleFieldChange('company_address_2', value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-section\",\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              children: \"\\u5176\\u4ED6\\u8CC7\\u8A0A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u5099\\u8A3B\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                placeholder: \"\\u8ACB\\u8F38\\u5165\\u5099\\u8A3B\\u8CC7\\u8A0A\",\n                rows: 3,\n                value: cardData.notes,\n                onChange: value => handleFieldChange('notes', value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 695,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 605,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        style: {\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          color: \"primary\",\n          size: \"large\",\n          block: true,\n          onClick: handleSave,\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(CheckOutline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 13\n          }, this), \" \\u4FDD\\u5B58\\u540D\\u7247\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 711,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 467,\n      columnNumber: 7\n    }, this), isMobileCameraMode ?\n    /*#__PURE__*/\n    // 移動端：使用全屏相機組件\n    _jsxDEV(MobileCameraModal, {\n      visible: cameraModalVisible,\n      onClose: stopCamera,\n      onPhotoTaken: handleMobilePhotoTaken,\n      cameraManager: cameraManager,\n      target: currentCaptureTarget\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 727,\n      columnNumber: 9\n    }, this) :\n    /*#__PURE__*/\n    // Web端：使用傳統Modal\n    _jsxDEV(Modal, {\n      visible: cameraModalVisible,\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"camera-modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"video\", {\n          ref: videoRef,\n          autoPlay: true,\n          playsInline: true,\n          style: {\n            width: '100%',\n            height: 'clamp(350px, 50vh, 500px)',\n            objectFit: 'cover',\n            borderRadius: '8px',\n            background: '#000'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n          ref: canvasRef,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 752,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '20px',\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            size: \"large\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              color: \"primary\",\n              size: \"large\",\n              onClick: takePhoto,\n              style: {\n                minWidth: '100px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CameraOutline, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 21\n              }, this), \" \\u62CD\\u7167\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              color: \"default\",\n              size: \"large\",\n              onClick: stopCamera,\n              style: {\n                minWidth: '100px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CloseOutline, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 21\n              }, this), \" \\u53D6\\u6D88\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 763,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 753,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 739,\n        columnNumber: 13\n      }, this),\n      onClose: stopCamera\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 736,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"file\",\n      ref: fileInputRef,\n      accept: \"image/*\",\n      style: {\n        display: 'none'\n      },\n      onChange: handleFileSelect\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 780,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 462,\n    columnNumber: 5\n  }, this);\n};\n_s(ScanUploadPage, \"80R3+Zg45BZOeBViyuhFxA+6H/g=\", false, function () {\n  return [useNavigate];\n});\n_c = ScanUploadPage;\nexport default ScanUploadPage;\nvar _c;\n$RefreshReg$(_c, \"ScanUploadPage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "useEffect", "useNavigate", "<PERSON><PERSON>", "Space", "Card", "Input", "Form", "Toast", "NavBar", "Loading", "Modal", "TextArea", "Divider", "CameraOutline", "PictureOutline", "CheckOutline", "CloseOutline", "EditSOutline", "ScanningOutline", "LoopOutline", "axios", "getCameraManager", "getDeviceType", "MobileCameraModal", "jsxDEV", "_jsxDEV", "ScanUploadPage", "_s", "navigate", "loading", "setLoading", "parseLoading", "setParseLoading", "frontImage", "setFrontImage", "file", "preview", "ocrText", "parseStatus", "backImage", "setBackImage", "cameraModalVisible", "setCameraModalVisible", "currentCaptureTarget", "setCurrentCaptureTarget", "stream", "setStream", "cameraManager", "setCameraManager", "isMobileCameraMode", "setIsMobileCameraMode", "cardData", "setCardData", "name", "company_name", "position", "mobile_phone", "office_phone", "email", "line_id", "notes", "company_address_1", "company_address_2", "fileInputRef", "videoRef", "canvasRef", "initializeCamera", "manager", "type", "console", "log", "error", "updateImageParseStatus", "side", "status", "prev", "parseAndFillOCRData", "response", "post", "ocr_text", "data", "success", "parsedFields", "parsed_fields", "prevData", "updatedData", "Object", "keys", "for<PERSON>ach", "field", "trim", "show", "content", "performOCR", "formData", "FormData", "append", "headers", "text", "handleManualParse", "currentImage", "handleImageUpload", "target", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "startCamera", "mediaStream", "videoElement", "current", "canvasElement", "stopCamera", "getTracks", "track", "stop", "<PERSON><PERSON><PERSON><PERSON>", "handleMobilePhotoTaken", "selectFromGallery", "_fileInputRef$current", "click", "handleFileSelect", "event", "files", "value", "handleSave", "saveData", "key", "handleFieldChange", "getParseStatusIcon", "style", "color", "animation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "document", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "className", "children", "onBack", "padding", "title", "marginBottom", "width", "justifyContent", "gap", "fill", "onClick", "flex", "disabled", "fontSize", "fontWeight", "textAlign", "src", "alt", "height", "objectFit", "borderRadius", "boxShadow", "transition", "border", "display", "flexDirection", "alignItems", "background", "marginTop", "backgroundColor", "extra", "layout", "<PERSON><PERSON>", "label", "required", "placeholder", "onChange", "rows", "direction", "size", "block", "visible", "onClose", "onPhotoTaken", "ref", "autoPlay", "playsInline", "min<PERSON><PERSON><PERSON>", "accept", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/pages/ScanUploadPage.js"], "sourcesContent": ["import React, { useState, useRef, useCallback, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Button,\n  Space,\n  Card,\n  Input,\n  Form,\n  Toast,\n  NavBar,\n  Loading,\n  Modal,\n  TextArea,\n  Divider\n} from 'antd-mobile';\nimport {\n  CameraOutline,\n  PictureOutline,\n  CheckOutline,\n  CloseOutline,\n  EditSOutline,\n  ScanningOutline,\n  LoopOutline\n} from 'antd-mobile-icons';\nimport axios from 'axios';\nimport { getCameraManager } from '../utils/cameraManager';\nimport { getDeviceType } from '../utils/deviceDetector';\nimport MobileCameraModal from '../components/MobileCameraModal';\n\nconst ScanUploadPage = () => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [parseLoading, setParseLoading] = useState(false);\n\n  // 圖片管理狀態\n  const [frontImage, setFrontImage] = useState({\n    file: null,\n    preview: null,\n    ocrText: '',\n    parseStatus: null // 'success', 'error', 'parsing', null\n  });\n  const [backImage, setBackImage] = useState({\n    file: null,\n    preview: null,\n    ocrText: '',\n    parseStatus: null // 'success', 'error', 'parsing', null\n  });\n  const [cameraModalVisible, setCameraModalVisible] = useState(false);\n  const [currentCaptureTarget, setCurrentCaptureTarget] = useState('front'); // 'front' | 'back'\n  const [stream, setStream] = useState(null);\n\n  // 新增：相機管理器和設備類型狀態\n  const [cameraManager, setCameraManager] = useState(null);\n  const [isMobileCameraMode, setIsMobileCameraMode] = useState(false);\n  \n  // 統一的名片資料狀態 - 標準化9個欄位\n  const [cardData, setCardData] = useState({\n    name: '',                    // 姓名\n    company_name: '',            // 公司名稱\n    position: '',                // 職位\n    mobile_phone: '',            // 手機\n    office_phone: '',            // 公司電話\n    email: '',                   // Email\n    line_id: '',                 // Line ID\n    notes: '',                   // 備註\n    company_address_1: '',       // 公司地址一\n    company_address_2: ''        // 公司地址二\n  });\n  \n  const fileInputRef = useRef(null);\n  const videoRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // 初始化相機管理器和設備檢測\n  useEffect(() => {\n    const initializeCamera = async () => {\n      try {\n        const manager = getCameraManager();\n        setCameraManager(manager);\n\n        const type = getDeviceType();\n        setIsMobileCameraMode(type === 'mobile' || type === 'tablet');\n\n        console.log(`設備類型: ${type}, 移動端相機模式: ${type === 'mobile' || type === 'tablet'}`);\n      } catch (error) {\n        console.error('初始化相機管理器失敗:', error);\n      }\n    };\n\n    initializeCamera();\n  }, []);\n\n  // 更新圖片解析狀態\n  const updateImageParseStatus = useCallback((side, status) => {\n    if (side === 'front') {\n      setFrontImage(prev => ({ ...prev, parseStatus: status }));\n    } else {\n      setBackImage(prev => ({ ...prev, parseStatus: status }));\n    }\n  }, []);\n\n  // 智能解析OCR文字並填充表單\n  const parseAndFillOCRData = useCallback(async (ocrText, side) => {\n    if (!ocrText) return;\n    \n    try {\n      updateImageParseStatus(side, 'parsing');\n      \n      // 調用後端智能解析API\n      const response = await axios.post('/api/v1/ocr/parse-fields', {\n        ocr_text: ocrText,\n        side: side\n      });\n\n      if (response.data.success) {\n        const parsedFields = response.data.parsed_fields;\n        \n        // 更新表單數據，保留已有數據，只更新新解析到的欄位\n        setCardData(prevData => {\n          const updatedData = { ...prevData };\n          \n          // 智能合併邏輯：如果欄位已有數據，則保留；如果沒有，則使用新解析的數據\n          Object.keys(parsedFields).forEach(field => {\n            if (parsedFields[field] && (!updatedData[field] || updatedData[field].trim() === '')) {\n              updatedData[field] = parsedFields[field];\n            }\n          });\n          \n          return updatedData;\n        });\n        \n        updateImageParseStatus(side, 'success');\n        \n        Toast.show({\n          content: `${side === 'front' ? '正面' : '反面'}資料解析完成！已自動填入相關欄位`,\n          position: 'center',\n        });\n      } else {\n        updateImageParseStatus(side, 'error');\n        Toast.show({\n          content: 'OCR資料解析失敗，請手動編輯',\n          position: 'center',\n        });\n      }\n    } catch (error) {\n      console.error('OCR解析錯誤:', error);\n      updateImageParseStatus(side, 'error');\n      Toast.show({\n        content: 'OCR資料解析失敗，請檢查網絡連接',\n        position: 'center',\n      });\n    }\n  }, [updateImageParseStatus]);\n\n  // 執行OCR並智能解析\n  const performOCR = useCallback(async (file, side) => {\n    if (!file) return;\n    \n    setLoading(true);\n    updateImageParseStatus(side, 'parsing');\n    \n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n\n      const response = await axios.post('/api/v1/ocr/image', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n\n      if (response.data.success) {\n        const ocrText = response.data.text;\n        \n        // 更新對應面的OCR結果\n        if (side === 'front') {\n          setFrontImage(prev => ({ ...prev, ocrText }));\n        } else {\n          setBackImage(prev => ({ ...prev, ocrText }));\n        }\n        \n        // 執行智能解析並填充表單\n        await parseAndFillOCRData(ocrText, side);\n        \n        Toast.show({\n          content: `${side === 'front' ? '正面' : '反面'}OCR識別完成！`,\n          position: 'center',\n        });\n      } else {\n        updateImageParseStatus(side, 'error');\n        Toast.show({\n          content: 'OCR識別失敗，請重試',\n          position: 'center',\n        });\n      }\n    } catch (error) {\n      console.error('OCR錯誤:', error);\n      updateImageParseStatus(side, 'error');\n      Toast.show({\n        content: 'OCR識別失敗，請檢查網絡連接並重試',\n        position: 'center',\n      });\n    } finally {\n      setLoading(false);\n    }\n  }, [parseAndFillOCRData, updateImageParseStatus]);\n\n  // 手動解析當前圖片\n  const handleManualParse = useCallback(async () => {\n    const currentImage = currentCaptureTarget === 'front' ? frontImage : backImage;\n    \n    if (!currentImage.file) {\n      Toast.show({\n        content: '請先拍攝或選擇圖片',\n        position: 'center',\n      });\n      return;\n    }\n\n    if (currentImage.ocrText) {\n      // 如果已有OCR文本，直接解析\n      setParseLoading(true);\n      try {\n        await parseAndFillOCRData(currentImage.ocrText, currentCaptureTarget);\n      } finally {\n        setParseLoading(false);\n      }\n    } else {\n      // 如果沒有OCR文本，執行完整的OCR流程\n      await performOCR(currentImage.file, currentCaptureTarget);\n    }\n  }, [currentCaptureTarget, frontImage, backImage, parseAndFillOCRData, performOCR]);\n\n  // 處理圖片上傳\n  const handleImageUpload = useCallback(async (file, target = currentCaptureTarget) => {\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      if (target === 'front') {\n        setFrontImage(prev => ({ \n          ...prev, \n          file, \n          preview: e.target.result,\n          parseStatus: null // 重置解析狀態\n        }));\n      } else {\n        setBackImage(prev => ({ \n          ...prev, \n          file, \n          preview: e.target.result,\n          parseStatus: null // 重置解析狀態\n        }));\n      }\n    };\n    reader.readAsDataURL(file);\n\n    // 自動執行OCR\n    await performOCR(file, target);\n  }, [performOCR, currentCaptureTarget]);\n\n  // 啟動攝像頭 - 使用新的相機管理器\n  const startCamera = async (target) => {\n    if (!cameraManager) {\n      Toast.show({\n        content: '相機管理器未初始化',\n        position: 'center',\n      });\n      return;\n    }\n\n    setCurrentCaptureTarget(target);\n\n    try {\n      if (isMobileCameraMode) {\n        // 移動端：使用全屏相機模式\n        setCameraModalVisible(true);\n      } else {\n        // Web端：使用傳統Modal模式\n        const mediaStream = await cameraManager.startCamera(target, {\n          videoElement: videoRef.current,\n          canvasElement: canvasRef.current\n        });\n        setStream(mediaStream);\n        setCameraModalVisible(true);\n      }\n    } catch (error) {\n      console.error('無法啟動攝像頭:', error);\n      Toast.show({\n        content: '無法啟動攝像頭，請檢查權限設置',\n        position: 'center',\n      });\n    }\n  };\n\n  // 停止攝像頭 - 使用新的相機管理器\n  const stopCamera = () => {\n    if (cameraManager) {\n      cameraManager.stopCamera();\n    }\n    if (stream) {\n      stream.getTracks().forEach(track => track.stop());\n      setStream(null);\n    }\n    setCameraModalVisible(false);\n  };\n\n  // 拍照 - 使用新的相機管理器\n  const takePhoto = async () => {\n    if (!cameraManager) return;\n\n    try {\n      const result = await cameraManager.takePhoto();\n      if (result && result.file) {\n        await handleImageUpload(result.file, currentCaptureTarget);\n        stopCamera();\n      }\n    } catch (error) {\n      console.error('拍照失敗:', error);\n      Toast.show({\n        content: '拍照失敗，請重試',\n        position: 'center',\n      });\n    }\n  };\n\n  // 移動端相機拍照完成回調\n  const handleMobilePhotoTaken = async (data) => {\n    if (data && data.file) {\n      await handleImageUpload(data.file, currentCaptureTarget);\n    }\n  };\n\n  // 從相冊選擇\n  const selectFromGallery = (target) => {\n    setCurrentCaptureTarget(target);\n    fileInputRef.current?.click();\n  };\n\n  // 處理文件選擇\n  const handleFileSelect = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      handleImageUpload(file, currentCaptureTarget);\n      // 清空input以便重複選擇同一文件\n      event.target.value = '';\n    }\n  };\n\n  // 保存名片資料\n  const handleSave = async () => {\n    // 驗證必填欄位\n    if (!cardData.name.trim()) {\n      Toast.show({\n        content: '請輸入姓名',\n        position: 'center',\n      });\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const saveData = new FormData();\n      \n      // 添加名片資料\n      Object.keys(cardData).forEach(key => {\n        if (cardData[key]) {\n          saveData.append(key, cardData[key]);\n        }\n      });\n      \n      // 添加圖片文件\n      if (frontImage.file) {\n        saveData.append('front_image', frontImage.file);\n      }\n      if (backImage.file) {\n        saveData.append('back_image', backImage.file);\n      }\n      \n      // 添加OCR原始文字\n      if (frontImage.ocrText) {\n        saveData.append('front_ocr_text', frontImage.ocrText);\n      }\n      if (backImage.ocrText) {\n        saveData.append('back_ocr_text', backImage.ocrText);\n      }\n\n      const response = await axios.post('/api/v1/cards', saveData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n\n      if (response.data.success) {\n        Toast.show({\n          content: '名片保存成功！',\n          position: 'center',\n        });\n        // 清空表單\n        setCardData({\n          name: '',\n          company_name: '',\n          position: '',\n          mobile_phone: '',\n          office_phone: '',\n          email: '',\n          line_id: '',\n          notes: '',\n          company_address_1: '',\n          company_address_2: ''\n        });\n        setFrontImage({ file: null, preview: null, ocrText: '', parseStatus: null });\n        setBackImage({ file: null, preview: null, ocrText: '', parseStatus: null });\n        // 導航到名片管理頁面\n        navigate('/cards');\n      }\n    } catch (error) {\n      console.error('保存失敗:', error);\n      Toast.show({\n        content: '保存失敗，請重試',\n        position: 'center',\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 表單欄位更新處理\n  const handleFieldChange = (field, value) => {\n    setCardData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  // 獲取解析狀態圖標和顏色\n  const getParseStatusIcon = (status) => {\n    switch (status) {\n      case 'parsing':\n        return <LoopOutline style={{ color: '#1677ff', animation: 'spin 1s linear infinite' }} />;\n      case 'success':\n        return <CheckOutline style={{ color: '#52c41a' }} />;\n      case 'error':\n        return <CloseOutline style={{ color: '#ff4d4f' }} />;\n      default:\n        return null;\n    }\n  };\n\n  // 添加旋轉動畫樣式\n  React.useEffect(() => {\n    const style = document.createElement('style');\n    style.textContent = `\n      @keyframes spin {\n        from { transform: rotate(0deg); }\n        to { transform: rotate(360deg); }\n      }\n    `;\n    document.head.appendChild(style);\n    return () => document.head.removeChild(style);\n  }, []);\n\n  return (\n    <div className=\"scan-upload-page\">\n      <NavBar onBack={() => navigate(-1)}>名片資料輸入</NavBar>\n      \n      {loading && <Loading />}\n      \n      <div className=\"content\" style={{ padding: '16px' }}>\n        {/* 圖片拍攝區域 */}\n        <Card title=\"拍攝名片\" style={{ marginBottom: '16px' }}>\n          <div className=\"image-capture-section\">\n            {/* 拍攝模式切換與解析操作 */}\n            <div className=\"capture-mode-switch\" style={{ marginBottom: '16px' }}>\n              <Space style={{ width: '100%', justifyContent: 'center', gap: '8px' }}>\n                <Button \n                  color={currentCaptureTarget === 'front' ? 'primary' : 'default'}\n                  fill={currentCaptureTarget === 'front' ? 'solid' : 'outline'}\n                  onClick={() => setCurrentCaptureTarget('front')}\n                  style={{ flex: 1 }}\n                >\n                  正面\n                </Button>\n                <Button \n                  color={currentCaptureTarget === 'back' ? 'primary' : 'default'}\n                  fill={currentCaptureTarget === 'back' ? 'solid' : 'outline'}\n                  onClick={() => setCurrentCaptureTarget('back')}\n                  style={{ flex: 1 }}\n                >\n                  反面\n                </Button>\n                <Button \n                  color=\"success\" \n                  fill=\"outline\"\n                  onClick={handleManualParse}\n                  disabled={(currentCaptureTarget === 'front' ? !frontImage.file : !backImage.file) || parseLoading}\n                  style={{ flex: 1 }}\n                >\n                  {parseLoading ? <LoopOutline style={{ animation: 'spin 1s linear infinite' }} /> : <ScanningOutline />} 解析\n                </Button>\n              </Space>\n            </div>\n\n            {/* 單一拍攝框 */}\n            <div className=\"single-capture-frame\">\n              <div className=\"current-side-title\" style={{ marginBottom: '8px', fontSize: '14px', fontWeight: 'bold', textAlign: 'center' }}>\n                當前拍攝: {currentCaptureTarget === 'front' ? '正面' : '反面'}\n                {/* 解析狀態指示 */}\n                {getParseStatusIcon((currentCaptureTarget === 'front' ? frontImage : backImage).parseStatus)}\n              </div>\n              \n              {/* 顯示當前選中面的圖片 */}\n              {(currentCaptureTarget === 'front' ? frontImage.preview : backImage.preview) ? (\n                <img\n                  src={currentCaptureTarget === 'front' ? frontImage.preview : backImage.preview}\n                  alt={`名片${currentCaptureTarget === 'front' ? '正面' : '反面'}`}\n                  style={{\n                    width: '100%',\n                    height: 'clamp(280px, 40vw, 400px)',\n                    objectFit: 'cover',\n                    borderRadius: '8px',\n                    marginBottom: '16px',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n                    transition: 'all 0.3s ease'\n                  }}\n                />\n              ) : (\n                <div\n                  style={{\n                    width: '100%',\n                    height: 'clamp(280px, 40vw, 400px)',\n                    border: '2px dashed #d9d9d9',\n                    borderRadius: '8px',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: '#999',\n                    marginBottom: '16px',\n                    background: '#fafafa',\n                    transition: 'all 0.3s ease'\n                  }}\n                >\n                  <CameraOutline style={{ fontSize: '64px', marginBottom: '12px', color: '#ccc' }} />\n                  <div style={{ fontSize: '16px', textAlign: 'center' }}>\n                    請拍攝名片{currentCaptureTarget === 'front' ? '正面' : '反面'}\n                  </div>\n                  <div style={{ fontSize: '14px', color: '#bbb', marginTop: '8px' }}>\n                    點擊下方按鈕開始拍照\n                  </div>\n                </div>\n              )}\n\n              {/* 拍攝操作按鈕 */}\n              <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>\n                <Button \n                  color=\"primary\" \n                  onClick={() => startCamera(currentCaptureTarget)}\n                  style={{ flex: 1 }}\n                >\n                  <CameraOutline /> 拍照\n                </Button>\n                <Button \n                  color=\"primary\" \n                  fill=\"outline\"\n                  onClick={() => selectFromGallery(currentCaptureTarget)}\n                  style={{ flex: 1 }}\n                >\n                  <PictureOutline /> 相冊\n                </Button>\n              </div>\n\n              {/* 拍攝狀態指示 */}\n              <div className=\"capture-status\" style={{ marginTop: '12px', textAlign: 'center' }}>\n                <Space>\n                  <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>\n                    <div style={{ \n                      width: '8px', \n                      height: '8px', \n                      borderRadius: '50%', \n                      backgroundColor: frontImage.preview ? '#52c41a' : '#d9d9d9' \n                    }}></div>\n                    <span style={{ fontSize: '12px', color: frontImage.preview ? '#52c41a' : '#8c8c8c' }}>\n                      正面 {frontImage.preview ? '已拍攝' : '未拍攝'}\n                    </span>\n                    {frontImage.parseStatus && getParseStatusIcon(frontImage.parseStatus)}\n                  </div>\n                  <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>\n                    <div style={{ \n                      width: '8px', \n                      height: '8px', \n                      borderRadius: '50%', \n                      backgroundColor: backImage.preview ? '#52c41a' : '#d9d9d9' \n                    }}></div>\n                    <span style={{ fontSize: '12px', color: backImage.preview ? '#52c41a' : '#8c8c8c' }}>\n                      反面 {backImage.preview ? '已拍攝' : '未拍攝'}\n                    </span>\n                    {backImage.parseStatus && getParseStatusIcon(backImage.parseStatus)}\n                  </div>\n                </Space>\n              </div>\n            </div>\n          </div>\n        </Card>\n\n        {/* 統一的名片資料編輯表單 */}\n        <Card title=\"名片資料\" extra={<EditSOutline />} style={{ marginBottom: '16px' }}>\n          <Form layout=\"vertical\">\n            {/* 基本資訊 */}\n            <div className=\"form-section\">\n              <Divider>基本資訊</Divider>\n              \n              <Form.Item label=\"姓名 *\" required>\n                <Input\n                  placeholder=\"請輸入姓名\"\n                  value={cardData.name}\n                  onChange={(value) => handleFieldChange('name', value)}\n                />\n              </Form.Item>\n              \n              <Form.Item label=\"公司名稱\">\n                <Input\n                  placeholder=\"請輸入公司名稱\"\n                  value={cardData.company_name}\n                  onChange={(value) => handleFieldChange('company_name', value)}\n                />\n              </Form.Item>\n              \n              <Form.Item label=\"職位\">\n                <Input\n                  placeholder=\"請輸入職位\"\n                  value={cardData.position}\n                  onChange={(value) => handleFieldChange('position', value)}\n                />\n              </Form.Item>\n            </div>\n\n            {/* 聯絡資訊 */}\n            <div className=\"form-section\">\n              <Divider>聯絡資訊</Divider>\n              \n              <Form.Item label=\"手機\">\n                <Input\n                  placeholder=\"請輸入手機號碼\"\n                  value={cardData.mobile_phone}\n                  onChange={(value) => handleFieldChange('mobile_phone', value)}\n                />\n              </Form.Item>\n              \n              <Form.Item label=\"公司電話\">\n                <Input\n                  placeholder=\"請輸入公司電話\"\n                  value={cardData.office_phone}\n                  onChange={(value) => handleFieldChange('office_phone', value)}\n                />\n              </Form.Item>\n              \n              <Form.Item label=\"Email\">\n                <Input\n                  placeholder=\"請輸入Email地址\"\n                  value={cardData.email}\n                  onChange={(value) => handleFieldChange('email', value)}\n                />\n              </Form.Item>\n              \n              <Form.Item label=\"Line ID\">\n                <Input\n                  placeholder=\"請輸入Line ID\"\n                  value={cardData.line_id}\n                  onChange={(value) => handleFieldChange('line_id', value)}\n                />\n              </Form.Item>\n            </div>\n\n            {/* 地址資訊 */}\n            <div className=\"form-section\">\n              <Divider>地址資訊</Divider>\n              \n              <Form.Item label=\"公司地址一\">\n                <Input\n                  placeholder=\"請輸入公司地址\"\n                  value={cardData.company_address_1}\n                  onChange={(value) => handleFieldChange('company_address_1', value)}\n                />\n              </Form.Item>\n              \n              <Form.Item label=\"公司地址二\">\n                <Input\n                  placeholder=\"請輸入公司地址（補充）\"\n                  value={cardData.company_address_2}\n                  onChange={(value) => handleFieldChange('company_address_2', value)}\n                />\n              </Form.Item>\n            </div>\n\n            {/* 備註 */}\n            <div className=\"form-section\">\n              <Divider>其他資訊</Divider>\n              \n              <Form.Item label=\"備註\">\n                <TextArea\n                  placeholder=\"請輸入備註資訊\"\n                  rows={3}\n                  value={cardData.notes}\n                  onChange={(value) => handleFieldChange('notes', value)}\n                />\n              </Form.Item>\n            </div>\n          </Form>\n        </Card>\n\n        {/* 操作按鈕 */}\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <Button \n            color=\"primary\" \n            size=\"large\" \n            block \n            onClick={handleSave}\n            disabled={loading}\n          >\n            <CheckOutline /> 保存名片\n          </Button>\n        </Space>\n      </div>\n\n      {/* 相機模態框 - 根據設備類型選擇不同的實現 */}\n      {isMobileCameraMode ? (\n        // 移動端：使用全屏相機組件\n        <MobileCameraModal\n          visible={cameraModalVisible}\n          onClose={stopCamera}\n          onPhotoTaken={handleMobilePhotoTaken}\n          cameraManager={cameraManager}\n          target={currentCaptureTarget}\n        />\n      ) : (\n        // Web端：使用傳統Modal\n        <Modal\n          visible={cameraModalVisible}\n          content={\n            <div className=\"camera-modal\">\n              <video\n                ref={videoRef}\n                autoPlay\n                playsInline\n                style={{\n                  width: '100%',\n                  height: 'clamp(350px, 50vh, 500px)',\n                  objectFit: 'cover',\n                  borderRadius: '8px',\n                  background: '#000'\n                }}\n              />\n              <canvas ref={canvasRef} style={{ display: 'none' }} />\n              <div style={{ marginTop: '20px', textAlign: 'center' }}>\n                <Space size=\"large\">\n                  <Button\n                    color=\"primary\"\n                    size=\"large\"\n                    onClick={takePhoto}\n                    style={{ minWidth: '100px' }}\n                  >\n                    <CameraOutline /> 拍照\n                  </Button>\n                  <Button\n                    color=\"default\"\n                    size=\"large\"\n                    onClick={stopCamera}\n                    style={{ minWidth: '100px' }}\n                  >\n                    <CloseOutline /> 取消\n                  </Button>\n                </Space>\n              </div>\n            </div>\n          }\n          onClose={stopCamera}\n        />\n      )}\n\n      {/* 隱藏的文件選擇輸入 */}\n      <input\n        type=\"file\"\n        ref={fileInputRef}\n        accept=\"image/*\"\n        style={{ display: 'none' }}\n        onChange={handleFileSelect}\n      />\n    </div>\n  );\n};\n\nexport default ScanUploadPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACvE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,OAAO,QACF,aAAa;AACpB,SACEC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,eAAe,EACfC,WAAW,QACN,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,OAAOC,iBAAiB,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC;IAC3CsC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,IAAI,CAAC;EACpB,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC;IACzCsC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,IAAI,CAAC;EACpB,CAAC,CAAC;EACF,MAAM,CAACG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC8C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/C,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EAC3E,MAAM,CAACgD,MAAM,EAAEC,SAAS,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;;EAE1C;EACA,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACA,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC;IACvCwD,IAAI,EAAE,EAAE;IAAqB;IAC7BC,YAAY,EAAE,EAAE;IAAa;IAC7BC,QAAQ,EAAE,EAAE;IAAiB;IAC7BC,YAAY,EAAE,EAAE;IAAa;IAC7BC,YAAY,EAAE,EAAE;IAAa;IAC7BC,KAAK,EAAE,EAAE;IAAoB;IAC7BC,OAAO,EAAE,EAAE;IAAkB;IAC7BC,KAAK,EAAE,EAAE;IAAoB;IAC7BC,iBAAiB,EAAE,EAAE;IAAQ;IAC7BC,iBAAiB,EAAE,EAAE,CAAQ;EAC/B,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAGjE,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMkE,QAAQ,GAAGlE,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMmE,SAAS,GAAGnE,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACAE,SAAS,CAAC,MAAM;IACd,MAAMkE,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMC,OAAO,GAAG9C,gBAAgB,CAAC,CAAC;QAClC2B,gBAAgB,CAACmB,OAAO,CAAC;QAEzB,MAAMC,IAAI,GAAG9C,aAAa,CAAC,CAAC;QAC5B4B,qBAAqB,CAACkB,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,CAAC;QAE7DC,OAAO,CAACC,GAAG,CAAC,SAASF,IAAI,cAAcA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,EAAE,CAAC;MAClF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACrC;IACF,CAAC;IAEDL,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,sBAAsB,GAAGzE,WAAW,CAAC,CAAC0E,IAAI,EAAEC,MAAM,KAAK;IAC3D,IAAID,IAAI,KAAK,OAAO,EAAE;MACpBvC,aAAa,CAACyC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAErC,WAAW,EAAEoC;MAAO,CAAC,CAAC,CAAC;IAC3D,CAAC,MAAM;MACLlC,YAAY,CAACmC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAErC,WAAW,EAAEoC;MAAO,CAAC,CAAC,CAAC;IAC1D;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,mBAAmB,GAAG7E,WAAW,CAAC,OAAOsC,OAAO,EAAEoC,IAAI,KAAK;IAC/D,IAAI,CAACpC,OAAO,EAAE;IAEd,IAAI;MACFmC,sBAAsB,CAACC,IAAI,EAAE,SAAS,CAAC;;MAEvC;MACA,MAAMI,QAAQ,GAAG,MAAMzD,KAAK,CAAC0D,IAAI,CAAC,0BAA0B,EAAE;QAC5DC,QAAQ,EAAE1C,OAAO;QACjBoC,IAAI,EAAEA;MACR,CAAC,CAAC;MAEF,IAAII,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;QACzB,MAAMC,YAAY,GAAGL,QAAQ,CAACG,IAAI,CAACG,aAAa;;QAEhD;QACA/B,WAAW,CAACgC,QAAQ,IAAI;UACtB,MAAMC,WAAW,GAAG;YAAE,GAAGD;UAAS,CAAC;;UAEnC;UACAE,MAAM,CAACC,IAAI,CAACL,YAAY,CAAC,CAACM,OAAO,CAACC,KAAK,IAAI;YACzC,IAAIP,YAAY,CAACO,KAAK,CAAC,KAAK,CAACJ,WAAW,CAACI,KAAK,CAAC,IAAIJ,WAAW,CAACI,KAAK,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE;cACpFL,WAAW,CAACI,KAAK,CAAC,GAAGP,YAAY,CAACO,KAAK,CAAC;YAC1C;UACF,CAAC,CAAC;UAEF,OAAOJ,WAAW;QACpB,CAAC,CAAC;QAEFb,sBAAsB,CAACC,IAAI,EAAE,SAAS,CAAC;QAEvClE,KAAK,CAACoF,IAAI,CAAC;UACTC,OAAO,EAAE,GAAGnB,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI,kBAAkB;UAC5DlB,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ,CAAC,MAAM;QACLiB,sBAAsB,CAACC,IAAI,EAAE,OAAO,CAAC;QACrClE,KAAK,CAACoF,IAAI,CAAC;UACTC,OAAO,EAAE,iBAAiB;UAC1BrC,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChCC,sBAAsB,CAACC,IAAI,EAAE,OAAO,CAAC;MACrClE,KAAK,CAACoF,IAAI,CAAC;QACTC,OAAO,EAAE,mBAAmB;QAC5BrC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACiB,sBAAsB,CAAC,CAAC;;EAE5B;EACA,MAAMqB,UAAU,GAAG9F,WAAW,CAAC,OAAOoC,IAAI,EAAEsC,IAAI,KAAK;IACnD,IAAI,CAACtC,IAAI,EAAE;IAEXL,UAAU,CAAC,IAAI,CAAC;IAChB0C,sBAAsB,CAACC,IAAI,EAAE,SAAS,CAAC;IAEvC,IAAI;MACF,MAAMqB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE7D,IAAI,CAAC;MAE7B,MAAM0C,QAAQ,GAAG,MAAMzD,KAAK,CAAC0D,IAAI,CAAC,mBAAmB,EAAEgB,QAAQ,EAAE;QAC/DG,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAIpB,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM5C,OAAO,GAAGwC,QAAQ,CAACG,IAAI,CAACkB,IAAI;;QAElC;QACA,IAAIzB,IAAI,KAAK,OAAO,EAAE;UACpBvC,aAAa,CAACyC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEtC;UAAQ,CAAC,CAAC,CAAC;QAC/C,CAAC,MAAM;UACLG,YAAY,CAACmC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEtC;UAAQ,CAAC,CAAC,CAAC;QAC9C;;QAEA;QACA,MAAMuC,mBAAmB,CAACvC,OAAO,EAAEoC,IAAI,CAAC;QAExClE,KAAK,CAACoF,IAAI,CAAC;UACTC,OAAO,EAAE,GAAGnB,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI,UAAU;UACpDlB,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ,CAAC,MAAM;QACLiB,sBAAsB,CAACC,IAAI,EAAE,OAAO,CAAC;QACrClE,KAAK,CAACoF,IAAI,CAAC;UACTC,OAAO,EAAE,aAAa;UACtBrC,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAC9BC,sBAAsB,CAACC,IAAI,EAAE,OAAO,CAAC;MACrClE,KAAK,CAACoF,IAAI,CAAC;QACTC,OAAO,EAAE,oBAAoB;QAC7BrC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAAC8C,mBAAmB,EAAEJ,sBAAsB,CAAC,CAAC;;EAEjD;EACA,MAAM2B,iBAAiB,GAAGpG,WAAW,CAAC,YAAY;IAChD,MAAMqG,YAAY,GAAGzD,oBAAoB,KAAK,OAAO,GAAGV,UAAU,GAAGM,SAAS;IAE9E,IAAI,CAAC6D,YAAY,CAACjE,IAAI,EAAE;MACtB5B,KAAK,CAACoF,IAAI,CAAC;QACTC,OAAO,EAAE,WAAW;QACpBrC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;IAEA,IAAI6C,YAAY,CAAC/D,OAAO,EAAE;MACxB;MACAL,eAAe,CAAC,IAAI,CAAC;MACrB,IAAI;QACF,MAAM4C,mBAAmB,CAACwB,YAAY,CAAC/D,OAAO,EAAEM,oBAAoB,CAAC;MACvE,CAAC,SAAS;QACRX,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC,MAAM;MACL;MACA,MAAM6D,UAAU,CAACO,YAAY,CAACjE,IAAI,EAAEQ,oBAAoB,CAAC;IAC3D;EACF,CAAC,EAAE,CAACA,oBAAoB,EAAEV,UAAU,EAAEM,SAAS,EAAEqC,mBAAmB,EAAEiB,UAAU,CAAC,CAAC;;EAElF;EACA,MAAMQ,iBAAiB,GAAGtG,WAAW,CAAC,OAAOoC,IAAI,EAAEmE,MAAM,GAAG3D,oBAAoB,KAAK;IACnF,MAAM4D,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MACrB,IAAIJ,MAAM,KAAK,OAAO,EAAE;QACtBpE,aAAa,CAACyC,IAAI,KAAK;UACrB,GAAGA,IAAI;UACPxC,IAAI;UACJC,OAAO,EAAEsE,CAAC,CAACJ,MAAM,CAACK,MAAM;UACxBrE,WAAW,EAAE,IAAI,CAAC;QACpB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLE,YAAY,CAACmC,IAAI,KAAK;UACpB,GAAGA,IAAI;UACPxC,IAAI;UACJC,OAAO,EAAEsE,CAAC,CAACJ,MAAM,CAACK,MAAM;UACxBrE,WAAW,EAAE,IAAI,CAAC;QACpB,CAAC,CAAC,CAAC;MACL;IACF,CAAC;IACDiE,MAAM,CAACK,aAAa,CAACzE,IAAI,CAAC;;IAE1B;IACA,MAAM0D,UAAU,CAAC1D,IAAI,EAAEmE,MAAM,CAAC;EAChC,CAAC,EAAE,CAACT,UAAU,EAAElD,oBAAoB,CAAC,CAAC;;EAEtC;EACA,MAAMkE,WAAW,GAAG,MAAOP,MAAM,IAAK;IACpC,IAAI,CAACvD,aAAa,EAAE;MAClBxC,KAAK,CAACoF,IAAI,CAAC;QACTC,OAAO,EAAE,WAAW;QACpBrC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;IAEAX,uBAAuB,CAAC0D,MAAM,CAAC;IAE/B,IAAI;MACF,IAAIrD,kBAAkB,EAAE;QACtB;QACAP,qBAAqB,CAAC,IAAI,CAAC;MAC7B,CAAC,MAAM;QACL;QACA,MAAMoE,WAAW,GAAG,MAAM/D,aAAa,CAAC8D,WAAW,CAACP,MAAM,EAAE;UAC1DS,YAAY,EAAE/C,QAAQ,CAACgD,OAAO;UAC9BC,aAAa,EAAEhD,SAAS,CAAC+C;QAC3B,CAAC,CAAC;QACFlE,SAAS,CAACgE,WAAW,CAAC;QACtBpE,qBAAqB,CAAC,IAAI,CAAC;MAC7B;IACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChChE,KAAK,CAACoF,IAAI,CAAC;QACTC,OAAO,EAAE,iBAAiB;QAC1BrC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAM2D,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAInE,aAAa,EAAE;MACjBA,aAAa,CAACmE,UAAU,CAAC,CAAC;IAC5B;IACA,IAAIrE,MAAM,EAAE;MACVA,MAAM,CAACsE,SAAS,CAAC,CAAC,CAAC3B,OAAO,CAAC4B,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MACjDvE,SAAS,CAAC,IAAI,CAAC;IACjB;IACAJ,qBAAqB,CAAC,KAAK,CAAC;EAC9B,CAAC;;EAED;EACA,MAAM4E,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAACvE,aAAa,EAAE;IAEpB,IAAI;MACF,MAAM4D,MAAM,GAAG,MAAM5D,aAAa,CAACuE,SAAS,CAAC,CAAC;MAC9C,IAAIX,MAAM,IAAIA,MAAM,CAACxE,IAAI,EAAE;QACzB,MAAMkE,iBAAiB,CAACM,MAAM,CAACxE,IAAI,EAAEQ,oBAAoB,CAAC;QAC1DuE,UAAU,CAAC,CAAC;MACd;IACF,CAAC,CAAC,OAAO3C,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BhE,KAAK,CAACoF,IAAI,CAAC;QACTC,OAAO,EAAE,UAAU;QACnBrC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMgE,sBAAsB,GAAG,MAAOvC,IAAI,IAAK;IAC7C,IAAIA,IAAI,IAAIA,IAAI,CAAC7C,IAAI,EAAE;MACrB,MAAMkE,iBAAiB,CAACrB,IAAI,CAAC7C,IAAI,EAAEQ,oBAAoB,CAAC;IAC1D;EACF,CAAC;;EAED;EACA,MAAM6E,iBAAiB,GAAIlB,MAAM,IAAK;IAAA,IAAAmB,qBAAA;IACpC7E,uBAAuB,CAAC0D,MAAM,CAAC;IAC/B,CAAAmB,qBAAA,GAAA1D,YAAY,CAACiD,OAAO,cAAAS,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMzF,IAAI,GAAGyF,KAAK,CAACtB,MAAM,CAACuB,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI1F,IAAI,EAAE;MACRkE,iBAAiB,CAAClE,IAAI,EAAEQ,oBAAoB,CAAC;MAC7C;MACAiF,KAAK,CAACtB,MAAM,CAACwB,KAAK,GAAG,EAAE;IACzB;EACF,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B;IACA,IAAI,CAAC5E,QAAQ,CAACE,IAAI,CAACqC,IAAI,CAAC,CAAC,EAAE;MACzBnF,KAAK,CAACoF,IAAI,CAAC;QACTC,OAAO,EAAE,OAAO;QAChBrC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;IAEAzB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMkG,QAAQ,GAAG,IAAIjC,QAAQ,CAAC,CAAC;;MAE/B;MACAT,MAAM,CAACC,IAAI,CAACpC,QAAQ,CAAC,CAACqC,OAAO,CAACyC,GAAG,IAAI;QACnC,IAAI9E,QAAQ,CAAC8E,GAAG,CAAC,EAAE;UACjBD,QAAQ,CAAChC,MAAM,CAACiC,GAAG,EAAE9E,QAAQ,CAAC8E,GAAG,CAAC,CAAC;QACrC;MACF,CAAC,CAAC;;MAEF;MACA,IAAIhG,UAAU,CAACE,IAAI,EAAE;QACnB6F,QAAQ,CAAChC,MAAM,CAAC,aAAa,EAAE/D,UAAU,CAACE,IAAI,CAAC;MACjD;MACA,IAAII,SAAS,CAACJ,IAAI,EAAE;QAClB6F,QAAQ,CAAChC,MAAM,CAAC,YAAY,EAAEzD,SAAS,CAACJ,IAAI,CAAC;MAC/C;;MAEA;MACA,IAAIF,UAAU,CAACI,OAAO,EAAE;QACtB2F,QAAQ,CAAChC,MAAM,CAAC,gBAAgB,EAAE/D,UAAU,CAACI,OAAO,CAAC;MACvD;MACA,IAAIE,SAAS,CAACF,OAAO,EAAE;QACrB2F,QAAQ,CAAChC,MAAM,CAAC,eAAe,EAAEzD,SAAS,CAACF,OAAO,CAAC;MACrD;MAEA,MAAMwC,QAAQ,GAAG,MAAMzD,KAAK,CAAC0D,IAAI,CAAC,eAAe,EAAEkD,QAAQ,EAAE;QAC3D/B,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAIpB,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;QACzB1E,KAAK,CAACoF,IAAI,CAAC;UACTC,OAAO,EAAE,SAAS;UAClBrC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF;QACAH,WAAW,CAAC;UACVC,IAAI,EAAE,EAAE;UACRC,YAAY,EAAE,EAAE;UAChBC,QAAQ,EAAE,EAAE;UACZC,YAAY,EAAE,EAAE;UAChBC,YAAY,EAAE,EAAE;UAChBC,KAAK,EAAE,EAAE;UACTC,OAAO,EAAE,EAAE;UACXC,KAAK,EAAE,EAAE;UACTC,iBAAiB,EAAE,EAAE;UACrBC,iBAAiB,EAAE;QACrB,CAAC,CAAC;QACF5B,aAAa,CAAC;UAAEC,IAAI,EAAE,IAAI;UAAEC,OAAO,EAAE,IAAI;UAAEC,OAAO,EAAE,EAAE;UAAEC,WAAW,EAAE;QAAK,CAAC,CAAC;QAC5EE,YAAY,CAAC;UAAEL,IAAI,EAAE,IAAI;UAAEC,OAAO,EAAE,IAAI;UAAEC,OAAO,EAAE,EAAE;UAAEC,WAAW,EAAE;QAAK,CAAC,CAAC;QAC3E;QACAV,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAO2C,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BhE,KAAK,CAACoF,IAAI,CAAC;QACTC,OAAO,EAAE,UAAU;QACnBrC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoG,iBAAiB,GAAGA,CAACzC,KAAK,EAAEqC,KAAK,KAAK;IAC1C1E,WAAW,CAACuB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACc,KAAK,GAAGqC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMK,kBAAkB,GAAIzD,MAAM,IAAK;IACrC,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,oBAAOjD,OAAA,CAACN,WAAW;UAACiH,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEC,SAAS,EAAE;UAA0B;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3F,KAAK,SAAS;QACZ,oBAAOjH,OAAA,CAACV,YAAY;UAACqH,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,OAAO;QACV,oBAAOjH,OAAA,CAACT,YAAY;UAACoH,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD;QACE,OAAO,IAAI;IACf;EACF,CAAC;;EAED;EACA9I,KAAK,CAACI,SAAS,CAAC,MAAM;IACpB,MAAMoI,KAAK,GAAGO,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CR,KAAK,CAACS,WAAW,GAAG;AACxB;AACA;AACA;AACA;AACA,KAAK;IACDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACX,KAAK,CAAC;IAChC,OAAO,MAAMO,QAAQ,CAACG,IAAI,CAACE,WAAW,CAACZ,KAAK,CAAC;EAC/C,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE3G,OAAA;IAAKwH,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BzH,OAAA,CAACjB,MAAM;MAAC2I,MAAM,EAAEA,CAAA,KAAMvH,QAAQ,CAAC,CAAC,CAAC,CAAE;MAAAsH,QAAA,EAAC;IAAM;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAElD7G,OAAO,iBAAIJ,OAAA,CAAChB,OAAO;MAAA8H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEvBjH,OAAA;MAAKwH,SAAS,EAAC,SAAS;MAACb,KAAK,EAAE;QAAEgB,OAAO,EAAE;MAAO,CAAE;MAAAF,QAAA,gBAElDzH,OAAA,CAACrB,IAAI;QAACiJ,KAAK,EAAC,0BAAM;QAACjB,KAAK,EAAE;UAAEkB,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,eACjDzH,OAAA;UAAKwH,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAEpCzH,OAAA;YAAKwH,SAAS,EAAC,qBAAqB;YAACb,KAAK,EAAE;cAAEkB,YAAY,EAAE;YAAO,CAAE;YAAAJ,QAAA,eACnEzH,OAAA,CAACtB,KAAK;cAACiI,KAAK,EAAE;gBAAEmB,KAAK,EAAE,MAAM;gBAAEC,cAAc,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAM,CAAE;cAAAP,QAAA,gBACpEzH,OAAA,CAACvB,MAAM;gBACLmI,KAAK,EAAE1F,oBAAoB,KAAK,OAAO,GAAG,SAAS,GAAG,SAAU;gBAChE+G,IAAI,EAAE/G,oBAAoB,KAAK,OAAO,GAAG,OAAO,GAAG,SAAU;gBAC7DgH,OAAO,EAAEA,CAAA,KAAM/G,uBAAuB,CAAC,OAAO,CAAE;gBAChDwF,KAAK,EAAE;kBAAEwB,IAAI,EAAE;gBAAE,CAAE;gBAAAV,QAAA,EACpB;cAED;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjH,OAAA,CAACvB,MAAM;gBACLmI,KAAK,EAAE1F,oBAAoB,KAAK,MAAM,GAAG,SAAS,GAAG,SAAU;gBAC/D+G,IAAI,EAAE/G,oBAAoB,KAAK,MAAM,GAAG,OAAO,GAAG,SAAU;gBAC5DgH,OAAO,EAAEA,CAAA,KAAM/G,uBAAuB,CAAC,MAAM,CAAE;gBAC/CwF,KAAK,EAAE;kBAAEwB,IAAI,EAAE;gBAAE,CAAE;gBAAAV,QAAA,EACpB;cAED;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjH,OAAA,CAACvB,MAAM;gBACLmI,KAAK,EAAC,SAAS;gBACfqB,IAAI,EAAC,SAAS;gBACdC,OAAO,EAAExD,iBAAkB;gBAC3B0D,QAAQ,EAAE,CAAClH,oBAAoB,KAAK,OAAO,GAAG,CAACV,UAAU,CAACE,IAAI,GAAG,CAACI,SAAS,CAACJ,IAAI,KAAKJ,YAAa;gBAClGqG,KAAK,EAAE;kBAAEwB,IAAI,EAAE;gBAAE,CAAE;gBAAAV,QAAA,GAElBnH,YAAY,gBAAGN,OAAA,CAACN,WAAW;kBAACiH,KAAK,EAAE;oBAAEE,SAAS,EAAE;kBAA0B;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGjH,OAAA,CAACP,eAAe;kBAAAqH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAAC,eACzG;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNjH,OAAA;YAAKwH,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCzH,OAAA;cAAKwH,SAAS,EAAC,oBAAoB;cAACb,KAAK,EAAE;gBAAEkB,YAAY,EAAE,KAAK;gBAAEQ,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE,MAAM;gBAAEC,SAAS,EAAE;cAAS,CAAE;cAAAd,QAAA,GAAC,4BACvH,EAACvG,oBAAoB,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI,EAEpDwF,kBAAkB,CAAC,CAACxF,oBAAoB,KAAK,OAAO,GAAGV,UAAU,GAAGM,SAAS,EAAED,WAAW,CAAC;YAAA;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,EAGL,CAAC/F,oBAAoB,KAAK,OAAO,GAAGV,UAAU,CAACG,OAAO,GAAGG,SAAS,CAACH,OAAO,iBACzEX,OAAA;cACEwI,GAAG,EAAEtH,oBAAoB,KAAK,OAAO,GAAGV,UAAU,CAACG,OAAO,GAAGG,SAAS,CAACH,OAAQ;cAC/E8H,GAAG,EAAE,KAAKvH,oBAAoB,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI,EAAG;cAC3DyF,KAAK,EAAE;gBACLmB,KAAK,EAAE,MAAM;gBACbY,MAAM,EAAE,2BAA2B;gBACnCC,SAAS,EAAE,OAAO;gBAClBC,YAAY,EAAE,KAAK;gBACnBf,YAAY,EAAE,MAAM;gBACpBgB,SAAS,EAAE,2BAA2B;gBACtCC,UAAU,EAAE;cACd;YAAE;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEFjH,OAAA;cACE2G,KAAK,EAAE;gBACLmB,KAAK,EAAE,MAAM;gBACbY,MAAM,EAAE,2BAA2B;gBACnCK,MAAM,EAAE,oBAAoB;gBAC5BH,YAAY,EAAE,KAAK;gBACnBI,OAAO,EAAE,MAAM;gBACfC,aAAa,EAAE,QAAQ;gBACvBC,UAAU,EAAE,QAAQ;gBACpBnB,cAAc,EAAE,QAAQ;gBACxBnB,KAAK,EAAE,MAAM;gBACbiB,YAAY,EAAE,MAAM;gBACpBsB,UAAU,EAAE,SAAS;gBACrBL,UAAU,EAAE;cACd,CAAE;cAAArB,QAAA,gBAEFzH,OAAA,CAACZ,aAAa;gBAACuH,KAAK,EAAE;kBAAE0B,QAAQ,EAAE,MAAM;kBAAER,YAAY,EAAE,MAAM;kBAAEjB,KAAK,EAAE;gBAAO;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnFjH,OAAA;gBAAK2G,KAAK,EAAE;kBAAE0B,QAAQ,EAAE,MAAM;kBAAEE,SAAS,EAAE;gBAAS,CAAE;gBAAAd,QAAA,GAAC,gCAChD,EAACvG,oBAAoB,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI;cAAA;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACNjH,OAAA;gBAAK2G,KAAK,EAAE;kBAAE0B,QAAQ,EAAE,MAAM;kBAAEzB,KAAK,EAAE,MAAM;kBAAEwC,SAAS,EAAE;gBAAM,CAAE;gBAAA3B,QAAA,EAAC;cAEnE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eAGDjH,OAAA;cAAK2G,KAAK,EAAE;gBAAEqC,OAAO,EAAE,MAAM;gBAAEhB,GAAG,EAAE,KAAK;gBAAED,cAAc,EAAE;cAAS,CAAE;cAAAN,QAAA,gBACpEzH,OAAA,CAACvB,MAAM;gBACLmI,KAAK,EAAC,SAAS;gBACfsB,OAAO,EAAEA,CAAA,KAAM9C,WAAW,CAAClE,oBAAoB,CAAE;gBACjDyF,KAAK,EAAE;kBAAEwB,IAAI,EAAE;gBAAE,CAAE;gBAAAV,QAAA,gBAEnBzH,OAAA,CAACZ,aAAa;kBAAA0H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBACnB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjH,OAAA,CAACvB,MAAM;gBACLmI,KAAK,EAAC,SAAS;gBACfqB,IAAI,EAAC,SAAS;gBACdC,OAAO,EAAEA,CAAA,KAAMnC,iBAAiB,CAAC7E,oBAAoB,CAAE;gBACvDyF,KAAK,EAAE;kBAAEwB,IAAI,EAAE;gBAAE,CAAE;gBAAAV,QAAA,gBAEnBzH,OAAA,CAACX,cAAc;kBAAAyH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBACpB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNjH,OAAA;cAAKwH,SAAS,EAAC,gBAAgB;cAACb,KAAK,EAAE;gBAAEyC,SAAS,EAAE,MAAM;gBAAEb,SAAS,EAAE;cAAS,CAAE;cAAAd,QAAA,eAChFzH,OAAA,CAACtB,KAAK;gBAAA+I,QAAA,gBACJzH,OAAA;kBAAK2G,KAAK,EAAE;oBAAEqC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAElB,GAAG,EAAE;kBAAM,CAAE;kBAAAP,QAAA,gBAChEzH,OAAA;oBAAK2G,KAAK,EAAE;sBACVmB,KAAK,EAAE,KAAK;sBACZY,MAAM,EAAE,KAAK;sBACbE,YAAY,EAAE,KAAK;sBACnBS,eAAe,EAAE7I,UAAU,CAACG,OAAO,GAAG,SAAS,GAAG;oBACpD;kBAAE;oBAAAmG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACTjH,OAAA;oBAAM2G,KAAK,EAAE;sBAAE0B,QAAQ,EAAE,MAAM;sBAAEzB,KAAK,EAAEpG,UAAU,CAACG,OAAO,GAAG,SAAS,GAAG;oBAAU,CAAE;oBAAA8G,QAAA,GAAC,eACjF,EAACjH,UAAU,CAACG,OAAO,GAAG,KAAK,GAAG,KAAK;kBAAA;oBAAAmG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC,EACNzG,UAAU,CAACK,WAAW,IAAI6F,kBAAkB,CAAClG,UAAU,CAACK,WAAW,CAAC;gBAAA;kBAAAiG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC,eACNjH,OAAA;kBAAK2G,KAAK,EAAE;oBAAEqC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAElB,GAAG,EAAE;kBAAM,CAAE;kBAAAP,QAAA,gBAChEzH,OAAA;oBAAK2G,KAAK,EAAE;sBACVmB,KAAK,EAAE,KAAK;sBACZY,MAAM,EAAE,KAAK;sBACbE,YAAY,EAAE,KAAK;sBACnBS,eAAe,EAAEvI,SAAS,CAACH,OAAO,GAAG,SAAS,GAAG;oBACnD;kBAAE;oBAAAmG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACTjH,OAAA;oBAAM2G,KAAK,EAAE;sBAAE0B,QAAQ,EAAE,MAAM;sBAAEzB,KAAK,EAAE9F,SAAS,CAACH,OAAO,GAAG,SAAS,GAAG;oBAAU,CAAE;oBAAA8G,QAAA,GAAC,eAChF,EAAC3G,SAAS,CAACH,OAAO,GAAG,KAAK,GAAG,KAAK;kBAAA;oBAAAmG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,EACNnG,SAAS,CAACD,WAAW,IAAI6F,kBAAkB,CAAC5F,SAAS,CAACD,WAAW,CAAC;gBAAA;kBAAAiG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGPjH,OAAA,CAACrB,IAAI;QAACiJ,KAAK,EAAC,0BAAM;QAAC0B,KAAK,eAAEtJ,OAAA,CAACR,YAAY;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACN,KAAK,EAAE;UAAEkB,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,eAC1EzH,OAAA,CAACnB,IAAI;UAAC0K,MAAM,EAAC,UAAU;UAAA9B,QAAA,gBAErBzH,OAAA;YAAKwH,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzH,OAAA,CAACb,OAAO;cAAAsI,QAAA,EAAC;YAAI;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAEvBjH,OAAA,CAACnB,IAAI,CAAC2K,IAAI;cAACC,KAAK,EAAC,gBAAM;cAACC,QAAQ;cAAAjC,QAAA,eAC9BzH,OAAA,CAACpB,KAAK;gBACJ+K,WAAW,EAAC,gCAAO;gBACnBtD,KAAK,EAAE3E,QAAQ,CAACE,IAAK;gBACrBgI,QAAQ,EAAGvD,KAAK,IAAKI,iBAAiB,CAAC,MAAM,EAAEJ,KAAK;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZjH,OAAA,CAACnB,IAAI,CAAC2K,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAhC,QAAA,eACrBzH,OAAA,CAACpB,KAAK;gBACJ+K,WAAW,EAAC,4CAAS;gBACrBtD,KAAK,EAAE3E,QAAQ,CAACG,YAAa;gBAC7B+H,QAAQ,EAAGvD,KAAK,IAAKI,iBAAiB,CAAC,cAAc,EAAEJ,KAAK;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZjH,OAAA,CAACnB,IAAI,CAAC2K,IAAI;cAACC,KAAK,EAAC,cAAI;cAAAhC,QAAA,eACnBzH,OAAA,CAACpB,KAAK;gBACJ+K,WAAW,EAAC,gCAAO;gBACnBtD,KAAK,EAAE3E,QAAQ,CAACI,QAAS;gBACzB8H,QAAQ,EAAGvD,KAAK,IAAKI,iBAAiB,CAAC,UAAU,EAAEJ,KAAK;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAGNjH,OAAA;YAAKwH,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzH,OAAA,CAACb,OAAO;cAAAsI,QAAA,EAAC;YAAI;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAEvBjH,OAAA,CAACnB,IAAI,CAAC2K,IAAI;cAACC,KAAK,EAAC,cAAI;cAAAhC,QAAA,eACnBzH,OAAA,CAACpB,KAAK;gBACJ+K,WAAW,EAAC,4CAAS;gBACrBtD,KAAK,EAAE3E,QAAQ,CAACK,YAAa;gBAC7B6H,QAAQ,EAAGvD,KAAK,IAAKI,iBAAiB,CAAC,cAAc,EAAEJ,KAAK;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZjH,OAAA,CAACnB,IAAI,CAAC2K,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAhC,QAAA,eACrBzH,OAAA,CAACpB,KAAK;gBACJ+K,WAAW,EAAC,4CAAS;gBACrBtD,KAAK,EAAE3E,QAAQ,CAACM,YAAa;gBAC7B4H,QAAQ,EAAGvD,KAAK,IAAKI,iBAAiB,CAAC,cAAc,EAAEJ,KAAK;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZjH,OAAA,CAACnB,IAAI,CAAC2K,IAAI;cAACC,KAAK,EAAC,OAAO;cAAAhC,QAAA,eACtBzH,OAAA,CAACpB,KAAK;gBACJ+K,WAAW,EAAC,qCAAY;gBACxBtD,KAAK,EAAE3E,QAAQ,CAACO,KAAM;gBACtB2H,QAAQ,EAAGvD,KAAK,IAAKI,iBAAiB,CAAC,OAAO,EAAEJ,KAAK;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZjH,OAAA,CAACnB,IAAI,CAAC2K,IAAI;cAACC,KAAK,EAAC,SAAS;cAAAhC,QAAA,eACxBzH,OAAA,CAACpB,KAAK;gBACJ+K,WAAW,EAAC,2BAAY;gBACxBtD,KAAK,EAAE3E,QAAQ,CAACQ,OAAQ;gBACxB0H,QAAQ,EAAGvD,KAAK,IAAKI,iBAAiB,CAAC,SAAS,EAAEJ,KAAK;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAGNjH,OAAA;YAAKwH,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzH,OAAA,CAACb,OAAO;cAAAsI,QAAA,EAAC;YAAI;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAEvBjH,OAAA,CAACnB,IAAI,CAAC2K,IAAI;cAACC,KAAK,EAAC,gCAAO;cAAAhC,QAAA,eACtBzH,OAAA,CAACpB,KAAK;gBACJ+K,WAAW,EAAC,4CAAS;gBACrBtD,KAAK,EAAE3E,QAAQ,CAACU,iBAAkB;gBAClCwH,QAAQ,EAAGvD,KAAK,IAAKI,iBAAiB,CAAC,mBAAmB,EAAEJ,KAAK;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZjH,OAAA,CAACnB,IAAI,CAAC2K,IAAI;cAACC,KAAK,EAAC,gCAAO;cAAAhC,QAAA,eACtBzH,OAAA,CAACpB,KAAK;gBACJ+K,WAAW,EAAC,oEAAa;gBACzBtD,KAAK,EAAE3E,QAAQ,CAACW,iBAAkB;gBAClCuH,QAAQ,EAAGvD,KAAK,IAAKI,iBAAiB,CAAC,mBAAmB,EAAEJ,KAAK;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAGNjH,OAAA;YAAKwH,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzH,OAAA,CAACb,OAAO;cAAAsI,QAAA,EAAC;YAAI;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAEvBjH,OAAA,CAACnB,IAAI,CAAC2K,IAAI;cAACC,KAAK,EAAC,cAAI;cAAAhC,QAAA,eACnBzH,OAAA,CAACd,QAAQ;gBACPyK,WAAW,EAAC,4CAAS;gBACrBE,IAAI,EAAE,CAAE;gBACRxD,KAAK,EAAE3E,QAAQ,CAACS,KAAM;gBACtByH,QAAQ,EAAGvD,KAAK,IAAKI,iBAAiB,CAAC,OAAO,EAAEJ,KAAK;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPjH,OAAA,CAACtB,KAAK;QAACoL,SAAS,EAAC,UAAU;QAACnD,KAAK,EAAE;UAAEmB,KAAK,EAAE;QAAO,CAAE;QAAAL,QAAA,eACnDzH,OAAA,CAACvB,MAAM;UACLmI,KAAK,EAAC,SAAS;UACfmD,IAAI,EAAC,OAAO;UACZC,KAAK;UACL9B,OAAO,EAAE5B,UAAW;UACpB8B,QAAQ,EAAEhI,OAAQ;UAAAqH,QAAA,gBAElBzH,OAAA,CAACV,YAAY;YAAAwH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BAClB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGLzF,kBAAkB;IAAA;IACjB;IACAxB,OAAA,CAACF,iBAAiB;MAChBmK,OAAO,EAAEjJ,kBAAmB;MAC5BkJ,OAAO,EAAEzE,UAAW;MACpB0E,YAAY,EAAErE,sBAAuB;MACrCxE,aAAa,EAAEA,aAAc;MAC7BuD,MAAM,EAAE3D;IAAqB;MAAA4F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;IAAA;IAEF;IACAjH,OAAA,CAACf,KAAK;MACJgL,OAAO,EAAEjJ,kBAAmB;MAC5BmD,OAAO,eACLnE,OAAA;QAAKwH,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BzH,OAAA;UACEoK,GAAG,EAAE7H,QAAS;UACd8H,QAAQ;UACRC,WAAW;UACX3D,KAAK,EAAE;YACLmB,KAAK,EAAE,MAAM;YACbY,MAAM,EAAE,2BAA2B;YACnCC,SAAS,EAAE,OAAO;YAClBC,YAAY,EAAE,KAAK;YACnBO,UAAU,EAAE;UACd;QAAE;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFjH,OAAA;UAAQoK,GAAG,EAAE5H,SAAU;UAACmE,KAAK,EAAE;YAAEqC,OAAO,EAAE;UAAO;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDjH,OAAA;UAAK2G,KAAK,EAAE;YAAEyC,SAAS,EAAE,MAAM;YAAEb,SAAS,EAAE;UAAS,CAAE;UAAAd,QAAA,eACrDzH,OAAA,CAACtB,KAAK;YAACqL,IAAI,EAAC,OAAO;YAAAtC,QAAA,gBACjBzH,OAAA,CAACvB,MAAM;cACLmI,KAAK,EAAC,SAAS;cACfmD,IAAI,EAAC,OAAO;cACZ7B,OAAO,EAAErC,SAAU;cACnBc,KAAK,EAAE;gBAAE4D,QAAQ,EAAE;cAAQ,CAAE;cAAA9C,QAAA,gBAE7BzH,OAAA,CAACZ,aAAa;gBAAA0H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBACnB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjH,OAAA,CAACvB,MAAM;cACLmI,KAAK,EAAC,SAAS;cACfmD,IAAI,EAAC,OAAO;cACZ7B,OAAO,EAAEzC,UAAW;cACpBkB,KAAK,EAAE;gBAAE4D,QAAQ,EAAE;cAAQ,CAAE;cAAA9C,QAAA,gBAE7BzH,OAAA,CAACT,YAAY;gBAAAuH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAClB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;MACDiD,OAAO,EAAEzE;IAAW;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACF,eAGDjH,OAAA;MACE2C,IAAI,EAAC,MAAM;MACXyH,GAAG,EAAE9H,YAAa;MAClBkI,MAAM,EAAC,SAAS;MAChB7D,KAAK,EAAE;QAAEqC,OAAO,EAAE;MAAO,CAAE;MAC3BY,QAAQ,EAAE1D;IAAiB;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC/G,EAAA,CAvvBID,cAAc;EAAA,QACDzB,WAAW;AAAA;AAAAiM,EAAA,GADxBxK,cAAc;AAyvBpB,eAAeA,cAAc;AAAC,IAAAwK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}