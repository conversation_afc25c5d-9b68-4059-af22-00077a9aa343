{"ast": null, "code": "import * as React from \"react\";\nfunction CheckShieldFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CheckShieldFill-CheckShieldFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CheckShieldFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CheckShieldFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M23.5828289,4.01695412 L39.8608414,7.34925941 C41.6942831,7.74902106 43,9.35659657 43,11.2141357 L43,29.7665668 C43,32.6436189 41.4221454,35.2941108 38.8789667,36.6891094 L25.4394833,43.5124845 C24.2329985,44.1742723 22.7670015,44.1742723 21.5605167,43.5124845 L9.12103331,36.6891094 C6.57785456,35.2941108 5,32.6436189 5,29.7665668 L5,11.2141357 C5,9.35659657 6.30571695,7.74902106 8.1391586,7.34925941 L23.4173942,4.01800934 C23.471863,4.00613301 23.528213,4.00577358 23.5828289,4.01695412 Z M32.7059681,19.0033083 L29.600427,19.0033083 C29.4950855,19.0033083 29.3939962,19.0448628 29.3191091,19.1189489 L29.3191091,19.1189489 L22.839,25.5297482 L19.3598926,22.0876137 C19.2850047,22.0135216 19.1839112,21.9719633 19.0785649,21.9719633 L19.0785649,21.9719633 L15.973927,21.9719633 C15.8671099,21.9719633 15.7647301,22.0146867 15.689598,22.0906147 C15.5342135,22.2476453 15.5355479,22.5009078 15.6925785,22.6562923 L15.6925785,22.6562923 L21.4327294,28.3362721 C21.8018489,28.7014801 22.2776035,28.8936948 22.7589706,28.9129163 L22.7589706,28.9129163 L22.9194955,28.9129163 L23.0796048,28.900102 C23.5052693,28.8488447 23.9176305,28.6609014 24.2457367,28.3362721 L24.2457367,28.3362721 L32.9872951,19.6876586 C33.0632362,19.6125249 33.1059681,19.5101359 33.1059681,19.4033083 C33.1059681,19.1823944 32.926882,19.0033083 32.7059681,19.0033083 L32.7059681,19.0033083 Z\",\n    id: \"CheckShieldFill-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default CheckShieldFill;", "map": {"version": 3, "names": ["React", "CheckShieldFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/CheckShieldFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction CheckShieldFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CheckShieldFill-CheckShieldFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CheckShieldFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CheckShieldFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M23.5828289,4.01695412 L39.8608414,7.34925941 C41.6942831,7.74902106 43,9.35659657 43,11.2141357 L43,29.7665668 C43,32.6436189 41.4221454,35.2941108 38.8789667,36.6891094 L25.4394833,43.5124845 C24.2329985,44.1742723 22.7670015,44.1742723 21.5605167,43.5124845 L9.12103331,36.6891094 C6.57785456,35.2941108 5,32.6436189 5,29.7665668 L5,11.2141357 C5,9.35659657 6.30571695,7.74902106 8.1391586,7.34925941 L23.4173942,4.01800934 C23.471863,4.00613301 23.528213,4.00577358 23.5828289,4.01695412 Z M32.7059681,19.0033083 L29.600427,19.0033083 C29.4950855,19.0033083 29.3939962,19.0448628 29.3191091,19.1189489 L29.3191091,19.1189489 L22.839,25.5297482 L19.3598926,22.0876137 C19.2850047,22.0135216 19.1839112,21.9719633 19.0785649,21.9719633 L19.0785649,21.9719633 L15.973927,21.9719633 C15.8671099,21.9719633 15.7647301,22.0146867 15.689598,22.0906147 C15.5342135,22.2476453 15.5355479,22.5009078 15.6925785,22.6562923 L15.6925785,22.6562923 L21.4327294,28.3362721 C21.8018489,28.7014801 22.2776035,28.8936948 22.7589706,28.9129163 L22.7589706,28.9129163 L22.9194955,28.9129163 L23.0796048,28.900102 C23.5052693,28.8488447 23.9176305,28.6609014 24.2457367,28.3362721 L24.2457367,28.3362721 L32.9872951,19.6876586 C33.0632362,19.6125249 33.1059681,19.5101359 33.1059681,19.4033083 C33.1059681,19.1823944 32.926882,19.0033083 32.7059681,19.0033083 L32.7059681,19.0033083 Z\",\n    id: \"CheckShieldFill-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default CheckShieldFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,iCAAiC;IACrCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,y1CAAy1C;IAC51CR,EAAE,EAAE,0CAA0C;IAC9CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}