{"ast": null, "code": "import * as React from \"react\";\nfunction UserContactOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UserContactOutline-UserContactOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UserContactOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UserContactOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24.5,4 C30.0112644,4 34.479027,8.46775399 34.479027,13.9790078 L34.479027,18.0209922 C34.479027,21.7144498 32.4724515,24.9392476 29.4899594,26.6647285 L29.4895135,27 L40.4649873,32.8658886 C42.8766161,34.1547938 44.2604463,36.7822067 43.9591026,39.5 C43.6750656,42.0617054 41.5099747,44 38.9325658,44 L10.0674342,44 C7.49002529,44 5.32493441,42.0617054 5.04089744,39.5 C4.73955374,36.7822067 6.12338392,34.1547938 8.53501271,32.8658886 L19.5104865,27 L19.5100406,26.6647285 C16.5275485,24.9392476 14.520973,21.7144498 14.520973,18.0209922 L14.520973,13.9790078 C14.520973,8.46775399 18.9887356,4 24.5,4 Z M24.5,7 C20.7258932,7 17.6514878,9.9957724 17.5250256,13.7390798 L17.5209788,13.9790078 L17.5209788,18.0209922 C17.5209788,20.4528116 18.7728519,22.6635411 20.7856095,23.9310701 L21.0123556,24.0679885 L22.3083607,24.817775 C22.4317831,24.8891793 22.5078628,25.0208861 22.5080524,25.1634751 L22.5125654,28.5568164 C22.512762,28.7046068 22.4314515,28.8404631 22.3011089,28.9101252 L9.94909066,35.5117138 C8.6194532,36.2223442 7.85648652,37.670953 8.0226306,39.1693924 C8.13239999,40.1593932 8.93277672,40.9205158 9.91149276,40.9941531 L10.0674342,41 L38.9325658,41 C39.9810601,41 40.8618227,40.2114985 40.9773694,39.1693924 C41.1359615,37.7390639 40.4479878,36.3541389 39.2286771,35.6131276 L39.0509093,35.5117138 L26.6988911,28.9101252 C26.5685485,28.8404631 26.487238,28.7046068 26.4874346,28.5568164 L26.4919476,25.1634751 C26.4921372,25.0208861 26.5682169,24.8891793 26.6916393,24.817775 L27.9876444,24.0679885 C30.0560904,22.8713167 31.3818366,20.7046996 31.4738944,18.2902478 L31.4790212,18.0209922 L31.4790212,13.9790078 C31.4790212,10.1246082 28.354407,7 24.5,7 Z M43.6,23 C43.8209139,23 44,23.1790861 44,23.4 L44,25.6 C44,25.8209139 43.8209139,26 43.6,26 L40.4,26 C40.1790861,26 40,25.8209139 40,25.6 L40,23.4 C40,23.1790861 40.1790861,23 40.4,23 L43.6,23 Z M43.6,17 C43.8209139,17 44,17.1790861 44,17.4 L44,19.6 C44,19.8209139 43.8209139,20 43.6,20 L37.4,20 C37.1790861,20 37,19.8209139 37,19.6 L37,17.4 C37,17.1790861 37.1790861,17 37.4,17 L43.6,17 Z M43.6,11 C43.8209139,11 44,11.1790861 44,11.4 L44,13.6 C44,13.8209139 43.8209139,14 43.6,14 L37.4,14 C37.1790861,14 37,13.8209139 37,13.6 L37,11.4 C37,11.1790861 37.1790861,11 37.4,11 L43.6,11 Z\",\n    id: \"UserContactOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default UserContactOutline;", "map": {"version": 3, "names": ["React", "UserContactOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/UserContactOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction UserContactOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UserContactOutline-UserContactOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UserContactOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UserContactOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24.5,4 C30.0112644,4 34.479027,8.46775399 34.479027,13.9790078 L34.479027,18.0209922 C34.479027,21.7144498 32.4724515,24.9392476 29.4899594,26.6647285 L29.4895135,27 L40.4649873,32.8658886 C42.8766161,34.1547938 44.2604463,36.7822067 43.9591026,39.5 C43.6750656,42.0617054 41.5099747,44 38.9325658,44 L10.0674342,44 C7.49002529,44 5.32493441,42.0617054 5.04089744,39.5 C4.73955374,36.7822067 6.12338392,34.1547938 8.53501271,32.8658886 L19.5104865,27 L19.5100406,26.6647285 C16.5275485,24.9392476 14.520973,21.7144498 14.520973,18.0209922 L14.520973,13.9790078 C14.520973,8.46775399 18.9887356,4 24.5,4 Z M24.5,7 C20.7258932,7 17.6514878,9.9957724 17.5250256,13.7390798 L17.5209788,13.9790078 L17.5209788,18.0209922 C17.5209788,20.4528116 18.7728519,22.6635411 20.7856095,23.9310701 L21.0123556,24.0679885 L22.3083607,24.817775 C22.4317831,24.8891793 22.5078628,25.0208861 22.5080524,25.1634751 L22.5125654,28.5568164 C22.512762,28.7046068 22.4314515,28.8404631 22.3011089,28.9101252 L9.94909066,35.5117138 C8.6194532,36.2223442 7.85648652,37.670953 8.0226306,39.1693924 C8.13239999,40.1593932 8.93277672,40.9205158 9.91149276,40.9941531 L10.0674342,41 L38.9325658,41 C39.9810601,41 40.8618227,40.2114985 40.9773694,39.1693924 C41.1359615,37.7390639 40.4479878,36.3541389 39.2286771,35.6131276 L39.0509093,35.5117138 L26.6988911,28.9101252 C26.5685485,28.8404631 26.487238,28.7046068 26.4874346,28.5568164 L26.4919476,25.1634751 C26.4921372,25.0208861 26.5682169,24.8891793 26.6916393,24.817775 L27.9876444,24.0679885 C30.0560904,22.8713167 31.3818366,20.7046996 31.4738944,18.2902478 L31.4790212,18.0209922 L31.4790212,13.9790078 C31.4790212,10.1246082 28.354407,7 24.5,7 Z M43.6,23 C43.8209139,23 44,23.1790861 44,23.4 L44,25.6 C44,25.8209139 43.8209139,26 43.6,26 L40.4,26 C40.1790861,26 40,25.8209139 40,25.6 L40,23.4 C40,23.1790861 40.1790861,23 40.4,23 L43.6,23 Z M43.6,17 C43.8209139,17 44,17.1790861 44,17.4 L44,19.6 C44,19.8209139 43.8209139,20 43.6,20 L37.4,20 C37.1790861,20 37,19.8209139 37,19.6 L37,17.4 C37,17.1790861 37.1790861,17 37.4,17 L43.6,17 Z M43.6,11 C43.8209139,11 44,11.1790861 44,11.4 L44,13.6 C44,13.8209139 43.8209139,14 43.6,14 L37.4,14 C37.1790861,14 37,13.8209139 37,13.6 L37,11.4 C37,11.1790861 37.1790861,11 37.4,11 L43.6,11 Z\",\n    id: \"UserContactOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default UserContactOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACjC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,uCAAuC;IAC3CC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,iCAAiC;IACrCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,stEAAstE;IACztER,EAAE,EAAE,6CAA6C;IACjDG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}