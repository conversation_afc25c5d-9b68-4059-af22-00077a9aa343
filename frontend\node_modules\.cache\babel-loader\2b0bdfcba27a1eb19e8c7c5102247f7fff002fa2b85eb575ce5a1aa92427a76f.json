{"ast": null, "code": "import * as React from \"react\";\nfunction CalculatorOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CalculatorOutline-CalculatorOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CalculatorOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CalculatorOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M38,4 C41.3137085,4 44,6.6862915 44,10 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,10 C4,6.6862915 6.6862915,4 10,4 L38,4 Z M38,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 Z M24,22.4 L24,24.6 C24,24.8209139 23.8209139,25 23.6,25 L6.4,25 C6.1790861,25 6,24.8209139 6,24.6 L6,22.4 C6,22.1790861 6.1790861,22 6.4,22 L23.6,22 C23.8209139,22 24,22.1790861 24,22.4 Z M19,31.4 L19,33.6 C19,33.8209139 18.8209139,34 18.6,34 L10.4,34 C10.1790861,34 10,33.8209139 10,33.6 L10,31.4 C10,31.1790861 10.1790861,31 10.4,31 L18.6,31 C18.8209139,31 19,31.1790861 19,31.4 Z M38,20.4 L38,22.6 C38,22.8209139 37.8209139,23 37.6,23 L29.4,23 C29.1790861,23 29,22.8209139 29,22.6 L29,20.4 C29,20.1790861 29.1790861,20 29.4,20 L37.6,20 C37.8209139,20 38,20.1790861 38,20.4 Z M38,25.4 L38,27.6 C38,27.8209139 37.8209139,28 37.6,28 L29.4,28 C29.1790861,28 29,27.8209139 29,27.6 L29,25.4 C29,25.1790861 29.1790861,25 29.4,25 L37.6,25 C37.8209139,25 38,25.1790861 38,25.4 Z M19,13.4 L19,15.6 C19,15.8209139 18.8209139,16 18.6,16 L10.4,16 C10.1790861,16 10,15.8209139 10,15.6 L10,13.4 C10,13.1790861 10.1790861,13 10.4,13 L18.6,13 C18.8209139,13 19,13.1790861 19,13.4 Z M25,6.4 L25,42.6 C25,42.8209139 24.8209139,43 24.6,43 L22.4,43 C22.1790861,43 22,42.8209139 22,42.6 L22,6.4 C22,6.1790861 22.1790861,6 22.4,6 L24.6,6 C24.8209139,6 25,6.1790861 25,6.4 Z M16,10.4 L16,18.6 C16,18.8209139 15.8209139,19 15.6,19 L13.4,19 C13.1790861,19 13,18.8209139 13,18.6 L13,10.4 C13,10.1790861 13.1790861,10 13.4,10 L15.6,10 C15.8209139,10 16,10.1790861 16,10.4 Z\",\n    id: \"CalculatorOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default CalculatorOutline;", "map": {"version": 3, "names": ["React", "CalculatorOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/CalculatorOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction CalculatorOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CalculatorOutline-CalculatorOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CalculatorOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CalculatorOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M38,4 C41.3137085,4 44,6.6862915 44,10 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,10 C4,6.6862915 6.6862915,4 10,4 L38,4 Z M38,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 Z M24,22.4 L24,24.6 C24,24.8209139 23.8209139,25 23.6,25 L6.4,25 C6.1790861,25 6,24.8209139 6,24.6 L6,22.4 C6,22.1790861 6.1790861,22 6.4,22 L23.6,22 C23.8209139,22 24,22.1790861 24,22.4 Z M19,31.4 L19,33.6 C19,33.8209139 18.8209139,34 18.6,34 L10.4,34 C10.1790861,34 10,33.8209139 10,33.6 L10,31.4 C10,31.1790861 10.1790861,31 10.4,31 L18.6,31 C18.8209139,31 19,31.1790861 19,31.4 Z M38,20.4 L38,22.6 C38,22.8209139 37.8209139,23 37.6,23 L29.4,23 C29.1790861,23 29,22.8209139 29,22.6 L29,20.4 C29,20.1790861 29.1790861,20 29.4,20 L37.6,20 C37.8209139,20 38,20.1790861 38,20.4 Z M38,25.4 L38,27.6 C38,27.8209139 37.8209139,28 37.6,28 L29.4,28 C29.1790861,28 29,27.8209139 29,27.6 L29,25.4 C29,25.1790861 29.1790861,25 29.4,25 L37.6,25 C37.8209139,25 38,25.1790861 38,25.4 Z M19,13.4 L19,15.6 C19,15.8209139 18.8209139,16 18.6,16 L10.4,16 C10.1790861,16 10,15.8209139 10,15.6 L10,13.4 C10,13.1790861 10.1790861,13 10.4,13 L18.6,13 C18.8209139,13 19,13.1790861 19,13.4 Z M25,6.4 L25,42.6 C25,42.8209139 24.8209139,43 24.6,43 L22.4,43 C22.1790861,43 22,42.8209139 22,42.6 L22,6.4 C22,6.1790861 22.1790861,6 22.4,6 L24.6,6 C24.8209139,6 25,6.1790861 25,6.4 Z M16,10.4 L16,18.6 C16,18.8209139 15.8209139,19 15.6,19 L13.4,19 C13.1790861,19 13,18.8209139 13,18.6 L13,10.4 C13,10.1790861 13.1790861,10 13.4,10 L15.6,10 C15.8209139,10 16,10.1790861 16,10.4 Z\",\n    id: \"CalculatorOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default CalculatorOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EAChC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,qCAAqC;IACzCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,gCAAgC;IACpCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,2wDAA2wD;IAC9wDR,EAAE,EAAE,gCAAgC;IACpCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}