{"ast": null, "code": "import * as React from \"react\";\nfunction CameraOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CameraOutline-CameraOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CameraOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CameraOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M29.7352369,5 L29.7352369,5 C30.8779691,5 31.9107146,5.6794308 32.3609512,6.72709926 L33.6600003,9.74999944 L38.2857146,9.74999944 L38.2857143,9.74999944 C41.441625,9.74999931 44,12.301973 44,15.449997 L44,37.3000024 L44,37.3000024 C44,40.448022 41.441625,43 38.2857143,43 L9.71428593,43 L9.71428568,43 C6.55837498,43 4,40.448022 4,37.3000024 C4,37.3000024 4,37.3000024 4,37.3000024 L4,15.449997 L4,15.4499979 C4,12.3019783 6.55837052,9.7500003 9.71428568,9.7500003 L14.3399999,9.7500003 L15.639049,6.72710012 L15.6390488,6.72710065 C16.0892854,5.67942774 17.1220309,5 18.2647631,5 L29.7352541,5 L29.7352369,5 Z M29.6874418,8 L18.3125379,8 L16.2885949,12.7058932 L9.8333318,12.7058932 L9.83333193,12.7058932 C8.33302878,12.7058098 7.09273709,13.8712377 7.00472224,15.3637716 L7,15.5294187 L7,37.1764725 L7,37.1766294 C7,38.6717398 8.16954247,39.9076725 9.66726906,39.9953033 L9.83333427,40 L38.1666681,40 L38.1666681,40 C39.6669712,40 40.9072629,38.8346555 40.9952778,37.3421215 L41,37.1764745 L41,15.5294206 L41,15.5292634 C41,14.034153 39.8304575,12.7982203 38.3327309,12.7105895 L38.1666662,12.7058928 L31.711403,12.7058928 L29.68746,8 L29.6874418,8 Z M24.5,16 C29.7468582,16 34,20.2531552 34,25.5 C34,30.7468582 29.7468448,35 24.5,35 C19.2531418,35 15,30.7468448 15,25.5 C15,20.2531418 19.2531552,16 24.5,16 Z M24.5,19 L24.5,19 C20.9101422,19 18,21.9101552 18,25.5 C18,29.0898579 20.9101552,32 24.5,32 L24.5,32 C28.0898578,32 31,29.0898448 31,25.5 C31,21.9101421 28.0898448,19 24.5,19 L24.5,19 Z M36.5,15 L36.5,15 C37.3284266,15 38,15.6715734 38,16.5 C38,17.3284266 37.3284266,18 36.5,18 L36.5,18 C35.6715734,18 35,17.3284266 35,16.5 C35,15.6715734 35.6715734,15 36.5,15 L36.5,15 Z\",\n    id: \"CameraOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default CameraOutline;", "map": {"version": 3, "names": ["React", "CameraOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/CameraOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction CameraOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CameraOutline-CameraOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CameraOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CameraOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M29.7352369,5 L29.7352369,5 C30.8779691,5 31.9107146,5.6794308 32.3609512,6.72709926 L33.6600003,9.74999944 L38.2857146,9.74999944 L38.2857143,9.74999944 C41.441625,9.74999931 44,12.301973 44,15.449997 L44,37.3000024 L44,37.3000024 C44,40.448022 41.441625,43 38.2857143,43 L9.71428593,43 L9.71428568,43 C6.55837498,43 4,40.448022 4,37.3000024 C4,37.3000024 4,37.3000024 4,37.3000024 L4,15.449997 L4,15.4499979 C4,12.3019783 6.55837052,9.7500003 9.71428568,9.7500003 L14.3399999,9.7500003 L15.639049,6.72710012 L15.6390488,6.72710065 C16.0892854,5.67942774 17.1220309,5 18.2647631,5 L29.7352541,5 L29.7352369,5 Z M29.6874418,8 L18.3125379,8 L16.2885949,12.7058932 L9.8333318,12.7058932 L9.83333193,12.7058932 C8.33302878,12.7058098 7.09273709,13.8712377 7.00472224,15.3637716 L7,15.5294187 L7,37.1764725 L7,37.1766294 C7,38.6717398 8.16954247,39.9076725 9.66726906,39.9953033 L9.83333427,40 L38.1666681,40 L38.1666681,40 C39.6669712,40 40.9072629,38.8346555 40.9952778,37.3421215 L41,37.1764745 L41,15.5294206 L41,15.5292634 C41,14.034153 39.8304575,12.7982203 38.3327309,12.7105895 L38.1666662,12.7058928 L31.711403,12.7058928 L29.68746,8 L29.6874418,8 Z M24.5,16 C29.7468582,16 34,20.2531552 34,25.5 C34,30.7468582 29.7468448,35 24.5,35 C19.2531418,35 15,30.7468448 15,25.5 C15,20.2531418 19.2531552,16 24.5,16 Z M24.5,19 L24.5,19 C20.9101422,19 18,21.9101552 18,25.5 C18,29.0898579 20.9101552,32 24.5,32 L24.5,32 C28.0898578,32 31,29.0898448 31,25.5 C31,21.9101421 28.0898448,19 24.5,19 L24.5,19 Z M36.5,15 L36.5,15 C37.3284266,15 38,15.6715734 38,16.5 C38,17.3284266 37.3284266,18 36.5,18 L36.5,18 C35.6715734,18 35,17.3284266 35,16.5 C35,15.6715734 35.6715734,15 36.5,15 L36.5,15 Z\",\n    id: \"CameraOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default CameraOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,6BAA6B;IACjCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,upDAAupD;IAC1pDR,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}