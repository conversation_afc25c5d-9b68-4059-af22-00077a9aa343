{"ast": null, "code": "import * as React from \"react\";\nfunction TeamFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TeamFill-TeamFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TeamFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"TeamFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M21.3075825,7 L21.3075821,7 C25.8674409,7 29.5639574,10.64701 29.5639574,15.1458369 L29.5639574,19.3737099 L29.5639574,19.3737587 C29.5639574,21.5025121 28.7193266,23.5466806 27.2108608,25.0687515 L25.3585641,26.9378651 L25.3585641,26.9378651 C25.2929033,27.0041177 25.256175,27.0931202 25.256257,27.1857813 L25.256257,27.4779684 C25.256257,27.604583 25.3298462,27.7196869 25.4447174,27.7763533 L37.4065695,33.5696273 L37.4065696,33.5696273 C38.7585628,34.2244461 39.6151827,35.5810327 39.6151827,37.0669828 L39.6151827,37.6194821 L39.6151827,37.6194821 C39.6151827,39.4864914 38.0811359,41 36.1887914,41 L6.42638993,41 C4.62524678,41 3.1346132,39.6240642 3.00717946,37.822245 L3,37.6185993 L3,37.066984 C3,35.678651 3.7484571,34.3965721 4.99498816,33.6820417 L5.20857649,33.5695941 L17.1704286,27.7763202 C17.2745306,27.7249662 17.3454275,27.6257994 17.3526071,27.5664767 L17.3588891,27.4779353 L17.3588891,27.1857482 C17.3588891,27.1060608 17.3319662,27.0299149 17.321197,27.0130921 L17.256582,26.9378319 L15.4042853,25.0696065 L15.4042854,25.0696066 C13.9827723,23.6331251 13.14788,21.7300447 13.0592994,19.7243668 L13.0512225,19.3737423 L13.0512225,15.1458692 C13.0512225,10.7542219 16.5808169,7.1558793 21.0365597,7.00447324 L21.3075825,7 Z M28.8459993,8.06249866 L28.8459995,8.06249866 C32.8228046,8.06249866 36.0801031,11.1798158 36.2013404,15.1015523 L36.2049301,15.322906 L36.2049301,18.886705 L36.2049301,18.8863575 C36.2049301,20.7016738 35.5156614,22.451136 34.2730005,23.7898718 L34.0836428,23.9873193 L32.4880098,25.5810673 L32.4880098,25.5810673 C32.4659344,25.6032101 32.4501501,25.6306918 32.4422409,25.6607547 L32.4359588,25.7059109 L32.4359588,25.9511709 C32.4359588,25.9919 32.4539074,26.0290873 32.4835228,26.0556498 L32.5167277,26.0777852 L42.9807822,31.0980913 L42.9807825,31.0980915 C44.1558543,31.6619836 44.9275408,32.8113083 44.9955128,34.0987662 L45,34.2829324 L45,34.7406909 L45,34.7400362 C45,36.3709135 43.7084698,37.7176181 42.0579856,37.8077354 L41.8874739,37.8121625 L41.7663208,37.8121625 L41.7663209,37.8121625 C41.7686405,37.7478482 41.7698372,37.6835019 41.7699105,37.6191494 L41.7699105,37.0666501 L41.7645259,36.8116504 L41.7645261,36.8116546 C41.6702202,34.608767 40.3630074,32.6329221 38.356985,31.6611924 L28.4466593,26.8613547 L28.7517855,26.5541154 L28.7517853,26.5541156 C30.6537758,24.6349774 31.7187286,22.057505 31.7186908,19.3734093 L31.7186908,15.1458268 C31.7186908,12.5046336 30.7081854,10.0963019 29.0479374,8.27677296 L28.8460088,8.06249866 L28.8459993,8.06249866 Z\",\n    id: \"TeamFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default TeamFill;", "map": {"version": 3, "names": ["React", "TeamFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/TeamFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction TeamFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TeamFill-TeamFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TeamFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"TeamFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M21.3075825,7 L21.3075821,7 C25.8674409,7 29.5639574,10.64701 29.5639574,15.1458369 L29.5639574,19.3737099 L29.5639574,19.3737587 C29.5639574,21.5025121 28.7193266,23.5466806 27.2108608,25.0687515 L25.3585641,26.9378651 L25.3585641,26.9378651 C25.2929033,27.0041177 25.256175,27.0931202 25.256257,27.1857813 L25.256257,27.4779684 C25.256257,27.604583 25.3298462,27.7196869 25.4447174,27.7763533 L37.4065695,33.5696273 L37.4065696,33.5696273 C38.7585628,34.2244461 39.6151827,35.5810327 39.6151827,37.0669828 L39.6151827,37.6194821 L39.6151827,37.6194821 C39.6151827,39.4864914 38.0811359,41 36.1887914,41 L6.42638993,41 C4.62524678,41 3.1346132,39.6240642 3.00717946,37.822245 L3,37.6185993 L3,37.066984 C3,35.678651 3.7484571,34.3965721 4.99498816,33.6820417 L5.20857649,33.5695941 L17.1704286,27.7763202 C17.2745306,27.7249662 17.3454275,27.6257994 17.3526071,27.5664767 L17.3588891,27.4779353 L17.3588891,27.1857482 C17.3588891,27.1060608 17.3319662,27.0299149 17.321197,27.0130921 L17.256582,26.9378319 L15.4042853,25.0696065 L15.4042854,25.0696066 C13.9827723,23.6331251 13.14788,21.7300447 13.0592994,19.7243668 L13.0512225,19.3737423 L13.0512225,15.1458692 C13.0512225,10.7542219 16.5808169,7.1558793 21.0365597,7.00447324 L21.3075825,7 Z M28.8459993,8.06249866 L28.8459995,8.06249866 C32.8228046,8.06249866 36.0801031,11.1798158 36.2013404,15.1015523 L36.2049301,15.322906 L36.2049301,18.886705 L36.2049301,18.8863575 C36.2049301,20.7016738 35.5156614,22.451136 34.2730005,23.7898718 L34.0836428,23.9873193 L32.4880098,25.5810673 L32.4880098,25.5810673 C32.4659344,25.6032101 32.4501501,25.6306918 32.4422409,25.6607547 L32.4359588,25.7059109 L32.4359588,25.9511709 C32.4359588,25.9919 32.4539074,26.0290873 32.4835228,26.0556498 L32.5167277,26.0777852 L42.9807822,31.0980913 L42.9807825,31.0980915 C44.1558543,31.6619836 44.9275408,32.8113083 44.9955128,34.0987662 L45,34.2829324 L45,34.7406909 L45,34.7400362 C45,36.3709135 43.7084698,37.7176181 42.0579856,37.8077354 L41.8874739,37.8121625 L41.7663208,37.8121625 L41.7663209,37.8121625 C41.7686405,37.7478482 41.7698372,37.6835019 41.7699105,37.6191494 L41.7699105,37.0666501 L41.7645259,36.8116504 L41.7645261,36.8116546 C41.6702202,34.608767 40.3630074,32.6329221 38.356985,31.6611924 L28.4466593,26.8613547 L28.7517855,26.5541154 L28.7517853,26.5541156 C30.6537758,24.6349774 31.7187286,22.057505 31.7186908,19.3734093 L31.7186908,15.1458268 C31.7186908,12.5046336 30.7081854,10.0963019 29.0479374,8.27677296 L28.8460088,8.06249866 L28.8459993,8.06249866 Z\",\n    id: \"TeamFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default TeamFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,mBAAmB;IACvBC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,uBAAuB;IAC3BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,09EAA09E;IAC79ER,EAAE,EAAE,uBAAuB;IAC3BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}