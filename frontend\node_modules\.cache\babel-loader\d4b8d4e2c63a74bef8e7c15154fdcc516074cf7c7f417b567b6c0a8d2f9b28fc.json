{"ast": null, "code": "export function nearest(arr, target) {\n  return arr.reduce((pre, cur) => {\n    return Math.abs(pre - target) < Math.abs(cur - target) ? pre : cur;\n  });\n}", "map": {"version": 3, "names": ["nearest", "arr", "target", "reduce", "pre", "cur", "Math", "abs"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/nearest.js"], "sourcesContent": ["export function nearest(arr, target) {\n  return arr.reduce((pre, cur) => {\n    return Math.abs(pre - target) < Math.abs(cur - target) ? pre : cur;\n  });\n}"], "mappings": "AAAA,OAAO,SAASA,OAAOA,CAACC,GAAG,EAAEC,MAAM,EAAE;EACnC,OAAOD,GAAG,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IAC9B,OAAOC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,MAAM,CAAC,GAAGI,IAAI,CAACC,GAAG,CAACF,GAAG,GAAGH,MAAM,CAAC,GAAGE,GAAG,GAAGC,GAAG;EACpE,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}