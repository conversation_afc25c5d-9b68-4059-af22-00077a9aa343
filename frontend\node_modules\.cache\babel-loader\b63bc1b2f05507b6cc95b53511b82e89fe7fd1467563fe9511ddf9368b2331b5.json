{"ast": null, "code": "// 找到树的深度\nexport function getTreeDeep(treeData, childrenName = 'children') {\n  const walker = tree => {\n    let deep = 0;\n    tree.forEach(item => {\n      if (item[childrenName]) {\n        deep = Math.max(deep, walker(item[childrenName]) + 1);\n      } else {\n        deep = Math.max(deep, 1);\n      }\n    });\n    return deep;\n  };\n  return walker(treeData);\n}", "map": {"version": 3, "names": ["getTreeDeep", "treeData", "<PERSON><PERSON><PERSON>", "walker", "tree", "deep", "for<PERSON>ach", "item", "Math", "max"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/tree.js"], "sourcesContent": ["// 找到树的深度\nexport function getTreeDeep(treeData, childrenName = 'children') {\n  const walker = tree => {\n    let deep = 0;\n    tree.forEach(item => {\n      if (item[childrenName]) {\n        deep = Math.max(deep, walker(item[childrenName]) + 1);\n      } else {\n        deep = Math.max(deep, 1);\n      }\n    });\n    return deep;\n  };\n  return walker(treeData);\n}"], "mappings": "AAAA;AACA,OAAO,SAASA,WAAWA,CAACC,QAAQ,EAAEC,YAAY,GAAG,UAAU,EAAE;EAC/D,MAAMC,MAAM,GAAGC,IAAI,IAAI;IACrB,IAAIC,IAAI,GAAG,CAAC;IACZD,IAAI,CAACE,OAAO,CAACC,IAAI,IAAI;MACnB,IAAIA,IAAI,CAACL,YAAY,CAAC,EAAE;QACtBG,IAAI,GAAGG,IAAI,CAACC,GAAG,CAACJ,IAAI,EAAEF,MAAM,CAACI,IAAI,CAACL,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC;MACvD,CAAC,MAAM;QACLG,IAAI,GAAGG,IAAI,CAACC,GAAG,CAACJ,IAAI,EAAE,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAOA,IAAI;EACb,CAAC;EACD,OAAOF,MAAM,CAACF,QAAQ,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}