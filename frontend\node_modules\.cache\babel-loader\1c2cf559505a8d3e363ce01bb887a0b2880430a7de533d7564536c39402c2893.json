{"ast": null, "code": "import * as React from \"react\";\nfunction CloseCircleFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CloseCircleFill-CloseCircleFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CloseCircleFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CloseCircleFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M18.6753876,16 L15.5637812,16 C15.4576916,16 15.3559474,16.0421451 15.2809323,16.1171635 C15.124726,16.2733766 15.1247316,16.5266426 15.2809447,16.6828489 L15.2809447,16.6828489 L22.299066,23.7006641 L14.6828159,31.3171619 C14.6078042,31.3921761 14.5656632,31.4939157 14.5656632,31.6 C14.5656632,31.8209139 14.7447493,32 14.9656632,32 L14.9656632,32 L18.0753284,32 C18.1814068,32 18.2831412,31.9578638 18.3581544,31.8828594 L18.3581544,31.8828594 L24.420066,25.8216641 L30.4818451,31.8828564 C30.5568585,31.9578626 30.6585942,32 30.7646741,32 L30.7646741,32 L33.8763476,32 C33.9824309,32 34.0841695,31.9578599 34.1591835,31.8828496 C34.315397,31.7266436 34.3154031,31.4733776 34.1591972,31.3171641 L34.1591972,31.3171641 L26.542066,23.6996641 L33.5591874,16.6828489 C33.6342057,16.6078338 33.6763508,16.5060896 33.6763508,16.4 C33.6763508,16.1790861 33.4972647,16 33.2763508,16 L33.2763508,16 L30.1637654,16 C30.0576705,16 29.9559218,16.0421493 29.8809058,16.1171741 L29.8809058,16.1171741 L24.420066,21.5786641 L18.9582218,16.1171488 C18.883208,16.0421394 18.7814701,16 18.6753876,16 L18.6753876,16 Z\",\n    id: \"CloseCircleFill-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default CloseCircleFill;", "map": {"version": 3, "names": ["React", "CloseCircleFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/CloseCircleFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction CloseCircleFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CloseCircleFill-CloseCircleFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CloseCircleFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CloseCircleFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M18.6753876,16 L15.5637812,16 C15.4576916,16 15.3559474,16.0421451 15.2809323,16.1171635 C15.124726,16.2733766 15.1247316,16.5266426 15.2809447,16.6828489 L15.2809447,16.6828489 L22.299066,23.7006641 L14.6828159,31.3171619 C14.6078042,31.3921761 14.5656632,31.4939157 14.5656632,31.6 C14.5656632,31.8209139 14.7447493,32 14.9656632,32 L14.9656632,32 L18.0753284,32 C18.1814068,32 18.2831412,31.9578638 18.3581544,31.8828594 L18.3581544,31.8828594 L24.420066,25.8216641 L30.4818451,31.8828564 C30.5568585,31.9578626 30.6585942,32 30.7646741,32 L30.7646741,32 L33.8763476,32 C33.9824309,32 34.0841695,31.9578599 34.1591835,31.8828496 C34.315397,31.7266436 34.3154031,31.4733776 34.1591972,31.3171641 L34.1591972,31.3171641 L26.542066,23.6996641 L33.5591874,16.6828489 C33.6342057,16.6078338 33.6763508,16.5060896 33.6763508,16.4 C33.6763508,16.1790861 33.4972647,16 33.2763508,16 L33.2763508,16 L30.1637654,16 C30.0576705,16 29.9559218,16.0421493 29.8809058,16.1171741 L29.8809058,16.1171741 L24.420066,21.5786641 L18.9582218,16.1171488 C18.883208,16.0421394 18.7814701,16 18.6753876,16 L18.6753876,16 Z\",\n    id: \"CloseCircleFill-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default CloseCircleFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,iCAAiC;IACrCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,6tCAA6tC;IAChuCR,EAAE,EAAE,0CAA0C;IAC9CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}