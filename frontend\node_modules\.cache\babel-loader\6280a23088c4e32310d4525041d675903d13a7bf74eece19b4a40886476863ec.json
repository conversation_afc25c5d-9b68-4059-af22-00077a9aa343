{"ast": null, "code": "import React from 'react';\nconst eventToPropRecord = {\n  'click': 'onClick',\n  'touchstart': 'onTouchStart'\n};\nexport function withStopPropagation(events, element) {\n  const props = Object.assign({}, element.props);\n  for (const key of events) {\n    const prop = eventToPropRecord[key];\n    props[prop] = function (e) {\n      var _a, _b;\n      e.stopPropagation();\n      (_b = (_a = element.props)[prop]) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n    };\n  }\n  return React.cloneElement(element, props);\n}", "map": {"version": 3, "names": ["React", "eventToPropRecord", "withStopPropagation", "events", "element", "props", "Object", "assign", "key", "prop", "e", "_a", "_b", "stopPropagation", "call", "cloneElement"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/with-stop-propagation.js"], "sourcesContent": ["import React from 'react';\nconst eventToPropRecord = {\n  'click': 'onClick',\n  'touchstart': 'onTouchStart'\n};\nexport function withStopPropagation(events, element) {\n  const props = Object.assign({}, element.props);\n  for (const key of events) {\n    const prop = eventToPropRecord[key];\n    props[prop] = function (e) {\n      var _a, _b;\n      e.stopPropagation();\n      (_b = (_a = element.props)[prop]) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n    };\n  }\n  return React.cloneElement(element, props);\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,iBAAiB,GAAG;EACxB,OAAO,EAAE,SAAS;EAClB,YAAY,EAAE;AAChB,CAAC;AACD,OAAO,SAASC,mBAAmBA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACnD,MAAMC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,OAAO,CAACC,KAAK,CAAC;EAC9C,KAAK,MAAMG,GAAG,IAAIL,MAAM,EAAE;IACxB,MAAMM,IAAI,GAAGR,iBAAiB,CAACO,GAAG,CAAC;IACnCH,KAAK,CAACI,IAAI,CAAC,GAAG,UAAUC,CAAC,EAAE;MACzB,IAAIC,EAAE,EAAEC,EAAE;MACVF,CAAC,CAACG,eAAe,CAAC,CAAC;MACnB,CAACD,EAAE,GAAG,CAACD,EAAE,GAAGP,OAAO,CAACC,KAAK,EAAEI,IAAI,CAAC,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACH,EAAE,EAAED,CAAC,CAAC;IACvF,CAAC;EACH;EACA,OAAOV,KAAK,CAACe,YAAY,CAACX,OAAO,EAAEC,KAAK,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}