{"ast": null, "code": "import * as React from \"react\";\nfunction LoopOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LoopOutline-LoopOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LoopOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"LoopOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M45,23.9999933 C45,33.0885744 38.2838255,40.6133592 29.5325428,41.9009499 L31.310863,43.6752375 C31.3851955,43.7498443 31.3848362,43.8704466 31.3100599,43.9446105 C31.2742535,43.9801239 31.2258053,44 31.1753176,44 L27.9766321,44 C27.8753372,44 27.7781618,43.9599955 27.7064956,43.8885712 L23.3699922,39.5619014 C23.2956597,39.4872946 23.296019,39.3666923 23.3707953,39.2925283 C23.4066017,39.257015 23.4550499,39.2370993 23.5055376,39.237139 L26.8636338,39.237139 C35.2989765,39.237139 42.1363635,32.4152121 42.1363635,23.9990424 C42.1363635,16.2752395 36.3766446,9.89425059 28.9092122,8.89616917 C27.9527528,7.94248644 27.2354083,7.22722438 26.7571786,6.75038302 C26.6997912,6.69316229 26.6424039,6.63594156 26.5850166,6.57872083 C26.4307025,6.42485467 26.4303393,6.17502516 26.5842055,6.02071102 C26.6582349,5.946466 26.7587699,5.90473742 26.8636159,5.90473742 L26.8636159,5.90473742 L26.8636159,5.90473742 C36.8797024,5.90473742 45,14.0066791 45,23.9999933 Z M5.86362172,24 C5.86362172,31.7237962 11.6233406,38.1047851 19.0907731,39.1028665 C20.0152405,40.0249432 20.708591,40.7165006 21.1708247,41.177539 C21.2372224,41.2437649 21.314401,41.320744 21.4023607,41.4084761 C21.5588083,41.5644731 21.5591362,41.8177595 21.4031161,41.9741841 C21.3275197,42.0499766 21.2246997,42.0923082 21.1176532,42.0917112 C20.8025471,42.0899536 20.6077184,42.0878593 20.5331671,42.0854283 C10.7962981,41.7679239 3,33.7919251 3,24 C3,14.9104881 9.71617454,7.38664079 18.4674572,6.09905014 L16.689137,4.32476253 C16.6148045,4.25015574 16.6151638,4.12955338 16.6899401,4.05538946 C16.7257465,4.01987612 16.7741947,4 16.8246824,4 L20.0233679,4 C20.1246629,4 20.2218382,4.04000449 20.2935044,4.11142881 L24.6300078,8.4380986 C24.7043403,8.51270537 24.703981,8.63330773 24.6292047,8.70747165 C24.5933983,8.74298499 24.5449502,8.76290073 24.4944624,8.762861 L21.1363662,8.762861 C12.7010088,8.76189672 5.86362172,15.5838236 5.86362172,24 Z\",\n    id: \"LoopOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default LoopOutline;", "map": {"version": 3, "names": ["React", "LoopOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/LoopOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction LoopOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LoopOutline-LoopOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LoopOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"LoopOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M45,23.9999933 C45,33.0885744 38.2838255,40.6133592 29.5325428,41.9009499 L31.310863,43.6752375 C31.3851955,43.7498443 31.3848362,43.8704466 31.3100599,43.9446105 C31.2742535,43.9801239 31.2258053,44 31.1753176,44 L27.9766321,44 C27.8753372,44 27.7781618,43.9599955 27.7064956,43.8885712 L23.3699922,39.5619014 C23.2956597,39.4872946 23.296019,39.3666923 23.3707953,39.2925283 C23.4066017,39.257015 23.4550499,39.2370993 23.5055376,39.237139 L26.8636338,39.237139 C35.2989765,39.237139 42.1363635,32.4152121 42.1363635,23.9990424 C42.1363635,16.2752395 36.3766446,9.89425059 28.9092122,8.89616917 C27.9527528,7.94248644 27.2354083,7.22722438 26.7571786,6.75038302 C26.6997912,6.69316229 26.6424039,6.63594156 26.5850166,6.57872083 C26.4307025,6.42485467 26.4303393,6.17502516 26.5842055,6.02071102 C26.6582349,5.946466 26.7587699,5.90473742 26.8636159,5.90473742 L26.8636159,5.90473742 L26.8636159,5.90473742 C36.8797024,5.90473742 45,14.0066791 45,23.9999933 Z M5.86362172,24 C5.86362172,31.7237962 11.6233406,38.1047851 19.0907731,39.1028665 C20.0152405,40.0249432 20.708591,40.7165006 21.1708247,41.177539 C21.2372224,41.2437649 21.314401,41.320744 21.4023607,41.4084761 C21.5588083,41.5644731 21.5591362,41.8177595 21.4031161,41.9741841 C21.3275197,42.0499766 21.2246997,42.0923082 21.1176532,42.0917112 C20.8025471,42.0899536 20.6077184,42.0878593 20.5331671,42.0854283 C10.7962981,41.7679239 3,33.7919251 3,24 C3,14.9104881 9.71617454,7.38664079 18.4674572,6.09905014 L16.689137,4.32476253 C16.6148045,4.25015574 16.6151638,4.12955338 16.6899401,4.05538946 C16.7257465,4.01987612 16.7741947,4 16.8246824,4 L20.0233679,4 C20.1246629,4 20.2218382,4.04000449 20.2935044,4.11142881 L24.6300078,8.4380986 C24.7043403,8.51270537 24.703981,8.63330773 24.6292047,8.70747165 C24.5933983,8.74298499 24.5449502,8.76290073 24.4944624,8.762861 L21.1363662,8.762861 C12.7010088,8.76189672 5.86362172,15.5838236 5.86362172,24 Z\",\n    id: \"LoopOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default LoopOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yBAAyB;IAC7BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,g4DAAg4D;IACn4DR,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}