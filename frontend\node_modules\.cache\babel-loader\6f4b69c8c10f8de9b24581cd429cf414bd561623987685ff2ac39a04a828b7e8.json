{"ast": null, "code": "import * as React from \"react\";\nfunction AaOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AaOutline-AAOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AaOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"AaOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M22.1563166,13.0000078 C22.309349,13.0004825 22.4468038,13.0948047 22.5038079,13.238463 L29.7166039,31.47846 C29.7938892,31.673224 29.7004546,31.8944864 29.5079114,31.9726632 C29.4637735,31.9905842 29.4166729,32.0000078 29.3691122,32.0000078 L27.3592981,32.0000078 C27.2062657,31.9995403 27.0688109,31.9052181 27.0118068,31.7615599 L25.3274124,27.5028915 C25.2670472,27.3502697 25.1195761,27.2500106 24.95545,27.2500106 L17.0460204,27.2500106 C16.8818418,27.2500106 16.7343348,27.350333 16.6740052,27.5030253 L14.9914339,31.7615599 C14.9341744,31.9058607 14.7957815,32.0003159 14.6420641,32.0000078 L12.6341254,32.0000078 C12.426651,32.0004231 12.2581285,31.8306265 12.2577192,31.6207587 C12.2577192,31.5720108 12.2668037,31.5236998 12.2847554,31.4784601 L19.4975514,13.238463 C19.5548109,13.0941622 19.6932038,12.999707 19.8469212,13.0000078 L22.1563166,13.0000078 Z M29.6687241,13.0000095 C29.7956612,13.0003528 29.9131025,13.0645667 29.9824028,13.1710128 L30.0180911,13.238463 L37.2308872,31.47846 C37.307752,31.6733942 37.2138404,31.8944495 37.0211292,31.9722001 C37.0005522,31.9805022 36.9792896,31.9869468 36.9575898,31.9914587 L36.8824564,32.0000095 L34.8726423,32.0000095 C34.7466415,31.9996687 34.6292003,31.9354548 34.5599,31.8290087 L34.5242116,31.7615586 L32.8407288,27.5029571 C32.7803814,27.3503 32.6328923,27.2500093 32.46874,27.2500093 L30.6768843,27.2500093 C30.5170532,27.2500093 30.3725645,27.1548624 30.3094219,27.0080326 L29.4278415,24.958033 C29.3405677,24.7550891 29.4343368,24.5198212 29.6372807,24.4325473 C29.6871983,24.4110808 29.7409663,24.4000098 29.7953039,24.4000098 L31.0254473,24.4000098 C31.2463612,24.4000098 31.4254473,24.2209237 31.4254473,24.0000098 C31.4254473,23.9496573 31.4159404,23.8997576 31.3974262,23.8529326 L28.8864174,17.5022389 C28.8051889,17.2968006 28.5727996,17.1961087 28.3673613,17.2773372 C28.2645421,17.317991 28.1831359,17.3993899 28.1424728,17.5022054 L27.5796221,18.9253594 C27.4983751,19.1307903 27.2659767,19.2314613 27.0605457,19.1502143 C26.961554,19.1110636 26.8822516,19.0340686 26.8401956,18.9362758 L25.8204317,16.5650158 C25.7786647,16.4678949 25.7770452,16.3581886 25.8159272,16.2598772 L27.0108854,13.2384764 C27.0681449,13.0941756 27.2065377,12.9997204 27.3602552,13.0000095 L29.6687241,13.0000095 Z M20.8539963,17.2773086 C20.7511787,17.3179618 20.6697734,17.3993588 20.6291097,17.5021723 L18.1173453,23.8528825 C18.0360959,24.0583125 18.1367641,24.2907121 18.3421941,24.3719615 C18.38903,24.3904856 18.4389431,24.3999976 18.4893092,24.3999976 L23.5120889,24.3999976 C23.7330028,24.3999976 23.9120889,24.2209115 23.9120889,23.9999976 C23.9120889,23.9496452 23.902582,23.8997455 23.8840678,23.8529204 L21.3730524,17.5022103 C21.2918239,17.296772 21.0594346,17.1960801 20.8539963,17.2773086 Z\",\n    id: \"AaOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default AaOutline;", "map": {"version": 3, "names": ["React", "AaOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/AaOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction AaOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AaOutline-AAOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AaOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"AaOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M22.1563166,13.0000078 C22.309349,13.0004825 22.4468038,13.0948047 22.5038079,13.238463 L29.7166039,31.47846 C29.7938892,31.673224 29.7004546,31.8944864 29.5079114,31.9726632 C29.4637735,31.9905842 29.4166729,32.0000078 29.3691122,32.0000078 L27.3592981,32.0000078 C27.2062657,31.9995403 27.0688109,31.9052181 27.0118068,31.7615599 L25.3274124,27.5028915 C25.2670472,27.3502697 25.1195761,27.2500106 24.95545,27.2500106 L17.0460204,27.2500106 C16.8818418,27.2500106 16.7343348,27.350333 16.6740052,27.5030253 L14.9914339,31.7615599 C14.9341744,31.9058607 14.7957815,32.0003159 14.6420641,32.0000078 L12.6341254,32.0000078 C12.426651,32.0004231 12.2581285,31.8306265 12.2577192,31.6207587 C12.2577192,31.5720108 12.2668037,31.5236998 12.2847554,31.4784601 L19.4975514,13.238463 C19.5548109,13.0941622 19.6932038,12.999707 19.8469212,13.0000078 L22.1563166,13.0000078 Z M29.6687241,13.0000095 C29.7956612,13.0003528 29.9131025,13.0645667 29.9824028,13.1710128 L30.0180911,13.238463 L37.2308872,31.47846 C37.307752,31.6733942 37.2138404,31.8944495 37.0211292,31.9722001 C37.0005522,31.9805022 36.9792896,31.9869468 36.9575898,31.9914587 L36.8824564,32.0000095 L34.8726423,32.0000095 C34.7466415,31.9996687 34.6292003,31.9354548 34.5599,31.8290087 L34.5242116,31.7615586 L32.8407288,27.5029571 C32.7803814,27.3503 32.6328923,27.2500093 32.46874,27.2500093 L30.6768843,27.2500093 C30.5170532,27.2500093 30.3725645,27.1548624 30.3094219,27.0080326 L29.4278415,24.958033 C29.3405677,24.7550891 29.4343368,24.5198212 29.6372807,24.4325473 C29.6871983,24.4110808 29.7409663,24.4000098 29.7953039,24.4000098 L31.0254473,24.4000098 C31.2463612,24.4000098 31.4254473,24.2209237 31.4254473,24.0000098 C31.4254473,23.9496573 31.4159404,23.8997576 31.3974262,23.8529326 L28.8864174,17.5022389 C28.8051889,17.2968006 28.5727996,17.1961087 28.3673613,17.2773372 C28.2645421,17.317991 28.1831359,17.3993899 28.1424728,17.5022054 L27.5796221,18.9253594 C27.4983751,19.1307903 27.2659767,19.2314613 27.0605457,19.1502143 C26.961554,19.1110636 26.8822516,19.0340686 26.8401956,18.9362758 L25.8204317,16.5650158 C25.7786647,16.4678949 25.7770452,16.3581886 25.8159272,16.2598772 L27.0108854,13.2384764 C27.0681449,13.0941756 27.2065377,12.9997204 27.3602552,13.0000095 L29.6687241,13.0000095 Z M20.8539963,17.2773086 C20.7511787,17.3179618 20.6697734,17.3993588 20.6291097,17.5021723 L18.1173453,23.8528825 C18.0360959,24.0583125 18.1367641,24.2907121 18.3421941,24.3719615 C18.38903,24.3904856 18.4389431,24.3999976 18.4893092,24.3999976 L23.5120889,24.3999976 C23.7330028,24.3999976 23.9120889,24.2209115 23.9120889,23.9999976 C23.9120889,23.9496452 23.902582,23.8997455 23.8840678,23.8529204 L21.3730524,17.5022103 C21.2918239,17.296772 21.0594346,17.1960801 20.8539963,17.2773086 Z\",\n    id: \"AaOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default AaOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,qBAAqB;IACzBC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,wBAAwB;IAC5BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,y+FAAy+F;IAC5+FR,EAAE,EAAE,oCAAoC;IACxCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}