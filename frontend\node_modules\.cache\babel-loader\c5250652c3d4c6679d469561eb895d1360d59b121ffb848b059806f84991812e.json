{"ast": null, "code": "import * as React from \"react\";\nfunction CloseShieldOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CloseShieldOutline-CloseShieldOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CloseShieldOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CloseShieldOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M23.5,4 L39.8608414,7.3485224 C41.6942831,7.74819585 43,9.35541667 43,11.212546 L43,29.7608837 C43,32.637301 41.4221454,35.2872082 38.8789667,36.681899 L25.4394833,43.5037686 C24.2329985,44.1654105 22.7670015,44.1654105 21.5605167,43.5037686 L9.12103331,36.681899 C6.57785456,35.2872082 5,32.637301 5,29.7608837 L5,11.212546 C5,9.35541667 6.30571695,7.74819585 8.1391586,7.3485224 L23.5,4 Z M23.5,7.03778638 L8.78478965,10.2465401 C8.36462594,10.3381319 8.05532106,10.6833926 8.00668805,11.0979156 L8,11.212546 L8,29.7608837 C8,31.480481 9.90227033,33.0705522 11.3717744,33.9684245 L11.5756458,34.0865183 L23.0151292,40.9083879 C23.2790477,41.053122 23.5926385,41.0712138 23.8689752,40.9626632 L23.9848708,40.9083879 L37.4243542,34.0865183 C38.9447328,33.2527357 39.9131065,31.7011782 39.9944389,29.9945186 L40,29.7608837 L40,11.212546 C40,10.7869539 39.7257088,10.4138599 39.3269393,10.2776704 L39.2152103,10.2465401 L23.5,7.03778638 Z M20.3495844,18 C20.4549192,18 20.5560025,18.0415492 20.6308884,18.1156269 L23.757,21.208 L26.8831116,18.1156269 C26.9579975,18.0415492 27.0590808,18 27.1644156,18 L30.270713,18 C30.4916269,18 30.670713,18.1790861 30.670713,18.4 C30.670713,18.5068532 30.6279608,18.6092645 30.5519876,18.6844022 L25.879,23.306 L30.8231343,28.1966225 C30.9801916,28.35198 30.9815696,28.6052422 30.8262122,28.7622996 C30.7510764,28.8382575 30.6486757,28.881 30.5418347,28.881 L27.4364131,28.881 C27.3310798,28.881 27.2299978,28.839452 27.1551121,28.7653761 L23.758,25.405 L20.3598807,28.7654162 C20.2849985,28.8394675 20.1839338,28.881 20.0786202,28.881 L16.9731653,28.881 C16.7522514,28.881 16.5731653,28.7019139 16.5731653,28.481 C16.5731653,28.374159 16.6159078,28.2717583 16.6918657,28.1966225 L21.636,23.306 L16.9638569,18.684376 C16.8068004,18.5290177 16.8054237,18.2757554 16.960782,18.1186989 C17.0359177,18.0427419 17.1383178,18 17.245158,18 L20.3495844,18 Z\",\n    id: \"CloseShieldOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default CloseShieldOutline;", "map": {"version": 3, "names": ["React", "CloseShieldOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/CloseShieldOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction CloseShieldOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CloseShieldOutline-CloseShieldOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CloseShieldOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CloseShieldOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M23.5,4 L39.8608414,7.3485224 C41.6942831,7.74819585 43,9.35541667 43,11.212546 L43,29.7608837 C43,32.637301 41.4221454,35.2872082 38.8789667,36.681899 L25.4394833,43.5037686 C24.2329985,44.1654105 22.7670015,44.1654105 21.5605167,43.5037686 L9.12103331,36.681899 C6.57785456,35.2872082 5,32.637301 5,29.7608837 L5,11.212546 C5,9.35541667 6.30571695,7.74819585 8.1391586,7.3485224 L23.5,4 Z M23.5,7.03778638 L8.78478965,10.2465401 C8.36462594,10.3381319 8.05532106,10.6833926 8.00668805,11.0979156 L8,11.212546 L8,29.7608837 C8,31.480481 9.90227033,33.0705522 11.3717744,33.9684245 L11.5756458,34.0865183 L23.0151292,40.9083879 C23.2790477,41.053122 23.5926385,41.0712138 23.8689752,40.9626632 L23.9848708,40.9083879 L37.4243542,34.0865183 C38.9447328,33.2527357 39.9131065,31.7011782 39.9944389,29.9945186 L40,29.7608837 L40,11.212546 C40,10.7869539 39.7257088,10.4138599 39.3269393,10.2776704 L39.2152103,10.2465401 L23.5,7.03778638 Z M20.3495844,18 C20.4549192,18 20.5560025,18.0415492 20.6308884,18.1156269 L23.757,21.208 L26.8831116,18.1156269 C26.9579975,18.0415492 27.0590808,18 27.1644156,18 L30.270713,18 C30.4916269,18 30.670713,18.1790861 30.670713,18.4 C30.670713,18.5068532 30.6279608,18.6092645 30.5519876,18.6844022 L25.879,23.306 L30.8231343,28.1966225 C30.9801916,28.35198 30.9815696,28.6052422 30.8262122,28.7622996 C30.7510764,28.8382575 30.6486757,28.881 30.5418347,28.881 L27.4364131,28.881 C27.3310798,28.881 27.2299978,28.839452 27.1551121,28.7653761 L23.758,25.405 L20.3598807,28.7654162 C20.2849985,28.8394675 20.1839338,28.881 20.0786202,28.881 L16.9731653,28.881 C16.7522514,28.881 16.5731653,28.7019139 16.5731653,28.481 C16.5731653,28.374159 16.6159078,28.2717583 16.6918657,28.1966225 L21.636,23.306 L16.9638569,18.684376 C16.8068004,18.5290177 16.8054237,18.2757554 16.960782,18.1186989 C17.0359177,18.0427419 17.1383178,18 17.245158,18 L20.3495844,18 Z\",\n    id: \"CloseShieldOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default CloseShieldOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACjC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,uCAAuC;IAC3CC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,iCAAiC;IACrCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,81DAA81D;IACj2DR,EAAE,EAAE,6CAA6C;IACjDG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}