{"ast": null, "code": "import { __rest } from \"tslib\";\nimport { QuestionCircleOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport { Field } from 'rc-field-form';\nimport FieldContext from 'rc-field-form/lib/FieldContext';\nimport React, { useCallback, useContext, useRef, useState } from 'react';\nimport { devWarning } from '../../utils/dev-log';\nimport { withNativeProps } from '../../utils/native-props';\nimport { undefinedFallback } from '../../utils/undefined-fallback';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport List from '../list';\nimport Popover from '../popover';\nimport { FormContext, NoStyleItemContext } from './context';\nimport { isSafeSetRefComponent, toArray } from './utils';\nconst NAME_SPLIT = '__SPLIT__';\nconst classPrefix = `adm-form-item`;\nconst MemoInput = React.memo(({\n  children\n}) => children, (prev, next) => prev.value === next.value && prev.update === next.update);\nconst FormItemLayout = props => {\n  var _a;\n  const {\n    locale,\n    form: componentConfig = {}\n  } = useConfig();\n  const {\n    style,\n    extra,\n    label,\n    help,\n    helpIcon,\n    required,\n    children,\n    htmlFor,\n    hidden,\n    arrow,\n    arrowIcon,\n    childElementPosition = 'normal'\n  } = mergeProps(componentConfig, props);\n  const context = useContext(FormContext);\n  const hasFeedback = props.hasFeedback !== undefined ? props.hasFeedback : context.hasFeedback;\n  const layout = props.layout || context.layout;\n  const disabled = (_a = props.disabled) !== null && _a !== void 0 ? _a : context.disabled;\n  const requiredMark = (() => {\n    const {\n      requiredMarkStyle\n    } = context;\n    switch (requiredMarkStyle) {\n      case 'asterisk':\n        return required && React.createElement(\"span\", {\n          className: `${classPrefix}-required-asterisk`\n        }, \"*\");\n      case 'text-required':\n        return required && React.createElement(\"span\", {\n          className: `${classPrefix}-required-text`\n        }, \"(\", locale.Form.required, \")\");\n      case 'text-optional':\n        return !required && React.createElement(\"span\", {\n          className: `${classPrefix}-required-text`\n        }, \"(\", locale.Form.optional, \")\");\n      case 'none':\n        return null;\n      default:\n        return null;\n    }\n  })();\n  const labelElement = !!label && React.createElement(\"label\", {\n    className: `${classPrefix}-label`,\n    htmlFor: htmlFor\n  }, label, requiredMark, help && React.createElement(Popover, {\n    content: help,\n    mode: 'dark',\n    trigger: 'click'\n  }, React.createElement(\"span\", {\n    className: `${classPrefix}-label-help`,\n    onClick: e => {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }, helpIcon || React.createElement(QuestionCircleOutline, null))));\n  const description = (!!props.description || hasFeedback) && React.createElement(React.Fragment, null, props.description, hasFeedback && React.createElement(React.Fragment, null, props.errors.map((error, index) => React.createElement(\"div\", {\n    key: `error-${index}`,\n    className: `${classPrefix}-feedback-error`\n  }, error)), props.warnings.map((warning, index) => React.createElement(\"div\", {\n    key: `warning-${index}`,\n    className: `${classPrefix}-feedback-warning`\n  }, warning))));\n  return withNativeProps(props, React.createElement(List.Item, {\n    style: style,\n    title: layout === 'vertical' && labelElement,\n    prefix: layout === 'horizontal' && labelElement,\n    extra: extra,\n    description: description,\n    className: classNames(classPrefix, `${classPrefix}-${layout}`, {\n      [`${classPrefix}-hidden`]: hidden,\n      [`${classPrefix}-has-error`]: props.errors.length\n    }),\n    disabled: disabled,\n    onClick: props.onClick,\n    clickable: props.clickable,\n    arrowIcon: arrowIcon || arrow\n  }, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-child`, `${classPrefix}-child-position-${childElementPosition}`)\n  }, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-child-inner`)\n  }, children))));\n};\nexport const FormItem = props => {\n  const {\n      // 样式相关\n      style,\n      // FormItem 相关\n      label,\n      help,\n      helpIcon,\n      extra,\n      hasFeedback,\n      name,\n      required,\n      noStyle,\n      hidden,\n      layout,\n      childElementPosition,\n      description,\n      // Field 相关\n      disabled,\n      rules,\n      children,\n      messageVariables,\n      trigger = 'onChange',\n      validateTrigger = trigger,\n      onClick,\n      shouldUpdate,\n      dependencies,\n      clickable,\n      arrow,\n      arrowIcon\n    } = props,\n    fieldProps = __rest(props, [\"style\", \"label\", \"help\", \"helpIcon\", \"extra\", \"hasFeedback\", \"name\", \"required\", \"noStyle\", \"hidden\", \"layout\", \"childElementPosition\", \"description\", \"disabled\", \"rules\", \"children\", \"messageVariables\", \"trigger\", \"validateTrigger\", \"onClick\", \"shouldUpdate\", \"dependencies\", \"clickable\", \"arrow\", \"arrowIcon\"]);\n  const {\n    name: formName\n  } = useContext(FormContext);\n  const {\n    validateTrigger: contextValidateTrigger\n  } = useContext(FieldContext);\n  const mergedValidateTrigger = undefinedFallback(validateTrigger, contextValidateTrigger, trigger);\n  const widgetRef = useRef(null);\n  const updateRef = useRef(0);\n  updateRef.current += 1;\n  const [subMetas, setSubMetas] = useState({});\n  const onSubMetaChange = useCallback((subMeta, namePath) => {\n    setSubMetas(prevSubMetas => {\n      const nextSubMetas = Object.assign({}, prevSubMetas);\n      const nameKey = namePath.join(NAME_SPLIT);\n      if (subMeta.destroy) {\n        delete nextSubMetas[nameKey];\n      } else {\n        nextSubMetas[nameKey] = subMeta;\n      }\n      return nextSubMetas;\n    });\n  }, [setSubMetas]);\n  function renderLayout(baseChildren, fieldId, meta, isRequired) {\n    var _a, _b;\n    if (noStyle && !hidden) {\n      return baseChildren;\n    }\n    const curErrors = (_a = meta === null || meta === void 0 ? void 0 : meta.errors) !== null && _a !== void 0 ? _a : [];\n    const errors = Object.keys(subMetas).reduce((subErrors, key) => {\n      var _a, _b;\n      const errors = (_b = (_a = subMetas[key]) === null || _a === void 0 ? void 0 : _a.errors) !== null && _b !== void 0 ? _b : [];\n      if (errors.length) {\n        subErrors = [...subErrors, ...errors];\n      }\n      return subErrors;\n    }, curErrors);\n    const curWarnings = (_b = meta === null || meta === void 0 ? void 0 : meta.warnings) !== null && _b !== void 0 ? _b : [];\n    const warnings = Object.keys(subMetas).reduce((subWarnings, key) => {\n      var _a, _b;\n      const warnings = (_b = (_a = subMetas[key]) === null || _a === void 0 ? void 0 : _a.warnings) !== null && _b !== void 0 ? _b : [];\n      if (warnings.length) {\n        subWarnings = [...subWarnings, ...warnings];\n      }\n      return subWarnings;\n    }, curWarnings);\n    return withNativeProps(props, React.createElement(FormItemLayout, {\n      style: style,\n      label: label,\n      extra: extra,\n      help: help,\n      helpIcon: helpIcon,\n      description: description,\n      required: isRequired,\n      disabled: disabled,\n      hasFeedback: hasFeedback,\n      htmlFor: fieldId,\n      errors: errors,\n      warnings: warnings,\n      onClick: onClick && (e => onClick(e, widgetRef)),\n      hidden: hidden,\n      layout: layout,\n      childElementPosition: childElementPosition,\n      clickable: clickable,\n      arrow: arrow,\n      arrowIcon: arrowIcon\n    }, React.createElement(NoStyleItemContext.Provider, {\n      value: onSubMetaChange\n    }, baseChildren)));\n  }\n  const isRenderProps = typeof children === 'function';\n  if (!name && !isRenderProps && !props.dependencies) {\n    return renderLayout(children);\n  }\n  let Variables = {};\n  Variables.label = typeof label === 'string' ? label : '';\n  if (messageVariables) {\n    Variables = Object.assign(Object.assign({}, Variables), messageVariables);\n  }\n  const notifyParentMetaChange = useContext(NoStyleItemContext);\n  const onMetaChange = meta => {\n    if (noStyle && notifyParentMetaChange) {\n      const namePath = meta.name;\n      notifyParentMetaChange(meta, namePath);\n    }\n  };\n  return React.createElement(Field, Object.assign({}, fieldProps, {\n    name: name,\n    shouldUpdate: shouldUpdate,\n    dependencies: dependencies,\n    rules: rules,\n    trigger: trigger,\n    validateTrigger: mergedValidateTrigger,\n    onMetaChange: onMetaChange,\n    messageVariables: Variables\n  }), (control, meta, context) => {\n    let childNode = null;\n    const isRequired = required !== undefined ? required : rules && rules.some(rule => !!(rule && typeof rule === 'object' && rule.required));\n    const nameList = toArray(name).length && meta ? meta.name : [];\n    const fieldId = (nameList.length > 0 && formName ? [formName, ...nameList] : nameList).join('_');\n    if (shouldUpdate && dependencies) {\n      devWarning('Form.Item', \"`shouldUpdate` and `dependencies` shouldn't be used together.\");\n    }\n    if (isRenderProps) {\n      if ((shouldUpdate || dependencies) && !name) {\n        childNode = children(context);\n      } else {\n        if (!(shouldUpdate || dependencies)) {\n          devWarning('Form.Item', '`children` of render props only work with `shouldUpdate` or `dependencies`.');\n        }\n        if (name) {\n          devWarning('Form.Item', \"Do not use `name` with `children` of render props since it's not a field.\");\n        }\n      }\n      // not render props\n    } else if (dependencies && !name) {\n      devWarning('Form.Item', 'Must set `name` or use render props when `dependencies` is set.');\n    } else if (React.isValidElement(children)) {\n      if (children.props.defaultValue) {\n        devWarning('Form.Item', '`defaultValue` will not work on controlled Field. You should use `initialValues` of Form instead.');\n      }\n      const childProps = Object.assign(Object.assign({}, children.props), control);\n      if (isSafeSetRefComponent(children)) {\n        childProps.ref = instance => {\n          const originRef = children.ref;\n          if (originRef) {\n            if (typeof originRef === 'function') {\n              originRef(instance);\n            }\n            if ('current' in originRef) {\n              originRef.current = instance;\n            }\n          }\n          widgetRef.current = instance;\n        };\n      }\n      if (!childProps.id) {\n        childProps.id = fieldId;\n      }\n      // We should keep user origin event handler\n      const triggers = new Set([...toArray(trigger), ...toArray(mergedValidateTrigger)]);\n      triggers.forEach(eventName => {\n        childProps[eventName] = (...args) => {\n          var _a, _b, _c;\n          (_a = control[eventName]) === null || _a === void 0 ? void 0 : _a.call(control, ...args);\n          (_c = (_b = children.props)[eventName]) === null || _c === void 0 ? void 0 : _c.call(_b, ...args);\n        };\n      });\n      childNode = React.createElement(MemoInput, {\n        value: control[props.valuePropName || 'value'],\n        update: updateRef.current\n      }, React.cloneElement(children, childProps));\n    } else {\n      if (name) {\n        devWarning('Form.Item', '`name` is only used for validate React element. If you are using Form.Item as layout display, please remove `name` instead.');\n      }\n      childNode = children;\n    }\n    return renderLayout(childNode, fieldId, meta, isRequired);\n  });\n};", "map": {"version": 3, "names": ["__rest", "QuestionCircleOutline", "classNames", "Field", "FieldContext", "React", "useCallback", "useContext", "useRef", "useState", "dev<PERSON><PERSON><PERSON>", "withNativeProps", "undefined<PERSON><PERSON><PERSON>", "mergeProps", "useConfig", "List", "Popover", "FormContext", "NoStyleItemContext", "isSafeSetRefComponent", "toArray", "NAME_SPLIT", "classPrefix", "MemoInput", "memo", "children", "prev", "next", "value", "update", "FormItemLayout", "props", "_a", "locale", "form", "componentConfig", "style", "extra", "label", "help", "helpIcon", "required", "htmlFor", "hidden", "arrow", "arrowIcon", "childElementPosition", "context", "hasFeedback", "undefined", "layout", "disabled", "requiredMark", "requiredMarkStyle", "createElement", "className", "Form", "optional", "labelElement", "content", "mode", "trigger", "onClick", "e", "stopPropagation", "preventDefault", "description", "Fragment", "errors", "map", "error", "index", "key", "warnings", "warning", "<PERSON><PERSON>", "title", "prefix", "length", "clickable", "FormItem", "name", "noStyle", "rules", "messageVariables", "validate<PERSON><PERSON>ger", "shouldUpdate", "dependencies", "fieldProps", "formName", "contextValidateTrigger", "mergedValidateTrigger", "widgetRef", "updateRef", "current", "subMetas", "setSubMetas", "onSubMetaChange", "subMeta", "namePath", "prevSubMetas", "nextSubMetas", "Object", "assign", "<PERSON><PERSON><PERSON>", "join", "destroy", "renderLayout", "baseChildren", "fieldId", "meta", "isRequired", "_b", "curErrors", "keys", "reduce", "subErrors", "curWarnings", "subWarnings", "Provider", "isRenderProps", "Variables", "notifyParentMetaChange", "onMetaChange", "control", "childNode", "some", "rule", "nameList", "isValidElement", "defaultValue", "childProps", "ref", "instance", "originRef", "id", "triggers", "Set", "for<PERSON>ach", "eventName", "args", "_c", "call", "valuePropName", "cloneElement"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/form/form-item.js"], "sourcesContent": ["import { __rest } from \"tslib\";\nimport { QuestionCircleOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport { Field } from 'rc-field-form';\nimport FieldContext from 'rc-field-form/lib/FieldContext';\nimport React, { useCallback, useContext, useRef, useState } from 'react';\nimport { devWarning } from '../../utils/dev-log';\nimport { withNativeProps } from '../../utils/native-props';\nimport { undefinedFallback } from '../../utils/undefined-fallback';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport List from '../list';\nimport Popover from '../popover';\nimport { FormContext, NoStyleItemContext } from './context';\nimport { isSafeSetRefComponent, toArray } from './utils';\nconst NAME_SPLIT = '__SPLIT__';\nconst classPrefix = `adm-form-item`;\nconst MemoInput = React.memo(({\n  children\n}) => children, (prev, next) => prev.value === next.value && prev.update === next.update);\nconst FormItemLayout = props => {\n  var _a;\n  const {\n    locale,\n    form: componentConfig = {}\n  } = useConfig();\n  const {\n    style,\n    extra,\n    label,\n    help,\n    helpIcon,\n    required,\n    children,\n    htmlFor,\n    hidden,\n    arrow,\n    arrowIcon,\n    childElementPosition = 'normal'\n  } = mergeProps(componentConfig, props);\n  const context = useContext(FormContext);\n  const hasFeedback = props.hasFeedback !== undefined ? props.hasFeedback : context.hasFeedback;\n  const layout = props.layout || context.layout;\n  const disabled = (_a = props.disabled) !== null && _a !== void 0 ? _a : context.disabled;\n  const requiredMark = (() => {\n    const {\n      requiredMarkStyle\n    } = context;\n    switch (requiredMarkStyle) {\n      case 'asterisk':\n        return required && React.createElement(\"span\", {\n          className: `${classPrefix}-required-asterisk`\n        }, \"*\");\n      case 'text-required':\n        return required && React.createElement(\"span\", {\n          className: `${classPrefix}-required-text`\n        }, \"(\", locale.Form.required, \")\");\n      case 'text-optional':\n        return !required && React.createElement(\"span\", {\n          className: `${classPrefix}-required-text`\n        }, \"(\", locale.Form.optional, \")\");\n      case 'none':\n        return null;\n      default:\n        return null;\n    }\n  })();\n  const labelElement = !!label && React.createElement(\"label\", {\n    className: `${classPrefix}-label`,\n    htmlFor: htmlFor\n  }, label, requiredMark, help && React.createElement(Popover, {\n    content: help,\n    mode: 'dark',\n    trigger: 'click'\n  }, React.createElement(\"span\", {\n    className: `${classPrefix}-label-help`,\n    onClick: e => {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }, helpIcon || React.createElement(QuestionCircleOutline, null))));\n  const description = (!!props.description || hasFeedback) && React.createElement(React.Fragment, null, props.description, hasFeedback && React.createElement(React.Fragment, null, props.errors.map((error, index) => React.createElement(\"div\", {\n    key: `error-${index}`,\n    className: `${classPrefix}-feedback-error`\n  }, error)), props.warnings.map((warning, index) => React.createElement(\"div\", {\n    key: `warning-${index}`,\n    className: `${classPrefix}-feedback-warning`\n  }, warning))));\n  return withNativeProps(props, React.createElement(List.Item, {\n    style: style,\n    title: layout === 'vertical' && labelElement,\n    prefix: layout === 'horizontal' && labelElement,\n    extra: extra,\n    description: description,\n    className: classNames(classPrefix, `${classPrefix}-${layout}`, {\n      [`${classPrefix}-hidden`]: hidden,\n      [`${classPrefix}-has-error`]: props.errors.length\n    }),\n    disabled: disabled,\n    onClick: props.onClick,\n    clickable: props.clickable,\n    arrowIcon: arrowIcon || arrow\n  }, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-child`, `${classPrefix}-child-position-${childElementPosition}`)\n  }, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-child-inner`)\n  }, children))));\n};\nexport const FormItem = props => {\n  const {\n      // 样式相关\n      style,\n      // FormItem 相关\n      label,\n      help,\n      helpIcon,\n      extra,\n      hasFeedback,\n      name,\n      required,\n      noStyle,\n      hidden,\n      layout,\n      childElementPosition,\n      description,\n      // Field 相关\n      disabled,\n      rules,\n      children,\n      messageVariables,\n      trigger = 'onChange',\n      validateTrigger = trigger,\n      onClick,\n      shouldUpdate,\n      dependencies,\n      clickable,\n      arrow,\n      arrowIcon\n    } = props,\n    fieldProps = __rest(props, [\"style\", \"label\", \"help\", \"helpIcon\", \"extra\", \"hasFeedback\", \"name\", \"required\", \"noStyle\", \"hidden\", \"layout\", \"childElementPosition\", \"description\", \"disabled\", \"rules\", \"children\", \"messageVariables\", \"trigger\", \"validateTrigger\", \"onClick\", \"shouldUpdate\", \"dependencies\", \"clickable\", \"arrow\", \"arrowIcon\"]);\n  const {\n    name: formName\n  } = useContext(FormContext);\n  const {\n    validateTrigger: contextValidateTrigger\n  } = useContext(FieldContext);\n  const mergedValidateTrigger = undefinedFallback(validateTrigger, contextValidateTrigger, trigger);\n  const widgetRef = useRef(null);\n  const updateRef = useRef(0);\n  updateRef.current += 1;\n  const [subMetas, setSubMetas] = useState({});\n  const onSubMetaChange = useCallback((subMeta, namePath) => {\n    setSubMetas(prevSubMetas => {\n      const nextSubMetas = Object.assign({}, prevSubMetas);\n      const nameKey = namePath.join(NAME_SPLIT);\n      if (subMeta.destroy) {\n        delete nextSubMetas[nameKey];\n      } else {\n        nextSubMetas[nameKey] = subMeta;\n      }\n      return nextSubMetas;\n    });\n  }, [setSubMetas]);\n  function renderLayout(baseChildren, fieldId, meta, isRequired) {\n    var _a, _b;\n    if (noStyle && !hidden) {\n      return baseChildren;\n    }\n    const curErrors = (_a = meta === null || meta === void 0 ? void 0 : meta.errors) !== null && _a !== void 0 ? _a : [];\n    const errors = Object.keys(subMetas).reduce((subErrors, key) => {\n      var _a, _b;\n      const errors = (_b = (_a = subMetas[key]) === null || _a === void 0 ? void 0 : _a.errors) !== null && _b !== void 0 ? _b : [];\n      if (errors.length) {\n        subErrors = [...subErrors, ...errors];\n      }\n      return subErrors;\n    }, curErrors);\n    const curWarnings = (_b = meta === null || meta === void 0 ? void 0 : meta.warnings) !== null && _b !== void 0 ? _b : [];\n    const warnings = Object.keys(subMetas).reduce((subWarnings, key) => {\n      var _a, _b;\n      const warnings = (_b = (_a = subMetas[key]) === null || _a === void 0 ? void 0 : _a.warnings) !== null && _b !== void 0 ? _b : [];\n      if (warnings.length) {\n        subWarnings = [...subWarnings, ...warnings];\n      }\n      return subWarnings;\n    }, curWarnings);\n    return withNativeProps(props, React.createElement(FormItemLayout, {\n      style: style,\n      label: label,\n      extra: extra,\n      help: help,\n      helpIcon: helpIcon,\n      description: description,\n      required: isRequired,\n      disabled: disabled,\n      hasFeedback: hasFeedback,\n      htmlFor: fieldId,\n      errors: errors,\n      warnings: warnings,\n      onClick: onClick && (e => onClick(e, widgetRef)),\n      hidden: hidden,\n      layout: layout,\n      childElementPosition: childElementPosition,\n      clickable: clickable,\n      arrow: arrow,\n      arrowIcon: arrowIcon\n    }, React.createElement(NoStyleItemContext.Provider, {\n      value: onSubMetaChange\n    }, baseChildren)));\n  }\n  const isRenderProps = typeof children === 'function';\n  if (!name && !isRenderProps && !props.dependencies) {\n    return renderLayout(children);\n  }\n  let Variables = {};\n  Variables.label = typeof label === 'string' ? label : '';\n  if (messageVariables) {\n    Variables = Object.assign(Object.assign({}, Variables), messageVariables);\n  }\n  const notifyParentMetaChange = useContext(NoStyleItemContext);\n  const onMetaChange = meta => {\n    if (noStyle && notifyParentMetaChange) {\n      const namePath = meta.name;\n      notifyParentMetaChange(meta, namePath);\n    }\n  };\n  return React.createElement(Field, Object.assign({}, fieldProps, {\n    name: name,\n    shouldUpdate: shouldUpdate,\n    dependencies: dependencies,\n    rules: rules,\n    trigger: trigger,\n    validateTrigger: mergedValidateTrigger,\n    onMetaChange: onMetaChange,\n    messageVariables: Variables\n  }), (control, meta, context) => {\n    let childNode = null;\n    const isRequired = required !== undefined ? required : rules && rules.some(rule => !!(rule && typeof rule === 'object' && rule.required));\n    const nameList = toArray(name).length && meta ? meta.name : [];\n    const fieldId = (nameList.length > 0 && formName ? [formName, ...nameList] : nameList).join('_');\n    if (shouldUpdate && dependencies) {\n      devWarning('Form.Item', \"`shouldUpdate` and `dependencies` shouldn't be used together.\");\n    }\n    if (isRenderProps) {\n      if ((shouldUpdate || dependencies) && !name) {\n        childNode = children(context);\n      } else {\n        if (!(shouldUpdate || dependencies)) {\n          devWarning('Form.Item', '`children` of render props only work with `shouldUpdate` or `dependencies`.');\n        }\n        if (name) {\n          devWarning('Form.Item', \"Do not use `name` with `children` of render props since it's not a field.\");\n        }\n      }\n      // not render props\n    } else if (dependencies && !name) {\n      devWarning('Form.Item', 'Must set `name` or use render props when `dependencies` is set.');\n    } else if (React.isValidElement(children)) {\n      if (children.props.defaultValue) {\n        devWarning('Form.Item', '`defaultValue` will not work on controlled Field. You should use `initialValues` of Form instead.');\n      }\n      const childProps = Object.assign(Object.assign({}, children.props), control);\n      if (isSafeSetRefComponent(children)) {\n        childProps.ref = instance => {\n          const originRef = children.ref;\n          if (originRef) {\n            if (typeof originRef === 'function') {\n              originRef(instance);\n            }\n            if ('current' in originRef) {\n              originRef.current = instance;\n            }\n          }\n          widgetRef.current = instance;\n        };\n      }\n      if (!childProps.id) {\n        childProps.id = fieldId;\n      }\n      // We should keep user origin event handler\n      const triggers = new Set([...toArray(trigger), ...toArray(mergedValidateTrigger)]);\n      triggers.forEach(eventName => {\n        childProps[eventName] = (...args) => {\n          var _a, _b, _c;\n          (_a = control[eventName]) === null || _a === void 0 ? void 0 : _a.call(control, ...args);\n          (_c = (_b = children.props)[eventName]) === null || _c === void 0 ? void 0 : _c.call(_b, ...args);\n        };\n      });\n      childNode = React.createElement(MemoInput, {\n        value: control[props.valuePropName || 'value'],\n        update: updateRef.current\n      }, React.cloneElement(children, childProps));\n    } else {\n      if (name) {\n        devWarning('Form.Item', '`name` is only used for validate React element. If you are using Form.Item as layout display, please remove `name` instead.');\n      }\n      childNode = children;\n    }\n    return renderLayout(childNode, fieldId, meta, isRequired);\n  });\n};"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,qBAAqB,QAAQ,mBAAmB;AACzD,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,KAAK,QAAQ,eAAe;AACrC,OAAOC,YAAY,MAAM,gCAAgC;AACzD,OAAOC,KAAK,IAAIC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxE,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,OAAO,MAAM,YAAY;AAChC,SAASC,WAAW,EAAEC,kBAAkB,QAAQ,WAAW;AAC3D,SAASC,qBAAqB,EAAEC,OAAO,QAAQ,SAAS;AACxD,MAAMC,UAAU,GAAG,WAAW;AAC9B,MAAMC,WAAW,GAAG,eAAe;AACnC,MAAMC,SAAS,GAAGlB,KAAK,CAACmB,IAAI,CAAC,CAAC;EAC5BC;AACF,CAAC,KAAKA,QAAQ,EAAE,CAACC,IAAI,EAAEC,IAAI,KAAKD,IAAI,CAACE,KAAK,KAAKD,IAAI,CAACC,KAAK,IAAIF,IAAI,CAACG,MAAM,KAAKF,IAAI,CAACE,MAAM,CAAC;AACzF,MAAMC,cAAc,GAAGC,KAAK,IAAI;EAC9B,IAAIC,EAAE;EACN,MAAM;IACJC,MAAM;IACNC,IAAI,EAAEC,eAAe,GAAG,CAAC;EAC3B,CAAC,GAAGrB,SAAS,CAAC,CAAC;EACf,MAAM;IACJsB,KAAK;IACLC,KAAK;IACLC,KAAK;IACLC,IAAI;IACJC,QAAQ;IACRC,QAAQ;IACRhB,QAAQ;IACRiB,OAAO;IACPC,MAAM;IACNC,KAAK;IACLC,SAAS;IACTC,oBAAoB,GAAG;EACzB,CAAC,GAAGjC,UAAU,CAACsB,eAAe,EAAEJ,KAAK,CAAC;EACtC,MAAMgB,OAAO,GAAGxC,UAAU,CAACU,WAAW,CAAC;EACvC,MAAM+B,WAAW,GAAGjB,KAAK,CAACiB,WAAW,KAAKC,SAAS,GAAGlB,KAAK,CAACiB,WAAW,GAAGD,OAAO,CAACC,WAAW;EAC7F,MAAME,MAAM,GAAGnB,KAAK,CAACmB,MAAM,IAAIH,OAAO,CAACG,MAAM;EAC7C,MAAMC,QAAQ,GAAG,CAACnB,EAAE,GAAGD,KAAK,CAACoB,QAAQ,MAAM,IAAI,IAAInB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGe,OAAO,CAACI,QAAQ;EACxF,MAAMC,YAAY,GAAG,CAAC,MAAM;IAC1B,MAAM;MACJC;IACF,CAAC,GAAGN,OAAO;IACX,QAAQM,iBAAiB;MACvB,KAAK,UAAU;QACb,OAAOZ,QAAQ,IAAIpC,KAAK,CAACiD,aAAa,CAAC,MAAM,EAAE;UAC7CC,SAAS,EAAE,GAAGjC,WAAW;QAC3B,CAAC,EAAE,GAAG,CAAC;MACT,KAAK,eAAe;QAClB,OAAOmB,QAAQ,IAAIpC,KAAK,CAACiD,aAAa,CAAC,MAAM,EAAE;UAC7CC,SAAS,EAAE,GAAGjC,WAAW;QAC3B,CAAC,EAAE,GAAG,EAAEW,MAAM,CAACuB,IAAI,CAACf,QAAQ,EAAE,GAAG,CAAC;MACpC,KAAK,eAAe;QAClB,OAAO,CAACA,QAAQ,IAAIpC,KAAK,CAACiD,aAAa,CAAC,MAAM,EAAE;UAC9CC,SAAS,EAAE,GAAGjC,WAAW;QAC3B,CAAC,EAAE,GAAG,EAAEW,MAAM,CAACuB,IAAI,CAACC,QAAQ,EAAE,GAAG,CAAC;MACpC,KAAK,MAAM;QACT,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF,CAAC,EAAE,CAAC;EACJ,MAAMC,YAAY,GAAG,CAAC,CAACpB,KAAK,IAAIjC,KAAK,CAACiD,aAAa,CAAC,OAAO,EAAE;IAC3DC,SAAS,EAAE,GAAGjC,WAAW,QAAQ;IACjCoB,OAAO,EAAEA;EACX,CAAC,EAAEJ,KAAK,EAAEc,YAAY,EAAEb,IAAI,IAAIlC,KAAK,CAACiD,aAAa,CAACtC,OAAO,EAAE;IAC3D2C,OAAO,EAAEpB,IAAI;IACbqB,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE;EACX,CAAC,EAAExD,KAAK,CAACiD,aAAa,CAAC,MAAM,EAAE;IAC7BC,SAAS,EAAE,GAAGjC,WAAW,aAAa;IACtCwC,OAAO,EAAEC,CAAC,IAAI;MACZA,CAAC,CAACC,eAAe,CAAC,CAAC;MACnBD,CAAC,CAACE,cAAc,CAAC,CAAC;IACpB;EACF,CAAC,EAAEzB,QAAQ,IAAInC,KAAK,CAACiD,aAAa,CAACrD,qBAAqB,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;EAClE,MAAMiE,WAAW,GAAG,CAAC,CAAC,CAACnC,KAAK,CAACmC,WAAW,IAAIlB,WAAW,KAAK3C,KAAK,CAACiD,aAAa,CAACjD,KAAK,CAAC8D,QAAQ,EAAE,IAAI,EAAEpC,KAAK,CAACmC,WAAW,EAAElB,WAAW,IAAI3C,KAAK,CAACiD,aAAa,CAACjD,KAAK,CAAC8D,QAAQ,EAAE,IAAI,EAAEpC,KAAK,CAACqC,MAAM,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAKlE,KAAK,CAACiD,aAAa,CAAC,KAAK,EAAE;IAC9OkB,GAAG,EAAE,SAASD,KAAK,EAAE;IACrBhB,SAAS,EAAE,GAAGjC,WAAW;EAC3B,CAAC,EAAEgD,KAAK,CAAC,CAAC,EAAEvC,KAAK,CAAC0C,QAAQ,CAACJ,GAAG,CAAC,CAACK,OAAO,EAAEH,KAAK,KAAKlE,KAAK,CAACiD,aAAa,CAAC,KAAK,EAAE;IAC5EkB,GAAG,EAAE,WAAWD,KAAK,EAAE;IACvBhB,SAAS,EAAE,GAAGjC,WAAW;EAC3B,CAAC,EAAEoD,OAAO,CAAC,CAAC,CAAC,CAAC;EACd,OAAO/D,eAAe,CAACoB,KAAK,EAAE1B,KAAK,CAACiD,aAAa,CAACvC,IAAI,CAAC4D,IAAI,EAAE;IAC3DvC,KAAK,EAAEA,KAAK;IACZwC,KAAK,EAAE1B,MAAM,KAAK,UAAU,IAAIQ,YAAY;IAC5CmB,MAAM,EAAE3B,MAAM,KAAK,YAAY,IAAIQ,YAAY;IAC/CrB,KAAK,EAAEA,KAAK;IACZ6B,WAAW,EAAEA,WAAW;IACxBX,SAAS,EAAErD,UAAU,CAACoB,WAAW,EAAE,GAAGA,WAAW,IAAI4B,MAAM,EAAE,EAAE;MAC7D,CAAC,GAAG5B,WAAW,SAAS,GAAGqB,MAAM;MACjC,CAAC,GAAGrB,WAAW,YAAY,GAAGS,KAAK,CAACqC,MAAM,CAACU;IAC7C,CAAC,CAAC;IACF3B,QAAQ,EAAEA,QAAQ;IAClBW,OAAO,EAAE/B,KAAK,CAAC+B,OAAO;IACtBiB,SAAS,EAAEhD,KAAK,CAACgD,SAAS;IAC1BlC,SAAS,EAAEA,SAAS,IAAID;EAC1B,CAAC,EAAEvC,KAAK,CAACiD,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAErD,UAAU,CAAC,GAAGoB,WAAW,QAAQ,EAAE,GAAGA,WAAW,mBAAmBwB,oBAAoB,EAAE;EACvG,CAAC,EAAEzC,KAAK,CAACiD,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAErD,UAAU,CAAC,GAAGoB,WAAW,cAAc;EACpD,CAAC,EAAEG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC;AACD,OAAO,MAAMuD,QAAQ,GAAGjD,KAAK,IAAI;EAC/B,MAAM;MACF;MACAK,KAAK;MACL;MACAE,KAAK;MACLC,IAAI;MACJC,QAAQ;MACRH,KAAK;MACLW,WAAW;MACXiC,IAAI;MACJxC,QAAQ;MACRyC,OAAO;MACPvC,MAAM;MACNO,MAAM;MACNJ,oBAAoB;MACpBoB,WAAW;MACX;MACAf,QAAQ;MACRgC,KAAK;MACL1D,QAAQ;MACR2D,gBAAgB;MAChBvB,OAAO,GAAG,UAAU;MACpBwB,eAAe,GAAGxB,OAAO;MACzBC,OAAO;MACPwB,YAAY;MACZC,YAAY;MACZR,SAAS;MACTnC,KAAK;MACLC;IACF,CAAC,GAAGd,KAAK;IACTyD,UAAU,GAAGxF,MAAM,CAAC+B,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,sBAAsB,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,kBAAkB,EAAE,SAAS,EAAE,iBAAiB,EAAE,SAAS,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;EACvV,MAAM;IACJkD,IAAI,EAAEQ;EACR,CAAC,GAAGlF,UAAU,CAACU,WAAW,CAAC;EAC3B,MAAM;IACJoE,eAAe,EAAEK;EACnB,CAAC,GAAGnF,UAAU,CAACH,YAAY,CAAC;EAC5B,MAAMuF,qBAAqB,GAAG/E,iBAAiB,CAACyE,eAAe,EAAEK,sBAAsB,EAAE7B,OAAO,CAAC;EACjG,MAAM+B,SAAS,GAAGpF,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMqF,SAAS,GAAGrF,MAAM,CAAC,CAAC,CAAC;EAC3BqF,SAAS,CAACC,OAAO,IAAI,CAAC;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMwF,eAAe,GAAG3F,WAAW,CAAC,CAAC4F,OAAO,EAAEC,QAAQ,KAAK;IACzDH,WAAW,CAACI,YAAY,IAAI;MAC1B,MAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,YAAY,CAAC;MACpD,MAAMI,OAAO,GAAGL,QAAQ,CAACM,IAAI,CAACpF,UAAU,CAAC;MACzC,IAAI6E,OAAO,CAACQ,OAAO,EAAE;QACnB,OAAOL,YAAY,CAACG,OAAO,CAAC;MAC9B,CAAC,MAAM;QACLH,YAAY,CAACG,OAAO,CAAC,GAAGN,OAAO;MACjC;MACA,OAAOG,YAAY;IACrB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACL,WAAW,CAAC,CAAC;EACjB,SAASW,YAAYA,CAACC,YAAY,EAAEC,OAAO,EAAEC,IAAI,EAAEC,UAAU,EAAE;IAC7D,IAAI/E,EAAE,EAAEgF,EAAE;IACV,IAAI9B,OAAO,IAAI,CAACvC,MAAM,EAAE;MACtB,OAAOiE,YAAY;IACrB;IACA,MAAMK,SAAS,GAAG,CAACjF,EAAE,GAAG8E,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC1C,MAAM,MAAM,IAAI,IAAIpC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;IACpH,MAAMoC,MAAM,GAAGkC,MAAM,CAACY,IAAI,CAACnB,QAAQ,CAAC,CAACoB,MAAM,CAAC,CAACC,SAAS,EAAE5C,GAAG,KAAK;MAC9D,IAAIxC,EAAE,EAAEgF,EAAE;MACV,MAAM5C,MAAM,GAAG,CAAC4C,EAAE,GAAG,CAAChF,EAAE,GAAG+D,QAAQ,CAACvB,GAAG,CAAC,MAAM,IAAI,IAAIxC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoC,MAAM,MAAM,IAAI,IAAI4C,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;MAC7H,IAAI5C,MAAM,CAACU,MAAM,EAAE;QACjBsC,SAAS,GAAG,CAAC,GAAGA,SAAS,EAAE,GAAGhD,MAAM,CAAC;MACvC;MACA,OAAOgD,SAAS;IAClB,CAAC,EAAEH,SAAS,CAAC;IACb,MAAMI,WAAW,GAAG,CAACL,EAAE,GAAGF,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACrC,QAAQ,MAAM,IAAI,IAAIuC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;IACxH,MAAMvC,QAAQ,GAAG6B,MAAM,CAACY,IAAI,CAACnB,QAAQ,CAAC,CAACoB,MAAM,CAAC,CAACG,WAAW,EAAE9C,GAAG,KAAK;MAClE,IAAIxC,EAAE,EAAEgF,EAAE;MACV,MAAMvC,QAAQ,GAAG,CAACuC,EAAE,GAAG,CAAChF,EAAE,GAAG+D,QAAQ,CAACvB,GAAG,CAAC,MAAM,IAAI,IAAIxC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyC,QAAQ,MAAM,IAAI,IAAIuC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;MACjI,IAAIvC,QAAQ,CAACK,MAAM,EAAE;QACnBwC,WAAW,GAAG,CAAC,GAAGA,WAAW,EAAE,GAAG7C,QAAQ,CAAC;MAC7C;MACA,OAAO6C,WAAW;IACpB,CAAC,EAAED,WAAW,CAAC;IACf,OAAO1G,eAAe,CAACoB,KAAK,EAAE1B,KAAK,CAACiD,aAAa,CAACxB,cAAc,EAAE;MAChEM,KAAK,EAAEA,KAAK;MACZE,KAAK,EAAEA,KAAK;MACZD,KAAK,EAAEA,KAAK;MACZE,IAAI,EAAEA,IAAI;MACVC,QAAQ,EAAEA,QAAQ;MAClB0B,WAAW,EAAEA,WAAW;MACxBzB,QAAQ,EAAEsE,UAAU;MACpB5D,QAAQ,EAAEA,QAAQ;MAClBH,WAAW,EAAEA,WAAW;MACxBN,OAAO,EAAEmE,OAAO;MAChBzC,MAAM,EAAEA,MAAM;MACdK,QAAQ,EAAEA,QAAQ;MAClBX,OAAO,EAAEA,OAAO,KAAKC,CAAC,IAAID,OAAO,CAACC,CAAC,EAAE6B,SAAS,CAAC,CAAC;MAChDjD,MAAM,EAAEA,MAAM;MACdO,MAAM,EAAEA,MAAM;MACdJ,oBAAoB,EAAEA,oBAAoB;MAC1CiC,SAAS,EAAEA,SAAS;MACpBnC,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA;IACb,CAAC,EAAExC,KAAK,CAACiD,aAAa,CAACpC,kBAAkB,CAACqG,QAAQ,EAAE;MAClD3F,KAAK,EAAEqE;IACT,CAAC,EAAEW,YAAY,CAAC,CAAC,CAAC;EACpB;EACA,MAAMY,aAAa,GAAG,OAAO/F,QAAQ,KAAK,UAAU;EACpD,IAAI,CAACwD,IAAI,IAAI,CAACuC,aAAa,IAAI,CAACzF,KAAK,CAACwD,YAAY,EAAE;IAClD,OAAOoB,YAAY,CAAClF,QAAQ,CAAC;EAC/B;EACA,IAAIgG,SAAS,GAAG,CAAC,CAAC;EAClBA,SAAS,CAACnF,KAAK,GAAG,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,EAAE;EACxD,IAAI8C,gBAAgB,EAAE;IACpBqC,SAAS,GAAGnB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEkB,SAAS,CAAC,EAAErC,gBAAgB,CAAC;EAC3E;EACA,MAAMsC,sBAAsB,GAAGnH,UAAU,CAACW,kBAAkB,CAAC;EAC7D,MAAMyG,YAAY,GAAGb,IAAI,IAAI;IAC3B,IAAI5B,OAAO,IAAIwC,sBAAsB,EAAE;MACrC,MAAMvB,QAAQ,GAAGW,IAAI,CAAC7B,IAAI;MAC1ByC,sBAAsB,CAACZ,IAAI,EAAEX,QAAQ,CAAC;IACxC;EACF,CAAC;EACD,OAAO9F,KAAK,CAACiD,aAAa,CAACnD,KAAK,EAAEmG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEf,UAAU,EAAE;IAC9DP,IAAI,EAAEA,IAAI;IACVK,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA,YAAY;IAC1BJ,KAAK,EAAEA,KAAK;IACZtB,OAAO,EAAEA,OAAO;IAChBwB,eAAe,EAAEM,qBAAqB;IACtCgC,YAAY,EAAEA,YAAY;IAC1BvC,gBAAgB,EAAEqC;EACpB,CAAC,CAAC,EAAE,CAACG,OAAO,EAAEd,IAAI,EAAE/D,OAAO,KAAK;IAC9B,IAAI8E,SAAS,GAAG,IAAI;IACpB,MAAMd,UAAU,GAAGtE,QAAQ,KAAKQ,SAAS,GAAGR,QAAQ,GAAG0C,KAAK,IAAIA,KAAK,CAAC2C,IAAI,CAACC,IAAI,IAAI,CAAC,EAAEA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACtF,QAAQ,CAAC,CAAC;IACzI,MAAMuF,QAAQ,GAAG5G,OAAO,CAAC6D,IAAI,CAAC,CAACH,MAAM,IAAIgC,IAAI,GAAGA,IAAI,CAAC7B,IAAI,GAAG,EAAE;IAC9D,MAAM4B,OAAO,GAAG,CAACmB,QAAQ,CAAClD,MAAM,GAAG,CAAC,IAAIW,QAAQ,GAAG,CAACA,QAAQ,EAAE,GAAGuC,QAAQ,CAAC,GAAGA,QAAQ,EAAEvB,IAAI,CAAC,GAAG,CAAC;IAChG,IAAInB,YAAY,IAAIC,YAAY,EAAE;MAChC7E,UAAU,CAAC,WAAW,EAAE,+DAA+D,CAAC;IAC1F;IACA,IAAI8G,aAAa,EAAE;MACjB,IAAI,CAAClC,YAAY,IAAIC,YAAY,KAAK,CAACN,IAAI,EAAE;QAC3C4C,SAAS,GAAGpG,QAAQ,CAACsB,OAAO,CAAC;MAC/B,CAAC,MAAM;QACL,IAAI,EAAEuC,YAAY,IAAIC,YAAY,CAAC,EAAE;UACnC7E,UAAU,CAAC,WAAW,EAAE,6EAA6E,CAAC;QACxG;QACA,IAAIuE,IAAI,EAAE;UACRvE,UAAU,CAAC,WAAW,EAAE,2EAA2E,CAAC;QACtG;MACF;MACA;IACF,CAAC,MAAM,IAAI6E,YAAY,IAAI,CAACN,IAAI,EAAE;MAChCvE,UAAU,CAAC,WAAW,EAAE,iEAAiE,CAAC;IAC5F,CAAC,MAAM,IAAIL,KAAK,CAAC4H,cAAc,CAACxG,QAAQ,CAAC,EAAE;MACzC,IAAIA,QAAQ,CAACM,KAAK,CAACmG,YAAY,EAAE;QAC/BxH,UAAU,CAAC,WAAW,EAAE,mGAAmG,CAAC;MAC9H;MACA,MAAMyH,UAAU,GAAG7B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE9E,QAAQ,CAACM,KAAK,CAAC,EAAE6F,OAAO,CAAC;MAC5E,IAAIzG,qBAAqB,CAACM,QAAQ,CAAC,EAAE;QACnC0G,UAAU,CAACC,GAAG,GAAGC,QAAQ,IAAI;UAC3B,MAAMC,SAAS,GAAG7G,QAAQ,CAAC2G,GAAG;UAC9B,IAAIE,SAAS,EAAE;YACb,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAE;cACnCA,SAAS,CAACD,QAAQ,CAAC;YACrB;YACA,IAAI,SAAS,IAAIC,SAAS,EAAE;cAC1BA,SAAS,CAACxC,OAAO,GAAGuC,QAAQ;YAC9B;UACF;UACAzC,SAAS,CAACE,OAAO,GAAGuC,QAAQ;QAC9B,CAAC;MACH;MACA,IAAI,CAACF,UAAU,CAACI,EAAE,EAAE;QAClBJ,UAAU,CAACI,EAAE,GAAG1B,OAAO;MACzB;MACA;MACA,MAAM2B,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC,GAAGrH,OAAO,CAACyC,OAAO,CAAC,EAAE,GAAGzC,OAAO,CAACuE,qBAAqB,CAAC,CAAC,CAAC;MAClF6C,QAAQ,CAACE,OAAO,CAACC,SAAS,IAAI;QAC5BR,UAAU,CAACQ,SAAS,CAAC,GAAG,CAAC,GAAGC,IAAI,KAAK;UACnC,IAAI5G,EAAE,EAAEgF,EAAE,EAAE6B,EAAE;UACd,CAAC7G,EAAE,GAAG4F,OAAO,CAACe,SAAS,CAAC,MAAM,IAAI,IAAI3G,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8G,IAAI,CAAClB,OAAO,EAAE,GAAGgB,IAAI,CAAC;UACxF,CAACC,EAAE,GAAG,CAAC7B,EAAE,GAAGvF,QAAQ,CAACM,KAAK,EAAE4G,SAAS,CAAC,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAAC9B,EAAE,EAAE,GAAG4B,IAAI,CAAC;QACnG,CAAC;MACH,CAAC,CAAC;MACFf,SAAS,GAAGxH,KAAK,CAACiD,aAAa,CAAC/B,SAAS,EAAE;QACzCK,KAAK,EAAEgG,OAAO,CAAC7F,KAAK,CAACgH,aAAa,IAAI,OAAO,CAAC;QAC9ClH,MAAM,EAAEgE,SAAS,CAACC;MACpB,CAAC,EAAEzF,KAAK,CAAC2I,YAAY,CAACvH,QAAQ,EAAE0G,UAAU,CAAC,CAAC;IAC9C,CAAC,MAAM;MACL,IAAIlD,IAAI,EAAE;QACRvE,UAAU,CAAC,WAAW,EAAE,6HAA6H,CAAC;MACxJ;MACAmH,SAAS,GAAGpG,QAAQ;IACtB;IACA,OAAOkF,YAAY,CAACkB,SAAS,EAAEhB,OAAO,EAAEC,IAAI,EAAEC,UAAU,CAAC;EAC3D,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}