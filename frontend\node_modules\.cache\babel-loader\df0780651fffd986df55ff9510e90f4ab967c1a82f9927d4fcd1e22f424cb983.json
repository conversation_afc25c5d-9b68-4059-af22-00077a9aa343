{"ast": null, "code": "import * as React from \"react\";\nfunction FileWrongOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FileWrongOutline-FileWrongOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FileWrongOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"FileWrongOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M38,4 C41.3137085,4 44,6.68555597 44,9.99835714 L44,27.593 C44,27.8139139 43.8209139,27.993 43.6,27.993 L41.4,27.993 C41.1790861,27.993 41,27.8139139 41,27.593 L41,9.99835714 C41,8.40111372 39.75108,7.09549131 38.1762728,7.00426987 L38,6.99917857 L10,6.99917857 C8.40231912,6.99917857 7.09633912,8.24775657 7.00509269,9.82213262 L7,9.99835714 L7,37.9912381 C7,39.5884815 8.24891996,40.8941039 9.82372721,40.9853253 L10,40.9904166 L26.6,40.9900098 C26.7932997,40.9899965 26.9545783,41.1271043 26.9918812,41.3093765 L27,41.39 L27,41.39 L27,43.589 C26.999986,43.8099084 26.8209084,43.9889923 26.6,43.989014 L10,43.9895952 C6.6862915,43.9895952 4,41.3040392 4,37.9912381 L4,9.99835714 C4,6.68555597 6.6862915,4 10,4 L38,4 Z M41.6719687,31.2652801 L43.2275262,32.8204117 C43.3643139,32.9569909 43.381439,33.1679701 43.2789489,33.3232435 L43.2275059,33.3859993 L43.2275059,33.3859993 L39.621,36.991 L43.2275232,40.5964595 C43.3642972,40.7330525 43.3814226,40.9440333 43.278934,41.099308 L43.2275262,41.1620645 L43.2275262,41.1620645 L41.6719687,42.717196 C41.5157121,42.8733261 41.2624784,42.8733102 41.1062414,42.7171605 L37.5,39.111 L33.8937586,42.7171605 C33.7375216,42.8733102 33.4842879,42.8733261 33.3280313,42.717196 L31.7724738,41.1620645 C31.635733,41.0254383 31.6186139,40.8144532 31.7211046,40.6591791 L31.7724404,40.5964231 L31.7724404,40.5964231 L35.378,36.991 L31.7724577,33.3860357 C31.6357096,33.2494169 31.6186005,33.0384327 31.7210998,32.8831647 L31.7724738,32.8204117 L31.7724738,32.8204117 L33.3280313,31.2652801 C33.4843206,31.1092247 33.7375129,31.1092166 33.8938123,31.265262 L37.5,34.87 L41.1061877,31.265262 C41.2624871,31.1092166 41.5156794,31.1092247 41.6719687,31.2652801 Z M19.6,29 C19.8209139,29 20,29.1790861 20,29.4 L20,31.6 C20,31.8209139 19.8209139,32 19.6,32 L14.4,32 C14.1790861,32 14,31.8209139 14,31.6 L14,29.4 C14,29.1790861 14.1790861,29 14.4,29 L19.6,29 Z M33.6,23 C33.8209139,23 34,23.1790861 34,23.4 L34,25.6 C34,25.8209139 33.8209139,26 33.6,26 L14.4,26 C14.1790861,26 14,25.8209139 14,25.6 L14,23.4 C14,23.1790861 14.1790861,23 14.4,23 L33.6,23 Z M33.6,17 C33.8209139,17 34,17.1790861 34,17.4 L34,19.6 C34,19.8209139 33.8209139,20 33.6,20 L14.4,20 C14.1790861,20 14,19.8209139 14,19.6 L14,17.4 C14,17.1790861 14.1790861,17 14.4,17 L33.6,17 Z\",\n    id: \"FileWrongOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default FileWrongOutline;", "map": {"version": 3, "names": ["React", "FileWrongOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/FileWrongOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction FileWrongOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FileWrongOutline-FileWrongOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FileWrongOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"FileWrongOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M38,4 C41.3137085,4 44,6.68555597 44,9.99835714 L44,27.593 C44,27.8139139 43.8209139,27.993 43.6,27.993 L41.4,27.993 C41.1790861,27.993 41,27.8139139 41,27.593 L41,9.99835714 C41,8.40111372 39.75108,7.09549131 38.1762728,7.00426987 L38,6.99917857 L10,6.99917857 C8.40231912,6.99917857 7.09633912,8.24775657 7.00509269,9.82213262 L7,9.99835714 L7,37.9912381 C7,39.5884815 8.24891996,40.8941039 9.82372721,40.9853253 L10,40.9904166 L26.6,40.9900098 C26.7932997,40.9899965 26.9545783,41.1271043 26.9918812,41.3093765 L27,41.39 L27,41.39 L27,43.589 C26.999986,43.8099084 26.8209084,43.9889923 26.6,43.989014 L10,43.9895952 C6.6862915,43.9895952 4,41.3040392 4,37.9912381 L4,9.99835714 C4,6.68555597 6.6862915,4 10,4 L38,4 Z M41.6719687,31.2652801 L43.2275262,32.8204117 C43.3643139,32.9569909 43.381439,33.1679701 43.2789489,33.3232435 L43.2275059,33.3859993 L43.2275059,33.3859993 L39.621,36.991 L43.2275232,40.5964595 C43.3642972,40.7330525 43.3814226,40.9440333 43.278934,41.099308 L43.2275262,41.1620645 L43.2275262,41.1620645 L41.6719687,42.717196 C41.5157121,42.8733261 41.2624784,42.8733102 41.1062414,42.7171605 L37.5,39.111 L33.8937586,42.7171605 C33.7375216,42.8733102 33.4842879,42.8733261 33.3280313,42.717196 L31.7724738,41.1620645 C31.635733,41.0254383 31.6186139,40.8144532 31.7211046,40.6591791 L31.7724404,40.5964231 L31.7724404,40.5964231 L35.378,36.991 L31.7724577,33.3860357 C31.6357096,33.2494169 31.6186005,33.0384327 31.7210998,32.8831647 L31.7724738,32.8204117 L31.7724738,32.8204117 L33.3280313,31.2652801 C33.4843206,31.1092247 33.7375129,31.1092166 33.8938123,31.265262 L37.5,34.87 L41.1061877,31.265262 C41.2624871,31.1092166 41.5156794,31.1092247 41.6719687,31.2652801 Z M19.6,29 C19.8209139,29 20,29.1790861 20,29.4 L20,31.6 C20,31.8209139 19.8209139,32 19.6,32 L14.4,32 C14.1790861,32 14,31.8209139 14,31.6 L14,29.4 C14,29.1790861 14.1790861,29 14.4,29 L19.6,29 Z M33.6,23 C33.8209139,23 34,23.1790861 34,23.4 L34,25.6 C34,25.8209139 33.8209139,26 33.6,26 L14.4,26 C14.1790861,26 14,25.8209139 14,25.6 L14,23.4 C14,23.1790861 14.1790861,23 14.4,23 L33.6,23 Z M33.6,17 C33.8209139,17 34,17.1790861 34,17.4 L34,19.6 C34,19.8209139 33.8209139,20 33.6,20 L14.4,20 C14.1790861,20 14,19.8209139 14,19.6 L14,17.4 C14,17.1790861 14.1790861,17 14.4,17 L33.6,17 Z\",\n    id: \"FileWrongOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default FileWrongOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,mCAAmC;IACvCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,+BAA+B;IACnCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,0uEAA0uE;IAC7uER,EAAE,EAAE,2CAA2C;IAC/CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}