{"ast": null, "code": "import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport classNames from 'classnames';\nimport { SwiperItem } from './swiper-item';\nimport { devWarning } from '../../utils/dev-log';\nimport { useSpring, animated } from '@react-spring/web';\nimport { useDrag } from '@use-gesture/react';\nimport PageIndicator from '../page-indicator';\nimport { staged } from 'staged-components';\nimport { useRefState } from '../../utils/use-ref-state';\nimport { bound } from '../../utils/bound';\nimport { useIsomorphicLayoutEffect, useGetState } from 'ahooks';\nimport { mergeFuncProps } from '../../utils/with-func-props';\nconst classPrefix = `adm-swiper`;\nconst eventToPropRecord = {\n  'mousedown': 'onMouseDown',\n  'mousemove': 'onMouseMove',\n  'mouseup': 'onMouseUp'\n};\nconst defaultProps = {\n  defaultIndex: 0,\n  allowTouchMove: true,\n  autoplay: false,\n  autoplayInterval: 3000,\n  loop: false,\n  direction: 'horizontal',\n  slideSize: 100,\n  trackOffset: 0,\n  stuckAtBoundary: true,\n  rubberband: true,\n  stopPropagation: []\n};\nlet currentUid;\nexport const Swiper = forwardRef(staged((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    direction,\n    total,\n    children,\n    indicator\n  } = props;\n  const [uid] = useState({});\n  const timeoutRef = useRef(null);\n  const isVertical = direction === 'vertical';\n  const slideRatio = props.slideSize / 100;\n  const offsetRatio = props.trackOffset / 100;\n  const {\n    validChildren,\n    count,\n    renderChildren\n  } = useMemo(() => {\n    let count = 0;\n    let renderChildren = undefined;\n    let validChildren = undefined;\n    if (typeof children === 'function') {\n      renderChildren = children;\n    } else {\n      validChildren = React.Children.map(children, child => {\n        if (!React.isValidElement(child)) return null;\n        if (child.type !== SwiperItem) {\n          devWarning('Swiper', 'The children of `Swiper` must be `Swiper.Item` components.');\n          return null;\n        }\n        count++;\n        return child;\n      });\n    }\n    return {\n      renderChildren,\n      validChildren,\n      count\n    };\n  }, [children]);\n  const mergedTotal = total !== null && total !== void 0 ? total : count;\n  if (mergedTotal === 0 || !validChildren && !renderChildren) {\n    devWarning('Swiper', '`Swiper` needs at least one child.');\n    return null;\n  }\n  return () => {\n    let loop = props.loop;\n    if (slideRatio * (mergedTotal - 1) < 1) {\n      loop = false;\n    }\n    const trackRef = useRef(null);\n    function getSlidePixels() {\n      const track = trackRef.current;\n      if (!track) return 0;\n      const trackPixels = isVertical ? track.offsetHeight : track.offsetWidth;\n      return trackPixels * props.slideSize / 100;\n    }\n    const [current, setCurrent, getCurrent] = useGetState(props.defaultIndex);\n    const [dragging, setDragging, draggingRef] = useRefState(false);\n    function boundIndex(current) {\n      let min = 0;\n      let max = mergedTotal - 1;\n      if (props.stuckAtBoundary) {\n        min += offsetRatio / slideRatio;\n        max -= (1 - slideRatio - offsetRatio) / slideRatio;\n      }\n      return bound(current, min, max);\n    }\n    const [{\n      position\n    }, api] = useSpring(() => ({\n      position: boundIndex(current) * 100,\n      config: {\n        tension: 200,\n        friction: 30\n      },\n      onRest: () => {\n        if (draggingRef.current) return;\n        if (!loop) return;\n        const rawX = position.get();\n        const totalWidth = 100 * mergedTotal;\n        const standardPosition = modulus(rawX, totalWidth);\n        if (standardPosition === rawX) return;\n        api.start({\n          position: standardPosition,\n          immediate: true\n        });\n      }\n    }), [mergedTotal]);\n    const dragCancelRef = useRef(null);\n    function forceCancelDrag() {\n      var _a;\n      (_a = dragCancelRef.current) === null || _a === void 0 ? void 0 : _a.call(dragCancelRef);\n      draggingRef.current = false;\n    }\n    const bind = useDrag(state => {\n      dragCancelRef.current = state.cancel;\n      if (!state.intentional) return;\n      if (state.first && !currentUid) {\n        currentUid = uid;\n      }\n      if (currentUid !== uid) return;\n      currentUid = state.last ? undefined : uid;\n      const slidePixels = getSlidePixels();\n      if (!slidePixels) return;\n      const paramIndex = isVertical ? 1 : 0;\n      const offset = state.offset[paramIndex];\n      const direction = state.direction[paramIndex];\n      const velocity = state.velocity[paramIndex];\n      setDragging(true);\n      if (!state.last) {\n        api.start({\n          position: offset * 100 / slidePixels,\n          immediate: true\n        });\n      } else {\n        const minIndex = Math.floor(offset / slidePixels);\n        const maxIndex = minIndex + 1;\n        const index = Math.round((offset + velocity * 2000 * direction) / slidePixels);\n        swipeTo(bound(index, minIndex, maxIndex));\n        window.setTimeout(() => {\n          setDragging(false);\n        });\n      }\n    }, {\n      transform: ([x, y]) => [-x, -y],\n      from: () => {\n        const slidePixels = getSlidePixels();\n        return [position.get() / 100 * slidePixels, position.get() / 100 * slidePixels];\n      },\n      triggerAllEvents: true,\n      bounds: () => {\n        if (loop) return {};\n        const slidePixels = getSlidePixels();\n        const lowerBound = boundIndex(0) * slidePixels;\n        const upperBound = boundIndex(mergedTotal - 1) * slidePixels;\n        return isVertical ? {\n          top: lowerBound,\n          bottom: upperBound\n        } : {\n          left: lowerBound,\n          right: upperBound\n        };\n      },\n      rubberband: props.rubberband,\n      axis: isVertical ? 'y' : 'x',\n      preventScroll: !isVertical,\n      pointer: {\n        touch: true\n      }\n    });\n    function swipeTo(index, immediate = false) {\n      var _a;\n      const roundedIndex = Math.round(index);\n      const targetIndex = loop ? modulus(roundedIndex, mergedTotal) : bound(roundedIndex, 0, mergedTotal - 1);\n      if (targetIndex !== getCurrent()) {\n        (_a = props.onIndexChange) === null || _a === void 0 ? void 0 : _a.call(props, targetIndex);\n      }\n      setCurrent(targetIndex);\n      api.start({\n        position: (loop ? roundedIndex : boundIndex(roundedIndex)) * 100,\n        immediate\n      });\n    }\n    function swipeNext() {\n      swipeTo(Math.round(position.get() / 100) + 1);\n    }\n    function swipePrev() {\n      swipeTo(Math.round(position.get() / 100) - 1);\n    }\n    useImperativeHandle(ref, () => ({\n      swipeTo,\n      swipeNext,\n      swipePrev\n    }));\n    useIsomorphicLayoutEffect(() => {\n      const maxIndex = mergedTotal - 1;\n      if (current > maxIndex) {\n        swipeTo(maxIndex, true);\n      }\n    });\n    const {\n      autoplay,\n      autoplayInterval\n    } = props;\n    const runTimeSwiper = () => {\n      timeoutRef.current = window.setTimeout(() => {\n        if (autoplay === 'reverse') {\n          swipePrev();\n        } else {\n          swipeNext();\n        }\n        runTimeSwiper();\n      }, autoplayInterval);\n    };\n    useEffect(() => {\n      if (!autoplay || dragging) return;\n      runTimeSwiper();\n      return () => {\n        if (timeoutRef.current) window.clearTimeout(timeoutRef.current);\n      };\n    }, [autoplay, autoplayInterval, dragging, mergedTotal]);\n    // ============================== Render ==============================\n    // Render Item\n    function renderItem(index, child) {\n      let itemStyle = {};\n      if (loop) {\n        itemStyle = {\n          [isVertical ? 'y' : 'x']: position.to(position => {\n            let finalPosition = -position + index * 100;\n            const totalWidth = mergedTotal * 100;\n            const flagWidth = totalWidth / 2;\n            finalPosition = modulus(finalPosition + flagWidth, totalWidth) - flagWidth;\n            return `${finalPosition}%`;\n          }),\n          [isVertical ? 'top' : 'left']: `-${index * 100}%`\n        };\n      }\n      return React.createElement(animated.div, {\n        className: classNames(`${classPrefix}-slide`, {\n          [`${classPrefix}-slide-active`]: current === index\n        }),\n        style: itemStyle,\n        key: index\n      }, child);\n    }\n    function renderItems() {\n      if (renderChildren && total) {\n        const offsetCount = 2;\n        const startIndex = Math.max(current - offsetCount, 0);\n        const endIndex = Math.min(current + offsetCount, total - 1);\n        const items = [];\n        for (let index = startIndex; index <= endIndex; index += 1) {\n          items.push(renderItem(index, renderChildren(index)));\n        }\n        return React.createElement(React.Fragment, null, React.createElement(\"div\", {\n          className: `${classPrefix}-slide-placeholder`,\n          style: {\n            width: `${startIndex * 100}%`\n          }\n        }), items);\n      }\n      return React.Children.map(validChildren, (child, index) => {\n        return renderItem(index, child);\n      });\n    }\n    // Render Track Inner\n    function renderTrackInner() {\n      if (loop) {\n        return React.createElement(\"div\", {\n          className: `${classPrefix}-track-inner`\n        }, renderItems());\n      } else {\n        return React.createElement(animated.div, {\n          className: `${classPrefix}-track-inner`,\n          style: {\n            [isVertical ? 'y' : 'x']: position.to(position => `${-position}%`)\n          }\n        }, renderItems());\n      }\n    }\n    // Render\n    const style = {\n      '--slide-size': `${props.slideSize}%`,\n      '--track-offset': `${props.trackOffset}%`\n    };\n    const dragProps = Object.assign({}, props.allowTouchMove ? bind() : {});\n    const stopPropagationProps = {};\n    for (const key of props.stopPropagation) {\n      const prop = eventToPropRecord[key];\n      stopPropagationProps[prop] = function (e) {\n        e.stopPropagation();\n      };\n    }\n    const mergedProps = mergeFuncProps(dragProps, stopPropagationProps);\n    let indicatorNode = null;\n    if (typeof indicator === 'function') {\n      indicatorNode = indicator(mergedTotal, current);\n    } else if (indicator !== false) {\n      indicatorNode = React.createElement(\"div\", {\n        className: `${classPrefix}-indicator`\n      }, React.createElement(PageIndicator, Object.assign({}, props.indicatorProps, {\n        total: mergedTotal,\n        current: current,\n        direction: direction\n      })));\n    }\n    return withNativeProps(props, React.createElement(\"div\", {\n      className: classNames(classPrefix, `${classPrefix}-${direction}`),\n      style: style\n    }, React.createElement(\"div\", Object.assign({\n      ref: trackRef,\n      className: classNames(`${classPrefix}-track`, {\n        [`${classPrefix}-track-allow-touch-move`]: props.allowTouchMove\n      }),\n      onClickCapture: e => {\n        if (draggingRef.current) {\n          e.stopPropagation();\n        }\n        forceCancelDrag();\n      }\n    }, mergedProps), renderTrackInner()), indicatorNode));\n  };\n}));\nfunction modulus(value, division) {\n  const remainder = value % division;\n  return remainder < 0 ? remainder + division : remainder;\n}", "map": {"version": 3, "names": ["React", "forwardRef", "useEffect", "useImperativeHandle", "useMemo", "useRef", "useState", "withNativeProps", "mergeProps", "classNames", "SwiperItem", "dev<PERSON><PERSON><PERSON>", "useSpring", "animated", "useDrag", "PageIndicator", "staged", "useRefState", "bound", "useIsomorphicLayoutEffect", "useGetState", "mergeFuncProps", "classPrefix", "eventToPropRecord", "defaultProps", "defaultIndex", "allowTouchMove", "autoplay", "autoplayInterval", "loop", "direction", "slideSize", "trackOffset", "stuckAtBoundary", "rubberband", "stopPropagation", "currentUid", "Swiper", "p", "ref", "props", "total", "children", "indicator", "uid", "timeoutRef", "isVertical", "slideRatio", "offsetRatio", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "count", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "undefined", "Children", "map", "child", "isValidElement", "type", "mergedTotal", "trackRef", "getSlidePixels", "track", "current", "trackPixels", "offsetHeight", "offsetWidth", "setCurrent", "get<PERSON>urrent", "dragging", "setDragging", "draggingRef", "boundIndex", "min", "max", "position", "api", "config", "tension", "friction", "onRest", "rawX", "get", "totalWidth", "standardPosition", "modulus", "start", "immediate", "dragCancelRef", "forceCancelDrag", "_a", "call", "bind", "state", "cancel", "intentional", "first", "last", "slidePixels", "paramIndex", "offset", "velocity", "minIndex", "Math", "floor", "maxIndex", "index", "round", "swipeTo", "window", "setTimeout", "transform", "x", "y", "from", "triggerAllEvents", "bounds", "lowerBound", "upperBound", "top", "bottom", "left", "right", "axis", "preventScroll", "pointer", "touch", "roundedIndex", "targetIndex", "onIndexChange", "swipeNext", "swipePrev", "runTimeSwiper", "clearTimeout", "renderItem", "itemStyle", "to", "finalPosition", "flagWidth", "createElement", "div", "className", "style", "key", "renderItems", "offsetCount", "startIndex", "endIndex", "items", "push", "Fragment", "width", "renderTrackInner", "dragProps", "Object", "assign", "stopPropagationProps", "prop", "e", "mergedProps", "indicatorNode", "indicatorProps", "onClickCapture", "value", "division", "remainder"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/swiper/swiper.js"], "sourcesContent": ["import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport classNames from 'classnames';\nimport { SwiperItem } from './swiper-item';\nimport { devWarning } from '../../utils/dev-log';\nimport { useSpring, animated } from '@react-spring/web';\nimport { useDrag } from '@use-gesture/react';\nimport PageIndicator from '../page-indicator';\nimport { staged } from 'staged-components';\nimport { useRefState } from '../../utils/use-ref-state';\nimport { bound } from '../../utils/bound';\nimport { useIsomorphicLayoutEffect, useGetState } from 'ahooks';\nimport { mergeFuncProps } from '../../utils/with-func-props';\nconst classPrefix = `adm-swiper`;\nconst eventToPropRecord = {\n  'mousedown': 'onMouseDown',\n  'mousemove': 'onMouseMove',\n  'mouseup': 'onMouseUp'\n};\nconst defaultProps = {\n  defaultIndex: 0,\n  allowTouchMove: true,\n  autoplay: false,\n  autoplayInterval: 3000,\n  loop: false,\n  direction: 'horizontal',\n  slideSize: 100,\n  trackOffset: 0,\n  stuckAtBoundary: true,\n  rubberband: true,\n  stopPropagation: []\n};\nlet currentUid;\nexport const Swiper = forwardRef(staged((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    direction,\n    total,\n    children,\n    indicator\n  } = props;\n  const [uid] = useState({});\n  const timeoutRef = useRef(null);\n  const isVertical = direction === 'vertical';\n  const slideRatio = props.slideSize / 100;\n  const offsetRatio = props.trackOffset / 100;\n  const {\n    validChildren,\n    count,\n    renderChildren\n  } = useMemo(() => {\n    let count = 0;\n    let renderChildren = undefined;\n    let validChildren = undefined;\n    if (typeof children === 'function') {\n      renderChildren = children;\n    } else {\n      validChildren = React.Children.map(children, child => {\n        if (!React.isValidElement(child)) return null;\n        if (child.type !== SwiperItem) {\n          devWarning('Swiper', 'The children of `Swiper` must be `Swiper.Item` components.');\n          return null;\n        }\n        count++;\n        return child;\n      });\n    }\n    return {\n      renderChildren,\n      validChildren,\n      count\n    };\n  }, [children]);\n  const mergedTotal = total !== null && total !== void 0 ? total : count;\n  if (mergedTotal === 0 || !validChildren && !renderChildren) {\n    devWarning('Swiper', '`Swiper` needs at least one child.');\n    return null;\n  }\n  return () => {\n    let loop = props.loop;\n    if (slideRatio * (mergedTotal - 1) < 1) {\n      loop = false;\n    }\n    const trackRef = useRef(null);\n    function getSlidePixels() {\n      const track = trackRef.current;\n      if (!track) return 0;\n      const trackPixels = isVertical ? track.offsetHeight : track.offsetWidth;\n      return trackPixels * props.slideSize / 100;\n    }\n    const [current, setCurrent, getCurrent] = useGetState(props.defaultIndex);\n    const [dragging, setDragging, draggingRef] = useRefState(false);\n    function boundIndex(current) {\n      let min = 0;\n      let max = mergedTotal - 1;\n      if (props.stuckAtBoundary) {\n        min += offsetRatio / slideRatio;\n        max -= (1 - slideRatio - offsetRatio) / slideRatio;\n      }\n      return bound(current, min, max);\n    }\n    const [{\n      position\n    }, api] = useSpring(() => ({\n      position: boundIndex(current) * 100,\n      config: {\n        tension: 200,\n        friction: 30\n      },\n      onRest: () => {\n        if (draggingRef.current) return;\n        if (!loop) return;\n        const rawX = position.get();\n        const totalWidth = 100 * mergedTotal;\n        const standardPosition = modulus(rawX, totalWidth);\n        if (standardPosition === rawX) return;\n        api.start({\n          position: standardPosition,\n          immediate: true\n        });\n      }\n    }), [mergedTotal]);\n    const dragCancelRef = useRef(null);\n    function forceCancelDrag() {\n      var _a;\n      (_a = dragCancelRef.current) === null || _a === void 0 ? void 0 : _a.call(dragCancelRef);\n      draggingRef.current = false;\n    }\n    const bind = useDrag(state => {\n      dragCancelRef.current = state.cancel;\n      if (!state.intentional) return;\n      if (state.first && !currentUid) {\n        currentUid = uid;\n      }\n      if (currentUid !== uid) return;\n      currentUid = state.last ? undefined : uid;\n      const slidePixels = getSlidePixels();\n      if (!slidePixels) return;\n      const paramIndex = isVertical ? 1 : 0;\n      const offset = state.offset[paramIndex];\n      const direction = state.direction[paramIndex];\n      const velocity = state.velocity[paramIndex];\n      setDragging(true);\n      if (!state.last) {\n        api.start({\n          position: offset * 100 / slidePixels,\n          immediate: true\n        });\n      } else {\n        const minIndex = Math.floor(offset / slidePixels);\n        const maxIndex = minIndex + 1;\n        const index = Math.round((offset + velocity * 2000 * direction) / slidePixels);\n        swipeTo(bound(index, minIndex, maxIndex));\n        window.setTimeout(() => {\n          setDragging(false);\n        });\n      }\n    }, {\n      transform: ([x, y]) => [-x, -y],\n      from: () => {\n        const slidePixels = getSlidePixels();\n        return [position.get() / 100 * slidePixels, position.get() / 100 * slidePixels];\n      },\n      triggerAllEvents: true,\n      bounds: () => {\n        if (loop) return {};\n        const slidePixels = getSlidePixels();\n        const lowerBound = boundIndex(0) * slidePixels;\n        const upperBound = boundIndex(mergedTotal - 1) * slidePixels;\n        return isVertical ? {\n          top: lowerBound,\n          bottom: upperBound\n        } : {\n          left: lowerBound,\n          right: upperBound\n        };\n      },\n      rubberband: props.rubberband,\n      axis: isVertical ? 'y' : 'x',\n      preventScroll: !isVertical,\n      pointer: {\n        touch: true\n      }\n    });\n    function swipeTo(index, immediate = false) {\n      var _a;\n      const roundedIndex = Math.round(index);\n      const targetIndex = loop ? modulus(roundedIndex, mergedTotal) : bound(roundedIndex, 0, mergedTotal - 1);\n      if (targetIndex !== getCurrent()) {\n        (_a = props.onIndexChange) === null || _a === void 0 ? void 0 : _a.call(props, targetIndex);\n      }\n      setCurrent(targetIndex);\n      api.start({\n        position: (loop ? roundedIndex : boundIndex(roundedIndex)) * 100,\n        immediate\n      });\n    }\n    function swipeNext() {\n      swipeTo(Math.round(position.get() / 100) + 1);\n    }\n    function swipePrev() {\n      swipeTo(Math.round(position.get() / 100) - 1);\n    }\n    useImperativeHandle(ref, () => ({\n      swipeTo,\n      swipeNext,\n      swipePrev\n    }));\n    useIsomorphicLayoutEffect(() => {\n      const maxIndex = mergedTotal - 1;\n      if (current > maxIndex) {\n        swipeTo(maxIndex, true);\n      }\n    });\n    const {\n      autoplay,\n      autoplayInterval\n    } = props;\n    const runTimeSwiper = () => {\n      timeoutRef.current = window.setTimeout(() => {\n        if (autoplay === 'reverse') {\n          swipePrev();\n        } else {\n          swipeNext();\n        }\n        runTimeSwiper();\n      }, autoplayInterval);\n    };\n    useEffect(() => {\n      if (!autoplay || dragging) return;\n      runTimeSwiper();\n      return () => {\n        if (timeoutRef.current) window.clearTimeout(timeoutRef.current);\n      };\n    }, [autoplay, autoplayInterval, dragging, mergedTotal]);\n    // ============================== Render ==============================\n    // Render Item\n    function renderItem(index, child) {\n      let itemStyle = {};\n      if (loop) {\n        itemStyle = {\n          [isVertical ? 'y' : 'x']: position.to(position => {\n            let finalPosition = -position + index * 100;\n            const totalWidth = mergedTotal * 100;\n            const flagWidth = totalWidth / 2;\n            finalPosition = modulus(finalPosition + flagWidth, totalWidth) - flagWidth;\n            return `${finalPosition}%`;\n          }),\n          [isVertical ? 'top' : 'left']: `-${index * 100}%`\n        };\n      }\n      return React.createElement(animated.div, {\n        className: classNames(`${classPrefix}-slide`, {\n          [`${classPrefix}-slide-active`]: current === index\n        }),\n        style: itemStyle,\n        key: index\n      }, child);\n    }\n    function renderItems() {\n      if (renderChildren && total) {\n        const offsetCount = 2;\n        const startIndex = Math.max(current - offsetCount, 0);\n        const endIndex = Math.min(current + offsetCount, total - 1);\n        const items = [];\n        for (let index = startIndex; index <= endIndex; index += 1) {\n          items.push(renderItem(index, renderChildren(index)));\n        }\n        return React.createElement(React.Fragment, null, React.createElement(\"div\", {\n          className: `${classPrefix}-slide-placeholder`,\n          style: {\n            width: `${startIndex * 100}%`\n          }\n        }), items);\n      }\n      return React.Children.map(validChildren, (child, index) => {\n        return renderItem(index, child);\n      });\n    }\n    // Render Track Inner\n    function renderTrackInner() {\n      if (loop) {\n        return React.createElement(\"div\", {\n          className: `${classPrefix}-track-inner`\n        }, renderItems());\n      } else {\n        return React.createElement(animated.div, {\n          className: `${classPrefix}-track-inner`,\n          style: {\n            [isVertical ? 'y' : 'x']: position.to(position => `${-position}%`)\n          }\n        }, renderItems());\n      }\n    }\n    // Render\n    const style = {\n      '--slide-size': `${props.slideSize}%`,\n      '--track-offset': `${props.trackOffset}%`\n    };\n    const dragProps = Object.assign({}, props.allowTouchMove ? bind() : {});\n    const stopPropagationProps = {};\n    for (const key of props.stopPropagation) {\n      const prop = eventToPropRecord[key];\n      stopPropagationProps[prop] = function (e) {\n        e.stopPropagation();\n      };\n    }\n    const mergedProps = mergeFuncProps(dragProps, stopPropagationProps);\n    let indicatorNode = null;\n    if (typeof indicator === 'function') {\n      indicatorNode = indicator(mergedTotal, current);\n    } else if (indicator !== false) {\n      indicatorNode = React.createElement(\"div\", {\n        className: `${classPrefix}-indicator`\n      }, React.createElement(PageIndicator, Object.assign({}, props.indicatorProps, {\n        total: mergedTotal,\n        current: current,\n        direction: direction\n      })));\n    }\n    return withNativeProps(props, React.createElement(\"div\", {\n      className: classNames(classPrefix, `${classPrefix}-${direction}`),\n      style: style\n    }, React.createElement(\"div\", Object.assign({\n      ref: trackRef,\n      className: classNames(`${classPrefix}-track`, {\n        [`${classPrefix}-track-allow-touch-move`]: props.allowTouchMove\n      }),\n      onClickCapture: e => {\n        if (draggingRef.current) {\n          e.stopPropagation();\n        }\n        forceCancelDrag();\n      }\n    }, mergedProps), renderTrackInner()), indicatorNode));\n  };\n}));\nfunction modulus(value, division) {\n  const remainder = value % division;\n  return remainder < 0 ? remainder + division : remainder;\n}"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACpG,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,SAAS,EAAEC,QAAQ,QAAQ,mBAAmB;AACvD,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,aAAa,MAAM,mBAAmB;AAC7C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,KAAK,QAAQ,mBAAmB;AACzC,SAASC,yBAAyB,EAAEC,WAAW,QAAQ,QAAQ;AAC/D,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,MAAMC,WAAW,GAAG,YAAY;AAChC,MAAMC,iBAAiB,GAAG;EACxB,WAAW,EAAE,aAAa;EAC1B,WAAW,EAAE,aAAa;EAC1B,SAAS,EAAE;AACb,CAAC;AACD,MAAMC,YAAY,GAAG;EACnBC,YAAY,EAAE,CAAC;EACfC,cAAc,EAAE,IAAI;EACpBC,QAAQ,EAAE,KAAK;EACfC,gBAAgB,EAAE,IAAI;EACtBC,IAAI,EAAE,KAAK;EACXC,SAAS,EAAE,YAAY;EACvBC,SAAS,EAAE,GAAG;EACdC,WAAW,EAAE,CAAC;EACdC,eAAe,EAAE,IAAI;EACrBC,UAAU,EAAE,IAAI;EAChBC,eAAe,EAAE;AACnB,CAAC;AACD,IAAIC,UAAU;AACd,OAAO,MAAMC,MAAM,GAAGpC,UAAU,CAACe,MAAM,CAAC,CAACsB,CAAC,EAAEC,GAAG,KAAK;EAClD,MAAMC,KAAK,GAAGhC,UAAU,CAACgB,YAAY,EAAEc,CAAC,CAAC;EACzC,MAAM;IACJR,SAAS;IACTW,KAAK;IACLC,QAAQ;IACRC;EACF,CAAC,GAAGH,KAAK;EACT,MAAM,CAACI,GAAG,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1B,MAAMuC,UAAU,GAAGxC,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMyC,UAAU,GAAGhB,SAAS,KAAK,UAAU;EAC3C,MAAMiB,UAAU,GAAGP,KAAK,CAACT,SAAS,GAAG,GAAG;EACxC,MAAMiB,WAAW,GAAGR,KAAK,CAACR,WAAW,GAAG,GAAG;EAC3C,MAAM;IACJiB,aAAa;IACbC,KAAK;IACLC;EACF,CAAC,GAAG/C,OAAO,CAAC,MAAM;IAChB,IAAI8C,KAAK,GAAG,CAAC;IACb,IAAIC,cAAc,GAAGC,SAAS;IAC9B,IAAIH,aAAa,GAAGG,SAAS;IAC7B,IAAI,OAAOV,QAAQ,KAAK,UAAU,EAAE;MAClCS,cAAc,GAAGT,QAAQ;IAC3B,CAAC,MAAM;MACLO,aAAa,GAAGjD,KAAK,CAACqD,QAAQ,CAACC,GAAG,CAACZ,QAAQ,EAAEa,KAAK,IAAI;QACpD,IAAI,CAACvD,KAAK,CAACwD,cAAc,CAACD,KAAK,CAAC,EAAE,OAAO,IAAI;QAC7C,IAAIA,KAAK,CAACE,IAAI,KAAK/C,UAAU,EAAE;UAC7BC,UAAU,CAAC,QAAQ,EAAE,4DAA4D,CAAC;UAClF,OAAO,IAAI;QACb;QACAuC,KAAK,EAAE;QACP,OAAOK,KAAK;MACd,CAAC,CAAC;IACJ;IACA,OAAO;MACLJ,cAAc;MACdF,aAAa;MACbC;IACF,CAAC;EACH,CAAC,EAAE,CAACR,QAAQ,CAAC,CAAC;EACd,MAAMgB,WAAW,GAAGjB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGS,KAAK;EACtE,IAAIQ,WAAW,KAAK,CAAC,IAAI,CAACT,aAAa,IAAI,CAACE,cAAc,EAAE;IAC1DxC,UAAU,CAAC,QAAQ,EAAE,oCAAoC,CAAC;IAC1D,OAAO,IAAI;EACb;EACA,OAAO,MAAM;IACX,IAAIkB,IAAI,GAAGW,KAAK,CAACX,IAAI;IACrB,IAAIkB,UAAU,IAAIW,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE;MACtC7B,IAAI,GAAG,KAAK;IACd;IACA,MAAM8B,QAAQ,GAAGtD,MAAM,CAAC,IAAI,CAAC;IAC7B,SAASuD,cAAcA,CAAA,EAAG;MACxB,MAAMC,KAAK,GAAGF,QAAQ,CAACG,OAAO;MAC9B,IAAI,CAACD,KAAK,EAAE,OAAO,CAAC;MACpB,MAAME,WAAW,GAAGjB,UAAU,GAAGe,KAAK,CAACG,YAAY,GAAGH,KAAK,CAACI,WAAW;MACvE,OAAOF,WAAW,GAAGvB,KAAK,CAACT,SAAS,GAAG,GAAG;IAC5C;IACA,MAAM,CAAC+B,OAAO,EAAEI,UAAU,EAAEC,UAAU,CAAC,GAAG/C,WAAW,CAACoB,KAAK,CAACf,YAAY,CAAC;IACzE,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,EAAEC,WAAW,CAAC,GAAGrD,WAAW,CAAC,KAAK,CAAC;IAC/D,SAASsD,UAAUA,CAACT,OAAO,EAAE;MAC3B,IAAIU,GAAG,GAAG,CAAC;MACX,IAAIC,GAAG,GAAGf,WAAW,GAAG,CAAC;MACzB,IAAIlB,KAAK,CAACP,eAAe,EAAE;QACzBuC,GAAG,IAAIxB,WAAW,GAAGD,UAAU;QAC/B0B,GAAG,IAAI,CAAC,CAAC,GAAG1B,UAAU,GAAGC,WAAW,IAAID,UAAU;MACpD;MACA,OAAO7B,KAAK,CAAC4C,OAAO,EAAEU,GAAG,EAAEC,GAAG,CAAC;IACjC;IACA,MAAM,CAAC;MACLC;IACF,CAAC,EAAEC,GAAG,CAAC,GAAG/D,SAAS,CAAC,OAAO;MACzB8D,QAAQ,EAAEH,UAAU,CAACT,OAAO,CAAC,GAAG,GAAG;MACnCc,MAAM,EAAE;QACNC,OAAO,EAAE,GAAG;QACZC,QAAQ,EAAE;MACZ,CAAC;MACDC,MAAM,EAAEA,CAAA,KAAM;QACZ,IAAIT,WAAW,CAACR,OAAO,EAAE;QACzB,IAAI,CAACjC,IAAI,EAAE;QACX,MAAMmD,IAAI,GAAGN,QAAQ,CAACO,GAAG,CAAC,CAAC;QAC3B,MAAMC,UAAU,GAAG,GAAG,GAAGxB,WAAW;QACpC,MAAMyB,gBAAgB,GAAGC,OAAO,CAACJ,IAAI,EAAEE,UAAU,CAAC;QAClD,IAAIC,gBAAgB,KAAKH,IAAI,EAAE;QAC/BL,GAAG,CAACU,KAAK,CAAC;UACRX,QAAQ,EAAES,gBAAgB;UAC1BG,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,EAAE,CAAC5B,WAAW,CAAC,CAAC;IAClB,MAAM6B,aAAa,GAAGlF,MAAM,CAAC,IAAI,CAAC;IAClC,SAASmF,eAAeA,CAAA,EAAG;MACzB,IAAIC,EAAE;MACN,CAACA,EAAE,GAAGF,aAAa,CAACzB,OAAO,MAAM,IAAI,IAAI2B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACH,aAAa,CAAC;MACxFjB,WAAW,CAACR,OAAO,GAAG,KAAK;IAC7B;IACA,MAAM6B,IAAI,GAAG7E,OAAO,CAAC8E,KAAK,IAAI;MAC5BL,aAAa,CAACzB,OAAO,GAAG8B,KAAK,CAACC,MAAM;MACpC,IAAI,CAACD,KAAK,CAACE,WAAW,EAAE;MACxB,IAAIF,KAAK,CAACG,KAAK,IAAI,CAAC3D,UAAU,EAAE;QAC9BA,UAAU,GAAGQ,GAAG;MAClB;MACA,IAAIR,UAAU,KAAKQ,GAAG,EAAE;MACxBR,UAAU,GAAGwD,KAAK,CAACI,IAAI,GAAG5C,SAAS,GAAGR,GAAG;MACzC,MAAMqD,WAAW,GAAGrC,cAAc,CAAC,CAAC;MACpC,IAAI,CAACqC,WAAW,EAAE;MAClB,MAAMC,UAAU,GAAGpD,UAAU,GAAG,CAAC,GAAG,CAAC;MACrC,MAAMqD,MAAM,GAAGP,KAAK,CAACO,MAAM,CAACD,UAAU,CAAC;MACvC,MAAMpE,SAAS,GAAG8D,KAAK,CAAC9D,SAAS,CAACoE,UAAU,CAAC;MAC7C,MAAME,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACF,UAAU,CAAC;MAC3C7B,WAAW,CAAC,IAAI,CAAC;MACjB,IAAI,CAACuB,KAAK,CAACI,IAAI,EAAE;QACfrB,GAAG,CAACU,KAAK,CAAC;UACRX,QAAQ,EAAEyB,MAAM,GAAG,GAAG,GAAGF,WAAW;UACpCX,SAAS,EAAE;QACb,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAMe,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,GAAGF,WAAW,CAAC;QACjD,MAAMO,QAAQ,GAAGH,QAAQ,GAAG,CAAC;QAC7B,MAAMI,KAAK,GAAGH,IAAI,CAACI,KAAK,CAAC,CAACP,MAAM,GAAGC,QAAQ,GAAG,IAAI,GAAGtE,SAAS,IAAImE,WAAW,CAAC;QAC9EU,OAAO,CAACzF,KAAK,CAACuF,KAAK,EAAEJ,QAAQ,EAAEG,QAAQ,CAAC,CAAC;QACzCI,MAAM,CAACC,UAAU,CAAC,MAAM;UACtBxC,WAAW,CAAC,KAAK,CAAC;QACpB,CAAC,CAAC;MACJ;IACF,CAAC,EAAE;MACDyC,SAAS,EAAEA,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,KAAK,CAAC,CAACD,CAAC,EAAE,CAACC,CAAC,CAAC;MAC/BC,IAAI,EAAEA,CAAA,KAAM;QACV,MAAMhB,WAAW,GAAGrC,cAAc,CAAC,CAAC;QACpC,OAAO,CAACc,QAAQ,CAACO,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGgB,WAAW,EAAEvB,QAAQ,CAACO,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGgB,WAAW,CAAC;MACjF,CAAC;MACDiB,gBAAgB,EAAE,IAAI;MACtBC,MAAM,EAAEA,CAAA,KAAM;QACZ,IAAItF,IAAI,EAAE,OAAO,CAAC,CAAC;QACnB,MAAMoE,WAAW,GAAGrC,cAAc,CAAC,CAAC;QACpC,MAAMwD,UAAU,GAAG7C,UAAU,CAAC,CAAC,CAAC,GAAG0B,WAAW;QAC9C,MAAMoB,UAAU,GAAG9C,UAAU,CAACb,WAAW,GAAG,CAAC,CAAC,GAAGuC,WAAW;QAC5D,OAAOnD,UAAU,GAAG;UAClBwE,GAAG,EAAEF,UAAU;UACfG,MAAM,EAAEF;QACV,CAAC,GAAG;UACFG,IAAI,EAAEJ,UAAU;UAChBK,KAAK,EAAEJ;QACT,CAAC;MACH,CAAC;MACDnF,UAAU,EAAEM,KAAK,CAACN,UAAU;MAC5BwF,IAAI,EAAE5E,UAAU,GAAG,GAAG,GAAG,GAAG;MAC5B6E,aAAa,EAAE,CAAC7E,UAAU;MAC1B8E,OAAO,EAAE;QACPC,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IACF,SAASlB,OAAOA,CAACF,KAAK,EAAEnB,SAAS,GAAG,KAAK,EAAE;MACzC,IAAIG,EAAE;MACN,MAAMqC,YAAY,GAAGxB,IAAI,CAACI,KAAK,CAACD,KAAK,CAAC;MACtC,MAAMsB,WAAW,GAAGlG,IAAI,GAAGuD,OAAO,CAAC0C,YAAY,EAAEpE,WAAW,CAAC,GAAGxC,KAAK,CAAC4G,YAAY,EAAE,CAAC,EAAEpE,WAAW,GAAG,CAAC,CAAC;MACvG,IAAIqE,WAAW,KAAK5D,UAAU,CAAC,CAAC,EAAE;QAChC,CAACsB,EAAE,GAAGjD,KAAK,CAACwF,aAAa,MAAM,IAAI,IAAIvC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAAClD,KAAK,EAAEuF,WAAW,CAAC;MAC7F;MACA7D,UAAU,CAAC6D,WAAW,CAAC;MACvBpD,GAAG,CAACU,KAAK,CAAC;QACRX,QAAQ,EAAE,CAAC7C,IAAI,GAAGiG,YAAY,GAAGvD,UAAU,CAACuD,YAAY,CAAC,IAAI,GAAG;QAChExC;MACF,CAAC,CAAC;IACJ;IACA,SAAS2C,SAASA,CAAA,EAAG;MACnBtB,OAAO,CAACL,IAAI,CAACI,KAAK,CAAChC,QAAQ,CAACO,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/C;IACA,SAASiD,SAASA,CAAA,EAAG;MACnBvB,OAAO,CAACL,IAAI,CAACI,KAAK,CAAChC,QAAQ,CAACO,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/C;IACA9E,mBAAmB,CAACoC,GAAG,EAAE,OAAO;MAC9BoE,OAAO;MACPsB,SAAS;MACTC;IACF,CAAC,CAAC,CAAC;IACH/G,yBAAyB,CAAC,MAAM;MAC9B,MAAMqF,QAAQ,GAAG9C,WAAW,GAAG,CAAC;MAChC,IAAII,OAAO,GAAG0C,QAAQ,EAAE;QACtBG,OAAO,CAACH,QAAQ,EAAE,IAAI,CAAC;MACzB;IACF,CAAC,CAAC;IACF,MAAM;MACJ7E,QAAQ;MACRC;IACF,CAAC,GAAGY,KAAK;IACT,MAAM2F,aAAa,GAAGA,CAAA,KAAM;MAC1BtF,UAAU,CAACiB,OAAO,GAAG8C,MAAM,CAACC,UAAU,CAAC,MAAM;QAC3C,IAAIlF,QAAQ,KAAK,SAAS,EAAE;UAC1BuG,SAAS,CAAC,CAAC;QACb,CAAC,MAAM;UACLD,SAAS,CAAC,CAAC;QACb;QACAE,aAAa,CAAC,CAAC;MACjB,CAAC,EAAEvG,gBAAgB,CAAC;IACtB,CAAC;IACD1B,SAAS,CAAC,MAAM;MACd,IAAI,CAACyB,QAAQ,IAAIyC,QAAQ,EAAE;MAC3B+D,aAAa,CAAC,CAAC;MACf,OAAO,MAAM;QACX,IAAItF,UAAU,CAACiB,OAAO,EAAE8C,MAAM,CAACwB,YAAY,CAACvF,UAAU,CAACiB,OAAO,CAAC;MACjE,CAAC;IACH,CAAC,EAAE,CAACnC,QAAQ,EAAEC,gBAAgB,EAAEwC,QAAQ,EAAEV,WAAW,CAAC,CAAC;IACvD;IACA;IACA,SAAS2E,UAAUA,CAAC5B,KAAK,EAAElD,KAAK,EAAE;MAChC,IAAI+E,SAAS,GAAG,CAAC,CAAC;MAClB,IAAIzG,IAAI,EAAE;QACRyG,SAAS,GAAG;UACV,CAACxF,UAAU,GAAG,GAAG,GAAG,GAAG,GAAG4B,QAAQ,CAAC6D,EAAE,CAAC7D,QAAQ,IAAI;YAChD,IAAI8D,aAAa,GAAG,CAAC9D,QAAQ,GAAG+B,KAAK,GAAG,GAAG;YAC3C,MAAMvB,UAAU,GAAGxB,WAAW,GAAG,GAAG;YACpC,MAAM+E,SAAS,GAAGvD,UAAU,GAAG,CAAC;YAChCsD,aAAa,GAAGpD,OAAO,CAACoD,aAAa,GAAGC,SAAS,EAAEvD,UAAU,CAAC,GAAGuD,SAAS;YAC1E,OAAO,GAAGD,aAAa,GAAG;UAC5B,CAAC,CAAC;UACF,CAAC1F,UAAU,GAAG,KAAK,GAAG,MAAM,GAAG,IAAI2D,KAAK,GAAG,GAAG;QAChD,CAAC;MACH;MACA,OAAOzG,KAAK,CAAC0I,aAAa,CAAC7H,QAAQ,CAAC8H,GAAG,EAAE;QACvCC,SAAS,EAAEnI,UAAU,CAAC,GAAGa,WAAW,QAAQ,EAAE;UAC5C,CAAC,GAAGA,WAAW,eAAe,GAAGwC,OAAO,KAAK2C;QAC/C,CAAC,CAAC;QACFoC,KAAK,EAAEP,SAAS;QAChBQ,GAAG,EAAErC;MACP,CAAC,EAAElD,KAAK,CAAC;IACX;IACA,SAASwF,WAAWA,CAAA,EAAG;MACrB,IAAI5F,cAAc,IAAIV,KAAK,EAAE;QAC3B,MAAMuG,WAAW,GAAG,CAAC;QACrB,MAAMC,UAAU,GAAG3C,IAAI,CAAC7B,GAAG,CAACX,OAAO,GAAGkF,WAAW,EAAE,CAAC,CAAC;QACrD,MAAME,QAAQ,GAAG5C,IAAI,CAAC9B,GAAG,CAACV,OAAO,GAAGkF,WAAW,EAAEvG,KAAK,GAAG,CAAC,CAAC;QAC3D,MAAM0G,KAAK,GAAG,EAAE;QAChB,KAAK,IAAI1C,KAAK,GAAGwC,UAAU,EAAExC,KAAK,IAAIyC,QAAQ,EAAEzC,KAAK,IAAI,CAAC,EAAE;UAC1D0C,KAAK,CAACC,IAAI,CAACf,UAAU,CAAC5B,KAAK,EAAEtD,cAAc,CAACsD,KAAK,CAAC,CAAC,CAAC;QACtD;QACA,OAAOzG,KAAK,CAAC0I,aAAa,CAAC1I,KAAK,CAACqJ,QAAQ,EAAE,IAAI,EAAErJ,KAAK,CAAC0I,aAAa,CAAC,KAAK,EAAE;UAC1EE,SAAS,EAAE,GAAGtH,WAAW,oBAAoB;UAC7CuH,KAAK,EAAE;YACLS,KAAK,EAAE,GAAGL,UAAU,GAAG,GAAG;UAC5B;QACF,CAAC,CAAC,EAAEE,KAAK,CAAC;MACZ;MACA,OAAOnJ,KAAK,CAACqD,QAAQ,CAACC,GAAG,CAACL,aAAa,EAAE,CAACM,KAAK,EAAEkD,KAAK,KAAK;QACzD,OAAO4B,UAAU,CAAC5B,KAAK,EAAElD,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ;IACA;IACA,SAASgG,gBAAgBA,CAAA,EAAG;MAC1B,IAAI1H,IAAI,EAAE;QACR,OAAO7B,KAAK,CAAC0I,aAAa,CAAC,KAAK,EAAE;UAChCE,SAAS,EAAE,GAAGtH,WAAW;QAC3B,CAAC,EAAEyH,WAAW,CAAC,CAAC,CAAC;MACnB,CAAC,MAAM;QACL,OAAO/I,KAAK,CAAC0I,aAAa,CAAC7H,QAAQ,CAAC8H,GAAG,EAAE;UACvCC,SAAS,EAAE,GAAGtH,WAAW,cAAc;UACvCuH,KAAK,EAAE;YACL,CAAC/F,UAAU,GAAG,GAAG,GAAG,GAAG,GAAG4B,QAAQ,CAAC6D,EAAE,CAAC7D,QAAQ,IAAI,GAAG,CAACA,QAAQ,GAAG;UACnE;QACF,CAAC,EAAEqE,WAAW,CAAC,CAAC,CAAC;MACnB;IACF;IACA;IACA,MAAMF,KAAK,GAAG;MACZ,cAAc,EAAE,GAAGrG,KAAK,CAACT,SAAS,GAAG;MACrC,gBAAgB,EAAE,GAAGS,KAAK,CAACR,WAAW;IACxC,CAAC;IACD,MAAMwH,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElH,KAAK,CAACd,cAAc,GAAGiE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACvE,MAAMgE,oBAAoB,GAAG,CAAC,CAAC;IAC/B,KAAK,MAAMb,GAAG,IAAItG,KAAK,CAACL,eAAe,EAAE;MACvC,MAAMyH,IAAI,GAAGrI,iBAAiB,CAACuH,GAAG,CAAC;MACnCa,oBAAoB,CAACC,IAAI,CAAC,GAAG,UAAUC,CAAC,EAAE;QACxCA,CAAC,CAAC1H,eAAe,CAAC,CAAC;MACrB,CAAC;IACH;IACA,MAAM2H,WAAW,GAAGzI,cAAc,CAACmI,SAAS,EAAEG,oBAAoB,CAAC;IACnE,IAAII,aAAa,GAAG,IAAI;IACxB,IAAI,OAAOpH,SAAS,KAAK,UAAU,EAAE;MACnCoH,aAAa,GAAGpH,SAAS,CAACe,WAAW,EAAEI,OAAO,CAAC;IACjD,CAAC,MAAM,IAAInB,SAAS,KAAK,KAAK,EAAE;MAC9BoH,aAAa,GAAG/J,KAAK,CAAC0I,aAAa,CAAC,KAAK,EAAE;QACzCE,SAAS,EAAE,GAAGtH,WAAW;MAC3B,CAAC,EAAEtB,KAAK,CAAC0I,aAAa,CAAC3H,aAAa,EAAE0I,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElH,KAAK,CAACwH,cAAc,EAAE;QAC5EvH,KAAK,EAAEiB,WAAW;QAClBI,OAAO,EAAEA,OAAO;QAChBhC,SAAS,EAAEA;MACb,CAAC,CAAC,CAAC,CAAC;IACN;IACA,OAAOvB,eAAe,CAACiC,KAAK,EAAExC,KAAK,CAAC0I,aAAa,CAAC,KAAK,EAAE;MACvDE,SAAS,EAAEnI,UAAU,CAACa,WAAW,EAAE,GAAGA,WAAW,IAAIQ,SAAS,EAAE,CAAC;MACjE+G,KAAK,EAAEA;IACT,CAAC,EAAE7I,KAAK,CAAC0I,aAAa,CAAC,KAAK,EAAEe,MAAM,CAACC,MAAM,CAAC;MAC1CnH,GAAG,EAAEoB,QAAQ;MACbiF,SAAS,EAAEnI,UAAU,CAAC,GAAGa,WAAW,QAAQ,EAAE;QAC5C,CAAC,GAAGA,WAAW,yBAAyB,GAAGkB,KAAK,CAACd;MACnD,CAAC,CAAC;MACFuI,cAAc,EAAEJ,CAAC,IAAI;QACnB,IAAIvF,WAAW,CAACR,OAAO,EAAE;UACvB+F,CAAC,CAAC1H,eAAe,CAAC,CAAC;QACrB;QACAqD,eAAe,CAAC,CAAC;MACnB;IACF,CAAC,EAAEsE,WAAW,CAAC,EAAEP,gBAAgB,CAAC,CAAC,CAAC,EAAEQ,aAAa,CAAC,CAAC;EACvD,CAAC;AACH,CAAC,CAAC,CAAC;AACH,SAAS3E,OAAOA,CAAC8E,KAAK,EAAEC,QAAQ,EAAE;EAChC,MAAMC,SAAS,GAAGF,KAAK,GAAGC,QAAQ;EAClC,OAAOC,SAAS,GAAG,CAAC,GAAGA,SAAS,GAAGD,QAAQ,GAAGC,SAAS;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}