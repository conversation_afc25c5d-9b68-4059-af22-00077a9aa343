{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useState, useCallback } from 'react';\nimport useLatest from '../useLatest';\nfunction useGetState(initialState) {\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var stateRef = useLatest(state);\n  var getState = useCallback(function () {\n    return stateRef.current;\n  }, []);\n  return [state, setState, getState];\n}\nexport default useGetState;", "map": {"version": 3, "names": ["__read", "useState", "useCallback", "useLatest", "useGetState", "initialState", "_a", "state", "setState", "stateRef", "getState", "current"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useGetState/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useState, useCallback } from 'react';\nimport useLatest from '../useLatest';\nfunction useGetState(initialState) {\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var stateRef = useLatest(state);\n  var getState = useCallback(function () {\n    return stateRef.current;\n  }, []);\n  return [state, setState, getState];\n}\nexport default useGetState;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC7C,OAAOC,SAAS,MAAM,cAAc;AACpC,SAASC,WAAWA,CAACC,YAAY,EAAE;EACjC,IAAIC,EAAE,GAAGN,MAAM,CAACC,QAAQ,CAACI,YAAY,CAAC,EAAE,CAAC,CAAC;IACxCE,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;IACbE,QAAQ,GAAGF,EAAE,CAAC,CAAC,CAAC;EAClB,IAAIG,QAAQ,GAAGN,SAAS,CAACI,KAAK,CAAC;EAC/B,IAAIG,QAAQ,GAAGR,WAAW,CAAC,YAAY;IACrC,OAAOO,QAAQ,CAACE,OAAO;EACzB,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACJ,KAAK,EAAEC,QAAQ,EAAEE,QAAQ,CAAC;AACpC;AACA,eAAeN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}