{"ast": null, "code": "import React from 'react';\nexport const ArrowLeft = () => {\n  return React.createElement(\"svg\", {\n    height: '1em',\n    viewBox: '0 0 44 44'\n  }, React.createElement(\"g\", {\n    stroke: 'none',\n    strokeWidth: '1',\n    fill: 'none',\n    fillRule: 'evenodd'\n  }, React.createElement(\"g\", {\n    transform: 'translate(-100.000000, -22.000000)'\n  }, React.createElement(\"g\", {\n    transform: 'translate(100.000000, 22.000000)'\n  }, React.createElement(\"rect\", {\n    x: '0',\n    y: '0',\n    width: '44',\n    height: '44'\n  }), React.createElement(\"g\", {\n    transform: 'translate(12.000000, 4.000000)',\n    fill: 'currentColor',\n    fillRule: 'nonzero'\n  }, React.createElement(\"path\", {\n    d: 'M19.4833058,2.71985611 L3.53051139,17.0699744 C3.0173831,17.5315665 2.97522952,18.3220903 3.43630803,18.8357433 L3.43630796,18.8357432 C3.46601289,18.8688164 3.49745845,18.9002801 3.53051133,18.9300007 L19.4833057,33.2801611 C20.1234001,33.8559077 20.1759552,34.8420707 19.6007967,35.4827774 C19.0256382,36.1235263 18.0404824,36.1761351 17.400388,35.6003885 L1.44759367,21.2502703 L1.4475933,21.25027 C1.33208743,21.1463692 1.22220259,21.036372 1.11840792,20.920748 C-0.49302969,19.1256817 -0.345639536,16.3628317 1.4475933,14.7497465 L17.4003877,0.399628282 C18.0404821,-0.176160428 19.0256378,-0.123509422 19.6007963,0.517239417 C20.1759548,1.1579461 20.1233997,2.14410915 19.4833053,2.7198557 L19.4833058,2.71985611 Z'\n  }))))));\n};", "map": {"version": 3, "names": ["React", "ArrowLeft", "createElement", "height", "viewBox", "stroke", "strokeWidth", "fill", "fillRule", "transform", "x", "y", "width", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/calendar/arrow-left.js"], "sourcesContent": ["import React from 'react';\nexport const ArrowLeft = () => {\n  return React.createElement(\"svg\", {\n    height: '1em',\n    viewBox: '0 0 44 44'\n  }, React.createElement(\"g\", {\n    stroke: 'none',\n    strokeWidth: '1',\n    fill: 'none',\n    fillRule: 'evenodd'\n  }, React.createElement(\"g\", {\n    transform: 'translate(-100.000000, -22.000000)'\n  }, React.createElement(\"g\", {\n    transform: 'translate(100.000000, 22.000000)'\n  }, React.createElement(\"rect\", {\n    x: '0',\n    y: '0',\n    width: '44',\n    height: '44'\n  }), React.createElement(\"g\", {\n    transform: 'translate(12.000000, 4.000000)',\n    fill: 'currentColor',\n    fillRule: 'nonzero'\n  }, React.createElement(\"path\", {\n    d: 'M19.4833058,2.71985611 L3.53051139,17.0699744 C3.0173831,17.5315665 2.97522952,18.3220903 3.43630803,18.8357433 L3.43630796,18.8357432 C3.46601289,18.8688164 3.49745845,18.9002801 3.53051133,18.9300007 L19.4833057,33.2801611 C20.1234001,33.8559077 20.1759552,34.8420707 19.6007967,35.4827774 C19.0256382,36.1235263 18.0404824,36.1761351 17.400388,35.6003885 L1.44759367,21.2502703 L1.4475933,21.25027 C1.33208743,21.1463692 1.22220259,21.036372 1.11840792,20.920748 C-0.49302969,19.1256817 -0.345639536,16.3628317 1.4475933,14.7497465 L17.4003877,0.399628282 C18.0404821,-0.176160428 19.0256378,-0.123509422 19.6007963,0.517239417 C20.1759548,1.1579461 20.1233997,2.14410915 19.4833053,2.7198557 L19.4833058,2.71985611 Z'\n  }))))));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAC7B,OAAOD,KAAK,CAACE,aAAa,CAAC,KAAK,EAAE;IAChCC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE;EACX,CAAC,EAAEJ,KAAK,CAACE,aAAa,CAAC,GAAG,EAAE;IAC1BG,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,GAAG;IAChBC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAER,KAAK,CAACE,aAAa,CAAC,GAAG,EAAE;IAC1BO,SAAS,EAAE;EACb,CAAC,EAAET,KAAK,CAACE,aAAa,CAAC,GAAG,EAAE;IAC1BO,SAAS,EAAE;EACb,CAAC,EAAET,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;IAC7BQ,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE,GAAG;IACNC,KAAK,EAAE,IAAI;IACXT,MAAM,EAAE;EACV,CAAC,CAAC,EAAEH,KAAK,CAACE,aAAa,CAAC,GAAG,EAAE;IAC3BO,SAAS,EAAE,gCAAgC;IAC3CF,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,EAAER,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;IAC7BW,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}