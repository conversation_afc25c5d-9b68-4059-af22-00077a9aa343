{"ast": null, "code": "import React, { memo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nexport const Corner = memo(props => withNativeProps(props, React.createElement(\"svg\", {\n  viewBox: '0 0 30 30'\n}, React.createElement(\"g\", {\n  stroke: 'none',\n  strokeWidth: '1',\n  fill: 'none',\n  fillRule: 'evenodd'\n}, React.createElement(\"path\", {\n  d: 'M30,0 C13.4314575,3.04359188e-15 -2.02906125e-15,13.4314575 0,30 L0,30 L0,0 Z',\n  fill: 'var(--adm-color-background)',\n  transform: 'translate(15.000000, 15.000000) scale(-1, -1) translate(-15.000000, -15.000000) '\n})))));", "map": {"version": 3, "names": ["React", "memo", "withNativeProps", "Corner", "props", "createElement", "viewBox", "stroke", "strokeWidth", "fill", "fillRule", "d", "transform"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/side-bar/corner.js"], "sourcesContent": ["import React, { memo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nexport const Corner = memo(props => withNativeProps(props, React.createElement(\"svg\", {\n  viewBox: '0 0 30 30'\n}, React.createElement(\"g\", {\n  stroke: 'none',\n  strokeWidth: '1',\n  fill: 'none',\n  fillRule: 'evenodd'\n}, React.createElement(\"path\", {\n  d: 'M30,0 C13.4314575,3.04359188e-15 -2.02906125e-15,13.4314575 0,30 L0,30 L0,0 Z',\n  fill: 'var(--adm-color-background)',\n  transform: 'translate(15.000000, 15.000000) scale(-1, -1) translate(-15.000000, -15.000000) '\n})))));"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,IAAI,QAAQ,OAAO;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,MAAM,GAAGF,IAAI,CAACG,KAAK,IAAIF,eAAe,CAACE,KAAK,EAAEJ,KAAK,CAACK,aAAa,CAAC,KAAK,EAAE;EACpFC,OAAO,EAAE;AACX,CAAC,EAAEN,KAAK,CAACK,aAAa,CAAC,GAAG,EAAE;EAC1BE,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE,GAAG;EAChBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE;AACZ,CAAC,EAAEV,KAAK,CAACK,aAAa,CAAC,MAAM,EAAE;EAC7BM,CAAC,EAAE,+EAA+E;EAClFF,IAAI,EAAE,6BAA6B;EACnCG,SAAS,EAAE;AACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}