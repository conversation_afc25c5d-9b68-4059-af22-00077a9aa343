{"ast": null, "code": "import { createErrorBlock } from './create-error-block';\nimport { busyImage, defaultImage, disconnectedImage, emptyImage } from './images';\nconst imageRecord = {\n  'default': defaultImage,\n  'disconnected': disconnectedImage,\n  'empty': emptyImage,\n  'busy': busyImage\n};\nexport const ErrorBlock = createErrorBlock(imageRecord);", "map": {"version": 3, "names": ["createErrorBlock", "busyImage", "defaultImage", "disconnectedImage", "emptyImage", "imageRecord", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/error-block/error-block.js"], "sourcesContent": ["import { createErrorBlock } from './create-error-block';\nimport { busyImage, defaultImage, disconnectedImage, emptyImage } from './images';\nconst imageRecord = {\n  'default': defaultImage,\n  'disconnected': disconnectedImage,\n  'empty': emptyImage,\n  'busy': busyImage\n};\nexport const ErrorBlock = createErrorBlock(imageRecord);"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,SAAS,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,UAAU;AACjF,MAAMC,WAAW,GAAG;EAClB,SAAS,EAAEH,YAAY;EACvB,cAAc,EAAEC,iBAAiB;EACjC,OAAO,EAAEC,UAAU;EACnB,MAAM,EAAEH;AACV,CAAC;AACD,OAAO,MAAMK,UAAU,GAAGN,gBAAgB,CAACK,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}