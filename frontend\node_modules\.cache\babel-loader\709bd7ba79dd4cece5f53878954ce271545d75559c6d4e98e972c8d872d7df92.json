{"ast": null, "code": "import dayjs from 'dayjs';\nexport function convertValueToRange(selectionMode, value) {\n  if (selectionMode === undefined || value === null) return null;\n  if (Array.isArray(value)) {\n    return value;\n  }\n  return [value, value];\n}\nexport function convertPageToDayjs(page) {\n  return dayjs().year(page.year).month(page.month - 1).date(1);\n}", "map": {"version": 3, "names": ["dayjs", "convertValueToRange", "selectionMode", "value", "undefined", "Array", "isArray", "convertPageToDayjs", "page", "year", "month", "date"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/calendar/convert.js"], "sourcesContent": ["import dayjs from 'dayjs';\nexport function convertValueToRange(selectionMode, value) {\n  if (selectionMode === undefined || value === null) return null;\n  if (Array.isArray(value)) {\n    return value;\n  }\n  return [value, value];\n}\nexport function convertPageToDayjs(page) {\n  return dayjs().year(page.year).month(page.month - 1).date(1);\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,SAASC,mBAAmBA,CAACC,aAAa,EAAEC,KAAK,EAAE;EACxD,IAAID,aAAa,KAAKE,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE,OAAO,IAAI;EAC9D,IAAIE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK;EACd;EACA,OAAO,CAACA,KAAK,EAAEA,KAAK,CAAC;AACvB;AACA,OAAO,SAASI,kBAAkBA,CAACC,IAAI,EAAE;EACvC,OAAOR,KAAK,CAAC,CAAC,CAACS,IAAI,CAACD,IAAI,CAACC,IAAI,CAAC,CAACC,KAAK,CAACF,IAAI,CAACE,KAAK,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}