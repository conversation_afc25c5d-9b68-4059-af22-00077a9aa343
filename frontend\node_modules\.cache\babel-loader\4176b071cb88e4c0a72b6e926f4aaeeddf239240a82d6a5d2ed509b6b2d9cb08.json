{"ast": null, "code": "import { CascadePicker } from './cascade-picker';\nimport React, { useEffect, useState } from 'react';\nimport { renderToBody } from '../../utils/render-to-body';\nexport function prompt(props) {\n  return new Promise(resolve => {\n    const Wrapper = () => {\n      const [visible, setVisible] = useState(false);\n      useEffect(() => {\n        setVisible(true);\n      }, []);\n      return React.createElement(CascadePicker, Object.assign({}, props, {\n        visible: visible,\n        onConfirm: (val, extend) => {\n          var _a;\n          (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(props, val, extend);\n          resolve(val);\n        },\n        onClose: () => {\n          var _a;\n          (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n          setVisible(false);\n          resolve(null);\n        },\n        afterClose: () => {\n          var _a;\n          (_a = props.afterClose) === null || _a === void 0 ? void 0 : _a.call(props);\n          unmount();\n        }\n      }));\n    };\n    const unmount = renderToBody(React.createElement(Wrapper, null));\n  });\n}", "map": {"version": 3, "names": ["CascadePicker", "React", "useEffect", "useState", "renderToBody", "prompt", "props", "Promise", "resolve", "Wrapper", "visible", "setVisible", "createElement", "Object", "assign", "onConfirm", "val", "extend", "_a", "call", "onClose", "afterClose", "unmount"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/cascade-picker/prompt.js"], "sourcesContent": ["import { CascadePicker } from './cascade-picker';\nimport React, { useEffect, useState } from 'react';\nimport { renderToBody } from '../../utils/render-to-body';\nexport function prompt(props) {\n  return new Promise(resolve => {\n    const Wrapper = () => {\n      const [visible, setVisible] = useState(false);\n      useEffect(() => {\n        setVisible(true);\n      }, []);\n      return React.createElement(CascadePicker, Object.assign({}, props, {\n        visible: visible,\n        onConfirm: (val, extend) => {\n          var _a;\n          (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(props, val, extend);\n          resolve(val);\n        },\n        onClose: () => {\n          var _a;\n          (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n          setVisible(false);\n          resolve(null);\n        },\n        afterClose: () => {\n          var _a;\n          (_a = props.afterClose) === null || _a === void 0 ? void 0 : _a.call(props);\n          unmount();\n        }\n      }));\n    };\n    const unmount = renderToBody(React.createElement(Wrapper, null));\n  });\n}"], "mappings": "AAAA,SAASA,aAAa,QAAQ,kBAAkB;AAChD,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,YAAY,QAAQ,4BAA4B;AACzD,OAAO,SAASC,MAAMA,CAACC,KAAK,EAAE;EAC5B,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;IAC5B,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;MAC7CD,SAAS,CAAC,MAAM;QACdS,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,EAAE,EAAE,CAAC;MACN,OAAOV,KAAK,CAACW,aAAa,CAACZ,aAAa,EAAEa,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,KAAK,EAAE;QACjEI,OAAO,EAAEA,OAAO;QAChBK,SAAS,EAAEA,CAACC,GAAG,EAAEC,MAAM,KAAK;UAC1B,IAAIC,EAAE;UACN,CAACA,EAAE,GAAGZ,KAAK,CAACS,SAAS,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACb,KAAK,EAAEU,GAAG,EAAEC,MAAM,CAAC;UACvFT,OAAO,CAACQ,GAAG,CAAC;QACd,CAAC;QACDI,OAAO,EAAEA,CAAA,KAAM;UACb,IAAIF,EAAE;UACN,CAACA,EAAE,GAAGZ,KAAK,CAACc,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACb,KAAK,CAAC;UACxEK,UAAU,CAAC,KAAK,CAAC;UACjBH,OAAO,CAAC,IAAI,CAAC;QACf,CAAC;QACDa,UAAU,EAAEA,CAAA,KAAM;UAChB,IAAIH,EAAE;UACN,CAACA,EAAE,GAAGZ,KAAK,CAACe,UAAU,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACb,KAAK,CAAC;UAC3EgB,OAAO,CAAC,CAAC;QACX;MACF,CAAC,CAAC,CAAC;IACL,CAAC;IACD,MAAMA,OAAO,GAAGlB,YAAY,CAACH,KAAK,CAACW,aAAa,CAACH,OAAO,EAAE,IAAI,CAAC,CAAC;EAClE,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}