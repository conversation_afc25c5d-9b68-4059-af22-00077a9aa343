{"ast": null, "code": "export const optionSkeleton = [];", "map": {"version": 3, "names": ["optionSkeleton"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/cascader-view/option-skeleton.js"], "sourcesContent": ["export const optionSkeleton = [];"], "mappings": "AAAA,OAAO,MAAMA,cAAc,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}