{"ast": null, "code": "import { show } from './show';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { getDefaultConfig } from '../config-provider';\nexport function alert(p) {\n  const defaultProps = {\n    confirmText: getDefaultConfig().locale.Modal.ok\n  };\n  const props = mergeProps(defaultProps, p);\n  return new Promise(resolve => {\n    show(Object.assign(Object.assign({}, props), {\n      closeOnAction: true,\n      actions: [{\n        key: 'confirm',\n        text: props.confirmText,\n        primary: true\n      }],\n      onAction: props.onConfirm,\n      onClose: () => {\n        var _a;\n        (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n        resolve();\n      }\n    }));\n  });\n}", "map": {"version": 3, "names": ["show", "mergeProps", "getDefaultConfig", "alert", "p", "defaultProps", "confirmText", "locale", "Modal", "ok", "props", "Promise", "resolve", "Object", "assign", "closeOnAction", "actions", "key", "text", "primary", "onAction", "onConfirm", "onClose", "_a", "call"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/modal/alert.js"], "sourcesContent": ["import { show } from './show';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { getDefaultConfig } from '../config-provider';\nexport function alert(p) {\n  const defaultProps = {\n    confirmText: getDefaultConfig().locale.Modal.ok\n  };\n  const props = mergeProps(defaultProps, p);\n  return new Promise(resolve => {\n    show(Object.assign(Object.assign({}, props), {\n      closeOnAction: true,\n      actions: [{\n        key: 'confirm',\n        text: props.confirmText,\n        primary: true\n      }],\n      onAction: props.onConfirm,\n      onClose: () => {\n        var _a;\n        (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n        resolve();\n      }\n    }));\n  });\n}"], "mappings": "AAAA,SAASA,IAAI,QAAQ,QAAQ;AAC7B,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAO,SAASC,KAAKA,CAACC,CAAC,EAAE;EACvB,MAAMC,YAAY,GAAG;IACnBC,WAAW,EAAEJ,gBAAgB,CAAC,CAAC,CAACK,MAAM,CAACC,KAAK,CAACC;EAC/C,CAAC;EACD,MAAMC,KAAK,GAAGT,UAAU,CAACI,YAAY,EAAED,CAAC,CAAC;EACzC,OAAO,IAAIO,OAAO,CAACC,OAAO,IAAI;IAC5BZ,IAAI,CAACa,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,KAAK,CAAC,EAAE;MAC3CK,aAAa,EAAE,IAAI;MACnBC,OAAO,EAAE,CAAC;QACRC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAER,KAAK,CAACJ,WAAW;QACvBa,OAAO,EAAE;MACX,CAAC,CAAC;MACFC,QAAQ,EAAEV,KAAK,CAACW,SAAS;MACzBC,OAAO,EAAEA,CAAA,KAAM;QACb,IAAIC,EAAE;QACN,CAACA,EAAE,GAAGb,KAAK,CAACY,OAAO,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACd,KAAK,CAAC;QACxEE,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}