/* 移動端全屏相機樣式 */

.mobile-camera-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: #000;
  display: flex;
  flex-direction: column;
}

.camera-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.camera-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #000;
}

/* 相機加載狀態 */
.camera-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  text-align: center;
}

/* 拍照指引線 */
.camera-guides {
  position: absolute;
  top: 15%;
  left: 8%;
  right: 8%;
  bottom: 25%;
  pointer-events: none;
  z-index: 5;
}

.guide-corner {
  position: absolute;
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.9);
}

.guide-corner.top-left {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}

.guide-corner.top-right {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
}

.guide-corner.bottom-left {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
}

.guide-corner.bottom-right {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
}

/* 控制按鈕區域 */
.camera-controls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  pointer-events: none;
  z-index: 10;
}

.controls-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  pointer-events: auto;
}

.controls-bottom {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  pointer-events: auto;
}

.control-button {
  width: 52px !important;
  height: 52px !important;
  border-radius: 50% !important;
  background: rgba(0, 0, 0, 0.6) !important;
  border: 2px solid rgba(255, 255, 255, 0.9) !important;
  color: white !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 22px !important;
  backdrop-filter: blur(4px) !important;
}

.control-button:active {
  background: rgba(0, 0, 0, 0.7) !important;
}

.close-button {
  /* 關閉按鈕特殊樣式 */
}

.switch-button {
  /* 切換按鈕特殊樣式 */
}

/* 拍照區域 */
.capture-area {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.capture-button {
  width: 90px !important;
  height: 90px !important;
  border-radius: 50% !important;
  background: rgba(255, 255, 255, 0.95) !important;
  border: 5px solid #1677ff !important;
  color: #1677ff !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 36px !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4) !important;
}

.capture-button:active {
  background: rgba(255, 255, 255, 1) !important;
  transform: scale(0.95);
}

.capture-button:disabled {
  opacity: 0.6 !important;
  transform: none !important;
}

/* 相機信息 */
.camera-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
}

.facing-mode-indicator {
  font-size: 14px;
  background: rgba(0, 0, 0, 0.5);
  padding: 6px 12px;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 響應式設計 */
@media (max-width: 480px) {
  .controls-top {
    padding: 16px;
  }

  .controls-bottom {
    padding: 16px;
  }

  .control-button {
    width: 48px !important;
    height: 48px !important;
    font-size: 20px !important;
  }

  .capture-button {
    width: 80px !important;
    height: 80px !important;
    font-size: 32px !important;
  }

  .camera-guides {
    top: 18%;
    bottom: 28%;
    left: 10%;
    right: 10%;
  }

  .guide-corner {
    width: 35px;
    height: 35px;
    border-width: 2px;
  }
}

/* 大屏幕優化 */
@media (min-width: 768px) {
  .capture-button {
    width: 100px !important;
    height: 100px !important;
    font-size: 40px !important;
  }

  .control-button {
    width: 56px !important;
    height: 56px !important;
    font-size: 24px !important;
  }

  .camera-guides {
    top: 12%;
    bottom: 22%;
    left: 6%;
    right: 6%;
  }

  .guide-corner {
    width: 50px;
    height: 50px;
    border-width: 4px;
  }
}

/* 橫屏適配 */
@media (orientation: landscape) {
  .camera-guides {
    top: 10%;
    bottom: 20%;
    left: 15%;
    right: 15%;
  }
  
  .controls-top {
    padding: 16px 20px;
  }
  
  .controls-bottom {
    padding: 16px 20px;
  }
}

/* 動畫效果 */
.mobile-camera-modal {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.capture-button {
  transition: all 0.2s ease;
}

.control-button {
  transition: all 0.2s ease;
}

/* 確保在所有設備上的兼容性 */
.camera-video {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* iOS Safari 特殊處理 */
@supports (-webkit-appearance: none) {
  .mobile-camera-modal {
    /* iOS Safari 全屏處理 */
    height: 100vh;
    height: -webkit-fill-available;
  }
  
  .camera-container {
    height: 100vh;
    height: -webkit-fill-available;
  }
}
