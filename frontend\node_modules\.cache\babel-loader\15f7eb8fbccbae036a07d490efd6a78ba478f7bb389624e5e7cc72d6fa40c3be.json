{"ast": null, "code": "import { useMemoizedFn } from 'ahooks';\nimport React, { useEffect, useRef } from 'react';\nexport const NativeInput = props => {\n  const inputRef = useRef(null);\n  const handleClick = useMemoizedFn(e => {\n    e.stopPropagation();\n    e.stopImmediatePropagation();\n    const latestChecked = e.target.checked;\n    if (latestChecked === props.checked) return;\n    props.onChange(latestChecked);\n  });\n  useEffect(() => {\n    if (props.disabled) return;\n    if (!inputRef.current) return;\n    const input = inputRef.current;\n    input.addEventListener('click', handleClick);\n    return () => {\n      input.removeEventListener('click', handleClick);\n    };\n  }, [props.disabled, props.onChange]);\n  return React.createElement(\"input\", {\n    ref: inputRef,\n    type: props.type,\n    checked: props.checked,\n    onChange: () => {},\n    disabled: props.disabled,\n    id: props.id\n  });\n};", "map": {"version": 3, "names": ["useMemoizedFn", "React", "useEffect", "useRef", "NativeInput", "props", "inputRef", "handleClick", "e", "stopPropagation", "stopImmediatePropagation", "latestChecked", "target", "checked", "onChange", "disabled", "current", "input", "addEventListener", "removeEventListener", "createElement", "ref", "type", "id"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/checkbox/native-input.js"], "sourcesContent": ["import { useMemoizedFn } from 'ahooks';\nimport React, { useEffect, useRef } from 'react';\nexport const NativeInput = props => {\n  const inputRef = useRef(null);\n  const handleClick = useMemoizedFn(e => {\n    e.stopPropagation();\n    e.stopImmediatePropagation();\n    const latestChecked = e.target.checked;\n    if (latestChecked === props.checked) return;\n    props.onChange(latestChecked);\n  });\n  useEffect(() => {\n    if (props.disabled) return;\n    if (!inputRef.current) return;\n    const input = inputRef.current;\n    input.addEventListener('click', handleClick);\n    return () => {\n      input.removeEventListener('click', handleClick);\n    };\n  }, [props.disabled, props.onChange]);\n  return React.createElement(\"input\", {\n    ref: inputRef,\n    type: props.type,\n    checked: props.checked,\n    onChange: () => {},\n    disabled: props.disabled,\n    id: props.id\n  });\n};"], "mappings": "AAAA,SAASA,aAAa,QAAQ,QAAQ;AACtC,OAAOC,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAO,MAAMC,WAAW,GAAGC,KAAK,IAAI;EAClC,MAAMC,QAAQ,GAAGH,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMI,WAAW,GAAGP,aAAa,CAACQ,CAAC,IAAI;IACrCA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBD,CAAC,CAACE,wBAAwB,CAAC,CAAC;IAC5B,MAAMC,aAAa,GAAGH,CAAC,CAACI,MAAM,CAACC,OAAO;IACtC,IAAIF,aAAa,KAAKN,KAAK,CAACQ,OAAO,EAAE;IACrCR,KAAK,CAACS,QAAQ,CAACH,aAAa,CAAC;EAC/B,CAAC,CAAC;EACFT,SAAS,CAAC,MAAM;IACd,IAAIG,KAAK,CAACU,QAAQ,EAAE;IACpB,IAAI,CAACT,QAAQ,CAACU,OAAO,EAAE;IACvB,MAAMC,KAAK,GAAGX,QAAQ,CAACU,OAAO;IAC9BC,KAAK,CAACC,gBAAgB,CAAC,OAAO,EAAEX,WAAW,CAAC;IAC5C,OAAO,MAAM;MACXU,KAAK,CAACE,mBAAmB,CAAC,OAAO,EAAEZ,WAAW,CAAC;IACjD,CAAC;EACH,CAAC,EAAE,CAACF,KAAK,CAACU,QAAQ,EAAEV,KAAK,CAACS,QAAQ,CAAC,CAAC;EACpC,OAAOb,KAAK,CAACmB,aAAa,CAAC,OAAO,EAAE;IAClCC,GAAG,EAAEf,QAAQ;IACbgB,IAAI,EAAEjB,KAAK,CAACiB,IAAI;IAChBT,OAAO,EAAER,KAAK,CAACQ,OAAO;IACtBC,QAAQ,EAAEA,CAAA,KAAM,CAAC,CAAC;IAClBC,QAAQ,EAAEV,KAAK,CAACU,QAAQ;IACxBQ,EAAE,EAAElB,KAAK,CAACkB;EACZ,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}