{"ast": null, "code": "export function mergeProps(...items) {\n  const ret = {};\n  items.forEach(item => {\n    if (item) {\n      Object.keys(item).forEach(key => {\n        if (item[key] !== undefined) {\n          ret[key] = item[key];\n        }\n      });\n    }\n  });\n  return ret;\n}\n/**\n * Merge props and return the first non-undefined value.\n * The later has higher priority. e.g. (10, 1, 5) => 5 wins.\n * This is useful with legacy props that have been deprecated.\n */\nexport function mergeProp(defaultProp, ...propList) {\n  for (let i = propList.length - 1; i >= 0; i -= 1) {\n    if (propList[i] !== undefined) {\n      return propList[i];\n    }\n  }\n  return defaultProp;\n}", "map": {"version": 3, "names": ["mergeProps", "items", "ret", "for<PERSON>ach", "item", "Object", "keys", "key", "undefined", "mergeProp", "defaultProp", "propList", "i", "length"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/with-default-props.js"], "sourcesContent": ["export function mergeProps(...items) {\n  const ret = {};\n  items.forEach(item => {\n    if (item) {\n      Object.keys(item).forEach(key => {\n        if (item[key] !== undefined) {\n          ret[key] = item[key];\n        }\n      });\n    }\n  });\n  return ret;\n}\n/**\n * Merge props and return the first non-undefined value.\n * The later has higher priority. e.g. (10, 1, 5) => 5 wins.\n * This is useful with legacy props that have been deprecated.\n */\nexport function mergeProp(defaultProp, ...propList) {\n  for (let i = propList.length - 1; i >= 0; i -= 1) {\n    if (propList[i] !== undefined) {\n      return propList[i];\n    }\n  }\n  return defaultProp;\n}"], "mappings": "AAAA,OAAO,SAASA,UAAUA,CAAC,GAAGC,KAAK,EAAE;EACnC,MAAMC,GAAG,GAAG,CAAC,CAAC;EACdD,KAAK,CAACE,OAAO,CAACC,IAAI,IAAI;IACpB,IAAIA,IAAI,EAAE;MACRC,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAACD,OAAO,CAACI,GAAG,IAAI;QAC/B,IAAIH,IAAI,CAACG,GAAG,CAAC,KAAKC,SAAS,EAAE;UAC3BN,GAAG,CAACK,GAAG,CAAC,GAAGH,IAAI,CAACG,GAAG,CAAC;QACtB;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOL,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,SAASA,CAACC,WAAW,EAAE,GAAGC,QAAQ,EAAE;EAClD,KAAK,IAAIC,CAAC,GAAGD,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IAChD,IAAID,QAAQ,CAACC,CAAC,CAAC,KAAKJ,SAAS,EAAE;MAC7B,OAAOG,QAAQ,CAACC,CAAC,CAAC;IACpB;EACF;EACA,OAAOF,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}