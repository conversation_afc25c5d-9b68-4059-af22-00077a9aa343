{"ast": null, "code": "import * as React from \"react\";\nfunction UploadOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UploadOutline-UploadOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UploadOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UploadOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M13.5,4.4 L13.5,6.6 C13.5,6.8209139 13.3209139,7 13.1,7 L10,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L35.9,7 C35.6790861,7 35.5,6.8209139 35.5,6.6 L35.5,4.4 C35.5,4.1790861 35.6790861,4 35.9,4 L38,4 L38,4 C41.3137085,4 44,6.6862915 44,10 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,10 C4,6.6862915 6.6862915,4 10,4 L13.1,4 C13.3209139,4 13.5,4.1790861 13.5,4.4 Z M24.4857797,4.00077369 C24.8310987,3.99014909 25.1797295,4.09295041 25.4712865,4.31053618 L25.5925692,4.41081881 L33.8698386,11.9750512 C33.9527631,12.0508323 34,12.1579904 34,12.2703259 L34,16.0746777 C34,16.0883641 33.994729,16.1015248 33.9852814,16.1114273 C33.9771016,16.1200009 33.9635695,16.1204652 33.9548213,16.1124724 L25.9996797,8.84421395 L25.9996797,8.84421395 L25.9999942,30.4842139 C26.0000032,30.7051278 25.8209197,30.8842166 25.6000058,30.8842198 C25.6000039,30.8842198 25.6000019,30.8842198 25.6,30.8842139 L23.4,30.8842139 C23.1790884,30.8842081 23.0000032,30.7051256 22.9999942,30.4842139 L22.9996797,8.81621395 L22.9996797,8.81621395 L15.669803,15.5130447 C15.5067093,15.6620527 15.2537008,15.6506341 15.1046928,15.4875404 C15.0373439,15.4138251 15,15.3175864 15,15.2177374 L15,12.242343 C15,12.1299975 15.0472452,12.0228309 15.1301825,11.947049 L23.3779936,4.41081881 L23.3779936,4.41081881 C23.691422,4.1243947 24.0908085,3.98840107 24.4857797,4.00077369 Z\",\n    id: \"UploadOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default UploadOutline;", "map": {"version": 3, "names": ["React", "UploadOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/UploadOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction UploadOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UploadOutline-UploadOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UploadOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UploadOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M13.5,4.4 L13.5,6.6 C13.5,6.8209139 13.3209139,7 13.1,7 L10,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L35.9,7 C35.6790861,7 35.5,6.8209139 35.5,6.6 L35.5,4.4 C35.5,4.1790861 35.6790861,4 35.9,4 L38,4 L38,4 C41.3137085,4 44,6.6862915 44,10 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,10 C4,6.6862915 6.6862915,4 10,4 L13.1,4 C13.3209139,4 13.5,4.1790861 13.5,4.4 Z M24.4857797,4.00077369 C24.8310987,3.99014909 25.1797295,4.09295041 25.4712865,4.31053618 L25.5925692,4.41081881 L33.8698386,11.9750512 C33.9527631,12.0508323 34,12.1579904 34,12.2703259 L34,16.0746777 C34,16.0883641 33.994729,16.1015248 33.9852814,16.1114273 C33.9771016,16.1200009 33.9635695,16.1204652 33.9548213,16.1124724 L25.9996797,8.84421395 L25.9996797,8.84421395 L25.9999942,30.4842139 C26.0000032,30.7051278 25.8209197,30.8842166 25.6000058,30.8842198 C25.6000039,30.8842198 25.6000019,30.8842198 25.6,30.8842139 L23.4,30.8842139 C23.1790884,30.8842081 23.0000032,30.7051256 22.9999942,30.4842139 L22.9996797,8.81621395 L22.9996797,8.81621395 L15.669803,15.5130447 C15.5067093,15.6620527 15.2537008,15.6506341 15.1046928,15.4875404 C15.0373439,15.4138251 15,15.3175864 15,15.2177374 L15,12.242343 C15,12.1299975 15.0472452,12.0228309 15.1301825,11.947049 L23.3779936,4.41081881 L23.3779936,4.41081881 C23.691422,4.1243947 24.0908085,3.98840107 24.4857797,4.00077369 Z\",\n    id: \"UploadOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default UploadOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,6BAA6B;IACjCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,0lDAA0lD;IAC7lDR,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}