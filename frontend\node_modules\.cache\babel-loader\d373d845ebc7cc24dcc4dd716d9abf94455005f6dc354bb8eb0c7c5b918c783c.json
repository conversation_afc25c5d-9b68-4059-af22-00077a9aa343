{"ast": null, "code": "import { animated, useSpring } from '@react-spring/web';\nimport { useSize } from 'ahooks';\nimport React, { useRef } from 'react';\nimport { bound } from '../../utils/bound';\nimport * as mat from '../../utils/matrix';\nimport { rubberbandIfOutOfBounds } from '../../utils/rubberband';\nimport { useDragAndPinch } from '../../utils/use-drag-and-pinch';\nconst classPrefix = `adm-image-viewer`;\nexport const Slide = props => {\n  const {\n    dragLockRef,\n    maxZoom,\n    imageRender,\n    index\n  } = props;\n  const initialMartix = useRef([]);\n  const controlRef = useRef(null);\n  const imgRef = useRef(null);\n  const [{\n    matrix\n  }, api] = useSpring(() => ({\n    matrix: mat.create(),\n    config: {\n      tension: 200\n    }\n  }));\n  const controlSize = useSize(controlRef);\n  const imgSize = useSize(imgRef);\n  const pinchLockRef = useRef(false);\n  /**\n   * Calculate the min and max value of x and y\n   */\n  const getMinAndMax = nextMatrix => {\n    if (!controlSize || !imgSize) return {\n      x: {\n        position: 0,\n        minX: 0,\n        maxX: 0\n      },\n      y: {\n        position: 0,\n        minY: 0,\n        maxY: 0\n      }\n    };\n    const controlLeft = -controlSize.width / 2;\n    const controlTop = -controlSize.height / 2;\n    const imgLeft = -imgSize.width / 2;\n    const imgTop = -imgSize.height / 2;\n    const zoom = mat.getScaleX(nextMatrix);\n    const scaledImgWidth = zoom * imgSize.width;\n    const scaledImgHeight = zoom * imgSize.height;\n    const minX = controlLeft - (scaledImgWidth - controlSize.width);\n    const maxX = controlLeft;\n    const minY = controlTop - (scaledImgHeight - controlSize.height);\n    const maxY = controlTop;\n    const [x, y] = mat.apply(nextMatrix, [imgLeft, imgTop]);\n    return {\n      x: {\n        position: x,\n        minX,\n        maxX\n      },\n      y: {\n        position: y,\n        minY,\n        maxY\n      }\n    };\n  };\n  /**\n   * Check if is reach the bound\n   */\n  const getReachBound = (position, min, max, buffer = 0) => {\n    return [position <= min - buffer, position >= max + buffer];\n  };\n  /**\n   * Limit the matrix in the bound\n   */\n  const boundMatrix = (nextMatrix, type, last = false) => {\n    if (!controlSize || !imgSize) return nextMatrix;\n    const zoom = mat.getScaleX(nextMatrix);\n    const scaledImgWidth = zoom * imgSize.width;\n    const scaledImgHeight = zoom * imgSize.height;\n    const {\n      x: {\n        position: x,\n        minX,\n        maxX\n      },\n      y: {\n        position: y,\n        minY,\n        maxY\n      }\n    } = getMinAndMax(nextMatrix);\n    if (type === 'translate') {\n      let boundedX = x;\n      let boundedY = y;\n      if (scaledImgWidth > controlSize.width) {\n        boundedX = last ? bound(x, minX, maxX) : rubberbandIfOutOfBounds(x, minX, maxX, zoom * 50);\n      } else {\n        boundedX = -scaledImgWidth / 2;\n      }\n      if (scaledImgHeight > controlSize.height) {\n        boundedY = last ? bound(y, minY, maxY) : rubberbandIfOutOfBounds(y, minY, maxY, zoom * 50);\n      } else {\n        boundedY = -scaledImgHeight / 2;\n      }\n      return mat.translate(nextMatrix, boundedX - x, boundedY - y);\n    }\n    if (type === 'scale' && last) {\n      const [boundedX, boundedY] = [scaledImgWidth > controlSize.width ? bound(x, minX, maxX) : -scaledImgWidth / 2, scaledImgHeight > controlSize.height ? bound(y, minY, maxY) : -scaledImgHeight / 2];\n      return mat.translate(nextMatrix, boundedX - x, boundedY - y);\n    }\n    return nextMatrix;\n  };\n  useDragAndPinch({\n    onDrag: state => {\n      var _a;\n      if (state.first) {\n        const {\n          x: {\n            position: x,\n            minX,\n            maxX\n          }\n        } = getMinAndMax(matrix.get());\n        initialMartix.current = getReachBound(x, minX, maxX);\n        return;\n      }\n      if (state.pinching) return state.cancel();\n      if (state.tap && state.elapsedTime > 0 && state.elapsedTime < 1000) {\n        // 判断点击时间>0是为了过滤掉非正常操作，例如用户长按选择图片之后的取消操作（也是一次点击）\n        (_a = props.onTap) === null || _a === void 0 ? void 0 : _a.call(props);\n        return;\n      }\n      const currentZoom = mat.getScaleX(matrix.get());\n      if (dragLockRef) {\n        dragLockRef.current = currentZoom !== 1;\n      }\n      if (!pinchLockRef.current && currentZoom <= 1) {\n        api.start({\n          matrix: mat.create()\n        });\n      } else {\n        const currentMatrix = matrix.get();\n        const offset = [state.offset[0] - mat.getTranslateX(currentMatrix), state.offset[1] - mat.getTranslateY(currentMatrix)];\n        const nextMatrix = mat.translate(currentMatrix, ...(state.last ? [offset[0] + state.velocity[0] * state.direction[0] * 200, offset[1] + state.velocity[1] * state.direction[1] * 200] : offset));\n        api.start({\n          matrix: boundMatrix(nextMatrix, 'translate', state.last),\n          immediate: !state.last\n        });\n        const {\n          x: {\n            position: x,\n            minX,\n            maxX\n          }\n        } = getMinAndMax(nextMatrix);\n        if (state.last && initialMartix.current.some(i => i) && getReachBound(x, minX, maxX).some(i => i)) {\n          if (dragLockRef) {\n            dragLockRef.current = false;\n          }\n          api.start({\n            matrix: mat.create()\n          });\n        }\n      }\n    },\n    onPinch: state => {\n      var _a;\n      pinchLockRef.current = !state.last;\n      const [d] = state.offset;\n      if (d < 0) return;\n      let mergedMaxZoom;\n      if (maxZoom === 'auto') {\n        mergedMaxZoom = controlSize && imgSize ? Math.max(controlSize.height / imgSize.height, controlSize.width / imgSize.width) : 1;\n      } else {\n        mergedMaxZoom = maxZoom;\n      }\n      const nextZoom = state.last ? bound(d, 1, mergedMaxZoom) : d;\n      (_a = props.onZoomChange) === null || _a === void 0 ? void 0 : _a.call(props, nextZoom);\n      if (state.last && nextZoom <= 1) {\n        api.start({\n          matrix: mat.create()\n        });\n        if (dragLockRef) {\n          dragLockRef.current = false;\n        }\n      } else {\n        if (!controlSize) return;\n        const currentMatrix = matrix.get();\n        const currentZoom = mat.getScaleX(currentMatrix);\n        const originOffsetX = state.origin[0] - controlSize.width / 2;\n        const originOffsetY = state.origin[1] - controlSize.height / 2;\n        let nextMatrix = mat.translate(currentMatrix, -originOffsetX, -originOffsetY);\n        nextMatrix = mat.scale(nextMatrix, nextZoom / currentZoom);\n        nextMatrix = mat.translate(nextMatrix, originOffsetX, originOffsetY);\n        api.start({\n          matrix: boundMatrix(nextMatrix, 'scale', state.last),\n          immediate: !state.last\n        });\n        if (dragLockRef) {\n          dragLockRef.current = true;\n        }\n      }\n    }\n  }, {\n    target: controlRef,\n    drag: {\n      from: () => [mat.getTranslateX(matrix.get()), mat.getTranslateY(matrix.get())],\n      pointer: {\n        touch: true\n      }\n    },\n    pinch: {\n      from: () => [mat.getScaleX(matrix.get()), 0],\n      pointer: {\n        touch: true\n      }\n    }\n  });\n  const customRendering = typeof imageRender === 'function' && imageRender(props.image, {\n    index\n  });\n  return React.createElement(\"div\", {\n    className: `${classPrefix}-slide`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-control`,\n    ref: controlRef\n  }, React.createElement(animated.div, {\n    className: `${classPrefix}-image-wrapper`,\n    style: {\n      matrix\n    }\n  }, customRendering ? customRendering : React.createElement(\"img\", {\n    ref: imgRef,\n    src: props.image,\n    draggable: false,\n    alt: props.image\n  }))));\n};", "map": {"version": 3, "names": ["animated", "useSpring", "useSize", "React", "useRef", "bound", "mat", "rubberbandIfOutOfBounds", "useDragAndPinch", "classPrefix", "Slide", "props", "dragLockRef", "max<PERSON><PERSON>", "imageRender", "index", "initialMartix", "controlRef", "imgRef", "matrix", "api", "create", "config", "tension", "controlSize", "imgSize", "pinchLockRef", "getMinAndMax", "nextMatrix", "x", "position", "minX", "maxX", "y", "minY", "maxY", "controlLeft", "width", "controlTop", "height", "imgLeft", "imgTop", "zoom", "getScaleX", "scaledImgWidth", "scaledImgHeight", "apply", "getReachBound", "min", "max", "buffer", "boundMatrix", "type", "last", "boundedX", "boundedY", "translate", "onDrag", "state", "_a", "first", "get", "current", "pinching", "cancel", "tap", "elapsedTime", "onTap", "call", "currentZoom", "start", "currentMatrix", "offset", "getTranslateX", "getTranslateY", "velocity", "direction", "immediate", "some", "i", "onPinch", "d", "mergedMaxZoom", "Math", "nextZoom", "onZoomChange", "originOffsetX", "origin", "originOffsetY", "scale", "target", "drag", "from", "pointer", "touch", "pinch", "customRendering", "image", "createElement", "className", "ref", "div", "style", "src", "draggable", "alt"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/image-viewer/slide.js"], "sourcesContent": ["import { animated, useSpring } from '@react-spring/web';\nimport { useSize } from 'ahooks';\nimport React, { useRef } from 'react';\nimport { bound } from '../../utils/bound';\nimport * as mat from '../../utils/matrix';\nimport { rubberbandIfOutOfBounds } from '../../utils/rubberband';\nimport { useDragAndPinch } from '../../utils/use-drag-and-pinch';\nconst classPrefix = `adm-image-viewer`;\nexport const Slide = props => {\n  const {\n    dragLockRef,\n    maxZoom,\n    imageRender,\n    index\n  } = props;\n  const initialMartix = useRef([]);\n  const controlRef = useRef(null);\n  const imgRef = useRef(null);\n  const [{\n    matrix\n  }, api] = useSpring(() => ({\n    matrix: mat.create(),\n    config: {\n      tension: 200\n    }\n  }));\n  const controlSize = useSize(controlRef);\n  const imgSize = useSize(imgRef);\n  const pinchLockRef = useRef(false);\n  /**\n   * Calculate the min and max value of x and y\n   */\n  const getMinAndMax = nextMatrix => {\n    if (!controlSize || !imgSize) return {\n      x: {\n        position: 0,\n        minX: 0,\n        maxX: 0\n      },\n      y: {\n        position: 0,\n        minY: 0,\n        maxY: 0\n      }\n    };\n    const controlLeft = -controlSize.width / 2;\n    const controlTop = -controlSize.height / 2;\n    const imgLeft = -imgSize.width / 2;\n    const imgTop = -imgSize.height / 2;\n    const zoom = mat.getScaleX(nextMatrix);\n    const scaledImgWidth = zoom * imgSize.width;\n    const scaledImgHeight = zoom * imgSize.height;\n    const minX = controlLeft - (scaledImgWidth - controlSize.width);\n    const maxX = controlLeft;\n    const minY = controlTop - (scaledImgHeight - controlSize.height);\n    const maxY = controlTop;\n    const [x, y] = mat.apply(nextMatrix, [imgLeft, imgTop]);\n    return {\n      x: {\n        position: x,\n        minX,\n        maxX\n      },\n      y: {\n        position: y,\n        minY,\n        maxY\n      }\n    };\n  };\n  /**\n   * Check if is reach the bound\n   */\n  const getReachBound = (position, min, max, buffer = 0) => {\n    return [position <= min - buffer, position >= max + buffer];\n  };\n  /**\n   * Limit the matrix in the bound\n   */\n  const boundMatrix = (nextMatrix, type, last = false) => {\n    if (!controlSize || !imgSize) return nextMatrix;\n    const zoom = mat.getScaleX(nextMatrix);\n    const scaledImgWidth = zoom * imgSize.width;\n    const scaledImgHeight = zoom * imgSize.height;\n    const {\n      x: {\n        position: x,\n        minX,\n        maxX\n      },\n      y: {\n        position: y,\n        minY,\n        maxY\n      }\n    } = getMinAndMax(nextMatrix);\n    if (type === 'translate') {\n      let boundedX = x;\n      let boundedY = y;\n      if (scaledImgWidth > controlSize.width) {\n        boundedX = last ? bound(x, minX, maxX) : rubberbandIfOutOfBounds(x, minX, maxX, zoom * 50);\n      } else {\n        boundedX = -scaledImgWidth / 2;\n      }\n      if (scaledImgHeight > controlSize.height) {\n        boundedY = last ? bound(y, minY, maxY) : rubberbandIfOutOfBounds(y, minY, maxY, zoom * 50);\n      } else {\n        boundedY = -scaledImgHeight / 2;\n      }\n      return mat.translate(nextMatrix, boundedX - x, boundedY - y);\n    }\n    if (type === 'scale' && last) {\n      const [boundedX, boundedY] = [scaledImgWidth > controlSize.width ? bound(x, minX, maxX) : -scaledImgWidth / 2, scaledImgHeight > controlSize.height ? bound(y, minY, maxY) : -scaledImgHeight / 2];\n      return mat.translate(nextMatrix, boundedX - x, boundedY - y);\n    }\n    return nextMatrix;\n  };\n  useDragAndPinch({\n    onDrag: state => {\n      var _a;\n      if (state.first) {\n        const {\n          x: {\n            position: x,\n            minX,\n            maxX\n          }\n        } = getMinAndMax(matrix.get());\n        initialMartix.current = getReachBound(x, minX, maxX);\n        return;\n      }\n      if (state.pinching) return state.cancel();\n      if (state.tap && state.elapsedTime > 0 && state.elapsedTime < 1000) {\n        // 判断点击时间>0是为了过滤掉非正常操作，例如用户长按选择图片之后的取消操作（也是一次点击）\n        (_a = props.onTap) === null || _a === void 0 ? void 0 : _a.call(props);\n        return;\n      }\n      const currentZoom = mat.getScaleX(matrix.get());\n      if (dragLockRef) {\n        dragLockRef.current = currentZoom !== 1;\n      }\n      if (!pinchLockRef.current && currentZoom <= 1) {\n        api.start({\n          matrix: mat.create()\n        });\n      } else {\n        const currentMatrix = matrix.get();\n        const offset = [state.offset[0] - mat.getTranslateX(currentMatrix), state.offset[1] - mat.getTranslateY(currentMatrix)];\n        const nextMatrix = mat.translate(currentMatrix, ...(state.last ? [offset[0] + state.velocity[0] * state.direction[0] * 200, offset[1] + state.velocity[1] * state.direction[1] * 200] : offset));\n        api.start({\n          matrix: boundMatrix(nextMatrix, 'translate', state.last),\n          immediate: !state.last\n        });\n        const {\n          x: {\n            position: x,\n            minX,\n            maxX\n          }\n        } = getMinAndMax(nextMatrix);\n        if (state.last && initialMartix.current.some(i => i) && getReachBound(x, minX, maxX).some(i => i)) {\n          if (dragLockRef) {\n            dragLockRef.current = false;\n          }\n          api.start({\n            matrix: mat.create()\n          });\n        }\n      }\n    },\n    onPinch: state => {\n      var _a;\n      pinchLockRef.current = !state.last;\n      const [d] = state.offset;\n      if (d < 0) return;\n      let mergedMaxZoom;\n      if (maxZoom === 'auto') {\n        mergedMaxZoom = controlSize && imgSize ? Math.max(controlSize.height / imgSize.height, controlSize.width / imgSize.width) : 1;\n      } else {\n        mergedMaxZoom = maxZoom;\n      }\n      const nextZoom = state.last ? bound(d, 1, mergedMaxZoom) : d;\n      (_a = props.onZoomChange) === null || _a === void 0 ? void 0 : _a.call(props, nextZoom);\n      if (state.last && nextZoom <= 1) {\n        api.start({\n          matrix: mat.create()\n        });\n        if (dragLockRef) {\n          dragLockRef.current = false;\n        }\n      } else {\n        if (!controlSize) return;\n        const currentMatrix = matrix.get();\n        const currentZoom = mat.getScaleX(currentMatrix);\n        const originOffsetX = state.origin[0] - controlSize.width / 2;\n        const originOffsetY = state.origin[1] - controlSize.height / 2;\n        let nextMatrix = mat.translate(currentMatrix, -originOffsetX, -originOffsetY);\n        nextMatrix = mat.scale(nextMatrix, nextZoom / currentZoom);\n        nextMatrix = mat.translate(nextMatrix, originOffsetX, originOffsetY);\n        api.start({\n          matrix: boundMatrix(nextMatrix, 'scale', state.last),\n          immediate: !state.last\n        });\n        if (dragLockRef) {\n          dragLockRef.current = true;\n        }\n      }\n    }\n  }, {\n    target: controlRef,\n    drag: {\n      from: () => [mat.getTranslateX(matrix.get()), mat.getTranslateY(matrix.get())],\n      pointer: {\n        touch: true\n      }\n    },\n    pinch: {\n      from: () => [mat.getScaleX(matrix.get()), 0],\n      pointer: {\n        touch: true\n      }\n    }\n  });\n  const customRendering = typeof imageRender === 'function' && imageRender(props.image, {\n    index\n  });\n  return React.createElement(\"div\", {\n    className: `${classPrefix}-slide`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-control`,\n    ref: controlRef\n  }, React.createElement(animated.div, {\n    className: `${classPrefix}-image-wrapper`,\n    style: {\n      matrix\n    }\n  }, customRendering ? customRendering : React.createElement(\"img\", {\n    ref: imgRef,\n    src: props.image,\n    draggable: false,\n    alt: props.image\n  }))));\n};"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,mBAAmB;AACvD,SAASC,OAAO,QAAQ,QAAQ;AAChC,OAAOC,KAAK,IAAIC,MAAM,QAAQ,OAAO;AACrC,SAASC,KAAK,QAAQ,mBAAmB;AACzC,OAAO,KAAKC,GAAG,MAAM,oBAAoB;AACzC,SAASC,uBAAuB,QAAQ,wBAAwB;AAChE,SAASC,eAAe,QAAQ,gCAAgC;AAChE,MAAMC,WAAW,GAAG,kBAAkB;AACtC,OAAO,MAAMC,KAAK,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,WAAW;IACXC,OAAO;IACPC,WAAW;IACXC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMK,aAAa,GAAGZ,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMa,UAAU,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMc,MAAM,GAAGd,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAM,CAAC;IACLe;EACF,CAAC,EAAEC,GAAG,CAAC,GAAGnB,SAAS,CAAC,OAAO;IACzBkB,MAAM,EAAEb,GAAG,CAACe,MAAM,CAAC,CAAC;IACpBC,MAAM,EAAE;MACNC,OAAO,EAAE;IACX;EACF,CAAC,CAAC,CAAC;EACH,MAAMC,WAAW,GAAGtB,OAAO,CAACe,UAAU,CAAC;EACvC,MAAMQ,OAAO,GAAGvB,OAAO,CAACgB,MAAM,CAAC;EAC/B,MAAMQ,YAAY,GAAGtB,MAAM,CAAC,KAAK,CAAC;EAClC;AACF;AACA;EACE,MAAMuB,YAAY,GAAGC,UAAU,IAAI;IACjC,IAAI,CAACJ,WAAW,IAAI,CAACC,OAAO,EAAE,OAAO;MACnCI,CAAC,EAAE;QACDC,QAAQ,EAAE,CAAC;QACXC,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE;MACR,CAAC;MACDC,CAAC,EAAE;QACDH,QAAQ,EAAE,CAAC;QACXI,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE;MACR;IACF,CAAC;IACD,MAAMC,WAAW,GAAG,CAACZ,WAAW,CAACa,KAAK,GAAG,CAAC;IAC1C,MAAMC,UAAU,GAAG,CAACd,WAAW,CAACe,MAAM,GAAG,CAAC;IAC1C,MAAMC,OAAO,GAAG,CAACf,OAAO,CAACY,KAAK,GAAG,CAAC;IAClC,MAAMI,MAAM,GAAG,CAAChB,OAAO,CAACc,MAAM,GAAG,CAAC;IAClC,MAAMG,IAAI,GAAGpC,GAAG,CAACqC,SAAS,CAACf,UAAU,CAAC;IACtC,MAAMgB,cAAc,GAAGF,IAAI,GAAGjB,OAAO,CAACY,KAAK;IAC3C,MAAMQ,eAAe,GAAGH,IAAI,GAAGjB,OAAO,CAACc,MAAM;IAC7C,MAAMR,IAAI,GAAGK,WAAW,IAAIQ,cAAc,GAAGpB,WAAW,CAACa,KAAK,CAAC;IAC/D,MAAML,IAAI,GAAGI,WAAW;IACxB,MAAMF,IAAI,GAAGI,UAAU,IAAIO,eAAe,GAAGrB,WAAW,CAACe,MAAM,CAAC;IAChE,MAAMJ,IAAI,GAAGG,UAAU;IACvB,MAAM,CAACT,CAAC,EAAEI,CAAC,CAAC,GAAG3B,GAAG,CAACwC,KAAK,CAAClB,UAAU,EAAE,CAACY,OAAO,EAAEC,MAAM,CAAC,CAAC;IACvD,OAAO;MACLZ,CAAC,EAAE;QACDC,QAAQ,EAAED,CAAC;QACXE,IAAI;QACJC;MACF,CAAC;MACDC,CAAC,EAAE;QACDH,QAAQ,EAAEG,CAAC;QACXC,IAAI;QACJC;MACF;IACF,CAAC;EACH,CAAC;EACD;AACF;AACA;EACE,MAAMY,aAAa,GAAGA,CAACjB,QAAQ,EAAEkB,GAAG,EAAEC,GAAG,EAAEC,MAAM,GAAG,CAAC,KAAK;IACxD,OAAO,CAACpB,QAAQ,IAAIkB,GAAG,GAAGE,MAAM,EAAEpB,QAAQ,IAAImB,GAAG,GAAGC,MAAM,CAAC;EAC7D,CAAC;EACD;AACF;AACA;EACE,MAAMC,WAAW,GAAGA,CAACvB,UAAU,EAAEwB,IAAI,EAAEC,IAAI,GAAG,KAAK,KAAK;IACtD,IAAI,CAAC7B,WAAW,IAAI,CAACC,OAAO,EAAE,OAAOG,UAAU;IAC/C,MAAMc,IAAI,GAAGpC,GAAG,CAACqC,SAAS,CAACf,UAAU,CAAC;IACtC,MAAMgB,cAAc,GAAGF,IAAI,GAAGjB,OAAO,CAACY,KAAK;IAC3C,MAAMQ,eAAe,GAAGH,IAAI,GAAGjB,OAAO,CAACc,MAAM;IAC7C,MAAM;MACJV,CAAC,EAAE;QACDC,QAAQ,EAAED,CAAC;QACXE,IAAI;QACJC;MACF,CAAC;MACDC,CAAC,EAAE;QACDH,QAAQ,EAAEG,CAAC;QACXC,IAAI;QACJC;MACF;IACF,CAAC,GAAGR,YAAY,CAACC,UAAU,CAAC;IAC5B,IAAIwB,IAAI,KAAK,WAAW,EAAE;MACxB,IAAIE,QAAQ,GAAGzB,CAAC;MAChB,IAAI0B,QAAQ,GAAGtB,CAAC;MAChB,IAAIW,cAAc,GAAGpB,WAAW,CAACa,KAAK,EAAE;QACtCiB,QAAQ,GAAGD,IAAI,GAAGhD,KAAK,CAACwB,CAAC,EAAEE,IAAI,EAAEC,IAAI,CAAC,GAAGzB,uBAAuB,CAACsB,CAAC,EAAEE,IAAI,EAAEC,IAAI,EAAEU,IAAI,GAAG,EAAE,CAAC;MAC5F,CAAC,MAAM;QACLY,QAAQ,GAAG,CAACV,cAAc,GAAG,CAAC;MAChC;MACA,IAAIC,eAAe,GAAGrB,WAAW,CAACe,MAAM,EAAE;QACxCgB,QAAQ,GAAGF,IAAI,GAAGhD,KAAK,CAAC4B,CAAC,EAAEC,IAAI,EAAEC,IAAI,CAAC,GAAG5B,uBAAuB,CAAC0B,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEO,IAAI,GAAG,EAAE,CAAC;MAC5F,CAAC,MAAM;QACLa,QAAQ,GAAG,CAACV,eAAe,GAAG,CAAC;MACjC;MACA,OAAOvC,GAAG,CAACkD,SAAS,CAAC5B,UAAU,EAAE0B,QAAQ,GAAGzB,CAAC,EAAE0B,QAAQ,GAAGtB,CAAC,CAAC;IAC9D;IACA,IAAImB,IAAI,KAAK,OAAO,IAAIC,IAAI,EAAE;MAC5B,MAAM,CAACC,QAAQ,EAAEC,QAAQ,CAAC,GAAG,CAACX,cAAc,GAAGpB,WAAW,CAACa,KAAK,GAAGhC,KAAK,CAACwB,CAAC,EAAEE,IAAI,EAAEC,IAAI,CAAC,GAAG,CAACY,cAAc,GAAG,CAAC,EAAEC,eAAe,GAAGrB,WAAW,CAACe,MAAM,GAAGlC,KAAK,CAAC4B,CAAC,EAAEC,IAAI,EAAEC,IAAI,CAAC,GAAG,CAACU,eAAe,GAAG,CAAC,CAAC;MAClM,OAAOvC,GAAG,CAACkD,SAAS,CAAC5B,UAAU,EAAE0B,QAAQ,GAAGzB,CAAC,EAAE0B,QAAQ,GAAGtB,CAAC,CAAC;IAC9D;IACA,OAAOL,UAAU;EACnB,CAAC;EACDpB,eAAe,CAAC;IACdiD,MAAM,EAAEC,KAAK,IAAI;MACf,IAAIC,EAAE;MACN,IAAID,KAAK,CAACE,KAAK,EAAE;QACf,MAAM;UACJ/B,CAAC,EAAE;YACDC,QAAQ,EAAED,CAAC;YACXE,IAAI;YACJC;UACF;QACF,CAAC,GAAGL,YAAY,CAACR,MAAM,CAAC0C,GAAG,CAAC,CAAC,CAAC;QAC9B7C,aAAa,CAAC8C,OAAO,GAAGf,aAAa,CAAClB,CAAC,EAAEE,IAAI,EAAEC,IAAI,CAAC;QACpD;MACF;MACA,IAAI0B,KAAK,CAACK,QAAQ,EAAE,OAAOL,KAAK,CAACM,MAAM,CAAC,CAAC;MACzC,IAAIN,KAAK,CAACO,GAAG,IAAIP,KAAK,CAACQ,WAAW,GAAG,CAAC,IAAIR,KAAK,CAACQ,WAAW,GAAG,IAAI,EAAE;QAClE;QACA,CAACP,EAAE,GAAGhD,KAAK,CAACwD,KAAK,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,IAAI,CAACzD,KAAK,CAAC;QACtE;MACF;MACA,MAAM0D,WAAW,GAAG/D,GAAG,CAACqC,SAAS,CAACxB,MAAM,CAAC0C,GAAG,CAAC,CAAC,CAAC;MAC/C,IAAIjD,WAAW,EAAE;QACfA,WAAW,CAACkD,OAAO,GAAGO,WAAW,KAAK,CAAC;MACzC;MACA,IAAI,CAAC3C,YAAY,CAACoC,OAAO,IAAIO,WAAW,IAAI,CAAC,EAAE;QAC7CjD,GAAG,CAACkD,KAAK,CAAC;UACRnD,MAAM,EAAEb,GAAG,CAACe,MAAM,CAAC;QACrB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAMkD,aAAa,GAAGpD,MAAM,CAAC0C,GAAG,CAAC,CAAC;QAClC,MAAMW,MAAM,GAAG,CAACd,KAAK,CAACc,MAAM,CAAC,CAAC,CAAC,GAAGlE,GAAG,CAACmE,aAAa,CAACF,aAAa,CAAC,EAAEb,KAAK,CAACc,MAAM,CAAC,CAAC,CAAC,GAAGlE,GAAG,CAACoE,aAAa,CAACH,aAAa,CAAC,CAAC;QACvH,MAAM3C,UAAU,GAAGtB,GAAG,CAACkD,SAAS,CAACe,aAAa,EAAE,IAAIb,KAAK,CAACL,IAAI,GAAG,CAACmB,MAAM,CAAC,CAAC,CAAC,GAAGd,KAAK,CAACiB,QAAQ,CAAC,CAAC,CAAC,GAAGjB,KAAK,CAACkB,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,EAAEJ,MAAM,CAAC,CAAC,CAAC,GAAGd,KAAK,CAACiB,QAAQ,CAAC,CAAC,CAAC,GAAGjB,KAAK,CAACkB,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAGJ,MAAM,CAAC,CAAC;QAChMpD,GAAG,CAACkD,KAAK,CAAC;UACRnD,MAAM,EAAEgC,WAAW,CAACvB,UAAU,EAAE,WAAW,EAAE8B,KAAK,CAACL,IAAI,CAAC;UACxDwB,SAAS,EAAE,CAACnB,KAAK,CAACL;QACpB,CAAC,CAAC;QACF,MAAM;UACJxB,CAAC,EAAE;YACDC,QAAQ,EAAED,CAAC;YACXE,IAAI;YACJC;UACF;QACF,CAAC,GAAGL,YAAY,CAACC,UAAU,CAAC;QAC5B,IAAI8B,KAAK,CAACL,IAAI,IAAIrC,aAAa,CAAC8C,OAAO,CAACgB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC,IAAIhC,aAAa,CAAClB,CAAC,EAAEE,IAAI,EAAEC,IAAI,CAAC,CAAC8C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC,EAAE;UACjG,IAAInE,WAAW,EAAE;YACfA,WAAW,CAACkD,OAAO,GAAG,KAAK;UAC7B;UACA1C,GAAG,CAACkD,KAAK,CAAC;YACRnD,MAAM,EAAEb,GAAG,CAACe,MAAM,CAAC;UACrB,CAAC,CAAC;QACJ;MACF;IACF,CAAC;IACD2D,OAAO,EAAEtB,KAAK,IAAI;MAChB,IAAIC,EAAE;MACNjC,YAAY,CAACoC,OAAO,GAAG,CAACJ,KAAK,CAACL,IAAI;MAClC,MAAM,CAAC4B,CAAC,CAAC,GAAGvB,KAAK,CAACc,MAAM;MACxB,IAAIS,CAAC,GAAG,CAAC,EAAE;MACX,IAAIC,aAAa;MACjB,IAAIrE,OAAO,KAAK,MAAM,EAAE;QACtBqE,aAAa,GAAG1D,WAAW,IAAIC,OAAO,GAAG0D,IAAI,CAAClC,GAAG,CAACzB,WAAW,CAACe,MAAM,GAAGd,OAAO,CAACc,MAAM,EAAEf,WAAW,CAACa,KAAK,GAAGZ,OAAO,CAACY,KAAK,CAAC,GAAG,CAAC;MAC/H,CAAC,MAAM;QACL6C,aAAa,GAAGrE,OAAO;MACzB;MACA,MAAMuE,QAAQ,GAAG1B,KAAK,CAACL,IAAI,GAAGhD,KAAK,CAAC4E,CAAC,EAAE,CAAC,EAAEC,aAAa,CAAC,GAAGD,CAAC;MAC5D,CAACtB,EAAE,GAAGhD,KAAK,CAAC0E,YAAY,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,IAAI,CAACzD,KAAK,EAAEyE,QAAQ,CAAC;MACvF,IAAI1B,KAAK,CAACL,IAAI,IAAI+B,QAAQ,IAAI,CAAC,EAAE;QAC/BhE,GAAG,CAACkD,KAAK,CAAC;UACRnD,MAAM,EAAEb,GAAG,CAACe,MAAM,CAAC;QACrB,CAAC,CAAC;QACF,IAAIT,WAAW,EAAE;UACfA,WAAW,CAACkD,OAAO,GAAG,KAAK;QAC7B;MACF,CAAC,MAAM;QACL,IAAI,CAACtC,WAAW,EAAE;QAClB,MAAM+C,aAAa,GAAGpD,MAAM,CAAC0C,GAAG,CAAC,CAAC;QAClC,MAAMQ,WAAW,GAAG/D,GAAG,CAACqC,SAAS,CAAC4B,aAAa,CAAC;QAChD,MAAMe,aAAa,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,CAAC,CAAC,GAAG/D,WAAW,CAACa,KAAK,GAAG,CAAC;QAC7D,MAAMmD,aAAa,GAAG9B,KAAK,CAAC6B,MAAM,CAAC,CAAC,CAAC,GAAG/D,WAAW,CAACe,MAAM,GAAG,CAAC;QAC9D,IAAIX,UAAU,GAAGtB,GAAG,CAACkD,SAAS,CAACe,aAAa,EAAE,CAACe,aAAa,EAAE,CAACE,aAAa,CAAC;QAC7E5D,UAAU,GAAGtB,GAAG,CAACmF,KAAK,CAAC7D,UAAU,EAAEwD,QAAQ,GAAGf,WAAW,CAAC;QAC1DzC,UAAU,GAAGtB,GAAG,CAACkD,SAAS,CAAC5B,UAAU,EAAE0D,aAAa,EAAEE,aAAa,CAAC;QACpEpE,GAAG,CAACkD,KAAK,CAAC;UACRnD,MAAM,EAAEgC,WAAW,CAACvB,UAAU,EAAE,OAAO,EAAE8B,KAAK,CAACL,IAAI,CAAC;UACpDwB,SAAS,EAAE,CAACnB,KAAK,CAACL;QACpB,CAAC,CAAC;QACF,IAAIzC,WAAW,EAAE;UACfA,WAAW,CAACkD,OAAO,GAAG,IAAI;QAC5B;MACF;IACF;EACF,CAAC,EAAE;IACD4B,MAAM,EAAEzE,UAAU;IAClB0E,IAAI,EAAE;MACJC,IAAI,EAAEA,CAAA,KAAM,CAACtF,GAAG,CAACmE,aAAa,CAACtD,MAAM,CAAC0C,GAAG,CAAC,CAAC,CAAC,EAAEvD,GAAG,CAACoE,aAAa,CAACvD,MAAM,CAAC0C,GAAG,CAAC,CAAC,CAAC,CAAC;MAC9EgC,OAAO,EAAE;QACPC,KAAK,EAAE;MACT;IACF,CAAC;IACDC,KAAK,EAAE;MACLH,IAAI,EAAEA,CAAA,KAAM,CAACtF,GAAG,CAACqC,SAAS,CAACxB,MAAM,CAAC0C,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC5CgC,OAAO,EAAE;QACPC,KAAK,EAAE;MACT;IACF;EACF,CAAC,CAAC;EACF,MAAME,eAAe,GAAG,OAAOlF,WAAW,KAAK,UAAU,IAAIA,WAAW,CAACH,KAAK,CAACsF,KAAK,EAAE;IACpFlF;EACF,CAAC,CAAC;EACF,OAAOZ,KAAK,CAAC+F,aAAa,CAAC,KAAK,EAAE;IAChCC,SAAS,EAAE,GAAG1F,WAAW;EAC3B,CAAC,EAAEN,KAAK,CAAC+F,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAG1F,WAAW,UAAU;IACnC2F,GAAG,EAAEnF;EACP,CAAC,EAAEd,KAAK,CAAC+F,aAAa,CAAClG,QAAQ,CAACqG,GAAG,EAAE;IACnCF,SAAS,EAAE,GAAG1F,WAAW,gBAAgB;IACzC6F,KAAK,EAAE;MACLnF;IACF;EACF,CAAC,EAAE6E,eAAe,GAAGA,eAAe,GAAG7F,KAAK,CAAC+F,aAAa,CAAC,KAAK,EAAE;IAChEE,GAAG,EAAElF,MAAM;IACXqF,GAAG,EAAE5F,KAAK,CAACsF,KAAK;IAChBO,SAAS,EAAE,KAAK;IAChBC,GAAG,EAAE9F,KAAK,CAACsF;EACb,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}