{"ast": null, "code": "import * as React from \"react\";\nfunction CouponOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CouponOutline-CouponOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CouponOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CouponOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12.6301701,6 C12.8364017,6.00011887 13.0088079,6.15695715 13.0282672,6.36226864 C13.0539026,6.63274323 13.0859188,6.84310873 13.1243159,6.99336513 C13.5660468,8.72195787 15.1337721,10 17,10 C18.8564018,10 20.417436,8.7353808 20.8686131,7.02063183 C20.909694,6.86449958 20.9438738,6.64481961 20.9711526,6.36159191 C20.9909643,6.15651771 21.1632812,6 21.3693101,6 L39,6 L39,6 C42.3137085,6 45,8.6862915 45,12 L45,36 C45,39.3137085 42.3137085,42 39,42 L21.3741275,42 L21.3741275,42 C21.1663728,42.0001191 20.9931805,41.8411155 20.9754657,41.6341174 C20.9592307,41.4444114 20.9394963,41.2936642 20.9162626,41.1818758 C20.5386744,39.365121 18.9287577,38 17,38 C15.0919698,38 13.4959646,39.335938 13.0963366,41.1234617 C13.0684387,41.248248 13.0449136,41.4188764 13.0257614,41.6353468 C13.0074064,41.841735 12.8345206,42 12.6273178,42 L9,42 L9,42 C5.6862915,42 3,39.3137085 3,36 L3,12 C3,8.6862915 5.6862915,6 9,6 L12.6301701,6 L12.6301701,6 Z M10.674,9 L9,9 C7.40231912,9 6.09633912,10.24892 6.00509269,11.8237272 L6,12 L6,36 C6,37.5976809 7.24891996,38.9036609 8.82372721,38.9949073 L9,39 L10.674,39 L10.7402259,38.8636637 C11.8566105,36.6398332 14.123165,35.0931185 16.7593502,35.004059 L17,35 C19.7384119,35 22.1095597,36.5724444 23.2597741,38.8636637 L23.325,39 L39,39 C40.5976809,39 41.9036609,37.75108 41.9949073,36.1762728 L42,36 L42,12 C42,10.4023191 40.75108,9.09633912 39.1762728,9.00509269 L39,9 L23.325,9 L23.2597741,9.1363363 C22.1095597,11.4275556 19.7384119,13 17,13 C14.2615881,13 11.8904403,11.4275556 10.7402259,9.1363363 L10.674,9 Z M17.6,18 C17.8209139,18 18,18.1790861 18,18.4 L18,29.6 C18,29.8209139 17.8209139,30 17.6,30 L15.4,30 C15.1790861,30 15,29.8209139 15,29.6 L15,18.4 C15,18.1790861 15.1790861,18 15.4,18 L17.6,18 Z\",\n    id: \"CouponOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default CouponOutline;", "map": {"version": 3, "names": ["React", "CouponOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/CouponOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction CouponOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CouponOutline-CouponOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CouponOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CouponOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12.6301701,6 C12.8364017,6.00011887 13.0088079,6.15695715 13.0282672,6.36226864 C13.0539026,6.63274323 13.0859188,6.84310873 13.1243159,6.99336513 C13.5660468,8.72195787 15.1337721,10 17,10 C18.8564018,10 20.417436,8.7353808 20.8686131,7.02063183 C20.909694,6.86449958 20.9438738,6.64481961 20.9711526,6.36159191 C20.9909643,6.15651771 21.1632812,6 21.3693101,6 L39,6 L39,6 C42.3137085,6 45,8.6862915 45,12 L45,36 C45,39.3137085 42.3137085,42 39,42 L21.3741275,42 L21.3741275,42 C21.1663728,42.0001191 20.9931805,41.8411155 20.9754657,41.6341174 C20.9592307,41.4444114 20.9394963,41.2936642 20.9162626,41.1818758 C20.5386744,39.365121 18.9287577,38 17,38 C15.0919698,38 13.4959646,39.335938 13.0963366,41.1234617 C13.0684387,41.248248 13.0449136,41.4188764 13.0257614,41.6353468 C13.0074064,41.841735 12.8345206,42 12.6273178,42 L9,42 L9,42 C5.6862915,42 3,39.3137085 3,36 L3,12 C3,8.6862915 5.6862915,6 9,6 L12.6301701,6 L12.6301701,6 Z M10.674,9 L9,9 C7.40231912,9 6.09633912,10.24892 6.00509269,11.8237272 L6,12 L6,36 C6,37.5976809 7.24891996,38.9036609 8.82372721,38.9949073 L9,39 L10.674,39 L10.7402259,38.8636637 C11.8566105,36.6398332 14.123165,35.0931185 16.7593502,35.004059 L17,35 C19.7384119,35 22.1095597,36.5724444 23.2597741,38.8636637 L23.325,39 L39,39 C40.5976809,39 41.9036609,37.75108 41.9949073,36.1762728 L42,36 L42,12 C42,10.4023191 40.75108,9.09633912 39.1762728,9.00509269 L39,9 L23.325,9 L23.2597741,9.1363363 C22.1095597,11.4275556 19.7384119,13 17,13 C14.2615881,13 11.8904403,11.4275556 10.7402259,9.1363363 L10.674,9 Z M17.6,18 C17.8209139,18 18,18.1790861 18,18.4 L18,29.6 C18,29.8209139 17.8209139,30 17.6,30 L15.4,30 C15.1790861,30 15,29.8209139 15,29.6 L15,18.4 C15,18.1790861 15.1790861,18 15.4,18 L17.6,18 Z\",\n    id: \"CouponOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default CouponOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,6BAA6B;IACjCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,gtDAAgtD;IACntDR,EAAE,EAAE,wCAAwC;IAC5CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}