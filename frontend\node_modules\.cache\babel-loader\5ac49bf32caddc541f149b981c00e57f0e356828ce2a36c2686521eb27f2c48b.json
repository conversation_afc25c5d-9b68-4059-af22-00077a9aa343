{"ast": null, "code": "/**\n * 数组打乱\n * @param array 任意数组\n * @returns any[] 打乱后的数组\n */\nexport function shuffle(array) {\n  const result = [...array];\n  for (let i = result.length; i > 0; i--) {\n    const j = Math.floor(Math.random() * i);\n    [result[i - 1], result[j]] = [result[j], result[i - 1]];\n  }\n  return result;\n}", "map": {"version": 3, "names": ["shuffle", "array", "result", "i", "length", "j", "Math", "floor", "random"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/shuffle.js"], "sourcesContent": ["/**\n * 数组打乱\n * @param array 任意数组\n * @returns any[] 打乱后的数组\n */\nexport function shuffle(array) {\n  const result = [...array];\n  for (let i = result.length; i > 0; i--) {\n    const j = Math.floor(Math.random() * i);\n    [result[i - 1], result[j]] = [result[j], result[i - 1]];\n  }\n  return result;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAACC,KAAK,EAAE;EAC7B,MAAMC,MAAM,GAAG,CAAC,GAAGD,KAAK,CAAC;EACzB,KAAK,IAAIE,CAAC,GAAGD,MAAM,CAACE,MAAM,EAAED,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IACtC,MAAME,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGL,CAAC,CAAC;IACvC,CAACD,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,EAAED,MAAM,CAACG,CAAC,CAAC,CAAC,GAAG,CAACH,MAAM,CAACG,CAAC,CAAC,EAAEH,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,CAAC;EACzD;EACA,OAAOD,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}