{"ast": null, "code": "import React, { memo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useSpring, animated } from '@react-spring/web';\nimport { useMotionReduced } from '../../utils/reduce-and-restore-motion';\nconst classPrefix = 'adm-spin-loading';\nconst colorRecord = {\n  default: 'var(--adm-color-weak)',\n  primary: 'var(--adm-color-primary)',\n  white: 'var(--adm-color-white)'\n};\nconst defaultProps = {\n  color: 'default'\n};\nconst circumference = 15 * 3.14159265358979 * 2;\nexport const SpinLoading = memo(p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  const motionReduced = useMotionReduced();\n  const {\n    percent\n  } = useSpring({\n    cancel: motionReduced,\n    loop: {\n      reverse: true\n    },\n    from: {\n      percent: 80\n    },\n    to: {\n      percent: 30\n    },\n    config: {\n      duration: 1200\n    }\n  });\n  return withNativeProps(props, React.createElement(animated.div, {\n    className: classPrefix,\n    style: {\n      '--color': (_a = colorRecord[props.color]) !== null && _a !== void 0 ? _a : props.color,\n      '--percent': percent\n    }\n  }, React.createElement(\"svg\", {\n    className: `${classPrefix}-svg`,\n    viewBox: '0 0 32 32'\n  }, React.createElement(animated.circle, {\n    className: `${classPrefix}-fill`,\n    fill: 'transparent',\n    strokeWidth: '2',\n    strokeDasharray: circumference,\n    strokeDashoffset: percent,\n    strokeLinecap: 'square',\n    r: 15,\n    cx: 16,\n    cy: 16\n  }))));\n});", "map": {"version": 3, "names": ["React", "memo", "withNativeProps", "mergeProps", "useSpring", "animated", "useMotionReduced", "classPrefix", "colorRecord", "default", "primary", "white", "defaultProps", "color", "circumference", "SpinLoading", "p", "_a", "props", "motionReduced", "percent", "cancel", "loop", "reverse", "from", "to", "config", "duration", "createElement", "div", "className", "style", "viewBox", "circle", "fill", "strokeWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "strokeLinecap", "r", "cx", "cy"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/spin-loading/spin-loading.js"], "sourcesContent": ["import React, { memo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useSpring, animated } from '@react-spring/web';\nimport { useMotionReduced } from '../../utils/reduce-and-restore-motion';\nconst classPrefix = 'adm-spin-loading';\nconst colorRecord = {\n  default: 'var(--adm-color-weak)',\n  primary: 'var(--adm-color-primary)',\n  white: 'var(--adm-color-white)'\n};\nconst defaultProps = {\n  color: 'default'\n};\nconst circumference = 15 * 3.14159265358979 * 2;\nexport const SpinLoading = memo(p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  const motionReduced = useMotionReduced();\n  const {\n    percent\n  } = useSpring({\n    cancel: motionReduced,\n    loop: {\n      reverse: true\n    },\n    from: {\n      percent: 80\n    },\n    to: {\n      percent: 30\n    },\n    config: {\n      duration: 1200\n    }\n  });\n  return withNativeProps(props, React.createElement(animated.div, {\n    className: classPrefix,\n    style: {\n      '--color': (_a = colorRecord[props.color]) !== null && _a !== void 0 ? _a : props.color,\n      '--percent': percent\n    }\n  }, React.createElement(\"svg\", {\n    className: `${classPrefix}-svg`,\n    viewBox: '0 0 32 32'\n  }, React.createElement(animated.circle, {\n    className: `${classPrefix}-fill`,\n    fill: 'transparent',\n    strokeWidth: '2',\n    strokeDasharray: circumference,\n    strokeDashoffset: percent,\n    strokeLinecap: 'square',\n    r: 15,\n    cx: 16,\n    cy: 16\n  }))));\n});"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,IAAI,QAAQ,OAAO;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,SAAS,EAAEC,QAAQ,QAAQ,mBAAmB;AACvD,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,MAAMC,WAAW,GAAG,kBAAkB;AACtC,MAAMC,WAAW,GAAG;EAClBC,OAAO,EAAE,uBAAuB;EAChCC,OAAO,EAAE,0BAA0B;EACnCC,KAAK,EAAE;AACT,CAAC;AACD,MAAMC,YAAY,GAAG;EACnBC,KAAK,EAAE;AACT,CAAC;AACD,MAAMC,aAAa,GAAG,EAAE,GAAG,gBAAgB,GAAG,CAAC;AAC/C,OAAO,MAAMC,WAAW,GAAGd,IAAI,CAACe,CAAC,IAAI;EACnC,IAAIC,EAAE;EACN,MAAMC,KAAK,GAAGf,UAAU,CAACS,YAAY,EAAEI,CAAC,CAAC;EACzC,MAAMG,aAAa,GAAGb,gBAAgB,CAAC,CAAC;EACxC,MAAM;IACJc;EACF,CAAC,GAAGhB,SAAS,CAAC;IACZiB,MAAM,EAAEF,aAAa;IACrBG,IAAI,EAAE;MACJC,OAAO,EAAE;IACX,CAAC;IACDC,IAAI,EAAE;MACJJ,OAAO,EAAE;IACX,CAAC;IACDK,EAAE,EAAE;MACFL,OAAO,EAAE;IACX,CAAC;IACDM,MAAM,EAAE;MACNC,QAAQ,EAAE;IACZ;EACF,CAAC,CAAC;EACF,OAAOzB,eAAe,CAACgB,KAAK,EAAElB,KAAK,CAAC4B,aAAa,CAACvB,QAAQ,CAACwB,GAAG,EAAE;IAC9DC,SAAS,EAAEvB,WAAW;IACtBwB,KAAK,EAAE;MACL,SAAS,EAAE,CAACd,EAAE,GAAGT,WAAW,CAACU,KAAK,CAACL,KAAK,CAAC,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGC,KAAK,CAACL,KAAK;MACvF,WAAW,EAAEO;IACf;EACF,CAAC,EAAEpB,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IAC5BE,SAAS,EAAE,GAAGvB,WAAW,MAAM;IAC/ByB,OAAO,EAAE;EACX,CAAC,EAAEhC,KAAK,CAAC4B,aAAa,CAACvB,QAAQ,CAAC4B,MAAM,EAAE;IACtCH,SAAS,EAAE,GAAGvB,WAAW,OAAO;IAChC2B,IAAI,EAAE,aAAa;IACnBC,WAAW,EAAE,GAAG;IAChBC,eAAe,EAAEtB,aAAa;IAC9BuB,gBAAgB,EAAEjB,OAAO;IACzBkB,aAAa,EAAE,QAAQ;IACvBC,CAAC,EAAE,EAAE;IACLC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}