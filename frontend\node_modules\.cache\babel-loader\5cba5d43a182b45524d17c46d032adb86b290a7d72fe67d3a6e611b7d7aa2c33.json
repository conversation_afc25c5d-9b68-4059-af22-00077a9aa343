{"ast": null, "code": "import { useInitialized } from './use-initialized';\nexport const ShouldRender = props => {\n  const shouldRender = useShouldRender(props.active, props.forceRender, props.destroyOnClose);\n  return shouldRender ? props.children : null;\n};\nexport function useShouldRender(active, forceRender, destroyOnClose) {\n  const initialized = useInitialized(active);\n  if (forceRender) return true;\n  if (active) return true;\n  if (!initialized) return false;\n  return !destroyOnClose;\n}", "map": {"version": 3, "names": ["useInitialized", "ShouldRender", "props", "shouldRender", "useShouldRender", "active", "forceRender", "destroyOnClose", "children", "initialized"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/should-render.js"], "sourcesContent": ["import { useInitialized } from './use-initialized';\nexport const ShouldRender = props => {\n  const shouldRender = useShouldRender(props.active, props.forceRender, props.destroyOnClose);\n  return shouldRender ? props.children : null;\n};\nexport function useShouldRender(active, forceRender, destroyOnClose) {\n  const initialized = useInitialized(active);\n  if (forceRender) return true;\n  if (active) return true;\n  if (!initialized) return false;\n  return !destroyOnClose;\n}"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mBAAmB;AAClD,OAAO,MAAMC,YAAY,GAAGC,KAAK,IAAI;EACnC,MAAMC,YAAY,GAAGC,eAAe,CAACF,KAAK,CAACG,MAAM,EAAEH,KAAK,CAACI,WAAW,EAAEJ,KAAK,CAACK,cAAc,CAAC;EAC3F,OAAOJ,YAAY,GAAGD,KAAK,CAACM,QAAQ,GAAG,IAAI;AAC7C,CAAC;AACD,OAAO,SAASJ,eAAeA,CAACC,MAAM,EAAEC,WAAW,EAAEC,cAAc,EAAE;EACnE,MAAME,WAAW,GAAGT,cAAc,CAACK,MAAM,CAAC;EAC1C,IAAIC,WAAW,EAAE,OAAO,IAAI;EAC5B,IAAID,MAAM,EAAE,OAAO,IAAI;EACvB,IAAI,CAACI,WAAW,EAAE,OAAO,KAAK;EAC9B,OAAO,CAACF,cAAc;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}