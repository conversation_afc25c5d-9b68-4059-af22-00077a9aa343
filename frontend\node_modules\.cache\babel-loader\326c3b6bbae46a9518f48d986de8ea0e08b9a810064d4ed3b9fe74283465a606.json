{"ast": null, "code": "import { createContext } from 'react';\nexport const CheckboxGroupContext = createContext(null);", "map": {"version": 3, "names": ["createContext", "CheckboxGroupContext"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/checkbox/group-context.js"], "sourcesContent": ["import { createContext } from 'react';\nexport const CheckboxGroupContext = createContext(null);"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,OAAO,MAAMC,oBAAoB,GAAGD,aAAa,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}