{"ast": null, "code": "import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nconst classPrefix = 'adm-safe-area';\nexport const SafeArea = props => {\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, `${classPrefix}-position-${props.position}`)\n  }));\n};", "map": {"version": 3, "names": ["React", "withNativeProps", "classNames", "classPrefix", "SafeArea", "props", "createElement", "className", "position"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/safe-area/safe-area.js"], "sourcesContent": ["import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nconst classPrefix = 'adm-safe-area';\nexport const SafeArea = props => {\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, `${classPrefix}-position-${props.position}`)\n  }));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,UAAU,MAAM,YAAY;AACnC,MAAMC,WAAW,GAAG,eAAe;AACnC,OAAO,MAAMC,QAAQ,GAAGC,KAAK,IAAI;EAC/B,OAAOJ,eAAe,CAACI,KAAK,EAAEL,KAAK,CAACM,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEL,UAAU,CAACC,WAAW,EAAE,GAAGA,WAAW,aAAaE,KAAK,CAACG,QAAQ,EAAE;EAChF,CAAC,CAAC,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}