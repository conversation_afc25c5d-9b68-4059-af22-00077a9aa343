{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useResultIcon } from './use-result-icon';\nconst classPrefix = `adm-result`;\nconst defaultProps = {\n  status: 'info'\n};\nexport const Result = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    status,\n    title,\n    description,\n    icon\n  } = props;\n  const fallbackIcon = useResultIcon(status);\n  if (!status) return null;\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, `${classPrefix}-${status}`)\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-icon`\n  }, icon || fallbackIcon), React.createElement(\"div\", {\n    className: `${classPrefix}-title`\n  }, title), !!description && React.createElement(\"div\", {\n    className: `${classPrefix}-description`\n  }, description)));\n};", "map": {"version": 3, "names": ["React", "classNames", "withNativeProps", "mergeProps", "useResultIcon", "classPrefix", "defaultProps", "status", "Result", "p", "props", "title", "description", "icon", "fallbackIcon", "createElement", "className"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/result/result.js"], "sourcesContent": ["import React from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useResultIcon } from './use-result-icon';\nconst classPrefix = `adm-result`;\nconst defaultProps = {\n  status: 'info'\n};\nexport const Result = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    status,\n    title,\n    description,\n    icon\n  } = props;\n  const fallbackIcon = useResultIcon(status);\n  if (!status) return null;\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, `${classPrefix}-${status}`)\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-icon`\n  }, icon || fallbackIcon), React.createElement(\"div\", {\n    className: `${classPrefix}-title`\n  }, title), !!description && React.createElement(\"div\", {\n    className: `${classPrefix}-description`\n  }, description)));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,aAAa,QAAQ,mBAAmB;AACjD,MAAMC,WAAW,GAAG,YAAY;AAChC,MAAMC,YAAY,GAAG;EACnBC,MAAM,EAAE;AACV,CAAC;AACD,OAAO,MAAMC,MAAM,GAAGC,CAAC,IAAI;EACzB,MAAMC,KAAK,GAAGP,UAAU,CAACG,YAAY,EAAEG,CAAC,CAAC;EACzC,MAAM;IACJF,MAAM;IACNI,KAAK;IACLC,WAAW;IACXC;EACF,CAAC,GAAGH,KAAK;EACT,MAAMI,YAAY,GAAGV,aAAa,CAACG,MAAM,CAAC;EAC1C,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;EACxB,OAAOL,eAAe,CAACQ,KAAK,EAAEV,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEf,UAAU,CAACI,WAAW,EAAE,GAAGA,WAAW,IAAIE,MAAM,EAAE;EAC/D,CAAC,EAAEP,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGX,WAAW;EAC3B,CAAC,EAAEQ,IAAI,IAAIC,YAAY,CAAC,EAAEd,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IACnDC,SAAS,EAAE,GAAGX,WAAW;EAC3B,CAAC,EAAEM,KAAK,CAAC,EAAE,CAAC,CAACC,WAAW,IAAIZ,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IACrDC,SAAS,EAAE,GAAGX,WAAW;EAC3B,CAAC,EAAEO,WAAW,CAAC,CAAC,CAAC;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}