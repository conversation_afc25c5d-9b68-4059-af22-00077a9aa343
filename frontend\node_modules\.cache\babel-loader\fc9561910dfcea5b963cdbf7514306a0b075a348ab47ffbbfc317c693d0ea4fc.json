{"ast": null, "code": "import { useIsomorphicLayoutEffect, useMemoizedFn } from 'ahooks';\nexport function useResizeEffect(effect, targetRef) {\n  const fn = useMemoizedFn(effect);\n  useIsomorphicLayoutEffect(() => {\n    const target = targetRef.current;\n    if (!target) return;\n    if (window.ResizeObserver) {\n      let animationFrame;\n      const observer = new ResizeObserver(() => {\n        animationFrame = window.requestAnimationFrame(() => fn(target));\n      });\n      observer.observe(target);\n      return () => {\n        window.cancelAnimationFrame(animationFrame);\n        observer.disconnect();\n      };\n    } else {\n      fn(target);\n    }\n  }, [targetRef]);\n}", "map": {"version": 3, "names": ["useIsomorphicLayoutEffect", "useMemoizedFn", "useResizeEffect", "effect", "targetRef", "fn", "target", "current", "window", "ResizeObserver", "animationFrame", "observer", "requestAnimationFrame", "observe", "cancelAnimationFrame", "disconnect"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/use-resize-effect.js"], "sourcesContent": ["import { useIsomorphicLayoutEffect, useMemoizedFn } from 'ahooks';\nexport function useResizeEffect(effect, targetRef) {\n  const fn = useMemoizedFn(effect);\n  useIsomorphicLayoutEffect(() => {\n    const target = targetRef.current;\n    if (!target) return;\n    if (window.ResizeObserver) {\n      let animationFrame;\n      const observer = new ResizeObserver(() => {\n        animationFrame = window.requestAnimationFrame(() => fn(target));\n      });\n      observer.observe(target);\n      return () => {\n        window.cancelAnimationFrame(animationFrame);\n        observer.disconnect();\n      };\n    } else {\n      fn(target);\n    }\n  }, [targetRef]);\n}"], "mappings": "AAAA,SAASA,yBAAyB,EAAEC,aAAa,QAAQ,QAAQ;AACjE,OAAO,SAASC,eAAeA,CAACC,MAAM,EAAEC,SAAS,EAAE;EACjD,MAAMC,EAAE,GAAGJ,aAAa,CAACE,MAAM,CAAC;EAChCH,yBAAyB,CAAC,MAAM;IAC9B,MAAMM,MAAM,GAAGF,SAAS,CAACG,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;IACb,IAAIE,MAAM,CAACC,cAAc,EAAE;MACzB,IAAIC,cAAc;MAClB,MAAMC,QAAQ,GAAG,IAAIF,cAAc,CAAC,MAAM;QACxCC,cAAc,GAAGF,MAAM,CAACI,qBAAqB,CAAC,MAAMP,EAAE,CAACC,MAAM,CAAC,CAAC;MACjE,CAAC,CAAC;MACFK,QAAQ,CAACE,OAAO,CAACP,MAAM,CAAC;MACxB,OAAO,MAAM;QACXE,MAAM,CAACM,oBAAoB,CAACJ,cAAc,CAAC;QAC3CC,QAAQ,CAACI,UAAU,CAAC,CAAC;MACvB,CAAC;IACH,CAAC,MAAM;MACLV,EAAE,CAACC,MAAM,CAAC;IACZ;EACF,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}