{"ast": null, "code": "import React, { forwardRef, useCallback, useImperative<PERSON>andle, useMemo, useRef } from 'react';\nimport classNames from 'classnames';\nimport { Popover } from './popover';\nconst classPrefix = `adm-popover-menu`;\nexport const PopoverMenu = forwardRef((props, ref) => {\n  const innerRef = useRef(null);\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  useImperativeHandle(ref, () => innerRef.current, []);\n  const onClick = useCallback(e => {\n    var _a;\n    const {\n      onAction\n    } = props;\n    if (onAction) {\n      onAction(e);\n    }\n    (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.hide();\n  }, [props.onAction]);\n  const overlay = useMemo(() => {\n    const whetherScroll = (props === null || props === void 0 ? void 0 : props.maxCount) && props.actions.length > (props === null || props === void 0 ? void 0 : props.maxCount);\n    const innerHeight = (props === null || props === void 0 ? void 0 : props.maxCount) && (props === null || props === void 0 ? void 0 : props.maxCount) * 48;\n    return React.createElement(\"div\", {\n      className: `${classPrefix}-list`\n    }, React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-list-inner`, {\n        [`${classPrefix}-list-scroll`]: whetherScroll\n      }),\n      style: {\n        height: innerHeight\n      }\n    }, props.actions.map((action, index) => {\n      var _a;\n      return React.createElement(\"a\", {\n        key: (_a = action.key) !== null && _a !== void 0 ? _a : index,\n        className: classNames(`${classPrefix}-item`, 'adm-plain-anchor', {\n          [`${classPrefix}-item-disabled`]: action.disabled\n        }),\n        onClick: () => {\n          var _a;\n          if (action.disabled) return;\n          onClick(action);\n          (_a = action.onClick) === null || _a === void 0 ? void 0 : _a.call(action);\n        }\n      }, action.icon && React.createElement(\"div\", {\n        className: `${classPrefix}-item-icon`\n      }, action.icon), React.createElement(\"div\", {\n        className: `${classPrefix}-item-text`\n      }, action.text));\n    })));\n  }, [props.actions, onClick]);\n  return React.createElement(Popover, Object.assign({\n    ref: innerRef\n  }, props, {\n    className: classNames(classPrefix, props.className),\n    content: overlay\n  }), props.children);\n});", "map": {"version": 3, "names": ["React", "forwardRef", "useCallback", "useImperativeHandle", "useMemo", "useRef", "classNames", "Popover", "classPrefix", "PopoverMenu", "props", "ref", "innerRef", "current", "onClick", "e", "_a", "onAction", "hide", "overlay", "whetherScroll", "maxCount", "actions", "length", "innerHeight", "createElement", "className", "style", "height", "map", "action", "index", "key", "disabled", "call", "icon", "text", "Object", "assign", "content", "children"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/popover/popover-menu.js"], "sourcesContent": ["import React, { forwardRef, useCallback, useImperative<PERSON>andle, useMemo, useRef } from 'react';\nimport classNames from 'classnames';\nimport { Popover } from './popover';\nconst classPrefix = `adm-popover-menu`;\nexport const PopoverMenu = forwardRef((props, ref) => {\n  const innerRef = useRef(null);\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  useImperativeHandle(ref, () => innerRef.current, []);\n  const onClick = useCallback(e => {\n    var _a;\n    const {\n      onAction\n    } = props;\n    if (onAction) {\n      onAction(e);\n    }\n    (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.hide();\n  }, [props.onAction]);\n  const overlay = useMemo(() => {\n    const whetherScroll = (props === null || props === void 0 ? void 0 : props.maxCount) && props.actions.length > (props === null || props === void 0 ? void 0 : props.maxCount);\n    const innerHeight = (props === null || props === void 0 ? void 0 : props.maxCount) && (props === null || props === void 0 ? void 0 : props.maxCount) * 48;\n    return React.createElement(\"div\", {\n      className: `${classPrefix}-list`\n    }, React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-list-inner`, {\n        [`${classPrefix}-list-scroll`]: whetherScroll\n      }),\n      style: {\n        height: innerHeight\n      }\n    }, props.actions.map((action, index) => {\n      var _a;\n      return React.createElement(\"a\", {\n        key: (_a = action.key) !== null && _a !== void 0 ? _a : index,\n        className: classNames(`${classPrefix}-item`, 'adm-plain-anchor', {\n          [`${classPrefix}-item-disabled`]: action.disabled\n        }),\n        onClick: () => {\n          var _a;\n          if (action.disabled) return;\n          onClick(action);\n          (_a = action.onClick) === null || _a === void 0 ? void 0 : _a.call(action);\n        }\n      }, action.icon && React.createElement(\"div\", {\n        className: `${classPrefix}-item-icon`\n      }, action.icon), React.createElement(\"div\", {\n        className: `${classPrefix}-item-text`\n      }, action.text));\n    })));\n  }, [props.actions, onClick]);\n  return React.createElement(Popover, Object.assign({\n    ref: innerRef\n  }, props, {\n    className: classNames(classPrefix, props.className),\n    content: overlay\n  }), props.children);\n});"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AAC5F,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,OAAO,QAAQ,WAAW;AACnC,MAAMC,WAAW,GAAG,kBAAkB;AACtC,OAAO,MAAMC,WAAW,GAAGR,UAAU,CAAC,CAACS,KAAK,EAAEC,GAAG,KAAK;EACpD,MAAMC,QAAQ,GAAGP,MAAM,CAAC,IAAI,CAAC;EAC7B;EACAF,mBAAmB,CAACQ,GAAG,EAAE,MAAMC,QAAQ,CAACC,OAAO,EAAE,EAAE,CAAC;EACpD,MAAMC,OAAO,GAAGZ,WAAW,CAACa,CAAC,IAAI;IAC/B,IAAIC,EAAE;IACN,MAAM;MACJC;IACF,CAAC,GAAGP,KAAK;IACT,IAAIO,QAAQ,EAAE;MACZA,QAAQ,CAACF,CAAC,CAAC;IACb;IACA,CAACC,EAAE,GAAGJ,QAAQ,CAACC,OAAO,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAAC,CAAC;EACxE,CAAC,EAAE,CAACR,KAAK,CAACO,QAAQ,CAAC,CAAC;EACpB,MAAME,OAAO,GAAGf,OAAO,CAAC,MAAM;IAC5B,MAAMgB,aAAa,GAAG,CAACV,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACW,QAAQ,KAAKX,KAAK,CAACY,OAAO,CAACC,MAAM,IAAIb,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACW,QAAQ,CAAC;IAC7K,MAAMG,WAAW,GAAG,CAACd,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACW,QAAQ,KAAK,CAACX,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACW,QAAQ,IAAI,EAAE;IACzJ,OAAOrB,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;MAChCC,SAAS,EAAE,GAAGlB,WAAW;IAC3B,CAAC,EAAER,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;MAC5BC,SAAS,EAAEpB,UAAU,CAAC,GAAGE,WAAW,aAAa,EAAE;QACjD,CAAC,GAAGA,WAAW,cAAc,GAAGY;MAClC,CAAC,CAAC;MACFO,KAAK,EAAE;QACLC,MAAM,EAAEJ;MACV;IACF,CAAC,EAAEd,KAAK,CAACY,OAAO,CAACO,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;MACtC,IAAIf,EAAE;MACN,OAAOhB,KAAK,CAACyB,aAAa,CAAC,GAAG,EAAE;QAC9BO,GAAG,EAAE,CAAChB,EAAE,GAAGc,MAAM,CAACE,GAAG,MAAM,IAAI,IAAIhB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGe,KAAK;QAC7DL,SAAS,EAAEpB,UAAU,CAAC,GAAGE,WAAW,OAAO,EAAE,kBAAkB,EAAE;UAC/D,CAAC,GAAGA,WAAW,gBAAgB,GAAGsB,MAAM,CAACG;QAC3C,CAAC,CAAC;QACFnB,OAAO,EAAEA,CAAA,KAAM;UACb,IAAIE,EAAE;UACN,IAAIc,MAAM,CAACG,QAAQ,EAAE;UACrBnB,OAAO,CAACgB,MAAM,CAAC;UACf,CAACd,EAAE,GAAGc,MAAM,CAAChB,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkB,IAAI,CAACJ,MAAM,CAAC;QAC5E;MACF,CAAC,EAAEA,MAAM,CAACK,IAAI,IAAInC,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;QAC3CC,SAAS,EAAE,GAAGlB,WAAW;MAC3B,CAAC,EAAEsB,MAAM,CAACK,IAAI,CAAC,EAAEnC,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;QAC1CC,SAAS,EAAE,GAAGlB,WAAW;MAC3B,CAAC,EAAEsB,MAAM,CAACM,IAAI,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,EAAE,CAAC1B,KAAK,CAACY,OAAO,EAAER,OAAO,CAAC,CAAC;EAC5B,OAAOd,KAAK,CAACyB,aAAa,CAAClB,OAAO,EAAE8B,MAAM,CAACC,MAAM,CAAC;IAChD3B,GAAG,EAAEC;EACP,CAAC,EAAEF,KAAK,EAAE;IACRgB,SAAS,EAAEpB,UAAU,CAACE,WAAW,EAAEE,KAAK,CAACgB,SAAS,CAAC;IACnDa,OAAO,EAAEpB;EACX,CAAC,CAAC,EAAET,KAAK,CAAC8B,QAAQ,CAAC;AACrB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}