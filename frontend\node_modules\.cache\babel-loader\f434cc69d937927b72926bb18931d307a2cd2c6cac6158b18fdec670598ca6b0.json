{"ast": null, "code": "import * as React from \"react\";\nfunction AppstoreOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AppstoreOutline-AppstoreOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AppstoreOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"AppstoreOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M17,25 C20.3137085,25 23,27.6862915 23,31 L23,38 C23,41.3137085 20.3137085,44 17,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,31 C4,27.6862915 6.6862915,25 10,25 L17,25 Z M38,25 C41.3137085,25 44,27.6862915 44,31 L44,38 C44,41.3137085 41.3137085,44 38,44 L31,44 C27.6862915,44 25,41.3137085 25,38 L25,31 C25,27.6862915 27.6862915,25 31,25 L38,25 Z M17,28 L10,28 C8.40231912,28 7.09633912,29.24892 7.00509269,30.8237272 L7,31 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L17,41 C18.5976809,41 19.9036609,39.75108 19.9949073,38.1762728 L20,38 L20,31 C20,29.4023191 18.75108,28.0963391 17.1762728,28.0050927 L17,28 Z M38,28 L31,28 C29.4023191,28 28.0963391,29.24892 28.0050927,30.8237272 L28,31 L28,38 C28,39.5976809 29.24892,40.9036609 30.8237272,40.9949073 L31,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,31 C41,29.4023191 39.75108,28.0963391 38.1762728,28.0050927 L38,28 Z M17,4 C20.3137085,4 23,6.6862915 23,10 L23,17 C23,20.3137085 20.3137085,23 17,23 L10,23 C6.6862915,23 4,20.3137085 4,17 L4,10 C4,6.6862915 6.6862915,4 10,4 L17,4 Z M38,4 C41.3137085,4 44,6.6862915 44,10 L44,17 C44,20.3137085 41.3137085,23 38,23 L31,23 C27.6862915,23 25,20.3137085 25,17 L25,10 C25,6.6862915 27.6862915,4 31,4 L38,4 Z M17,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,17 C7,18.5976809 8.24891996,19.9036609 9.82372721,19.9949073 L10,20 L17,20 C18.5976809,20 19.9036609,18.75108 19.9949073,17.1762728 L20,17 L20,10 C20,8.40231912 18.75108,7.09633912 17.1762728,7.00509269 L17,7 Z M38,7 L31,7 C29.4023191,7 28.0963391,8.24891996 28.0050927,9.82372721 L28,10 L28,17 C28,18.5976809 29.24892,19.9036609 30.8237272,19.9949073 L31,20 L38,20 C39.5976809,20 40.9036609,18.75108 40.9949073,17.1762728 L41,17 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 Z\",\n    id: \"AppstoreOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default AppstoreOutline;", "map": {"version": 3, "names": ["React", "AppstoreOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/AppstoreOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction AppstoreOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AppstoreOutline-AppstoreOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AppstoreOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"AppstoreOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M17,25 C20.3137085,25 23,27.6862915 23,31 L23,38 C23,41.3137085 20.3137085,44 17,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,31 C4,27.6862915 6.6862915,25 10,25 L17,25 Z M38,25 C41.3137085,25 44,27.6862915 44,31 L44,38 C44,41.3137085 41.3137085,44 38,44 L31,44 C27.6862915,44 25,41.3137085 25,38 L25,31 C25,27.6862915 27.6862915,25 31,25 L38,25 Z M17,28 L10,28 C8.40231912,28 7.09633912,29.24892 7.00509269,30.8237272 L7,31 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L17,41 C18.5976809,41 19.9036609,39.75108 19.9949073,38.1762728 L20,38 L20,31 C20,29.4023191 18.75108,28.0963391 17.1762728,28.0050927 L17,28 Z M38,28 L31,28 C29.4023191,28 28.0963391,29.24892 28.0050927,30.8237272 L28,31 L28,38 C28,39.5976809 29.24892,40.9036609 30.8237272,40.9949073 L31,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,31 C41,29.4023191 39.75108,28.0963391 38.1762728,28.0050927 L38,28 Z M17,4 C20.3137085,4 23,6.6862915 23,10 L23,17 C23,20.3137085 20.3137085,23 17,23 L10,23 C6.6862915,23 4,20.3137085 4,17 L4,10 C4,6.6862915 6.6862915,4 10,4 L17,4 Z M38,4 C41.3137085,4 44,6.6862915 44,10 L44,17 C44,20.3137085 41.3137085,23 38,23 L31,23 C27.6862915,23 25,20.3137085 25,17 L25,10 C25,6.6862915 27.6862915,4 31,4 L38,4 Z M17,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,17 C7,18.5976809 8.24891996,19.9036609 9.82372721,19.9949073 L10,20 L17,20 C18.5976809,20 19.9036609,18.75108 19.9949073,17.1762728 L20,17 L20,10 C20,8.40231912 18.75108,7.09633912 17.1762728,7.00509269 L17,7 Z M38,7 L31,7 C29.4023191,7 28.0963391,8.24891996 28.0050927,9.82372721 L28,10 L28,17 C28,18.5976809 29.24892,19.9036609 30.8237272,19.9949073 L31,20 L38,20 C39.5976809,20 40.9036609,18.75108 40.9949073,17.1762728 L41,17 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 Z\",\n    id: \"AppstoreOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default AppstoreOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,iCAAiC;IACrCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,yzDAAyzD;IAC5zDR,EAAE,EAAE,0CAA0C;IAC9CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}