{"ast": null, "code": "import * as React from \"react\";\nfunction EditFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EditFill-EditFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EditFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"EditFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M27.5325468,13.8464269 L34.9564908,21.0652619 L34.9564907,21.0652619 C35.3664155,21.4639258 35.3664155,22.110162 34.9564908,22.5088231 L15.217454,41.701895 L15.217454,41.7018949 C15.0204038,41.8929911 14.7533811,42 14.4750596,42 L8.10012604,42 L8.10012595,42 C6.9402636,42 6.00000168,41.085849 6.00000168,39.9581836 L6.00000168,33.7612927 L6.00000168,33.7613009 C5.99950341,33.4903544 6.1098059,33.2303255 6.30661496,33.0384938 L26.0477683,13.8464748 L26.0477683,13.8464748 C26.4578166,13.447931 27.1225068,13.447931 27.5325522,13.8464747 L27.5325468,13.8464269 Z M40.1542901,8.79293609 L40.15429,8.79293597 C42.614941,11.184869 42.6152806,15.0632844 40.1550449,17.455605 C40.1547933,17.4558496 40.1545418,17.4560941 40.1542903,17.4563386 L38.6695064,18.8998999 L38.6695064,18.8998998 C38.2596604,19.2988107 37.5947974,19.2991701 37.1844961,18.900703 C37.1842206,18.9004355 37.1839453,18.9001678 37.1836701,18.8998999 L29.7597261,11.6800599 L29.7597261,11.68006 C29.3498014,11.281396 29.3498014,10.6351598 29.759726,10.2364988 L31.2445099,8.79395684 L31.2445098,8.79395696 C33.7049196,6.40201439 37.6938664,6.40201439 40.1542565,8.79395672 L40.1542901,8.79293609 Z\",\n    id: \"EditFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default EditFill;", "map": {"version": 3, "names": ["React", "EditFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/EditFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction EditFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EditFill-EditFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EditFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"EditFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M27.5325468,13.8464269 L34.9564908,21.0652619 L34.9564907,21.0652619 C35.3664155,21.4639258 35.3664155,22.110162 34.9564908,22.5088231 L15.217454,41.701895 L15.217454,41.7018949 C15.0204038,41.8929911 14.7533811,42 14.4750596,42 L8.10012604,42 L8.10012595,42 C6.9402636,42 6.00000168,41.085849 6.00000168,39.9581836 L6.00000168,33.7612927 L6.00000168,33.7613009 C5.99950341,33.4903544 6.1098059,33.2303255 6.30661496,33.0384938 L26.0477683,13.8464748 L26.0477683,13.8464748 C26.4578166,13.447931 27.1225068,13.447931 27.5325522,13.8464747 L27.5325468,13.8464269 Z M40.1542901,8.79293609 L40.15429,8.79293597 C42.614941,11.184869 42.6152806,15.0632844 40.1550449,17.455605 C40.1547933,17.4558496 40.1545418,17.4560941 40.1542903,17.4563386 L38.6695064,18.8998999 L38.6695064,18.8998998 C38.2596604,19.2988107 37.5947974,19.2991701 37.1844961,18.900703 C37.1842206,18.9004355 37.1839453,18.9001678 37.1836701,18.8998999 L29.7597261,11.6800599 L29.7597261,11.68006 C29.3498014,11.281396 29.3498014,10.6351598 29.759726,10.2364988 L31.2445099,8.79395684 L31.2445098,8.79395696 C33.7049196,6.40201439 37.6938664,6.40201439 40.1542565,8.79395672 L40.1542901,8.79293609 Z\",\n    id: \"EditFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default EditFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,mBAAmB;IACvBC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,uBAAuB;IAC3BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,8oCAA8oC;IACjpCR,EAAE,EAAE,uBAAuB;IAC3BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}