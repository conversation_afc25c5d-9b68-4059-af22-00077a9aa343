{"ast": null, "code": "import * as React from \"react\";\nfunction AlipaySquareFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AlipaySquareFill-AlipaySquareFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AlipaySquareFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"AlipaySquareFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37.5946094,4 C41.1340625,4 44,6.86820313 44,10.4083984 L44,37.5930078 C44,41.1309375 41.1343359,44 37.5946094,44 L10.4076953,44 C6.86707031,44 4,41.1309375 4,37.5930078 L4,10.4083984 C4,6.86820312 6.86710937,4 10.4076953,4 L37.5946094,4 Z M26.2660937,9.33332031 L22.3282422,9.33332031 C21.6445312,9.33332031 21.6376953,9.99917969 21.6376172,10.0125 L21.6376172,14.0457031 L11.8794141,14.0457031 L11.8794141,15.6235156 L21.6376172,15.6235156 L21.6376172,18.4500391 L13.5808594,18.4500391 L13.5808594,20.0278125 L29.2063672,20.0278125 C28.6345703,21.9905078 27.8667969,23.8335547 26.9555469,25.5077344 C21.8855469,23.8406641 16.4751953,22.4896094 13.0765625,23.3214062 C10.9031641,23.8553906 9.50292969,24.8076562 8.68015625,25.8053125 C4.90515625,30.3819531 7.61234375,37.3333203 15.5849219,37.3333203 C20.2983984,37.3333203 24.8391797,34.7142578 28.3589062,30.3989453 C33.6073047,32.9133984 43.9999905,37.2304687 43.9999905,37.2304687 L43.9999905,31.0780078 L43.9984375,31.0778906 C43.9460156,31.073125 42.5796875,30.9358984 36.94125,29.0541406 C35.3427734,28.5210156 33.196875,27.7047656 30.8073828,26.8434766 C32.2430859,24.3573047 33.3886328,21.5262891 34.1419141,18.4500391 L26.2660547,18.4500391 L26.2660547,15.6234766 L35.9142578,15.6234766 L35.9142578,14.0457422 L26.2660547,14.0457422 L26.2660547,9.33332031 L26.2660937,9.33332031 Z M24.6666797,28.7101953 C21.8591797,32.3766797 18.2846875,34.6666797 14.7085156,34.6666797 C8.55535156,34.6666797 6.73582031,29.8128906 9.776875,27.1577734 C10.7915234,26.2603516 12.6464062,25.8222266 13.6346875,25.7237891 C17.29,25.3623438 20.6730078,26.7585547 24.6666797,28.7101953 L24.6666797,28.7101953 Z\",\n    id: \"AlipaySquareFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default AlipaySquareFill;", "map": {"version": 3, "names": ["React", "AlipaySquareFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/AlipaySquareFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction AlipaySquareFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AlipaySquareFill-AlipaySquareFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AlipaySquareFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"AlipaySquareFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37.5946094,4 C41.1340625,4 44,6.86820313 44,10.4083984 L44,37.5930078 C44,41.1309375 41.1343359,44 37.5946094,44 L10.4076953,44 C6.86707031,44 4,41.1309375 4,37.5930078 L4,10.4083984 C4,6.86820312 6.86710937,4 10.4076953,4 L37.5946094,4 Z M26.2660937,9.33332031 L22.3282422,9.33332031 C21.6445312,9.33332031 21.6376953,9.99917969 21.6376172,10.0125 L21.6376172,14.0457031 L11.8794141,14.0457031 L11.8794141,15.6235156 L21.6376172,15.6235156 L21.6376172,18.4500391 L13.5808594,18.4500391 L13.5808594,20.0278125 L29.2063672,20.0278125 C28.6345703,21.9905078 27.8667969,23.8335547 26.9555469,25.5077344 C21.8855469,23.8406641 16.4751953,22.4896094 13.0765625,23.3214062 C10.9031641,23.8553906 9.50292969,24.8076562 8.68015625,25.8053125 C4.90515625,30.3819531 7.61234375,37.3333203 15.5849219,37.3333203 C20.2983984,37.3333203 24.8391797,34.7142578 28.3589062,30.3989453 C33.6073047,32.9133984 43.9999905,37.2304687 43.9999905,37.2304687 L43.9999905,31.0780078 L43.9984375,31.0778906 C43.9460156,31.073125 42.5796875,30.9358984 36.94125,29.0541406 C35.3427734,28.5210156 33.196875,27.7047656 30.8073828,26.8434766 C32.2430859,24.3573047 33.3886328,21.5262891 34.1419141,18.4500391 L26.2660547,18.4500391 L26.2660547,15.6234766 L35.9142578,15.6234766 L35.9142578,14.0457422 L26.2660547,14.0457422 L26.2660547,9.33332031 L26.2660937,9.33332031 Z M24.6666797,28.7101953 C21.8591797,32.3766797 18.2846875,34.6666797 14.7085156,34.6666797 C8.55535156,34.6666797 6.73582031,29.8128906 9.776875,27.1577734 C10.7915234,26.2603516 12.6464062,25.8222266 13.6346875,25.7237891 C17.29,25.3623438 20.6730078,26.7585547 24.6666797,28.7101953 L24.6666797,28.7101953 Z\",\n    id: \"AlipaySquareFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default AlipaySquareFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,mCAAmC;IACvCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,+BAA+B;IACnCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,onDAAonD;IACvnDR,EAAE,EAAE,+BAA+B;IACnCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}