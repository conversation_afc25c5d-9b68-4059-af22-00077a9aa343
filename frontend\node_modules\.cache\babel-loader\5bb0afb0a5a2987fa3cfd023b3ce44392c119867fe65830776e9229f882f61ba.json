{"ast": null, "code": "import { useMemo } from 'react';\nexport function useColumnsFn(options) {\n  const depth = useMemo(() => {\n    let depth = 0;\n    function traverse(options, currentDepth) {\n      if (currentDepth > depth) depth = currentDepth;\n      const nextDepth = currentDepth + 1;\n      options.forEach(option => {\n        if (option.children) {\n          traverse(option.children, nextDepth);\n        }\n      });\n    }\n    traverse(options, 1);\n    return depth;\n  }, [options]);\n  return selected => {\n    const columns = [];\n    let currentOptions = options;\n    let i = 0;\n    while (true) {\n      columns.push(currentOptions.map(option => ({\n        label: option.label,\n        value: option.value\n      })));\n      const x = selected[i];\n      const targetOptions = currentOptions.find(option => option.value === x);\n      if (!targetOptions || !targetOptions.children) break;\n      currentOptions = targetOptions.children;\n      i++;\n    }\n    while (i < depth - 1) {\n      columns.push([]);\n      i++;\n    }\n    return columns;\n  };\n}", "map": {"version": 3, "names": ["useMemo", "useColumnsFn", "options", "depth", "traverse", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "option", "children", "selected", "columns", "currentOptions", "i", "push", "map", "label", "value", "x", "targetOptions", "find"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/cascade-picker/cascade-picker-utils.js"], "sourcesContent": ["import { useMemo } from 'react';\nexport function useColumnsFn(options) {\n  const depth = useMemo(() => {\n    let depth = 0;\n    function traverse(options, currentDepth) {\n      if (currentDepth > depth) depth = currentDepth;\n      const nextDepth = currentDepth + 1;\n      options.forEach(option => {\n        if (option.children) {\n          traverse(option.children, nextDepth);\n        }\n      });\n    }\n    traverse(options, 1);\n    return depth;\n  }, [options]);\n  return selected => {\n    const columns = [];\n    let currentOptions = options;\n    let i = 0;\n    while (true) {\n      columns.push(currentOptions.map(option => ({\n        label: option.label,\n        value: option.value\n      })));\n      const x = selected[i];\n      const targetOptions = currentOptions.find(option => option.value === x);\n      if (!targetOptions || !targetOptions.children) break;\n      currentOptions = targetOptions.children;\n      i++;\n    }\n    while (i < depth - 1) {\n      columns.push([]);\n      i++;\n    }\n    return columns;\n  };\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAE;EACpC,MAAMC,KAAK,GAAGH,OAAO,CAAC,MAAM;IAC1B,IAAIG,KAAK,GAAG,CAAC;IACb,SAASC,QAAQA,CAACF,OAAO,EAAEG,YAAY,EAAE;MACvC,IAAIA,YAAY,GAAGF,KAAK,EAAEA,KAAK,GAAGE,YAAY;MAC9C,MAAMC,SAAS,GAAGD,YAAY,GAAG,CAAC;MAClCH,OAAO,CAACK,OAAO,CAACC,MAAM,IAAI;QACxB,IAAIA,MAAM,CAACC,QAAQ,EAAE;UACnBL,QAAQ,CAACI,MAAM,CAACC,QAAQ,EAAEH,SAAS,CAAC;QACtC;MACF,CAAC,CAAC;IACJ;IACAF,QAAQ,CAACF,OAAO,EAAE,CAAC,CAAC;IACpB,OAAOC,KAAK;EACd,CAAC,EAAE,CAACD,OAAO,CAAC,CAAC;EACb,OAAOQ,QAAQ,IAAI;IACjB,MAAMC,OAAO,GAAG,EAAE;IAClB,IAAIC,cAAc,GAAGV,OAAO;IAC5B,IAAIW,CAAC,GAAG,CAAC;IACT,OAAO,IAAI,EAAE;MACXF,OAAO,CAACG,IAAI,CAACF,cAAc,CAACG,GAAG,CAACP,MAAM,KAAK;QACzCQ,KAAK,EAAER,MAAM,CAACQ,KAAK;QACnBC,KAAK,EAAET,MAAM,CAACS;MAChB,CAAC,CAAC,CAAC,CAAC;MACJ,MAAMC,CAAC,GAAGR,QAAQ,CAACG,CAAC,CAAC;MACrB,MAAMM,aAAa,GAAGP,cAAc,CAACQ,IAAI,CAACZ,MAAM,IAAIA,MAAM,CAACS,KAAK,KAAKC,CAAC,CAAC;MACvE,IAAI,CAACC,aAAa,IAAI,CAACA,aAAa,CAACV,QAAQ,EAAE;MAC/CG,cAAc,GAAGO,aAAa,CAACV,QAAQ;MACvCI,CAAC,EAAE;IACL;IACA,OAAOA,CAAC,GAAGV,KAAK,GAAG,CAAC,EAAE;MACpBQ,OAAO,CAACG,IAAI,CAAC,EAAE,CAAC;MAChBD,CAAC,EAAE;IACL;IACA,OAAOF,OAAO;EAChB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}