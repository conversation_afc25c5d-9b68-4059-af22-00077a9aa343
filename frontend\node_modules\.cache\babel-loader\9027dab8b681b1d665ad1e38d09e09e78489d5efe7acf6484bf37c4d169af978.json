{"ast": null, "code": "import classNames from 'classnames';\nimport React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';\nimport { bound } from '../../utils/bound';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nconst classPrefix = 'adm-passcode-input';\nconst defaultProps = {\n  defaultValue: '',\n  length: 6,\n  plain: false,\n  error: false,\n  seperated: false,\n  caret: true,\n  inputMode: 'numeric'\n};\nexport const PasscodeInput = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  // 防止 length 值不合法\n  const cellLength = props.length > 0 && props.length < Infinity ? Math.floor(props.length) : defaultProps.length;\n  const {\n    locale\n  } = useConfig();\n  const [focused, setFocused] = useState(false);\n  const [value, setValue] = usePropsValue(props);\n  const rootRef = useRef(null);\n  const nativeInputRef = useRef(null);\n  useEffect(() => {\n    var _a;\n    if (value.length >= cellLength) {\n      (_a = props.onFill) === null || _a === void 0 ? void 0 : _a.call(props, value);\n    }\n  }, [value, cellLength]);\n  const onFocus = () => {\n    var _a, _b;\n    if (!props.keyboard) {\n      (_a = nativeInputRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n    setFocused(true);\n    (_b = props.onFocus) === null || _b === void 0 ? void 0 : _b.call(props);\n  };\n  useEffect(() => {\n    if (!focused) return;\n    const timeout = window.setTimeout(() => {\n      var _a;\n      (_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.scrollIntoView({\n        block: 'center',\n        inline: 'center',\n        behavior: 'smooth'\n      });\n    }, 100);\n    return () => {\n      window.clearTimeout(timeout);\n    };\n  }, [focused]);\n  const onBlur = () => {\n    var _a;\n    setFocused(false);\n    (_a = props.onBlur) === null || _a === void 0 ? void 0 : _a.call(props);\n  };\n  useImperativeHandle(ref, () => ({\n    focus: () => {\n      var _a;\n      return (_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    },\n    blur: () => {\n      var _a, _b;\n      (_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n      (_b = nativeInputRef.current) === null || _b === void 0 ? void 0 : _b.blur();\n    }\n  }));\n  const renderCells = () => {\n    const cells = [];\n    const chars = value.split('');\n    const caretIndex = chars.length; // 光标位置index等于当前文字长度\n    const focusedIndex = bound(chars.length, 0, cellLength - 1);\n    for (let i = 0; i < cellLength; i++) {\n      cells.push(React.createElement(\"div\", {\n        className: classNames(`${classPrefix}-cell`, {\n          [`${classPrefix}-cell-caret`]: props.caret && caretIndex === i && focused,\n          [`${classPrefix}-cell-focused`]: focusedIndex === i && focused,\n          [`${classPrefix}-cell-dot`]: !props.plain && chars[i]\n        }),\n        key: i\n      }, chars[i] && props.plain ? chars[i] : ''));\n    }\n    return cells;\n  };\n  const cls = classNames(classPrefix, {\n    [`${classPrefix}-focused`]: focused,\n    [`${classPrefix}-error`]: props.error,\n    [`${classPrefix}-seperated`]: props.seperated\n  });\n  return React.createElement(React.Fragment, null, withNativeProps(props, React.createElement(\"div\", {\n    ref: rootRef,\n    tabIndex: 0,\n    className: cls,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    role: 'button',\n    \"aria-label\": locale.PasscodeInput.name\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-cell-container`\n  }, renderCells()), React.createElement(\"input\", {\n    ref: nativeInputRef,\n    className: `${classPrefix}-native-input`,\n    value: value,\n    type: 'text',\n    pattern: '[0-9]*',\n    inputMode: props.inputMode,\n    onChange: e => {\n      setValue(e.target.value.slice(0, props.length));\n    },\n    \"aria-hidden\": true\n  }))), props.keyboard && React.cloneElement(props.keyboard, {\n    visible: focused,\n    onInput: v => {\n      if (value.length < cellLength) {\n        setValue((value + v).slice(0, props.length));\n      }\n    },\n    onDelete: () => {\n      setValue(value.slice(0, -1));\n    },\n    onClose: () => {\n      var _a;\n      (_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n    }\n  }));\n});", "map": {"version": 3, "names": ["classNames", "React", "forwardRef", "useEffect", "useImperativeHandle", "useRef", "useState", "bound", "withNativeProps", "usePropsValue", "mergeProps", "useConfig", "classPrefix", "defaultProps", "defaultValue", "length", "plain", "error", "seperated", "caret", "inputMode", "PasscodeInput", "p", "ref", "props", "cellLength", "Infinity", "Math", "floor", "locale", "focused", "setFocused", "value", "setValue", "rootRef", "nativeInputRef", "_a", "onFill", "call", "onFocus", "_b", "keyboard", "current", "focus", "timeout", "window", "setTimeout", "scrollIntoView", "block", "inline", "behavior", "clearTimeout", "onBlur", "blur", "renderCells", "cells", "chars", "split", "caretIndex", "focusedIndex", "i", "push", "createElement", "className", "key", "cls", "Fragment", "tabIndex", "role", "name", "type", "pattern", "onChange", "e", "target", "slice", "cloneElement", "visible", "onInput", "v", "onDelete", "onClose"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/passcode-input/passcode-input.js"], "sourcesContent": ["import classNames from 'classnames';\nimport React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';\nimport { bound } from '../../utils/bound';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nconst classPrefix = 'adm-passcode-input';\nconst defaultProps = {\n  defaultValue: '',\n  length: 6,\n  plain: false,\n  error: false,\n  seperated: false,\n  caret: true,\n  inputMode: 'numeric'\n};\nexport const PasscodeInput = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  // 防止 length 值不合法\n  const cellLength = props.length > 0 && props.length < Infinity ? Math.floor(props.length) : defaultProps.length;\n  const {\n    locale\n  } = useConfig();\n  const [focused, setFocused] = useState(false);\n  const [value, setValue] = usePropsValue(props);\n  const rootRef = useRef(null);\n  const nativeInputRef = useRef(null);\n  useEffect(() => {\n    var _a;\n    if (value.length >= cellLength) {\n      (_a = props.onFill) === null || _a === void 0 ? void 0 : _a.call(props, value);\n    }\n  }, [value, cellLength]);\n  const onFocus = () => {\n    var _a, _b;\n    if (!props.keyboard) {\n      (_a = nativeInputRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n    setFocused(true);\n    (_b = props.onFocus) === null || _b === void 0 ? void 0 : _b.call(props);\n  };\n  useEffect(() => {\n    if (!focused) return;\n    const timeout = window.setTimeout(() => {\n      var _a;\n      (_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.scrollIntoView({\n        block: 'center',\n        inline: 'center',\n        behavior: 'smooth'\n      });\n    }, 100);\n    return () => {\n      window.clearTimeout(timeout);\n    };\n  }, [focused]);\n  const onBlur = () => {\n    var _a;\n    setFocused(false);\n    (_a = props.onBlur) === null || _a === void 0 ? void 0 : _a.call(props);\n  };\n  useImperativeHandle(ref, () => ({\n    focus: () => {\n      var _a;\n      return (_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    },\n    blur: () => {\n      var _a, _b;\n      (_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n      (_b = nativeInputRef.current) === null || _b === void 0 ? void 0 : _b.blur();\n    }\n  }));\n  const renderCells = () => {\n    const cells = [];\n    const chars = value.split('');\n    const caretIndex = chars.length; // 光标位置index等于当前文字长度\n    const focusedIndex = bound(chars.length, 0, cellLength - 1);\n    for (let i = 0; i < cellLength; i++) {\n      cells.push(React.createElement(\"div\", {\n        className: classNames(`${classPrefix}-cell`, {\n          [`${classPrefix}-cell-caret`]: props.caret && caretIndex === i && focused,\n          [`${classPrefix}-cell-focused`]: focusedIndex === i && focused,\n          [`${classPrefix}-cell-dot`]: !props.plain && chars[i]\n        }),\n        key: i\n      }, chars[i] && props.plain ? chars[i] : ''));\n    }\n    return cells;\n  };\n  const cls = classNames(classPrefix, {\n    [`${classPrefix}-focused`]: focused,\n    [`${classPrefix}-error`]: props.error,\n    [`${classPrefix}-seperated`]: props.seperated\n  });\n  return React.createElement(React.Fragment, null, withNativeProps(props, React.createElement(\"div\", {\n    ref: rootRef,\n    tabIndex: 0,\n    className: cls,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    role: 'button',\n    \"aria-label\": locale.PasscodeInput.name\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-cell-container`\n  }, renderCells()), React.createElement(\"input\", {\n    ref: nativeInputRef,\n    className: `${classPrefix}-native-input`,\n    value: value,\n    type: 'text',\n    pattern: '[0-9]*',\n    inputMode: props.inputMode,\n    onChange: e => {\n      setValue(e.target.value.slice(0, props.length));\n    },\n    \"aria-hidden\": true\n  }))), props.keyboard && React.cloneElement(props.keyboard, {\n    visible: focused,\n    onInput: v => {\n      if (value.length < cellLength) {\n        setValue((value + v).slice(0, props.length));\n      }\n    },\n    onDelete: () => {\n      setValue(value.slice(0, -1));\n    },\n    onClose: () => {\n      var _a;\n      (_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n    }\n  }));\n});"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC3F,SAASC,KAAK,QAAQ,mBAAmB;AACzC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,MAAMC,WAAW,GAAG,oBAAoB;AACxC,MAAMC,YAAY,GAAG;EACnBC,YAAY,EAAE,EAAE;EAChBC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE,KAAK;EACZC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EACXC,SAAS,EAAE;AACb,CAAC;AACD,OAAO,MAAMC,aAAa,GAAGnB,UAAU,CAAC,CAACoB,CAAC,EAAEC,GAAG,KAAK;EAClD,MAAMC,KAAK,GAAGd,UAAU,CAACG,YAAY,EAAES,CAAC,CAAC;EACzC;EACA,MAAMG,UAAU,GAAGD,KAAK,CAACT,MAAM,GAAG,CAAC,IAAIS,KAAK,CAACT,MAAM,GAAGW,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACJ,KAAK,CAACT,MAAM,CAAC,GAAGF,YAAY,CAACE,MAAM;EAC/G,MAAM;IACJc;EACF,CAAC,GAAGlB,SAAS,CAAC,CAAC;EACf,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,aAAa,CAACe,KAAK,CAAC;EAC9C,MAAMU,OAAO,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAM8B,cAAc,GAAG9B,MAAM,CAAC,IAAI,CAAC;EACnCF,SAAS,CAAC,MAAM;IACd,IAAIiC,EAAE;IACN,IAAIJ,KAAK,CAACjB,MAAM,IAAIU,UAAU,EAAE;MAC9B,CAACW,EAAE,GAAGZ,KAAK,CAACa,MAAM,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACd,KAAK,EAAEQ,KAAK,CAAC;IAChF;EACF,CAAC,EAAE,CAACA,KAAK,EAAEP,UAAU,CAAC,CAAC;EACvB,MAAMc,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAIH,EAAE,EAAEI,EAAE;IACV,IAAI,CAAChB,KAAK,CAACiB,QAAQ,EAAE;MACnB,CAACL,EAAE,GAAGD,cAAc,CAACO,OAAO,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,KAAK,CAAC,CAAC;IAC/E;IACAZ,UAAU,CAAC,IAAI,CAAC;IAChB,CAACS,EAAE,GAAGhB,KAAK,CAACe,OAAO,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACF,IAAI,CAACd,KAAK,CAAC;EAC1E,CAAC;EACDrB,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2B,OAAO,EAAE;IACd,MAAMc,OAAO,GAAGC,MAAM,CAACC,UAAU,CAAC,MAAM;MACtC,IAAIV,EAAE;MACN,CAACA,EAAE,GAAGF,OAAO,CAACQ,OAAO,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,cAAc,CAAC;QAC5EC,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE,QAAQ;QAChBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;IACP,OAAO,MAAM;MACXL,MAAM,CAACM,YAAY,CAACP,OAAO,CAAC;IAC9B,CAAC;EACH,CAAC,EAAE,CAACd,OAAO,CAAC,CAAC;EACb,MAAMsB,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAIhB,EAAE;IACNL,UAAU,CAAC,KAAK,CAAC;IACjB,CAACK,EAAE,GAAGZ,KAAK,CAAC4B,MAAM,MAAM,IAAI,IAAIhB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACd,KAAK,CAAC;EACzE,CAAC;EACDpB,mBAAmB,CAACmB,GAAG,EAAE,OAAO;IAC9BoB,KAAK,EAAEA,CAAA,KAAM;MACX,IAAIP,EAAE;MACN,OAAO,CAACA,EAAE,GAAGF,OAAO,CAACQ,OAAO,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,KAAK,CAAC,CAAC;IAC/E,CAAC;IACDU,IAAI,EAAEA,CAAA,KAAM;MACV,IAAIjB,EAAE,EAAEI,EAAE;MACV,CAACJ,EAAE,GAAGF,OAAO,CAACQ,OAAO,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,IAAI,CAAC,CAAC;MACrE,CAACb,EAAE,GAAGL,cAAc,CAACO,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACa,IAAI,CAAC,CAAC;IAC9E;EACF,CAAC,CAAC,CAAC;EACH,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,KAAK,GAAG,EAAE;IAChB,MAAMC,KAAK,GAAGxB,KAAK,CAACyB,KAAK,CAAC,EAAE,CAAC;IAC7B,MAAMC,UAAU,GAAGF,KAAK,CAACzC,MAAM,CAAC,CAAC;IACjC,MAAM4C,YAAY,GAAGpD,KAAK,CAACiD,KAAK,CAACzC,MAAM,EAAE,CAAC,EAAEU,UAAU,GAAG,CAAC,CAAC;IAC3D,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnC,UAAU,EAAEmC,CAAC,EAAE,EAAE;MACnCL,KAAK,CAACM,IAAI,CAAC5D,KAAK,CAAC6D,aAAa,CAAC,KAAK,EAAE;QACpCC,SAAS,EAAE/D,UAAU,CAAC,GAAGY,WAAW,OAAO,EAAE;UAC3C,CAAC,GAAGA,WAAW,aAAa,GAAGY,KAAK,CAACL,KAAK,IAAIuC,UAAU,KAAKE,CAAC,IAAI9B,OAAO;UACzE,CAAC,GAAGlB,WAAW,eAAe,GAAG+C,YAAY,KAAKC,CAAC,IAAI9B,OAAO;UAC9D,CAAC,GAAGlB,WAAW,WAAW,GAAG,CAACY,KAAK,CAACR,KAAK,IAAIwC,KAAK,CAACI,CAAC;QACtD,CAAC,CAAC;QACFI,GAAG,EAAEJ;MACP,CAAC,EAAEJ,KAAK,CAACI,CAAC,CAAC,IAAIpC,KAAK,CAACR,KAAK,GAAGwC,KAAK,CAACI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IAC9C;IACA,OAAOL,KAAK;EACd,CAAC;EACD,MAAMU,GAAG,GAAGjE,UAAU,CAACY,WAAW,EAAE;IAClC,CAAC,GAAGA,WAAW,UAAU,GAAGkB,OAAO;IACnC,CAAC,GAAGlB,WAAW,QAAQ,GAAGY,KAAK,CAACP,KAAK;IACrC,CAAC,GAAGL,WAAW,YAAY,GAAGY,KAAK,CAACN;EACtC,CAAC,CAAC;EACF,OAAOjB,KAAK,CAAC6D,aAAa,CAAC7D,KAAK,CAACiE,QAAQ,EAAE,IAAI,EAAE1D,eAAe,CAACgB,KAAK,EAAEvB,KAAK,CAAC6D,aAAa,CAAC,KAAK,EAAE;IACjGvC,GAAG,EAAEW,OAAO;IACZiC,QAAQ,EAAE,CAAC;IACXJ,SAAS,EAAEE,GAAG;IACd1B,OAAO,EAAEA,OAAO;IAChBa,MAAM,EAAEA,MAAM;IACdgB,IAAI,EAAE,QAAQ;IACd,YAAY,EAAEvC,MAAM,CAACR,aAAa,CAACgD;EACrC,CAAC,EAAEpE,KAAK,CAAC6D,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGnD,WAAW;EAC3B,CAAC,EAAE0C,WAAW,CAAC,CAAC,CAAC,EAAErD,KAAK,CAAC6D,aAAa,CAAC,OAAO,EAAE;IAC9CvC,GAAG,EAAEY,cAAc;IACnB4B,SAAS,EAAE,GAAGnD,WAAW,eAAe;IACxCoB,KAAK,EAAEA,KAAK;IACZsC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,QAAQ;IACjBnD,SAAS,EAAEI,KAAK,CAACJ,SAAS;IAC1BoD,QAAQ,EAAEC,CAAC,IAAI;MACbxC,QAAQ,CAACwC,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAAC2C,KAAK,CAAC,CAAC,EAAEnD,KAAK,CAACT,MAAM,CAAC,CAAC;IACjD,CAAC;IACD,aAAa,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,EAAES,KAAK,CAACiB,QAAQ,IAAIxC,KAAK,CAAC2E,YAAY,CAACpD,KAAK,CAACiB,QAAQ,EAAE;IACzDoC,OAAO,EAAE/C,OAAO;IAChBgD,OAAO,EAAEC,CAAC,IAAI;MACZ,IAAI/C,KAAK,CAACjB,MAAM,GAAGU,UAAU,EAAE;QAC7BQ,QAAQ,CAAC,CAACD,KAAK,GAAG+C,CAAC,EAAEJ,KAAK,CAAC,CAAC,EAAEnD,KAAK,CAACT,MAAM,CAAC,CAAC;MAC9C;IACF,CAAC;IACDiE,QAAQ,EAAEA,CAAA,KAAM;MACd/C,QAAQ,CAACD,KAAK,CAAC2C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IACDM,OAAO,EAAEA,CAAA,KAAM;MACb,IAAI7C,EAAE;MACN,CAACA,EAAE,GAAGF,OAAO,CAACQ,OAAO,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,IAAI,CAAC,CAAC;IACvE;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}