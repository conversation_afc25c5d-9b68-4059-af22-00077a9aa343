{"ast": null, "code": "import React from 'react';\nimport { isFragment } from 'react-is';\nexport function traverseReactNode(children, fn) {\n  let i = 0;\n  function handle(target) {\n    React.Children.forEach(target, child => {\n      if (!isFragment(child)) {\n        fn(child, i);\n        i += 1;\n      } else {\n        handle(child.props.children);\n      }\n    });\n  }\n  handle(children);\n}", "map": {"version": 3, "names": ["React", "isFragment", "traverseReactNode", "children", "fn", "i", "handle", "target", "Children", "for<PERSON>ach", "child", "props"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/traverse-react-node.js"], "sourcesContent": ["import React from 'react';\nimport { isFragment } from 'react-is';\nexport function traverseReactNode(children, fn) {\n  let i = 0;\n  function handle(target) {\n    React.Children.forEach(target, child => {\n      if (!isFragment(child)) {\n        fn(child, i);\n        i += 1;\n      } else {\n        handle(child.props.children);\n      }\n    });\n  }\n  handle(children);\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAO,SAASC,iBAAiBA,CAACC,QAAQ,EAAEC,EAAE,EAAE;EAC9C,IAAIC,CAAC,GAAG,CAAC;EACT,SAASC,MAAMA,CAACC,MAAM,EAAE;IACtBP,KAAK,CAACQ,QAAQ,CAACC,OAAO,CAACF,MAAM,EAAEG,KAAK,IAAI;MACtC,IAAI,CAACT,UAAU,CAACS,KAAK,CAAC,EAAE;QACtBN,EAAE,CAACM,KAAK,EAAEL,CAAC,CAAC;QACZA,CAAC,IAAI,CAAC;MACR,CAAC,MAAM;QACLC,MAAM,CAACI,KAAK,CAACC,KAAK,CAACR,QAAQ,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EACAG,MAAM,CAACH,QAAQ,CAAC;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}