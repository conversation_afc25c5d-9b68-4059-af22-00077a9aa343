{"ast": null, "code": "import { __awaiter } from \"tslib\";\nimport * as ReactDOM from 'react-dom';\n// Let compiler not to search module usage\nconst fullClone = Object.assign({}, ReactDOM);\nconst {\n  version,\n  render: reactRender,\n  unmountComponentAtNode\n} = fullClone;\nlet createRoot;\ntry {\n  const mainVersion = Number((version || '').split('.')[0]);\n  if (mainVersion >= 18 && fullClone.createRoot) {\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    createRoot = fullClone.createRoot;\n  }\n} catch (e) {\n  // Do nothing;\n}\nfunction toggleWarning(skip) {\n  const {\n    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n  } = fullClone;\n  if (__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED && typeof __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED === 'object') {\n    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.usingClientEntryPoint = skip;\n  }\n}\nconst MARK = '__antd_mobile_root__';\nfunction legacyRender(node, container) {\n  reactRender(node, container);\n}\nfunction concurrentRender(node, container) {\n  toggleWarning(true);\n  const root = container[MARK] || createRoot(container);\n  toggleWarning(false);\n  root.render(node);\n  container[MARK] = root;\n}\nexport function render(node, container) {\n  if (createRoot) {\n    concurrentRender(node, container);\n    return;\n  }\n  legacyRender(node, container);\n}\n// ========================== Unmount =========================\nfunction legacyUnmount(container) {\n  return unmountComponentAtNode(container);\n}\nfunction concurrentUnmount(container) {\n  return __awaiter(this, void 0, void 0, function* () {\n    // Delay to unmount to avoid React 18 sync warning\n    return Promise.resolve().then(() => {\n      var _a;\n      (_a = container[MARK]) === null || _a === void 0 ? void 0 : _a.unmount();\n      delete container[MARK];\n    });\n  });\n}\nexport function unmount(container) {\n  if (createRoot) {\n    return concurrentUnmount(container);\n  }\n  return legacyUnmount(container);\n}", "map": {"version": 3, "names": ["__awaiter", "ReactDOM", "fullClone", "Object", "assign", "version", "render", "reactRender", "unmountComponentAtNode", "createRoot", "mainVersion", "Number", "split", "e", "toggle<PERSON><PERSON>ning", "skip", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "usingClientEntryPoint", "MARK", "legacyRender", "node", "container", "concurrentRender", "root", "legacyUnmount", "concurrentUnmount", "Promise", "resolve", "then", "_a", "unmount"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/render.js"], "sourcesContent": ["import { __awaiter } from \"tslib\";\nimport * as ReactDOM from 'react-dom';\n// Let compiler not to search module usage\nconst fullClone = Object.assign({}, ReactDOM);\nconst {\n  version,\n  render: reactRender,\n  unmountComponentAtNode\n} = fullClone;\nlet createRoot;\ntry {\n  const mainVersion = Number((version || '').split('.')[0]);\n  if (mainVersion >= 18 && fullClone.createRoot) {\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    createRoot = fullClone.createRoot;\n  }\n} catch (e) {\n  // Do nothing;\n}\nfunction toggleWarning(skip) {\n  const {\n    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n  } = fullClone;\n  if (__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED && typeof __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED === 'object') {\n    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.usingClientEntryPoint = skip;\n  }\n}\nconst MARK = '__antd_mobile_root__';\nfunction legacyRender(node, container) {\n  reactRender(node, container);\n}\nfunction concurrentRender(node, container) {\n  toggleWarning(true);\n  const root = container[MARK] || createRoot(container);\n  toggleWarning(false);\n  root.render(node);\n  container[MARK] = root;\n}\nexport function render(node, container) {\n  if (createRoot) {\n    concurrentRender(node, container);\n    return;\n  }\n  legacyRender(node, container);\n}\n// ========================== Unmount =========================\nfunction legacyUnmount(container) {\n  return unmountComponentAtNode(container);\n}\nfunction concurrentUnmount(container) {\n  return __awaiter(this, void 0, void 0, function* () {\n    // Delay to unmount to avoid React 18 sync warning\n    return Promise.resolve().then(() => {\n      var _a;\n      (_a = container[MARK]) === null || _a === void 0 ? void 0 : _a.unmount();\n      delete container[MARK];\n    });\n  });\n}\nexport function unmount(container) {\n  if (createRoot) {\n    return concurrentUnmount(container);\n  }\n  return legacyUnmount(container);\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC;AACA,MAAMC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,QAAQ,CAAC;AAC7C,MAAM;EACJI,OAAO;EACPC,MAAM,EAAEC,WAAW;EACnBC;AACF,CAAC,GAAGN,SAAS;AACb,IAAIO,UAAU;AACd,IAAI;EACF,MAAMC,WAAW,GAAGC,MAAM,CAAC,CAACN,OAAO,IAAI,EAAE,EAAEO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACzD,IAAIF,WAAW,IAAI,EAAE,IAAIR,SAAS,CAACO,UAAU,EAAE;IAC7C;IACAA,UAAU,GAAGP,SAAS,CAACO,UAAU;EACnC;AACF,CAAC,CAAC,OAAOI,CAAC,EAAE;EACV;AAAA;AAEF,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,MAAM;IACJC;EACF,CAAC,GAAGd,SAAS;EACb,IAAIc,kDAAkD,IAAI,OAAOA,kDAAkD,KAAK,QAAQ,EAAE;IAChIA,kDAAkD,CAACC,qBAAqB,GAAGF,IAAI;EACjF;AACF;AACA,MAAMG,IAAI,GAAG,sBAAsB;AACnC,SAASC,YAAYA,CAACC,IAAI,EAAEC,SAAS,EAAE;EACrCd,WAAW,CAACa,IAAI,EAAEC,SAAS,CAAC;AAC9B;AACA,SAASC,gBAAgBA,CAACF,IAAI,EAAEC,SAAS,EAAE;EACzCP,aAAa,CAAC,IAAI,CAAC;EACnB,MAAMS,IAAI,GAAGF,SAAS,CAACH,IAAI,CAAC,IAAIT,UAAU,CAACY,SAAS,CAAC;EACrDP,aAAa,CAAC,KAAK,CAAC;EACpBS,IAAI,CAACjB,MAAM,CAACc,IAAI,CAAC;EACjBC,SAAS,CAACH,IAAI,CAAC,GAAGK,IAAI;AACxB;AACA,OAAO,SAASjB,MAAMA,CAACc,IAAI,EAAEC,SAAS,EAAE;EACtC,IAAIZ,UAAU,EAAE;IACda,gBAAgB,CAACF,IAAI,EAAEC,SAAS,CAAC;IACjC;EACF;EACAF,YAAY,CAACC,IAAI,EAAEC,SAAS,CAAC;AAC/B;AACA;AACA,SAASG,aAAaA,CAACH,SAAS,EAAE;EAChC,OAAOb,sBAAsB,CAACa,SAAS,CAAC;AAC1C;AACA,SAASI,iBAAiBA,CAACJ,SAAS,EAAE;EACpC,OAAOrB,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;IAClD;IACA,OAAO0B,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MAClC,IAAIC,EAAE;MACN,CAACA,EAAE,GAAGR,SAAS,CAACH,IAAI,CAAC,MAAM,IAAI,IAAIW,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,OAAO,CAAC,CAAC;MACxE,OAAOT,SAAS,CAACH,IAAI,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,OAAO,SAASY,OAAOA,CAACT,SAAS,EAAE;EACjC,IAAIZ,UAAU,EAAE;IACd,OAAOgB,iBAAiB,CAACJ,SAAS,CAAC;EACrC;EACA,OAAOG,aAAa,CAACH,SAAS,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}