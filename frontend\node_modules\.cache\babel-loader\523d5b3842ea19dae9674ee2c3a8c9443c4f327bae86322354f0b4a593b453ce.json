{"ast": null, "code": "import * as React from \"react\";\nfunction UndoOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UndoOutline-UndoOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UndoOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UndoOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37.6816851,4.67509546 L39.8814657,6.85361151 C39.9573221,6.92873467 40,7.03106463 40,7.13782458 L40,12.7941829 L40,12.7941829 C40,13.6148733 39.334699,14.2801742 38.5140087,14.2801742 L32.7855772,14.2799123 C32.6801467,14.2799075 32.5789814,14.2382783 32.504082,14.164078 L30.3115658,11.9920253 C30.1546254,11.8365497 30.153438,11.5832865 30.3089136,11.4263461 C30.3840286,11.3505234 30.4863305,11.3078645 30.5930607,11.3078596 C31.6352757,11.3078111 32.6774907,11.3077627 33.7197057,11.3077142 C33.9940808,11.3077015 34.4056434,11.3076824 34.9543935,11.3076569 C34.447242,10.9019845 34.0603405,10.6058972 33.7936891,10.4193952 C31.0251782,8.48303631 27.6466787,7.3455482 24,7.3455482 C14.6111593,7.3455482 7,14.8856258 7,24.1867828 C7,33.4879399 14.6111593,41.0280174 24,41.0280174 C32.9375071,41.0280174 40.2641356,34.1954384 40.9478833,25.5152879 C40.9655577,25.2909127 40.979903,24.9777463 40.9909194,24.575789 C40.9968894,24.3592445 41.1741425,24.1867828 41.3907692,24.1867828 C41.6454621,24.1867828 41.848539,24.1867828 42,24.1867828 C42.2242544,24.1867828 42.505085,24.1867828 42.8424919,24.1867828 C43.0040925,24.1867828 43.2540143,24.1867828 43.5922573,24.1867828 L43.5922573,24.1867425 C43.8131934,24.1867425 43.9922976,24.3658467 43.9922976,24.5867828 C43.9922976,24.5898274 43.9922628,24.592872 43.9921933,24.5959158 C43.9839849,24.955365 43.9734263,25.2373729 43.9605177,25.4419395 C43.3069184,35.7996864 34.62013,44 24,44 C12.954305,44 4,35.1293205 4,24.1867828 C4,13.2442451 12.954305,4.37356563 24,4.37356563 C28.4185686,4.37356563 32.5024707,5.79306104 35.8123933,8.19684145 C36.0846021,8.3945289 36.4808621,8.70576637 37.0011734,9.13055386 C37.0010426,8.55895913 37.0009446,8.13026308 37.0008792,7.84446571 C37.0006593,6.88277715 37.0004394,5.92108858 37.0002194,4.95940002 C37.0001689,4.73848612 37.179214,4.55935907 37.4001279,4.55930855 C37.5055732,4.55928443 37.6067628,4.60089741 37.6816851,4.67509546 Z\",\n    id: \"UndoOutline-\\u8DEF\\u5F84\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default UndoOutline;", "map": {"version": 3, "names": ["React", "UndoOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/UndoOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction UndoOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UndoOutline-UndoOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UndoOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UndoOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37.6816851,4.67509546 L39.8814657,6.85361151 C39.9573221,6.92873467 40,7.03106463 40,7.13782458 L40,12.7941829 L40,12.7941829 C40,13.6148733 39.334699,14.2801742 38.5140087,14.2801742 L32.7855772,14.2799123 C32.6801467,14.2799075 32.5789814,14.2382783 32.504082,14.164078 L30.3115658,11.9920253 C30.1546254,11.8365497 30.153438,11.5832865 30.3089136,11.4263461 C30.3840286,11.3505234 30.4863305,11.3078645 30.5930607,11.3078596 C31.6352757,11.3078111 32.6774907,11.3077627 33.7197057,11.3077142 C33.9940808,11.3077015 34.4056434,11.3076824 34.9543935,11.3076569 C34.447242,10.9019845 34.0603405,10.6058972 33.7936891,10.4193952 C31.0251782,8.48303631 27.6466787,7.3455482 24,7.3455482 C14.6111593,7.3455482 7,14.8856258 7,24.1867828 C7,33.4879399 14.6111593,41.0280174 24,41.0280174 C32.9375071,41.0280174 40.2641356,34.1954384 40.9478833,25.5152879 C40.9655577,25.2909127 40.979903,24.9777463 40.9909194,24.575789 C40.9968894,24.3592445 41.1741425,24.1867828 41.3907692,24.1867828 C41.6454621,24.1867828 41.848539,24.1867828 42,24.1867828 C42.2242544,24.1867828 42.505085,24.1867828 42.8424919,24.1867828 C43.0040925,24.1867828 43.2540143,24.1867828 43.5922573,24.1867828 L43.5922573,24.1867425 C43.8131934,24.1867425 43.9922976,24.3658467 43.9922976,24.5867828 C43.9922976,24.5898274 43.9922628,24.592872 43.9921933,24.5959158 C43.9839849,24.955365 43.9734263,25.2373729 43.9605177,25.4419395 C43.3069184,35.7996864 34.62013,44 24,44 C12.954305,44 4,35.1293205 4,24.1867828 C4,13.2442451 12.954305,4.37356563 24,4.37356563 C28.4185686,4.37356563 32.5024707,5.79306104 35.8123933,8.19684145 C36.0846021,8.3945289 36.4808621,8.70576637 37.0011734,9.13055386 C37.0010426,8.55895913 37.0009446,8.13026308 37.0008792,7.84446571 C37.0006593,6.88277715 37.0004394,5.92108858 37.0002194,4.95940002 C37.0001689,4.73848612 37.179214,4.55935907 37.4001279,4.55930855 C37.5055732,4.55928443 37.6067628,4.60089741 37.6816851,4.67509546 Z\",\n    id: \"UndoOutline-\\u8DEF\\u5F84\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default UndoOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yBAAyB;IAC7BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,y4DAAy4D;IAC54DR,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}