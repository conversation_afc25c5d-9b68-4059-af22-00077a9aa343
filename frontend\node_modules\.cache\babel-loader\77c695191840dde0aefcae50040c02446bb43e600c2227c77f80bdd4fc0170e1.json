{"ast": null, "code": "import { animated, useSpring } from '@react-spring/web';\nimport { useDrag } from '@use-gesture/react';\nimport React, { forwardRef, useImperativeHandle, useRef } from 'react';\nimport { bound } from '../../utils/bound';\nimport { convertPx } from '../../utils/convert-px';\nimport { Slide } from './slide';\nconst classPrefix = `adm-image-viewer`;\nexport const Slides = forwardRef((props, ref) => {\n  const slideWidth = window.innerWidth + convertPx(16);\n  const [{\n    x\n  }, api] = useSpring(() => ({\n    x: props.defaultIndex * slideWidth,\n    config: {\n      tension: 250,\n      clamp: true\n    }\n  }));\n  const count = props.images.length;\n  function swipeTo(index, immediate = false) {\n    var _a;\n    const i = bound(index, 0, count - 1);\n    (_a = props.onIndexChange) === null || _a === void 0 ? void 0 : _a.call(props, i);\n    api.start({\n      x: i * slideWidth,\n      immediate\n    });\n  }\n  useImperativeHandle(ref, () => ({\n    swipeTo\n  }));\n  const dragLockRef = useRef(false);\n  const bind = useDrag(state => {\n    if (dragLockRef.current) return;\n    const [offsetX] = state.offset;\n    if (state.last) {\n      const minIndex = Math.floor(offsetX / slideWidth);\n      const maxIndex = minIndex + 1;\n      const velocityOffset = Math.min(state.velocity[0] * 2000, slideWidth) * state.direction[0];\n      swipeTo(bound(Math.round((offsetX + velocityOffset) / slideWidth), minIndex, maxIndex));\n    } else {\n      api.start({\n        x: offsetX,\n        immediate: true\n      });\n    }\n  }, {\n    transform: ([x, y]) => [-x, y],\n    from: () => [x.get(), 0],\n    bounds: () => ({\n      left: 0,\n      right: (count - 1) * slideWidth\n    }),\n    rubberband: true,\n    axis: 'x',\n    pointer: {\n      touch: true\n    }\n  });\n  return React.createElement(\"div\", Object.assign({\n    className: `${classPrefix}-slides`\n  }, bind()), React.createElement(animated.div, {\n    className: `${classPrefix}-indicator`\n  }, x.to(v => {\n    const index = bound(Math.round(v / slideWidth), 0, count - 1);\n    return `${index + 1} / ${count}`;\n  })), React.createElement(animated.div, {\n    className: `${classPrefix}-slides-inner`,\n    style: {\n      x: x.to(x => -x)\n    }\n  }, props.images.map((image, index) => React.createElement(Slide, {\n    key: index,\n    image: image,\n    onTap: props.onTap,\n    maxZoom: props.maxZoom,\n    imageRender: props.imageRender,\n    index: index,\n    onZoomChange: zoom => {\n      if (zoom !== 1) {\n        const index = Math.round(x.get() / slideWidth);\n        api.start({\n          x: index * slideWidth\n        });\n      }\n    },\n    dragLockRef: dragLockRef\n  }))));\n});", "map": {"version": 3, "names": ["animated", "useSpring", "useDrag", "React", "forwardRef", "useImperativeHandle", "useRef", "bound", "convertPx", "Slide", "classPrefix", "Slides", "props", "ref", "slideWidth", "window", "innerWidth", "x", "api", "defaultIndex", "config", "tension", "clamp", "count", "images", "length", "swipeTo", "index", "immediate", "_a", "i", "onIndexChange", "call", "start", "dragLockRef", "bind", "state", "current", "offsetX", "offset", "last", "minIndex", "Math", "floor", "maxIndex", "velocityOffset", "min", "velocity", "direction", "round", "transform", "y", "from", "get", "bounds", "left", "right", "rubberband", "axis", "pointer", "touch", "createElement", "Object", "assign", "className", "div", "to", "v", "style", "map", "image", "key", "onTap", "max<PERSON><PERSON>", "imageRender", "onZoomChange", "zoom"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/image-viewer/slides.js"], "sourcesContent": ["import { animated, useSpring } from '@react-spring/web';\nimport { useDrag } from '@use-gesture/react';\nimport React, { forwardRef, useImperativeHandle, useRef } from 'react';\nimport { bound } from '../../utils/bound';\nimport { convertPx } from '../../utils/convert-px';\nimport { Slide } from './slide';\nconst classPrefix = `adm-image-viewer`;\nexport const Slides = forwardRef((props, ref) => {\n  const slideWidth = window.innerWidth + convertPx(16);\n  const [{\n    x\n  }, api] = useSpring(() => ({\n    x: props.defaultIndex * slideWidth,\n    config: {\n      tension: 250,\n      clamp: true\n    }\n  }));\n  const count = props.images.length;\n  function swipeTo(index, immediate = false) {\n    var _a;\n    const i = bound(index, 0, count - 1);\n    (_a = props.onIndexChange) === null || _a === void 0 ? void 0 : _a.call(props, i);\n    api.start({\n      x: i * slideWidth,\n      immediate\n    });\n  }\n  useImperativeHandle(ref, () => ({\n    swipeTo\n  }));\n  const dragLockRef = useRef(false);\n  const bind = useDrag(state => {\n    if (dragLockRef.current) return;\n    const [offsetX] = state.offset;\n    if (state.last) {\n      const minIndex = Math.floor(offsetX / slideWidth);\n      const maxIndex = minIndex + 1;\n      const velocityOffset = Math.min(state.velocity[0] * 2000, slideWidth) * state.direction[0];\n      swipeTo(bound(Math.round((offsetX + velocityOffset) / slideWidth), minIndex, maxIndex));\n    } else {\n      api.start({\n        x: offsetX,\n        immediate: true\n      });\n    }\n  }, {\n    transform: ([x, y]) => [-x, y],\n    from: () => [x.get(), 0],\n    bounds: () => ({\n      left: 0,\n      right: (count - 1) * slideWidth\n    }),\n    rubberband: true,\n    axis: 'x',\n    pointer: {\n      touch: true\n    }\n  });\n  return React.createElement(\"div\", Object.assign({\n    className: `${classPrefix}-slides`\n  }, bind()), React.createElement(animated.div, {\n    className: `${classPrefix}-indicator`\n  }, x.to(v => {\n    const index = bound(Math.round(v / slideWidth), 0, count - 1);\n    return `${index + 1} / ${count}`;\n  })), React.createElement(animated.div, {\n    className: `${classPrefix}-slides-inner`,\n    style: {\n      x: x.to(x => -x)\n    }\n  }, props.images.map((image, index) => React.createElement(Slide, {\n    key: index,\n    image: image,\n    onTap: props.onTap,\n    maxZoom: props.maxZoom,\n    imageRender: props.imageRender,\n    index: index,\n    onZoomChange: zoom => {\n      if (zoom !== 1) {\n        const index = Math.round(x.get() / slideWidth);\n        api.start({\n          x: index * slideWidth\n        });\n      }\n    },\n    dragLockRef: dragLockRef\n  }))));\n});"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,mBAAmB;AACvD,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,KAAK,IAAIC,UAAU,EAAEC,mBAAmB,EAAEC,MAAM,QAAQ,OAAO;AACtE,SAASC,KAAK,QAAQ,mBAAmB;AACzC,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,KAAK,QAAQ,SAAS;AAC/B,MAAMC,WAAW,GAAG,kBAAkB;AACtC,OAAO,MAAMC,MAAM,GAAGP,UAAU,CAAC,CAACQ,KAAK,EAAEC,GAAG,KAAK;EAC/C,MAAMC,UAAU,GAAGC,MAAM,CAACC,UAAU,GAAGR,SAAS,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC;IACLS;EACF,CAAC,EAAEC,GAAG,CAAC,GAAGjB,SAAS,CAAC,OAAO;IACzBgB,CAAC,EAAEL,KAAK,CAACO,YAAY,GAAGL,UAAU;IAClCM,MAAM,EAAE;MACNC,OAAO,EAAE,GAAG;MACZC,KAAK,EAAE;IACT;EACF,CAAC,CAAC,CAAC;EACH,MAAMC,KAAK,GAAGX,KAAK,CAACY,MAAM,CAACC,MAAM;EACjC,SAASC,OAAOA,CAACC,KAAK,EAAEC,SAAS,GAAG,KAAK,EAAE;IACzC,IAAIC,EAAE;IACN,MAAMC,CAAC,GAAGvB,KAAK,CAACoB,KAAK,EAAE,CAAC,EAAEJ,KAAK,GAAG,CAAC,CAAC;IACpC,CAACM,EAAE,GAAGjB,KAAK,CAACmB,aAAa,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACpB,KAAK,EAAEkB,CAAC,CAAC;IACjFZ,GAAG,CAACe,KAAK,CAAC;MACRhB,CAAC,EAAEa,CAAC,GAAGhB,UAAU;MACjBc;IACF,CAAC,CAAC;EACJ;EACAvB,mBAAmB,CAACQ,GAAG,EAAE,OAAO;IAC9Ba;EACF,CAAC,CAAC,CAAC;EACH,MAAMQ,WAAW,GAAG5B,MAAM,CAAC,KAAK,CAAC;EACjC,MAAM6B,IAAI,GAAGjC,OAAO,CAACkC,KAAK,IAAI;IAC5B,IAAIF,WAAW,CAACG,OAAO,EAAE;IACzB,MAAM,CAACC,OAAO,CAAC,GAAGF,KAAK,CAACG,MAAM;IAC9B,IAAIH,KAAK,CAACI,IAAI,EAAE;MACd,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,OAAO,GAAGxB,UAAU,CAAC;MACjD,MAAM8B,QAAQ,GAAGH,QAAQ,GAAG,CAAC;MAC7B,MAAMI,cAAc,GAAGH,IAAI,CAACI,GAAG,CAACV,KAAK,CAACW,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,EAAEjC,UAAU,CAAC,GAAGsB,KAAK,CAACY,SAAS,CAAC,CAAC,CAAC;MAC1FtB,OAAO,CAACnB,KAAK,CAACmC,IAAI,CAACO,KAAK,CAAC,CAACX,OAAO,GAAGO,cAAc,IAAI/B,UAAU,CAAC,EAAE2B,QAAQ,EAAEG,QAAQ,CAAC,CAAC;IACzF,CAAC,MAAM;MACL1B,GAAG,CAACe,KAAK,CAAC;QACRhB,CAAC,EAAEqB,OAAO;QACVV,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDsB,SAAS,EAAEA,CAAC,CAACjC,CAAC,EAAEkC,CAAC,CAAC,KAAK,CAAC,CAAClC,CAAC,EAAEkC,CAAC,CAAC;IAC9BC,IAAI,EAAEA,CAAA,KAAM,CAACnC,CAAC,CAACoC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACxBC,MAAM,EAAEA,CAAA,MAAO;MACbC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAACjC,KAAK,GAAG,CAAC,IAAIT;IACvB,CAAC,CAAC;IACF2C,UAAU,EAAE,IAAI;IAChBC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE;MACPC,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACF,OAAOzD,KAAK,CAAC0D,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC9CC,SAAS,EAAE,GAAGtD,WAAW;EAC3B,CAAC,EAAEyB,IAAI,CAAC,CAAC,CAAC,EAAEhC,KAAK,CAAC0D,aAAa,CAAC7D,QAAQ,CAACiE,GAAG,EAAE;IAC5CD,SAAS,EAAE,GAAGtD,WAAW;EAC3B,CAAC,EAAEO,CAAC,CAACiD,EAAE,CAACC,CAAC,IAAI;IACX,MAAMxC,KAAK,GAAGpB,KAAK,CAACmC,IAAI,CAACO,KAAK,CAACkB,CAAC,GAAGrD,UAAU,CAAC,EAAE,CAAC,EAAES,KAAK,GAAG,CAAC,CAAC;IAC7D,OAAO,GAAGI,KAAK,GAAG,CAAC,MAAMJ,KAAK,EAAE;EAClC,CAAC,CAAC,CAAC,EAAEpB,KAAK,CAAC0D,aAAa,CAAC7D,QAAQ,CAACiE,GAAG,EAAE;IACrCD,SAAS,EAAE,GAAGtD,WAAW,eAAe;IACxC0D,KAAK,EAAE;MACLnD,CAAC,EAAEA,CAAC,CAACiD,EAAE,CAACjD,CAAC,IAAI,CAACA,CAAC;IACjB;EACF,CAAC,EAAEL,KAAK,CAACY,MAAM,CAAC6C,GAAG,CAAC,CAACC,KAAK,EAAE3C,KAAK,KAAKxB,KAAK,CAAC0D,aAAa,CAACpD,KAAK,EAAE;IAC/D8D,GAAG,EAAE5C,KAAK;IACV2C,KAAK,EAAEA,KAAK;IACZE,KAAK,EAAE5D,KAAK,CAAC4D,KAAK;IAClBC,OAAO,EAAE7D,KAAK,CAAC6D,OAAO;IACtBC,WAAW,EAAE9D,KAAK,CAAC8D,WAAW;IAC9B/C,KAAK,EAAEA,KAAK;IACZgD,YAAY,EAAEC,IAAI,IAAI;MACpB,IAAIA,IAAI,KAAK,CAAC,EAAE;QACd,MAAMjD,KAAK,GAAGe,IAAI,CAACO,KAAK,CAAChC,CAAC,CAACoC,GAAG,CAAC,CAAC,GAAGvC,UAAU,CAAC;QAC9CI,GAAG,CAACe,KAAK,CAAC;UACRhB,CAAC,EAAEU,KAAK,GAAGb;QACb,CAAC,CAAC;MACJ;IACF,CAAC;IACDoB,WAAW,EAAEA;EACf,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}