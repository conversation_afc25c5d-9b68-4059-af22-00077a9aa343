{"ast": null, "code": "import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport classNames from 'classnames';\nimport Popup from '../popup';\nimport SafeArea from '../safe-area';\nimport { renderImperatively } from '../../utils/render-imperatively';\nconst classPrefix = `adm-action-sheet`;\nconst defaultProps = {\n  visible: false,\n  actions: [],\n  cancelText: '',\n  closeOnAction: false,\n  closeOnMaskClick: true,\n  safeArea: true,\n  destroyOnClose: false,\n  forceRender: false\n};\nexport const ActionSheet = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    styles\n  } = props;\n  return React.createElement(Popup, {\n    visible: props.visible,\n    onMaskClick: () => {\n      var _a, _b;\n      (_a = props.onMaskClick) === null || _a === void 0 ? void 0 : _a.call(props);\n      if (props.closeOnMaskClick) {\n        (_b = props.onClose) === null || _b === void 0 ? void 0 : _b.call(props);\n      }\n    },\n    afterClose: props.afterClose,\n    className: classNames(`${classPrefix}-popup`, props.popupClassName),\n    style: props.popupStyle,\n    getContainer: props.getContainer,\n    destroyOnClose: props.destroyOnClose,\n    forceRender: props.forceRender,\n    bodyStyle: styles === null || styles === void 0 ? void 0 : styles.body,\n    maskStyle: styles === null || styles === void 0 ? void 0 : styles.mask\n  }, withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, props.extra && React.createElement(\"div\", {\n    className: `${classPrefix}-extra`\n  }, props.extra), React.createElement(\"div\", {\n    className: `${classPrefix}-button-list`\n  }, props.actions.map((action, index) => React.createElement(\"div\", {\n    key: action.key,\n    className: `${classPrefix}-button-item-wrapper`\n  }, React.createElement(\"a\", {\n    className: classNames('adm-plain-anchor', `${classPrefix}-button-item`, {\n      [`${classPrefix}-button-item-danger`]: action.danger,\n      [`${classPrefix}-button-item-disabled`]: action.disabled,\n      [`${classPrefix}-button-item-bold`]: action.bold\n    }),\n    onClick: () => {\n      var _a, _b, _c;\n      (_a = action.onClick) === null || _a === void 0 ? void 0 : _a.call(action);\n      (_b = props.onAction) === null || _b === void 0 ? void 0 : _b.call(props, action, index);\n      if (props.closeOnAction) {\n        (_c = props.onClose) === null || _c === void 0 ? void 0 : _c.call(props);\n      }\n    },\n    role: 'option',\n    \"aria-disabled\": action.disabled\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-button-item-name`\n  }, action.text), action.description && React.createElement(\"div\", {\n    className: `${classPrefix}-button-item-description`\n  }, action.description))))), props.cancelText && React.createElement(\"div\", {\n    className: `${classPrefix}-cancel`,\n    role: 'option',\n    \"aria-label\": props.cancelText\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-button-item-wrapper`\n  }, React.createElement(\"a\", {\n    className: classNames('adm-plain-anchor', `${classPrefix}-button-item`),\n    onClick: props.onClose\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-button-item-name`\n  }, props.cancelText)))), props.safeArea && React.createElement(SafeArea, {\n    position: 'bottom'\n  }))));\n};\nexport function showActionSheet(props) {\n  return renderImperatively(React.createElement(ActionSheet, Object.assign({}, props)));\n}", "map": {"version": 3, "names": ["React", "withNativeProps", "mergeProps", "classNames", "Popup", "SafeArea", "renderImperatively", "classPrefix", "defaultProps", "visible", "actions", "cancelText", "closeOnAction", "closeOnMaskClick", "safeArea", "destroyOnClose", "forceRender", "ActionSheet", "p", "props", "styles", "createElement", "onMaskClick", "_a", "_b", "call", "onClose", "afterClose", "className", "popupClassName", "style", "popupStyle", "getContainer", "bodyStyle", "body", "maskStyle", "mask", "extra", "map", "action", "index", "key", "danger", "disabled", "bold", "onClick", "_c", "onAction", "role", "text", "description", "position", "showActionSheet", "Object", "assign"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/action-sheet/action-sheet.js"], "sourcesContent": ["import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport classNames from 'classnames';\nimport Popup from '../popup';\nimport SafeArea from '../safe-area';\nimport { renderImperatively } from '../../utils/render-imperatively';\nconst classPrefix = `adm-action-sheet`;\nconst defaultProps = {\n  visible: false,\n  actions: [],\n  cancelText: '',\n  closeOnAction: false,\n  closeOnMaskClick: true,\n  safeArea: true,\n  destroyOnClose: false,\n  forceRender: false\n};\nexport const ActionSheet = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    styles\n  } = props;\n  return React.createElement(Popup, {\n    visible: props.visible,\n    onMaskClick: () => {\n      var _a, _b;\n      (_a = props.onMaskClick) === null || _a === void 0 ? void 0 : _a.call(props);\n      if (props.closeOnMaskClick) {\n        (_b = props.onClose) === null || _b === void 0 ? void 0 : _b.call(props);\n      }\n    },\n    afterClose: props.afterClose,\n    className: classNames(`${classPrefix}-popup`, props.popupClassName),\n    style: props.popupStyle,\n    getContainer: props.getContainer,\n    destroyOnClose: props.destroyOnClose,\n    forceRender: props.forceRender,\n    bodyStyle: styles === null || styles === void 0 ? void 0 : styles.body,\n    maskStyle: styles === null || styles === void 0 ? void 0 : styles.mask\n  }, withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, props.extra && React.createElement(\"div\", {\n    className: `${classPrefix}-extra`\n  }, props.extra), React.createElement(\"div\", {\n    className: `${classPrefix}-button-list`\n  }, props.actions.map((action, index) => React.createElement(\"div\", {\n    key: action.key,\n    className: `${classPrefix}-button-item-wrapper`\n  }, React.createElement(\"a\", {\n    className: classNames('adm-plain-anchor', `${classPrefix}-button-item`, {\n      [`${classPrefix}-button-item-danger`]: action.danger,\n      [`${classPrefix}-button-item-disabled`]: action.disabled,\n      [`${classPrefix}-button-item-bold`]: action.bold\n    }),\n    onClick: () => {\n      var _a, _b, _c;\n      (_a = action.onClick) === null || _a === void 0 ? void 0 : _a.call(action);\n      (_b = props.onAction) === null || _b === void 0 ? void 0 : _b.call(props, action, index);\n      if (props.closeOnAction) {\n        (_c = props.onClose) === null || _c === void 0 ? void 0 : _c.call(props);\n      }\n    },\n    role: 'option',\n    \"aria-disabled\": action.disabled\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-button-item-name`\n  }, action.text), action.description && React.createElement(\"div\", {\n    className: `${classPrefix}-button-item-description`\n  }, action.description))))), props.cancelText && React.createElement(\"div\", {\n    className: `${classPrefix}-cancel`,\n    role: 'option',\n    \"aria-label\": props.cancelText\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-button-item-wrapper`\n  }, React.createElement(\"a\", {\n    className: classNames('adm-plain-anchor', `${classPrefix}-button-item`),\n    onClick: props.onClose\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-button-item-name`\n  }, props.cancelText)))), props.safeArea && React.createElement(SafeArea, {\n    position: 'bottom'\n  }))));\n};\nexport function showActionSheet(props) {\n  return renderImperatively(React.createElement(ActionSheet, Object.assign({}, props)));\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,QAAQ,MAAM,cAAc;AACnC,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,MAAMC,WAAW,GAAG,kBAAkB;AACtC,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,EAAE;EACXC,UAAU,EAAE,EAAE;EACdC,aAAa,EAAE,KAAK;EACpBC,gBAAgB,EAAE,IAAI;EACtBC,QAAQ,EAAE,IAAI;EACdC,cAAc,EAAE,KAAK;EACrBC,WAAW,EAAE;AACf,CAAC;AACD,OAAO,MAAMC,WAAW,GAAGC,CAAC,IAAI;EAC9B,MAAMC,KAAK,GAAGjB,UAAU,CAACM,YAAY,EAAEU,CAAC,CAAC;EACzC,MAAM;IACJE;EACF,CAAC,GAAGD,KAAK;EACT,OAAOnB,KAAK,CAACqB,aAAa,CAACjB,KAAK,EAAE;IAChCK,OAAO,EAAEU,KAAK,CAACV,OAAO;IACtBa,WAAW,EAAEA,CAAA,KAAM;MACjB,IAAIC,EAAE,EAAEC,EAAE;MACV,CAACD,EAAE,GAAGJ,KAAK,CAACG,WAAW,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACN,KAAK,CAAC;MAC5E,IAAIA,KAAK,CAACN,gBAAgB,EAAE;QAC1B,CAACW,EAAE,GAAGL,KAAK,CAACO,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACN,KAAK,CAAC;MAC1E;IACF,CAAC;IACDQ,UAAU,EAAER,KAAK,CAACQ,UAAU;IAC5BC,SAAS,EAAEzB,UAAU,CAAC,GAAGI,WAAW,QAAQ,EAAEY,KAAK,CAACU,cAAc,CAAC;IACnEC,KAAK,EAAEX,KAAK,CAACY,UAAU;IACvBC,YAAY,EAAEb,KAAK,CAACa,YAAY;IAChCjB,cAAc,EAAEI,KAAK,CAACJ,cAAc;IACpCC,WAAW,EAAEG,KAAK,CAACH,WAAW;IAC9BiB,SAAS,EAAEb,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACc,IAAI;IACtEC,SAAS,EAAEf,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACgB;EACpE,CAAC,EAAEnC,eAAe,CAACkB,KAAK,EAAEnB,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IACnDO,SAAS,EAAErB;EACb,CAAC,EAAEY,KAAK,CAACkB,KAAK,IAAIrC,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IAC3CO,SAAS,EAAE,GAAGrB,WAAW;EAC3B,CAAC,EAAEY,KAAK,CAACkB,KAAK,CAAC,EAAErC,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IAC1CO,SAAS,EAAE,GAAGrB,WAAW;EAC3B,CAAC,EAAEY,KAAK,CAACT,OAAO,CAAC4B,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAKxC,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IACjEoB,GAAG,EAAEF,MAAM,CAACE,GAAG;IACfb,SAAS,EAAE,GAAGrB,WAAW;EAC3B,CAAC,EAAEP,KAAK,CAACqB,aAAa,CAAC,GAAG,EAAE;IAC1BO,SAAS,EAAEzB,UAAU,CAAC,kBAAkB,EAAE,GAAGI,WAAW,cAAc,EAAE;MACtE,CAAC,GAAGA,WAAW,qBAAqB,GAAGgC,MAAM,CAACG,MAAM;MACpD,CAAC,GAAGnC,WAAW,uBAAuB,GAAGgC,MAAM,CAACI,QAAQ;MACxD,CAAC,GAAGpC,WAAW,mBAAmB,GAAGgC,MAAM,CAACK;IAC9C,CAAC,CAAC;IACFC,OAAO,EAAEA,CAAA,KAAM;MACb,IAAItB,EAAE,EAAEC,EAAE,EAAEsB,EAAE;MACd,CAACvB,EAAE,GAAGgB,MAAM,CAACM,OAAO,MAAM,IAAI,IAAItB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACc,MAAM,CAAC;MAC1E,CAACf,EAAE,GAAGL,KAAK,CAAC4B,QAAQ,MAAM,IAAI,IAAIvB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACN,KAAK,EAAEoB,MAAM,EAAEC,KAAK,CAAC;MACxF,IAAIrB,KAAK,CAACP,aAAa,EAAE;QACvB,CAACkC,EAAE,GAAG3B,KAAK,CAACO,OAAO,MAAM,IAAI,IAAIoB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACrB,IAAI,CAACN,KAAK,CAAC;MAC1E;IACF,CAAC;IACD6B,IAAI,EAAE,QAAQ;IACd,eAAe,EAAET,MAAM,CAACI;EAC1B,CAAC,EAAE3C,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IAC5BO,SAAS,EAAE,GAAGrB,WAAW;EAC3B,CAAC,EAAEgC,MAAM,CAACU,IAAI,CAAC,EAAEV,MAAM,CAACW,WAAW,IAAIlD,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IAChEO,SAAS,EAAE,GAAGrB,WAAW;EAC3B,CAAC,EAAEgC,MAAM,CAACW,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE/B,KAAK,CAACR,UAAU,IAAIX,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IACzEO,SAAS,EAAE,GAAGrB,WAAW,SAAS;IAClCyC,IAAI,EAAE,QAAQ;IACd,YAAY,EAAE7B,KAAK,CAACR;EACtB,CAAC,EAAEX,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IAC5BO,SAAS,EAAE,GAAGrB,WAAW;EAC3B,CAAC,EAAEP,KAAK,CAACqB,aAAa,CAAC,GAAG,EAAE;IAC1BO,SAAS,EAAEzB,UAAU,CAAC,kBAAkB,EAAE,GAAGI,WAAW,cAAc,CAAC;IACvEsC,OAAO,EAAE1B,KAAK,CAACO;EACjB,CAAC,EAAE1B,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IAC5BO,SAAS,EAAE,GAAGrB,WAAW;EAC3B,CAAC,EAAEY,KAAK,CAACR,UAAU,CAAC,CAAC,CAAC,CAAC,EAAEQ,KAAK,CAACL,QAAQ,IAAId,KAAK,CAACqB,aAAa,CAAChB,QAAQ,EAAE;IACvE8C,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AACD,OAAO,SAASC,eAAeA,CAACjC,KAAK,EAAE;EACrC,OAAOb,kBAAkB,CAACN,KAAK,CAACqB,aAAa,CAACJ,WAAW,EAAEoC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnC,KAAK,CAAC,CAAC,CAAC;AACvF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}