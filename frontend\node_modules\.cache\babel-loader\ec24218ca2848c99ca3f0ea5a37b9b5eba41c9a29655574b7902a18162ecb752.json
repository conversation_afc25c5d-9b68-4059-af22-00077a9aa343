{"ast": null, "code": "import * as React from \"react\";\nfunction HeartOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"HeartOutline-HeartOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"HeartOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"HeartOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M6.60303038,8.73081219 C11.1967926,3.97413179 18.5198212,3.76592648 23.3525096,8.10619626 L23.665784,8.39850141 L23.665784,8.39850141 L23.7159853,8.44807668 C23.874066,8.60458777 24.1229529,8.60507888 24.2816085,8.44919278 L24.4080172,8.32694173 L24.4080172,8.32694173 C29.2353836,3.76025092 36.7266646,3.89487441 41.3969696,8.73081219 C46.2010101,13.7052284 46.2010101,21.7703507 41.3969696,26.744767 L26.8994949,41.7563959 C25.2981481,43.4145347 22.7018519,43.4145347 21.1005051,41.7563959 L6.60303038,26.744767 C1.79898987,21.7703507 1.79898987,13.7052284 6.60303038,8.73081219 Z M8.77765158,10.9825565 C5.23896103,14.6467471 5.17577013,20.5469195 8.58807887,24.2909666 L8.77765158,24.4930226 L23.2751263,39.5046516 C23.6468675,39.8895767 24.2330812,39.9170713 24.6354614,39.5871355 L24.7248737,39.5046516 L39.2223484,24.4930226 C42.8253788,20.7622104 42.8253788,14.7133687 39.2223484,10.9825565 C35.6836579,7.31836599 29.9855687,7.25293402 26.3697567,10.7862606 L26.1746212,10.9825565 L24.3133061,12.9098833 C24.1545722,13.0742467 23.8977087,13.075894 23.7370208,12.913579 L21.6564622,10.811953 C18.0368282,7.2463877 12.318732,7.31589138 8.77765158,10.9825565 Z\",\n    id: \"HeartOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default HeartOutline;", "map": {"version": 3, "names": ["React", "HeartOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/HeartOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction HeartOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"HeartOutline-HeartOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"HeartOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"HeartOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M6.60303038,8.73081219 C11.1967926,3.97413179 18.5198212,3.76592648 23.3525096,8.10619626 L23.665784,8.39850141 L23.665784,8.39850141 L23.7159853,8.44807668 C23.874066,8.60458777 24.1229529,8.60507888 24.2816085,8.44919278 L24.4080172,8.32694173 L24.4080172,8.32694173 C29.2353836,3.76025092 36.7266646,3.89487441 41.3969696,8.73081219 C46.2010101,13.7052284 46.2010101,21.7703507 41.3969696,26.744767 L26.8994949,41.7563959 C25.2981481,43.4145347 22.7018519,43.4145347 21.1005051,41.7563959 L6.60303038,26.744767 C1.79898987,21.7703507 1.79898987,13.7052284 6.60303038,8.73081219 Z M8.77765158,10.9825565 C5.23896103,14.6467471 5.17577013,20.5469195 8.58807887,24.2909666 L8.77765158,24.4930226 L23.2751263,39.5046516 C23.6468675,39.8895767 24.2330812,39.9170713 24.6354614,39.5871355 L24.7248737,39.5046516 L39.2223484,24.4930226 C42.8253788,20.7622104 42.8253788,14.7133687 39.2223484,10.9825565 C35.6836579,7.31836599 29.9855687,7.25293402 26.3697567,10.7862606 L26.1746212,10.9825565 L24.3133061,12.9098833 C24.1545722,13.0742467 23.8977087,13.075894 23.7370208,12.913579 L21.6564622,10.811953 C18.0368282,7.2463877 12.318732,7.31589138 8.77765158,10.9825565 Z\",\n    id: \"HeartOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default HeartOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,2BAA2B;IAC/BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,2BAA2B;IAC/BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,+oCAA+oC;IAClpCR,EAAE,EAAE,2BAA2B;IAC/BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}