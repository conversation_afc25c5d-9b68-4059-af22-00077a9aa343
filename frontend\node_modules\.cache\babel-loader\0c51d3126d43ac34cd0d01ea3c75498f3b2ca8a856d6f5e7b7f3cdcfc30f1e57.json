{"ast": null, "code": "import * as React from \"react\";\nfunction UpOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UpOutline-UpOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UpOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M5.11219264,30.7053818 L22.6612572,12.5234393 L22.6612572,12.5234393 C23.2125856,11.969699 24.0863155,11.9371261 24.6755735,12.4257204 L24.7825775,12.5234393 L42.8834676,30.7045714 C42.9580998,30.7795345 43,30.8810078 43,30.9867879 L43,34.1135102 C43,34.3344241 42.8209139,34.5135102 42.6,34.5135102 C42.4936115,34.5135102 42.391606,34.4711279 42.316542,34.3957362 L23.7816937,15.7799842 L23.7816937,15.7799842 L5.6866816,34.3764658 C5.53262122,34.5347957 5.27937888,34.5382568 5.121049,34.3841964 C5.04365775,34.3088921 5,34.205497 5,34.0975148 L5,30.9831711 C5,30.8795372 5.04022164,30.7799483 5.11219264,30.7053818 Z\",\n    id: \"UpOutline-up\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default UpOutline;", "map": {"version": 3, "names": ["React", "UpOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/UpOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction UpOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UpOutline-UpOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UpOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M5.11219264,30.7053818 L22.6612572,12.5234393 L22.6612572,12.5234393 C23.2125856,11.969699 24.0863155,11.9371261 24.6755735,12.4257204 L24.7825775,12.5234393 L42.8834676,30.7045714 C42.9580998,30.7795345 43,30.8810078 43,30.9867879 L43,34.1135102 C43,34.3344241 42.8209139,34.5135102 42.6,34.5135102 C42.4936115,34.5135102 42.391606,34.4711279 42.316542,34.3957362 L23.7816937,15.7799842 L23.7816937,15.7799842 L5.6866816,34.3764658 C5.53262122,34.5347957 5.27937888,34.5382568 5.121049,34.3841964 C5.04365775,34.3088921 5,34.205497 5,34.0975148 L5,30.9831711 C5,30.8795372 5.04022164,30.7799483 5.11219264,30.7053818 Z\",\n    id: \"UpOutline-up\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default UpOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,qBAAqB;IACzBC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,aAAaH,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IACtFc,EAAE,EAAE,wBAAwB;IAC5BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,6mBAA6mB;IAChnBR,EAAE,EAAE,cAAc;IAClBG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}