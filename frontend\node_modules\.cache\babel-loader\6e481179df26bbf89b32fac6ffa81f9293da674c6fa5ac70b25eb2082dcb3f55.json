{"ast": null, "code": "import * as React from \"react\";\nfunction ExclamationCircleOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ExclamationCircleOutline-ExclamationCircleOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ExclamationCircleOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ExclamationCircleOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M25.1,31 C25.3209139,31 25.5,31.1790861 25.5,31.4 L25.5,33.6 C25.5,33.8209139 25.3209139,34 25.1,34 L22.9,34 C22.6790861,34 22.5,33.8209139 22.5,33.6 L22.5,31.4 C22.5,31.1790861 22.6790861,31 22.9,31 L25.1,31 Z M25.1,14 C25.3209139,14 25.5,14.1790861 25.5,14.4 L25.5,27.6 C25.5,27.8209139 25.3209139,28 25.1,28 L22.9,28 C22.6790861,28 22.5,27.8209139 22.5,27.6 L22.5,14.4 C22.5,14.1790861 22.6790861,14 22.9,14 L25.1,14 Z\",\n    id: \"ExclamationCircleOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\"\n  }))));\n}\nexport default ExclamationCircleOutline;", "map": {"version": 3, "names": ["React", "ExclamationCircleOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/ExclamationCircleOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction ExclamationCircleOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ExclamationCircleOutline-ExclamationCircleOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ExclamationCircleOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ExclamationCircleOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M25.1,31 C25.3209139,31 25.5,31.1790861 25.5,31.4 L25.5,33.6 C25.5,33.8209139 25.3209139,34 25.1,34 L22.9,34 C22.6790861,34 22.5,33.8209139 22.5,33.6 L22.5,31.4 C22.5,31.1790861 22.6790861,31 22.9,31 L25.1,31 Z M25.1,14 C25.3209139,14 25.5,14.1790861 25.5,14.4 L25.5,27.6 C25.5,27.8209139 25.3209139,28 25.1,28 L22.9,28 C22.6790861,28 22.5,27.8209139 22.5,27.6 L22.5,14.4 C22.5,14.1790861 22.6790861,14 22.9,14 L25.1,14 Z\",\n    id: \"ExclamationCircleOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\"\n  }))));\n}\n\nexport default ExclamationCircleOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,wBAAwBA,CAACC,KAAK,EAAE;EACvC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,mDAAmD;IACvDC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,uCAAuC;IAC3CG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,msBAAmsB;IACtsBR,EAAE,EAAE,mDAAmD;IACvDG,IAAI,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAenB,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}