{"ast": null, "code": "import * as React from \"react\";\nfunction ArrowsAltOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ArrowsAltOutline-ArrowsAltOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ArrowsAltOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ArrowsAltOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M27.54027,25.4192995 L37.9992424,35.878922 L37.9992424,35.878922 L37.9999293,26.5005854 C37.9999455,26.2796715 38.1790447,26.1005985 38.3999586,26.1006147 C38.506035,26.1006225 38.6077646,26.1427646 38.682772,26.217772 L40.8828427,28.4178427 C40.9578573,28.4928573 41,28.5945988 41,28.7006854 L41,38.0000814 L41,38.0000814 C41,39.5977189 39.7511139,40.9036635 38.1763494,40.9949074 L38.0000214,41 L28.7016885,41 C28.5956001,41 28.4938569,40.9578558 28.4188421,40.882839 L26.2188213,38.6827611 C26.0626136,38.5265493 26.0626169,38.2732833 26.2188286,38.1170756 C26.2938429,38.0420633 26.3955829,37.999922 26.5016677,37.999922 L35.8782424,37.999922 L35.8782424,37.999922 L25.4189935,27.540576 C25.2627863,27.3843648 25.2627869,27.1310998 25.4189948,26.9748893 L26.9745934,25.4192907 C27.1307943,25.2630722 27.3840603,25.2630722 27.54027,25.4192819 C27.5402729,25.4192848 27.5402759,25.4192878 27.54027,25.4192995 Z M19.5801734,7.11717343 L21.78015,9.31715001 C21.9363597,9.47335972 21.9363597,9.72662572 21.78015,9.88283543 C21.7051347,9.95785072 21.6033919,9.99999352 21.4973043,9.99999272 L12.1212424,9.99992204 L12.1212424,9.99992204 L22.5812531,20.4603418 C22.7374521,20.6165579 22.7374496,20.8698197 22.5812476,21.0260328 L21.025649,22.5816313 C20.8694312,22.7378225 20.6161755,22.7378285 20.4599503,22.5816447 L9.9992424,12.121922 L9.9992424,12.121922 L9.99992925,21.4981366 C9.99994543,21.7190505 9.82087245,21.8981497 9.59995855,21.8981659 C9.49386181,21.8981736 9.39210827,21.8560303 9.31708654,21.7810086 L7.11716153,19.5810836 C7.04214447,19.5060665 7.00000153,19.4043208 7.00000424,19.2982306 L7.0002424,10.0009006 L7.0002424,10.0009006 C7.0002424,8.40323116 8.24915346,7.09726047 9.82394948,7.00601469 L10.000221,7.00092204 L19.2972917,7.00001615 C19.4033918,7.00000581 19.5051493,7.04214933 19.5801734,7.11717343 Z\",\n    id: \"ArrowsAltOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default ArrowsAltOutline;", "map": {"version": 3, "names": ["React", "ArrowsAltOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/ArrowsAltOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction ArrowsAltOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ArrowsAltOutline-ArrowsAltOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ArrowsAltOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ArrowsAltOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M27.54027,25.4192995 L37.9992424,35.878922 L37.9992424,35.878922 L37.9999293,26.5005854 C37.9999455,26.2796715 38.1790447,26.1005985 38.3999586,26.1006147 C38.506035,26.1006225 38.6077646,26.1427646 38.682772,26.217772 L40.8828427,28.4178427 C40.9578573,28.4928573 41,28.5945988 41,28.7006854 L41,38.0000814 L41,38.0000814 C41,39.5977189 39.7511139,40.9036635 38.1763494,40.9949074 L38.0000214,41 L28.7016885,41 C28.5956001,41 28.4938569,40.9578558 28.4188421,40.882839 L26.2188213,38.6827611 C26.0626136,38.5265493 26.0626169,38.2732833 26.2188286,38.1170756 C26.2938429,38.0420633 26.3955829,37.999922 26.5016677,37.999922 L35.8782424,37.999922 L35.8782424,37.999922 L25.4189935,27.540576 C25.2627863,27.3843648 25.2627869,27.1310998 25.4189948,26.9748893 L26.9745934,25.4192907 C27.1307943,25.2630722 27.3840603,25.2630722 27.54027,25.4192819 C27.5402729,25.4192848 27.5402759,25.4192878 27.54027,25.4192995 Z M19.5801734,7.11717343 L21.78015,9.31715001 C21.9363597,9.47335972 21.9363597,9.72662572 21.78015,9.88283543 C21.7051347,9.95785072 21.6033919,9.99999352 21.4973043,9.99999272 L12.1212424,9.99992204 L12.1212424,9.99992204 L22.5812531,20.4603418 C22.7374521,20.6165579 22.7374496,20.8698197 22.5812476,21.0260328 L21.025649,22.5816313 C20.8694312,22.7378225 20.6161755,22.7378285 20.4599503,22.5816447 L9.9992424,12.121922 L9.9992424,12.121922 L9.99992925,21.4981366 C9.99994543,21.7190505 9.82087245,21.8981497 9.59995855,21.8981659 C9.49386181,21.8981736 9.39210827,21.8560303 9.31708654,21.7810086 L7.11716153,19.5810836 C7.04214447,19.5060665 7.00000153,19.4043208 7.00000424,19.2982306 L7.0002424,10.0009006 L7.0002424,10.0009006 C7.0002424,8.40323116 8.24915346,7.09726047 9.82394948,7.00601469 L10.000221,7.00092204 L19.2972917,7.00001615 C19.4033918,7.00000581 19.5051493,7.04214933 19.5801734,7.11717343 Z\",\n    id: \"ArrowsAltOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default ArrowsAltOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,mCAAmC;IACvCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,+BAA+B;IACnCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,oyDAAoyD;IACvyDR,EAAE,EAAE,+BAA+B;IACnCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}