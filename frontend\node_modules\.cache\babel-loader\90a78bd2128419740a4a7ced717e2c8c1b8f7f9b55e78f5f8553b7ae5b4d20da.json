{"ast": null, "code": "import { createContext } from 'react';\nexport const CheckListContext = createContext(null);", "map": {"version": 3, "names": ["createContext", "CheckListContext"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/check-list/context.js"], "sourcesContent": ["import { createContext } from 'react';\nexport const CheckListContext = createContext(null);"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,OAAO,MAAMC,gBAAgB,GAAGD,aAAa,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}