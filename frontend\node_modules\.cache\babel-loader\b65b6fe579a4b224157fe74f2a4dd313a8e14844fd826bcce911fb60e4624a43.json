{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport <PERSON>ton from '../button';\nimport { withNativeProps } from '../../utils/native-props';\nexport const DialogActionButton = props => {\n  const {\n    action\n  } = props;\n  return withNativeProps(props.action, React.createElement(<PERSON><PERSON>, {\n    key: action.key,\n    onClick: props.onAction,\n    className: classNames('adm-dialog-button', {\n      'adm-dialog-button-bold': action.bold\n    }),\n    fill: 'none',\n    shape: 'rectangular',\n    block: true,\n    color: action.danger ? 'danger' : 'primary',\n    loading: 'auto',\n    disabled: action.disabled\n  }, action.text));\n};", "map": {"version": 3, "names": ["React", "classNames", "<PERSON><PERSON>", "withNativeProps", "DialogActionButton", "props", "action", "createElement", "key", "onClick", "onAction", "className", "bold", "fill", "shape", "block", "color", "danger", "loading", "disabled", "text"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/dialog/dialog-action-button.js"], "sourcesContent": ["import React from 'react';\nimport classNames from 'classnames';\nimport <PERSON>ton from '../button';\nimport { withNativeProps } from '../../utils/native-props';\nexport const DialogActionButton = props => {\n  const {\n    action\n  } = props;\n  return withNativeProps(props.action, React.createElement(<PERSON><PERSON>, {\n    key: action.key,\n    onClick: props.onAction,\n    className: classNames('adm-dialog-button', {\n      'adm-dialog-button-bold': action.bold\n    }),\n    fill: 'none',\n    shape: 'rectangular',\n    block: true,\n    color: action.danger ? 'danger' : 'primary',\n    loading: 'auto',\n    disabled: action.disabled\n  }, action.text));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,kBAAkB,GAAGC,KAAK,IAAI;EACzC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAOF,eAAe,CAACE,KAAK,CAACC,MAAM,EAAEN,KAAK,CAACO,aAAa,CAACL,MAAM,EAAE;IAC/DM,GAAG,EAAEF,MAAM,CAACE,GAAG;IACfC,OAAO,EAAEJ,KAAK,CAACK,QAAQ;IACvBC,SAAS,EAAEV,UAAU,CAAC,mBAAmB,EAAE;MACzC,wBAAwB,EAAEK,MAAM,CAACM;IACnC,CAAC,CAAC;IACFC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAEV,MAAM,CAACW,MAAM,GAAG,QAAQ,GAAG,SAAS;IAC3CC,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAEb,MAAM,CAACa;EACnB,CAAC,EAAEb,MAAM,CAACc,IAAI,CAAC,CAAC;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}