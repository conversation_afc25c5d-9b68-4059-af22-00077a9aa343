{"ast": null, "code": "import React, { useEffect, useMemo } from 'react';\nimport classNames from 'classnames';\nimport Image from '../image';\nimport SpinLoading from '../spin-loading';\nimport { useConfig } from '../config-provider';\nconst classPrefix = `adm-image-uploader`;\nconst PreviewItem = props => {\n  const {\n    locale\n  } = useConfig();\n  const {\n    url,\n    file,\n    deletable,\n    deleteIcon,\n    onDelete,\n    imageFit\n  } = props;\n  const src = useMemo(() => {\n    if (url) {\n      return url;\n    }\n    if (file) {\n      return URL.createObjectURL(file);\n    }\n    return '';\n  }, [url, file]);\n  useEffect(() => {\n    return () => {\n      if (file) URL.revokeObjectURL(src);\n    };\n  }, [src, file]);\n  function renderLoading() {\n    return props.status === 'pending' && React.createElement(\"div\", {\n      className: `${classPrefix}-cell-mask`\n    }, React.createElement(\"span\", {\n      className: `${classPrefix}-cell-loading`\n    }, React.createElement(SpinLoading, {\n      color: 'white'\n    }), React.createElement(\"span\", {\n      className: `${classPrefix}-cell-mask-message`\n    }, locale.ImageUploader.uploading)));\n  }\n  function renderDelete() {\n    return deletable && React.createElement(\"span\", {\n      className: `${classPrefix}-cell-delete`,\n      onClick: onDelete\n    }, deleteIcon);\n  }\n  return React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-cell`, props.status === 'fail' && `${classPrefix}-cell-fail`)\n  }, React.createElement(Image, {\n    className: `${classPrefix}-cell-image`,\n    src: src,\n    fit: imageFit,\n    onClick: props.onClick\n  }), renderLoading(), renderDelete());\n};\nexport default PreviewItem;", "map": {"version": 3, "names": ["React", "useEffect", "useMemo", "classNames", "Image", "SpinLoading", "useConfig", "classPrefix", "PreviewItem", "props", "locale", "url", "file", "deletable", "deleteIcon", "onDelete", "imageFit", "src", "URL", "createObjectURL", "revokeObjectURL", "renderLoading", "status", "createElement", "className", "color", "ImageUploader", "uploading", "renderDelete", "onClick", "fit"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/image-uploader/preview-item.js"], "sourcesContent": ["import React, { useEffect, useMemo } from 'react';\nimport classNames from 'classnames';\nimport Image from '../image';\nimport SpinLoading from '../spin-loading';\nimport { useConfig } from '../config-provider';\nconst classPrefix = `adm-image-uploader`;\nconst PreviewItem = props => {\n  const {\n    locale\n  } = useConfig();\n  const {\n    url,\n    file,\n    deletable,\n    deleteIcon,\n    onDelete,\n    imageFit\n  } = props;\n  const src = useMemo(() => {\n    if (url) {\n      return url;\n    }\n    if (file) {\n      return URL.createObjectURL(file);\n    }\n    return '';\n  }, [url, file]);\n  useEffect(() => {\n    return () => {\n      if (file) URL.revokeObjectURL(src);\n    };\n  }, [src, file]);\n  function renderLoading() {\n    return props.status === 'pending' && React.createElement(\"div\", {\n      className: `${classPrefix}-cell-mask`\n    }, React.createElement(\"span\", {\n      className: `${classPrefix}-cell-loading`\n    }, React.createElement(SpinLoading, {\n      color: 'white'\n    }), React.createElement(\"span\", {\n      className: `${classPrefix}-cell-mask-message`\n    }, locale.ImageUploader.uploading)));\n  }\n  function renderDelete() {\n    return deletable && React.createElement(\"span\", {\n      className: `${classPrefix}-cell-delete`,\n      onClick: onDelete\n    }, deleteIcon);\n  }\n  return React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-cell`, props.status === 'fail' && `${classPrefix}-cell-fail`)\n  }, React.createElement(Image, {\n    className: `${classPrefix}-cell-image`,\n    src: src,\n    fit: imageFit,\n    onClick: props.onClick\n  }), renderLoading(), renderDelete());\n};\nexport default PreviewItem;"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AACjD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,WAAW,MAAM,iBAAiB;AACzC,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,MAAMC,WAAW,GAAG,oBAAoB;AACxC,MAAMC,WAAW,GAAGC,KAAK,IAAI;EAC3B,MAAM;IACJC;EACF,CAAC,GAAGJ,SAAS,CAAC,CAAC;EACf,MAAM;IACJK,GAAG;IACHC,IAAI;IACJC,SAAS;IACTC,UAAU;IACVC,QAAQ;IACRC;EACF,CAAC,GAAGP,KAAK;EACT,MAAMQ,GAAG,GAAGf,OAAO,CAAC,MAAM;IACxB,IAAIS,GAAG,EAAE;MACP,OAAOA,GAAG;IACZ;IACA,IAAIC,IAAI,EAAE;MACR,OAAOM,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;IAClC;IACA,OAAO,EAAE;EACX,CAAC,EAAE,CAACD,GAAG,EAAEC,IAAI,CAAC,CAAC;EACfX,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIW,IAAI,EAAEM,GAAG,CAACE,eAAe,CAACH,GAAG,CAAC;IACpC,CAAC;EACH,CAAC,EAAE,CAACA,GAAG,EAAEL,IAAI,CAAC,CAAC;EACf,SAASS,aAAaA,CAAA,EAAG;IACvB,OAAOZ,KAAK,CAACa,MAAM,KAAK,SAAS,IAAItB,KAAK,CAACuB,aAAa,CAAC,KAAK,EAAE;MAC9DC,SAAS,EAAE,GAAGjB,WAAW;IAC3B,CAAC,EAAEP,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAE;MAC7BC,SAAS,EAAE,GAAGjB,WAAW;IAC3B,CAAC,EAAEP,KAAK,CAACuB,aAAa,CAAClB,WAAW,EAAE;MAClCoB,KAAK,EAAE;IACT,CAAC,CAAC,EAAEzB,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAE;MAC9BC,SAAS,EAAE,GAAGjB,WAAW;IAC3B,CAAC,EAAEG,MAAM,CAACgB,aAAa,CAACC,SAAS,CAAC,CAAC,CAAC;EACtC;EACA,SAASC,YAAYA,CAAA,EAAG;IACtB,OAAOf,SAAS,IAAIb,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAE;MAC9CC,SAAS,EAAE,GAAGjB,WAAW,cAAc;MACvCsB,OAAO,EAAEd;IACX,CAAC,EAAED,UAAU,CAAC;EAChB;EACA,OAAOd,KAAK,CAACuB,aAAa,CAAC,KAAK,EAAE;IAChCC,SAAS,EAAErB,UAAU,CAAC,GAAGI,WAAW,OAAO,EAAEE,KAAK,CAACa,MAAM,KAAK,MAAM,IAAI,GAAGf,WAAW,YAAY;EACpG,CAAC,EAAEP,KAAK,CAACuB,aAAa,CAACnB,KAAK,EAAE;IAC5BoB,SAAS,EAAE,GAAGjB,WAAW,aAAa;IACtCU,GAAG,EAAEA,GAAG;IACRa,GAAG,EAAEd,QAAQ;IACba,OAAO,EAAEpB,KAAK,CAACoB;EACjB,CAAC,CAAC,EAAER,aAAa,CAAC,CAAC,EAAEO,YAAY,CAAC,CAAC,CAAC;AACtC,CAAC;AACD,eAAepB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}