{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nconst classPrefix = `adm-slider-mark`;\nconst Marks = ({\n  marks,\n  upperBound,\n  lowerBound,\n  max,\n  min\n}) => {\n  const marksKeys = Object.keys(marks);\n  const range = max - min;\n  const elements = marksKeys.map(parseFloat).sort((a, b) => a - b).filter(point => point >= min && point <= max).map(point => {\n    const markPoint = marks[point];\n    if (!markPoint && markPoint !== 0) {\n      return null;\n    }\n    const isActive = point <= upperBound && point >= lowerBound;\n    const markClassName = classNames({\n      [`${classPrefix}-text`]: true,\n      [`${classPrefix}-text-active`]: isActive\n    });\n    const style = {\n      left: `${(point - min) / range * 100}%`\n    };\n    return React.createElement(\"span\", {\n      className: markClassName,\n      style: style,\n      key: point\n    }, markPoint);\n  });\n  return React.createElement(\"div\", {\n    className: classPrefix\n  }, elements);\n};\nexport default Marks;", "map": {"version": 3, "names": ["React", "classNames", "classPrefix", "Marks", "marks", "upperBound", "lowerBound", "max", "min", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "range", "elements", "map", "parseFloat", "sort", "a", "b", "filter", "point", "markPoint", "isActive", "mark<PERSON><PERSON><PERSON>ame", "style", "left", "createElement", "className", "key"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/slider/marks.js"], "sourcesContent": ["import React from 'react';\nimport classNames from 'classnames';\nconst classPrefix = `adm-slider-mark`;\nconst Marks = ({\n  marks,\n  upperBound,\n  lowerBound,\n  max,\n  min\n}) => {\n  const marksKeys = Object.keys(marks);\n  const range = max - min;\n  const elements = marksKeys.map(parseFloat).sort((a, b) => a - b).filter(point => point >= min && point <= max).map(point => {\n    const markPoint = marks[point];\n    if (!markPoint && markPoint !== 0) {\n      return null;\n    }\n    const isActive = point <= upperBound && point >= lowerBound;\n    const markClassName = classNames({\n      [`${classPrefix}-text`]: true,\n      [`${classPrefix}-text-active`]: isActive\n    });\n    const style = {\n      left: `${(point - min) / range * 100}%`\n    };\n    return React.createElement(\"span\", {\n      className: markClassName,\n      style: style,\n      key: point\n    }, markPoint);\n  });\n  return React.createElement(\"div\", {\n    className: classPrefix\n  }, elements);\n};\nexport default Marks;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,MAAMC,WAAW,GAAG,iBAAiB;AACrC,MAAMC,KAAK,GAAGA,CAAC;EACbC,KAAK;EACLC,UAAU;EACVC,UAAU;EACVC,GAAG;EACHC;AACF,CAAC,KAAK;EACJ,MAAMC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACP,KAAK,CAAC;EACpC,MAAMQ,KAAK,GAAGL,GAAG,GAAGC,GAAG;EACvB,MAAMK,QAAQ,GAAGJ,SAAS,CAACK,GAAG,CAACC,UAAU,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,IAAIZ,GAAG,IAAIY,KAAK,IAAIb,GAAG,CAAC,CAACO,GAAG,CAACM,KAAK,IAAI;IAC1H,MAAMC,SAAS,GAAGjB,KAAK,CAACgB,KAAK,CAAC;IAC9B,IAAI,CAACC,SAAS,IAAIA,SAAS,KAAK,CAAC,EAAE;MACjC,OAAO,IAAI;IACb;IACA,MAAMC,QAAQ,GAAGF,KAAK,IAAIf,UAAU,IAAIe,KAAK,IAAId,UAAU;IAC3D,MAAMiB,aAAa,GAAGtB,UAAU,CAAC;MAC/B,CAAC,GAAGC,WAAW,OAAO,GAAG,IAAI;MAC7B,CAAC,GAAGA,WAAW,cAAc,GAAGoB;IAClC,CAAC,CAAC;IACF,MAAME,KAAK,GAAG;MACZC,IAAI,EAAE,GAAG,CAACL,KAAK,GAAGZ,GAAG,IAAII,KAAK,GAAG,GAAG;IACtC,CAAC;IACD,OAAOZ,KAAK,CAAC0B,aAAa,CAAC,MAAM,EAAE;MACjCC,SAAS,EAAEJ,aAAa;MACxBC,KAAK,EAAEA,KAAK;MACZI,GAAG,EAAER;IACP,CAAC,EAAEC,SAAS,CAAC;EACf,CAAC,CAAC;EACF,OAAOrB,KAAK,CAAC0B,aAAa,CAAC,KAAK,EAAE;IAChCC,SAAS,EAAEzB;EACb,CAAC,EAAEW,QAAQ,CAAC;AACd,CAAC;AACD,eAAeV,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}