{"ast": null, "code": "import * as React from \"react\";\nfunction FlagOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FlagOutline-FlagOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FlagOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"FlagOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M7.4,4 L39.0248171,4 L39.0248171,4 C40.1154214,4 40.9995309,4.8954305 40.9995309,6 C40.9995309,6.16442306 40.9795113,6.32821867 40.9399254,6.48767872 L37.85767,18.9036254 C37.8419584,18.9669146 37.8419584,19.0330854 37.85767,19.0963746 L40.9399254,31.5123213 L40.9399254,31.5123213 C41.2058577,32.5835501 40.5640152,33.6702938 39.5063301,33.9396313 C39.3488861,33.9797241 39.1871614,34 39.0248171,34 L10.362,34 C10.1410861,33.9999972 9.962,34.1790833 9.962,34.3999972 C9.962,34.3999981 9.962,34.3999991 9.96200283,34.4 L9.96206783,43.6 C9.96207222,43.8209139 9.78298738,44.0000013 9.56207348,44.0000028 C9.56207254,44.0000028 9.5620716,44.0000028 9.56207065,44 L7.4,44 C7.1790861,44 7,43.8209139 7,43.6 L7,4.4 C7,4.1790861 7.1790861,4 7.4,4 Z M37.2467145,7 L10.3620707,7 C10.1411568,7 9.96207065,7.1790861 9.96207065,7.4 L9.96207065,30.6 C9.96207065,30.8209139 10.1411568,31 10.3620707,31 L37.2467145,31 C37.4676284,31 37.6467145,30.8209139 37.6467145,30.6 C37.6467145,30.5675194 37.6427583,30.5351597 37.6349334,30.5036357 L34.8034031,19.0963643 C34.787695,19.0330816 34.787695,18.9669184 34.8034031,18.9036357 L37.6349334,7.49636429 C37.6881539,7.2819569 37.5574861,7.06500162 37.3430788,7.01178109 C37.3115548,7.00395616 37.2791951,7 37.2467145,7 Z\",\n    id: \"FlagOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default FlagOutline;", "map": {"version": 3, "names": ["React", "FlagOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/FlagOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction FlagOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FlagOutline-FlagOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FlagOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"FlagOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M7.4,4 L39.0248171,4 L39.0248171,4 C40.1154214,4 40.9995309,4.8954305 40.9995309,6 C40.9995309,6.16442306 40.9795113,6.32821867 40.9399254,6.48767872 L37.85767,18.9036254 C37.8419584,18.9669146 37.8419584,19.0330854 37.85767,19.0963746 L40.9399254,31.5123213 L40.9399254,31.5123213 C41.2058577,32.5835501 40.5640152,33.6702938 39.5063301,33.9396313 C39.3488861,33.9797241 39.1871614,34 39.0248171,34 L10.362,34 C10.1410861,33.9999972 9.962,34.1790833 9.962,34.3999972 C9.962,34.3999981 9.962,34.3999991 9.96200283,34.4 L9.96206783,43.6 C9.96207222,43.8209139 9.78298738,44.0000013 9.56207348,44.0000028 C9.56207254,44.0000028 9.5620716,44.0000028 9.56207065,44 L7.4,44 C7.1790861,44 7,43.8209139 7,43.6 L7,4.4 C7,4.1790861 7.1790861,4 7.4,4 Z M37.2467145,7 L10.3620707,7 C10.1411568,7 9.96207065,7.1790861 9.96207065,7.4 L9.96207065,30.6 C9.96207065,30.8209139 10.1411568,31 10.3620707,31 L37.2467145,31 C37.4676284,31 37.6467145,30.8209139 37.6467145,30.6 C37.6467145,30.5675194 37.6427583,30.5351597 37.6349334,30.5036357 L34.8034031,19.0963643 C34.787695,19.0330816 34.787695,18.9669184 34.8034031,18.9036357 L37.6349334,7.49636429 C37.6881539,7.2819569 37.5574861,7.06500162 37.3430788,7.01178109 C37.3115548,7.00395616 37.2791951,7 37.2467145,7 Z\",\n    id: \"FlagOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default FlagOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yBAAyB;IAC7BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,quCAAquC;IACxuCR,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}