[{"C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\CardManagerPage.js": "3", "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\CardDetailPage.js": "4", "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\ScanUploadPage.js": "5", "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\AddCardPage.js": "6", "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\utils\\deviceDetector.js": "7", "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\utils\\cameraManager.js": "8", "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\components\\MobileCameraModal.js": "9", "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\utils\\cameraStrategies.js": "10", "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\components\\CameraTestPage.js": "11"}, {"size": 264, "mtime": 1749235559432, "results": "12", "hashOfConfig": "13"}, {"size": 2019, "mtime": 1749238203079, "results": "14", "hashOfConfig": "13"}, {"size": 10965, "mtime": 1749235559714, "results": "15", "hashOfConfig": "13"}, {"size": 13542, "mtime": 1749235559785, "results": "16", "hashOfConfig": "13"}, {"size": 25960, "mtime": 1749239247671, "results": "17", "hashOfConfig": "13"}, {"size": 7581, "mtime": 1749235559834, "results": "18", "hashOfConfig": "13"}, {"size": 6074, "mtime": 1749236555626, "results": "19", "hashOfConfig": "13"}, {"size": 7171, "mtime": 1749236649750, "results": "20", "hashOfConfig": "13"}, {"size": 6118, "mtime": 1749238068001, "results": "21", "hashOfConfig": "13"}, {"size": 8510, "mtime": 1749236606468, "results": "22", "hashOfConfig": "13"}, {"size": 7244, "mtime": 1749239135425, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1b7dy3n", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\CardManagerPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\CardDetailPage.js", ["57"], [], "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\ScanUploadPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\AddCardPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\utils\\deviceDetector.js", ["58"], [], "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\utils\\cameraManager.js", ["59"], [], "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\components\\MobileCameraModal.js", [], [], "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\utils\\cameraStrategies.js", ["60"], [], "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\components\\CameraTestPage.js", [], [], {"ruleId": "61", "severity": 1, "message": "62", "line": 52, "column": 6, "nodeType": "63", "endLine": 52, "endColumn": 10, "suggestions": "64"}, {"ruleId": "65", "severity": 1, "message": "66", "line": 220, "column": 1, "nodeType": "67", "endLine": 234, "endColumn": 3}, {"ruleId": "65", "severity": 1, "message": "66", "line": 309, "column": 1, "nodeType": "67", "endLine": 315, "endColumn": 3}, {"ruleId": "65", "severity": 1, "message": "66", "line": 353, "column": 1, "nodeType": "67", "endLine": 357, "endColumn": 3}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCardData'. Either include it or remove the dependency array.", "ArrayExpression", ["68"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "69", "fix": "70"}, "Update the dependencies array to be: [id, loadCardData]", {"range": "71", "text": "72"}, [1349, 1353], "[id, loadCardData]"]