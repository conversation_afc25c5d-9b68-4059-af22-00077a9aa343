[{"C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\CardManagerPage.js": "3", "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\CardDetailPage.js": "4", "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\ScanUploadPage.js": "5", "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\AddCardPage.js": "6"}, {"size": 264, "mtime": 1749235559432, "results": "7", "hashOfConfig": "8"}, {"size": 1724, "mtime": 1749235559547, "results": "9", "hashOfConfig": "8"}, {"size": 10965, "mtime": 1749235559714, "results": "10", "hashOfConfig": "8"}, {"size": 13542, "mtime": 1749235559785, "results": "11", "hashOfConfig": "8"}, {"size": 23248, "mtime": 1749235559665, "results": "12", "hashOfConfig": "8"}, {"size": 7581, "mtime": 1749235559834, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1b7dy3n", {"filePath": "17", "messages": "18", "suppressedMessages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\CardManagerPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\CardDetailPage.js", ["32"], [], "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\ScanUploadPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\src\\pages\\AddCardPage.js", [], [], {"ruleId": "33", "severity": 1, "message": "34", "line": 52, "column": 6, "nodeType": "35", "endLine": 52, "endColumn": 10, "suggestions": "36"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCardData'. Either include it or remove the dependency array.", "ArrayExpression", ["37"], {"desc": "38", "fix": "39"}, "Update the dependencies array to be: [id, loadCardData]", {"range": "40", "text": "41"}, [1349, 1353], "[id, loadCardData]"]