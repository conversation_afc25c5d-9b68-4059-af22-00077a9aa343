{"ast": null, "code": "import * as React from \"react\";\nfunction BankcardOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"BankcardOutline-BankcardOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"BankcardOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"BankcardOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M39,5 C42.3137085,5 45,7.6862915 45,11 L45,37 C45,40.3137085 42.3137085,43 39,43 L9,43 C5.6862915,43 3,40.3137085 3,37 L3,11 C3,7.6862915 5.6862915,5 9,5 L39,5 Z M39,8 L9,8 C7.********,8 6.********,9.******** 6.********,10.8237272 L6,11 L6,37 C6,38.5976809 7.********,39.9036609 8.********,39.9949073 L9,40 L39,40 C40.5976809,40 41.9036609,38.75108 41.9949073,37.1762728 L42,37 L42,11 C42,9.******** 40.75108,8.******** 39.1762728,8.******** L39,8 Z M43,15.4 L43,17.6 C43,17.8209139 42.8209139,18 42.6,18 L5.4,18 C5.1790861,18 5,17.8209139 5,17.6 L5,15.4 C5,15.1790861 5.1790861,15 5.4,15 L42.6,15 C42.8209139,15 43,15.1790861 43,15.4 Z M19,33.4 L19,35.6 C19,35.8209139 18.8209139,36 18.6,36 L10.4,36 C10.1790861,36 10,35.8209139 10,35.6 L10,33.4 C10,33.1790861 10.1790861,33 10.4,33 L18.6,33 C18.8209139,33 19,33.1790861 19,33.4 Z\",\n    id: \"BankcardOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default BankcardOutline;", "map": {"version": 3, "names": ["React", "BankcardOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/BankcardOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction BankcardOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"BankcardOutline-BankcardOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"BankcardOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"BankcardOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M39,5 C42.3137085,5 45,7.6862915 45,11 L45,37 C45,40.3137085 42.3137085,43 39,43 L9,43 C5.6862915,43 3,40.3137085 3,37 L3,11 C3,7.6862915 5.6862915,5 9,5 L39,5 Z M39,8 L9,8 C7.********,8 6.********,9.******** 6.********,10.8237272 L6,11 L6,37 C6,38.5976809 7.********,39.9036609 8.********,39.9949073 L9,40 L39,40 C40.5976809,40 41.9036609,38.75108 41.9949073,37.1762728 L42,37 L42,11 C42,9.******** 40.75108,8.******** 39.1762728,8.******** L39,8 Z M43,15.4 L43,17.6 C43,17.8209139 42.8209139,18 42.6,18 L5.4,18 C5.1790861,18 5,17.8209139 5,17.6 L5,15.4 C5,15.1790861 5.1790861,15 5.4,15 L42.6,15 C42.8209139,15 43,15.1790861 43,15.4 Z M19,33.4 L19,35.6 C19,35.8209139 18.8209139,36 18.6,36 L10.4,36 C10.1790861,36 10,35.8209139 10,35.6 L10,33.4 C10,33.1790861 10.1790861,33 10.4,33 L18.6,33 C18.8209139,33 19,33.1790861 19,33.4 Z\",\n    id: \"BankcardOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default BankcardOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,iCAAiC;IACrCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,i0BAAi0B;IACp0BR,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}