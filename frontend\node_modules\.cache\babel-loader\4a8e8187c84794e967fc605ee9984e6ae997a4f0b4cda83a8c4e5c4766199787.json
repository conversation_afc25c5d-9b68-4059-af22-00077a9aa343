{"ast": null, "code": "import { __awaiter } from \"tslib\";\nimport React from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport classNames from 'classnames';\nimport { DialogActionButton } from './dialog-action-button';\nimport Image from '../image';\nimport AutoCenter from '../auto-center';\nimport CenterPopup from '../center-popup';\nconst defaultProps = {\n  actions: [],\n  closeOnAction: false,\n  closeOnMaskClick: false,\n  getContainer: null\n};\nexport const Dialog = p => {\n  const props = mergeProps(defaultProps, p);\n  const element = React.createElement(React.Fragment, null, !!props.image && React.createElement(\"div\", {\n    className: cls('image-container')\n  }, React.createElement(Image, {\n    src: props.image,\n    alt: 'dialog header image',\n    width: '100%'\n  })), !!props.header && React.createElement(\"div\", {\n    className: cls('header')\n  }, React.createElement(AutoCenter, null, props.header)), !!props.title && React.createElement(\"div\", {\n    className: cls('title')\n  }, props.title), React.createElement(\"div\", {\n    className: classNames(cls('content'), !props.content && cls('content-empty'))\n  }, typeof props.content === 'string' ? React.createElement(AutoCenter, null, props.content) : props.content), React.createElement(\"div\", {\n    className: cls('footer')\n  }, props.actions.map((row, index) => {\n    const actions = Array.isArray(row) ? row : [row];\n    return React.createElement(\"div\", {\n      className: cls('action-row'),\n      key: index\n    }, actions.map((action, index) => React.createElement(DialogActionButton, {\n      key: action.key,\n      action: action,\n      onAction: () => __awaiter(void 0, void 0, void 0, function* () {\n        var _a, _b, _c;\n        yield Promise.all([(_a = action.onClick) === null || _a === void 0 ? void 0 : _a.call(action), (_b = props.onAction) === null || _b === void 0 ? void 0 : _b.call(props, action, index)]);\n        if (props.closeOnAction) {\n          (_c = props.onClose) === null || _c === void 0 ? void 0 : _c.call(props);\n        }\n      })\n    })));\n  })));\n  return React.createElement(CenterPopup, {\n    className: classNames(cls(), props.className),\n    style: props.style,\n    afterClose: props.afterClose,\n    afterShow: props.afterShow,\n    onMaskClick: props.closeOnMaskClick ? () => {\n      var _a;\n      (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n    } : undefined,\n    visible: props.visible,\n    getContainer: props.getContainer,\n    bodyStyle: props.bodyStyle,\n    bodyClassName: classNames(cls('body'), props.image && cls('with-image'), props.bodyClassName),\n    maskStyle: props.maskStyle,\n    maskClassName: props.maskClassName,\n    stopPropagation: props.stopPropagation,\n    disableBodyScroll: props.disableBodyScroll,\n    destroyOnClose: props.destroyOnClose,\n    forceRender: props.forceRender,\n    role: 'dialog',\n    \"aria-label\": props['aria-label']\n  }, element);\n};\nfunction cls(name = '') {\n  return 'adm-dialog' + (name && '-') + name;\n}", "map": {"version": 3, "names": ["__awaiter", "React", "mergeProps", "classNames", "DialogActionButton", "Image", "AutoCenter", "CenterPopup", "defaultProps", "actions", "closeOnAction", "closeOnMaskClick", "getContainer", "Dialog", "p", "props", "element", "createElement", "Fragment", "image", "className", "cls", "src", "alt", "width", "header", "title", "content", "map", "row", "index", "Array", "isArray", "key", "action", "onAction", "_a", "_b", "_c", "Promise", "all", "onClick", "call", "onClose", "style", "afterClose", "afterShow", "onMaskClick", "undefined", "visible", "bodyStyle", "bodyClassName", "maskStyle", "maskClassName", "stopPropagation", "disableBodyScroll", "destroyOnClose", "forceRender", "role", "name"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/dialog/dialog.js"], "sourcesContent": ["import { __awaiter } from \"tslib\";\nimport React from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport classNames from 'classnames';\nimport { DialogActionButton } from './dialog-action-button';\nimport Image from '../image';\nimport AutoCenter from '../auto-center';\nimport CenterPopup from '../center-popup';\nconst defaultProps = {\n  actions: [],\n  closeOnAction: false,\n  closeOnMaskClick: false,\n  getContainer: null\n};\nexport const Dialog = p => {\n  const props = mergeProps(defaultProps, p);\n  const element = React.createElement(React.Fragment, null, !!props.image && React.createElement(\"div\", {\n    className: cls('image-container')\n  }, React.createElement(Image, {\n    src: props.image,\n    alt: 'dialog header image',\n    width: '100%'\n  })), !!props.header && React.createElement(\"div\", {\n    className: cls('header')\n  }, React.createElement(AutoCenter, null, props.header)), !!props.title && React.createElement(\"div\", {\n    className: cls('title')\n  }, props.title), React.createElement(\"div\", {\n    className: classNames(cls('content'), !props.content && cls('content-empty'))\n  }, typeof props.content === 'string' ? React.createElement(AutoCenter, null, props.content) : props.content), React.createElement(\"div\", {\n    className: cls('footer')\n  }, props.actions.map((row, index) => {\n    const actions = Array.isArray(row) ? row : [row];\n    return React.createElement(\"div\", {\n      className: cls('action-row'),\n      key: index\n    }, actions.map((action, index) => React.createElement(DialogActionButton, {\n      key: action.key,\n      action: action,\n      onAction: () => __awaiter(void 0, void 0, void 0, function* () {\n        var _a, _b, _c;\n        yield Promise.all([(_a = action.onClick) === null || _a === void 0 ? void 0 : _a.call(action), (_b = props.onAction) === null || _b === void 0 ? void 0 : _b.call(props, action, index)]);\n        if (props.closeOnAction) {\n          (_c = props.onClose) === null || _c === void 0 ? void 0 : _c.call(props);\n        }\n      })\n    })));\n  })));\n  return React.createElement(CenterPopup, {\n    className: classNames(cls(), props.className),\n    style: props.style,\n    afterClose: props.afterClose,\n    afterShow: props.afterShow,\n    onMaskClick: props.closeOnMaskClick ? () => {\n      var _a;\n      (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n    } : undefined,\n    visible: props.visible,\n    getContainer: props.getContainer,\n    bodyStyle: props.bodyStyle,\n    bodyClassName: classNames(cls('body'), props.image && cls('with-image'), props.bodyClassName),\n    maskStyle: props.maskStyle,\n    maskClassName: props.maskClassName,\n    stopPropagation: props.stopPropagation,\n    disableBodyScroll: props.disableBodyScroll,\n    destroyOnClose: props.destroyOnClose,\n    forceRender: props.forceRender,\n    role: 'dialog',\n    \"aria-label\": props['aria-label']\n  }, element);\n};\nfunction cls(name = '') {\n  return 'adm-dialog' + (name && '-') + name;\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,UAAU,MAAM,gBAAgB;AACvC,OAAOC,WAAW,MAAM,iBAAiB;AACzC,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,EAAE;EACXC,aAAa,EAAE,KAAK;EACpBC,gBAAgB,EAAE,KAAK;EACvBC,YAAY,EAAE;AAChB,CAAC;AACD,OAAO,MAAMC,MAAM,GAAGC,CAAC,IAAI;EACzB,MAAMC,KAAK,GAAGb,UAAU,CAACM,YAAY,EAAEM,CAAC,CAAC;EACzC,MAAME,OAAO,GAAGf,KAAK,CAACgB,aAAa,CAAChB,KAAK,CAACiB,QAAQ,EAAE,IAAI,EAAE,CAAC,CAACH,KAAK,CAACI,KAAK,IAAIlB,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IACpGG,SAAS,EAAEC,GAAG,CAAC,iBAAiB;EAClC,CAAC,EAAEpB,KAAK,CAACgB,aAAa,CAACZ,KAAK,EAAE;IAC5BiB,GAAG,EAAEP,KAAK,CAACI,KAAK;IAChBI,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC,CAAC,CAAC,EAAE,CAAC,CAACT,KAAK,CAACU,MAAM,IAAIxB,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAChDG,SAAS,EAAEC,GAAG,CAAC,QAAQ;EACzB,CAAC,EAAEpB,KAAK,CAACgB,aAAa,CAACX,UAAU,EAAE,IAAI,EAAES,KAAK,CAACU,MAAM,CAAC,CAAC,EAAE,CAAC,CAACV,KAAK,CAACW,KAAK,IAAIzB,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IACnGG,SAAS,EAAEC,GAAG,CAAC,OAAO;EACxB,CAAC,EAAEN,KAAK,CAACW,KAAK,CAAC,EAAEzB,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAC1CG,SAAS,EAAEjB,UAAU,CAACkB,GAAG,CAAC,SAAS,CAAC,EAAE,CAACN,KAAK,CAACY,OAAO,IAAIN,GAAG,CAAC,eAAe,CAAC;EAC9E,CAAC,EAAE,OAAON,KAAK,CAACY,OAAO,KAAK,QAAQ,GAAG1B,KAAK,CAACgB,aAAa,CAACX,UAAU,EAAE,IAAI,EAAES,KAAK,CAACY,OAAO,CAAC,GAAGZ,KAAK,CAACY,OAAO,CAAC,EAAE1B,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IACvIG,SAAS,EAAEC,GAAG,CAAC,QAAQ;EACzB,CAAC,EAAEN,KAAK,CAACN,OAAO,CAACmB,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;IACnC,MAAMrB,OAAO,GAAGsB,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;IAChD,OAAO5B,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;MAChCG,SAAS,EAAEC,GAAG,CAAC,YAAY,CAAC;MAC5BY,GAAG,EAAEH;IACP,CAAC,EAAErB,OAAO,CAACmB,GAAG,CAAC,CAACM,MAAM,EAAEJ,KAAK,KAAK7B,KAAK,CAACgB,aAAa,CAACb,kBAAkB,EAAE;MACxE6B,GAAG,EAAEC,MAAM,CAACD,GAAG;MACfC,MAAM,EAAEA,MAAM;MACdC,QAAQ,EAAEA,CAAA,KAAMnC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;QAC7D,IAAIoC,EAAE,EAAEC,EAAE,EAAEC,EAAE;QACd,MAAMC,OAAO,CAACC,GAAG,CAAC,CAAC,CAACJ,EAAE,GAAGF,MAAM,CAACO,OAAO,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,IAAI,CAACR,MAAM,CAAC,EAAE,CAACG,EAAE,GAAGtB,KAAK,CAACoB,QAAQ,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,IAAI,CAAC3B,KAAK,EAAEmB,MAAM,EAAEJ,KAAK,CAAC,CAAC,CAAC;QACzL,IAAIf,KAAK,CAACL,aAAa,EAAE;UACvB,CAAC4B,EAAE,GAAGvB,KAAK,CAAC4B,OAAO,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,IAAI,CAAC3B,KAAK,CAAC;QAC1E;MACF,CAAC;IACH,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC;EACJ,OAAOd,KAAK,CAACgB,aAAa,CAACV,WAAW,EAAE;IACtCa,SAAS,EAAEjB,UAAU,CAACkB,GAAG,CAAC,CAAC,EAAEN,KAAK,CAACK,SAAS,CAAC;IAC7CwB,KAAK,EAAE7B,KAAK,CAAC6B,KAAK;IAClBC,UAAU,EAAE9B,KAAK,CAAC8B,UAAU;IAC5BC,SAAS,EAAE/B,KAAK,CAAC+B,SAAS;IAC1BC,WAAW,EAAEhC,KAAK,CAACJ,gBAAgB,GAAG,MAAM;MAC1C,IAAIyB,EAAE;MACN,CAACA,EAAE,GAAGrB,KAAK,CAAC4B,OAAO,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,IAAI,CAAC3B,KAAK,CAAC;IAC1E,CAAC,GAAGiC,SAAS;IACbC,OAAO,EAAElC,KAAK,CAACkC,OAAO;IACtBrC,YAAY,EAAEG,KAAK,CAACH,YAAY;IAChCsC,SAAS,EAAEnC,KAAK,CAACmC,SAAS;IAC1BC,aAAa,EAAEhD,UAAU,CAACkB,GAAG,CAAC,MAAM,CAAC,EAAEN,KAAK,CAACI,KAAK,IAAIE,GAAG,CAAC,YAAY,CAAC,EAAEN,KAAK,CAACoC,aAAa,CAAC;IAC7FC,SAAS,EAAErC,KAAK,CAACqC,SAAS;IAC1BC,aAAa,EAAEtC,KAAK,CAACsC,aAAa;IAClCC,eAAe,EAAEvC,KAAK,CAACuC,eAAe;IACtCC,iBAAiB,EAAExC,KAAK,CAACwC,iBAAiB;IAC1CC,cAAc,EAAEzC,KAAK,CAACyC,cAAc;IACpCC,WAAW,EAAE1C,KAAK,CAAC0C,WAAW;IAC9BC,IAAI,EAAE,QAAQ;IACd,YAAY,EAAE3C,KAAK,CAAC,YAAY;EAClC,CAAC,EAAEC,OAAO,CAAC;AACb,CAAC;AACD,SAASK,GAAGA,CAACsC,IAAI,GAAG,EAAE,EAAE;EACtB,OAAO,YAAY,IAAIA,IAAI,IAAI,GAAG,CAAC,GAAGA,IAAI;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}