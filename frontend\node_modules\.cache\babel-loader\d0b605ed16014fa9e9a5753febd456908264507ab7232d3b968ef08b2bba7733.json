{"ast": null, "code": "import * as React from \"react\";\nfunction ChatAddOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ChatAddOutline-ChatAddOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ChatAddOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ChatAddOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,5 C35.0454654,5 44,13.5065417 44,23.9999335 C44,34.4933254 35.0454654,43.000129 24,43.000129 C20.5361038,43.0086689 17.1256565,42.1499296 14.0827434,40.5027329 L9.12272737,42.6506288 C8.20180797,43.0490167 7.13075643,42.6289802 6.73046083,41.7124519 C6.57362597,41.3533578 6.53805635,40.953324 6.6290919,40.5723965 L7.89727419,35.2705244 C5.44727043,32.1174388 4,28.2188348 4,23.999959 C4,13.5065671 12.9545346,5 24,5 Z M24.0000207,7.7142478 C14.4327303,7.7142478 6.7272871,15.0337886 6.7272871,23.9999335 C6.7272871,27.3746822 7.816377,30.5920198 9.81910787,33.2999345 L10.0545624,33.6102666 L10.851834,34.636263 L9.70274404,39.4387082 L14.2191094,37.4844293 L15.3900175,38.1213809 C17.9854744,39.5319012 20.9309429,40.2855624 24.0000207,40.2855624 C33.5672971,40.2855624 41.2727403,32.9660501 41.2727403,23.9999051 C41.2727403,15.0337601 33.5672971,7.7142478 24.0000207,7.7142478 Z M25.1,16 C25.3209139,16 25.5,16.1790861 25.5,16.4 L25.5,21.9788399 L31.1,21.9791421 C31.3209055,21.9791756 31.4999784,22.1582582 31.5,22.3791637 L31.5,24.5666619 C31.5000479,24.7599615 31.3629411,24.9212368 31.1806686,24.9585353 L31.1,24.9666071 L31.1,24.9666071 L25.5,24.9658399 L25.5,30.5458255 C25.5,30.7667394 25.3209139,30.9458255 25.1,30.9458255 L22.9,30.9458255 C22.6790861,30.9458255 22.5,30.7667394 22.5,30.5458255 L22.5,24.9658399 L16.9,24.9666071 C16.6790861,24.9666921 16.4999755,24.7876306 16.4999452,24.5667167 L16.5,24.5666619 L16.5,22.3791637 C16.5000216,22.1582582 16.6790945,21.9791756 16.9,21.9791421 L22.5,21.9788399 L22.5,16.4 C22.5,16.1790861 22.6790861,16 22.9,16 L25.1,16 Z\",\n    id: \"ChatAddOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default ChatAddOutline;", "map": {"version": 3, "names": ["React", "ChatAddOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/ChatAddOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction ChatAddOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ChatAddOutline-ChatAddOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ChatAddOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ChatAddOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,5 C35.0454654,5 44,13.5065417 44,23.9999335 C44,34.4933254 35.0454654,43.000129 24,43.000129 C20.5361038,43.0086689 17.1256565,42.1499296 14.0827434,40.5027329 L9.12272737,42.6506288 C8.20180797,43.0490167 7.13075643,42.6289802 6.73046083,41.7124519 C6.57362597,41.3533578 6.53805635,40.953324 6.6290919,40.5723965 L7.89727419,35.2705244 C5.44727043,32.1174388 4,28.2188348 4,23.999959 C4,13.5065671 12.9545346,5 24,5 Z M24.0000207,7.7142478 C14.4327303,7.7142478 6.7272871,15.0337886 6.7272871,23.9999335 C6.7272871,27.3746822 7.816377,30.5920198 9.81910787,33.2999345 L10.0545624,33.6102666 L10.851834,34.636263 L9.70274404,39.4387082 L14.2191094,37.4844293 L15.3900175,38.1213809 C17.9854744,39.5319012 20.9309429,40.2855624 24.0000207,40.2855624 C33.5672971,40.2855624 41.2727403,32.9660501 41.2727403,23.9999051 C41.2727403,15.0337601 33.5672971,7.7142478 24.0000207,7.7142478 Z M25.1,16 C25.3209139,16 25.5,16.1790861 25.5,16.4 L25.5,21.9788399 L31.1,21.9791421 C31.3209055,21.9791756 31.4999784,22.1582582 31.5,22.3791637 L31.5,24.5666619 C31.5000479,24.7599615 31.3629411,24.9212368 31.1806686,24.9585353 L31.1,24.9666071 L31.1,24.9666071 L25.5,24.9658399 L25.5,30.5458255 C25.5,30.7667394 25.3209139,30.9458255 25.1,30.9458255 L22.9,30.9458255 C22.6790861,30.9458255 22.5,30.7667394 22.5,30.5458255 L22.5,24.9658399 L16.9,24.9666071 C16.6790861,24.9666921 16.4999755,24.7876306 16.4999452,24.5667167 L16.5,24.5666619 L16.5,22.3791637 C16.5000216,22.1582582 16.6790945,21.9791756 16.9,21.9791421 L22.5,21.9788399 L22.5,16.4 C22.5,16.1790861 22.6790861,16 22.9,16 L25.1,16 Z\",\n    id: \"ChatAddOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default ChatAddOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,+BAA+B;IACnCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,6BAA6B;IACjCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,mjDAAmjD;IACtjDR,EAAE,EAAE,yCAAyC;IAC7CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}