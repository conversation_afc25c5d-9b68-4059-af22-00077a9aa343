{"ast": null, "code": "import { bound } from './bound';\nexport function rubberband(distance, dimension, constant) {\n  return distance * dimension * constant / (dimension + constant * distance);\n}\nexport function rubberbandIfOutOfBounds(position, min, max, dimension, constant = 0.15) {\n  if (constant === 0) return bound(position, min, max);\n  if (position < min) return -rubberband(min - position, dimension, constant) + min;\n  if (position > max) return +rubberband(position - max, dimension, constant) + max;\n  return position;\n}", "map": {"version": 3, "names": ["bound", "rubberband", "distance", "dimension", "constant", "rubberbandIfOutOfBounds", "position", "min", "max"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/rubberband.js"], "sourcesContent": ["import { bound } from './bound';\nexport function rubberband(distance, dimension, constant) {\n  return distance * dimension * constant / (dimension + constant * distance);\n}\nexport function rubberbandIfOutOfBounds(position, min, max, dimension, constant = 0.15) {\n  if (constant === 0) return bound(position, min, max);\n  if (position < min) return -rubberband(min - position, dimension, constant) + min;\n  if (position > max) return +rubberband(position - max, dimension, constant) + max;\n  return position;\n}"], "mappings": "AAAA,SAASA,KAAK,QAAQ,SAAS;AAC/B,OAAO,SAASC,UAAUA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EACxD,OAAOF,QAAQ,GAAGC,SAAS,GAAGC,QAAQ,IAAID,SAAS,GAAGC,QAAQ,GAAGF,QAAQ,CAAC;AAC5E;AACA,OAAO,SAASG,uBAAuBA,CAACC,QAAQ,EAAEC,GAAG,EAAEC,GAAG,EAAEL,SAAS,EAAEC,QAAQ,GAAG,IAAI,EAAE;EACtF,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAOJ,KAAK,CAACM,QAAQ,EAAEC,GAAG,EAAEC,GAAG,CAAC;EACpD,IAAIF,QAAQ,GAAGC,GAAG,EAAE,OAAO,CAACN,UAAU,CAACM,GAAG,GAAGD,QAAQ,EAAEH,SAAS,EAAEC,QAAQ,CAAC,GAAGG,GAAG;EACjF,IAAID,QAAQ,GAAGE,GAAG,EAAE,OAAO,CAACP,UAAU,CAACK,QAAQ,GAAGE,GAAG,EAAEL,SAAS,EAAEC,QAAQ,CAAC,GAAGI,GAAG;EACjF,OAAOF,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}