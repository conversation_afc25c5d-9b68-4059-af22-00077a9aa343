{"ast": null, "code": "import * as React from \"react\";\nfunction KeyOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"KeyOutline-KeyOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"KeyOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"KeyOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M29.2560785,4 C37.175035,4 43.5946176,10.4195826 43.5946176,18.3385391 C43.5946176,26.2574956 37.175035,32.6770783 29.2560785,32.6770783 C25.8305509,32.6770783 22.6855854,31.4758493 20.2194378,29.471647 L16.0256176,33.6650213 L20.0588981,37.6984676 C20.2151078,37.8546773 20.2151078,38.1079433 20.0588981,38.2641531 L18.5268846,39.7961666 C18.3706749,39.9523763 18.1174089,39.9523763 17.9611992,39.7961666 L13.9276176,35.7630213 L9.8113188,39.8809978 C9.65510908,40.0372075 9.40184309,40.0372075 9.24563337,39.8809978 L7.71361985,38.3489842 C7.55741013,38.1927745 7.55741013,37.9395085 7.71361985,37.7832988 L18.1229706,27.3751798 C16.1187684,24.9090322 14.9175393,21.7640667 14.9175393,18.3385391 C14.9175393,10.4195826 21.337122,4 29.2560785,4 Z M29.2560785,6.9665943 C22.9755268,6.9665943 17.8841337,12.0579874 17.8841337,18.3385391 C17.8841337,24.6190908 22.9755268,29.710484 29.2560785,29.710484 C35.5366302,29.710484 40.6280233,24.6190908 40.6280233,18.3385391 C40.6280233,12.0579874 35.5366302,6.9665943 29.2560785,6.9665943 Z\",\n    id: \"KeyOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\"\n  }))));\n}\nexport default KeyOutline;", "map": {"version": 3, "names": ["React", "KeyOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/KeyOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction KeyOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"KeyOutline-KeyOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"KeyOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"KeyOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M29.2560785,4 C37.175035,4 43.5946176,10.4195826 43.5946176,18.3385391 C43.5946176,26.2574956 37.175035,32.6770783 29.2560785,32.6770783 C25.8305509,32.6770783 22.6855854,31.4758493 20.2194378,29.471647 L16.0256176,33.6650213 L20.0588981,37.6984676 C20.2151078,37.8546773 20.2151078,38.1079433 20.0588981,38.2641531 L18.5268846,39.7961666 C18.3706749,39.9523763 18.1174089,39.9523763 17.9611992,39.7961666 L13.9276176,35.7630213 L9.8113188,39.8809978 C9.65510908,40.0372075 9.40184309,40.0372075 9.24563337,39.8809978 L7.71361985,38.3489842 C7.55741013,38.1927745 7.55741013,37.9395085 7.71361985,37.7832988 L18.1229706,27.3751798 C16.1187684,24.9090322 14.9175393,21.7640667 14.9175393,18.3385391 C14.9175393,10.4195826 21.337122,4 29.2560785,4 Z M29.2560785,6.9665943 C22.9755268,6.9665943 17.8841337,12.0579874 17.8841337,18.3385391 C17.8841337,24.6190908 22.9755268,29.710484 29.2560785,29.710484 C35.5366302,29.710484 40.6280233,24.6190908 40.6280233,18.3385391 C40.6280233,12.0579874 35.5366302,6.9665943 29.2560785,6.9665943 Z\",\n    id: \"KeyOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\"\n  }))));\n}\n\nexport default KeyOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,uBAAuB;IAC3BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,yBAAyB;IAC7BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,2gCAA2gC;IAC9gCR,EAAE,EAAE,qCAAqC;IACzCG,IAAI,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAenB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}