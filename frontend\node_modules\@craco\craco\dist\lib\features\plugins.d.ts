import type { Base<PERSON>ontext, CracoConfig, DevServerContext, Jest<PERSON>ontext, WebpackContext } from '@craco/types';
import type { Config as JestConfig } from '@jest/types';
import type { Configuration as WebpackConfig } from 'webpack';
import type { Configuration as DevServerConfig } from 'webpack-dev-server';
export declare function applyCracoConfigPlugins(cracoConfig: CracoConfig, context: BaseContext): CracoConfig;
export declare function applyWebpackConfigPlugins(cracoConfig: CracoConfig, webpackConfig: WebpackConfig, context: WebpackContext): WebpackConfig;
export declare function applyDevServerConfigPlugins(cracoConfig: CracoConfig, devServerConfig: DevServerConfig, context: DevServerContext): DevServerConfig;
export declare function applyJestConfigPlugins(cracoConfig: CracoConfig, jestConfig: JestConfig.InitialOptions, context: JestContext): Partial<{
    automock: boolean;
    bail: number | boolean;
    cache: boolean;
    cacheDirectory: string;
    ci: boolean;
    clearMocks: boolean;
    changedFilesWithAncestor: boolean;
    changedSince: string;
    collectCoverage: boolean;
    collectCoverageFrom: string[];
    collectCoverageOnlyFrom: {
        [key: string]: boolean;
    };
    coverageDirectory: string;
    coveragePathIgnorePatterns: string[];
    coverageProvider: "babel" | "v8";
    coverageReporters: JestConfig.CoverageReporters;
    coverageThreshold: {
        [path: string]: JestConfig.CoverageThresholdValue;
        global: JestConfig.CoverageThresholdValue;
    };
    dependencyExtractor: string;
    detectLeaks: boolean;
    detectOpenHandles: boolean;
    displayName: string | JestConfig.DisplayName;
    expand: boolean;
    extensionsToTreatAsEsm: string[];
    extraGlobals: string[];
    filter: string;
    findRelatedTests: boolean;
    forceCoverageMatch: string[];
    forceExit: boolean;
    json: boolean;
    globals: JestConfig.ConfigGlobals;
    globalSetup: string | null | undefined;
    globalTeardown: string | null | undefined;
    haste: JestConfig.HasteConfig;
    injectGlobals: boolean;
    reporters: (string | JestConfig.ReporterConfig)[];
    logHeapUsage: boolean;
    lastCommit: boolean;
    listTests: boolean;
    maxConcurrency: number;
    maxWorkers: string | number;
    moduleDirectories: string[];
    moduleFileExtensions: string[];
    moduleLoader: string;
    moduleNameMapper: {
        [key: string]: string | string[];
    };
    modulePathIgnorePatterns: string[];
    modulePaths: string[];
    name: string;
    noStackTrace: boolean;
    notify: boolean;
    notifyMode: string;
    onlyChanged: boolean;
    onlyFailures: boolean;
    outputFile: string;
    passWithNoTests: boolean;
    preprocessorIgnorePatterns: string[];
    preset: string | null | undefined;
    prettierPath: string | null | undefined;
    projects: (string | JestConfig.InitialProjectOptions)[];
    replname: string | null | undefined;
    resetMocks: boolean;
    resetModules: boolean;
    resolver: string | null | undefined;
    restoreMocks: boolean;
    rootDir: string;
    roots: string[];
    runner: string;
    runTestsByPath: boolean;
    scriptPreprocessor: string;
    setupFiles: string[];
    setupTestFrameworkScriptFile: string;
    setupFilesAfterEnv: string[];
    silent: boolean;
    skipFilter: boolean;
    skipNodeResolution: boolean;
    slowTestThreshold: number;
    snapshotResolver: string;
    snapshotSerializers: string[];
    snapshotFormat: JestConfig.PrettyFormatOptions;
    errorOnDeprecated: boolean;
    testEnvironment: string;
    testEnvironmentOptions: Record<string, unknown>;
    testFailureExitCode: string | number;
    testLocationInResults: boolean;
    testMatch: string[];
    testNamePattern: string;
    testPathDirs: string[];
    testPathIgnorePatterns: string[];
    testRegex: string | string[];
    testResultsProcessor: string;
    testRunner: string;
    testSequencer: string;
    testURL: string;
    testTimeout: number;
    timers: "real" | "fake" | "modern" | "legacy";
    transform: {
        [regex: string]: string | JestConfig.TransformerConfig;
    };
    transformIgnorePatterns: string[];
    watchPathIgnorePatterns: string[];
    unmockedModulePathPatterns: string[];
    updateSnapshot: boolean;
    useStderr: boolean;
    verbose?: boolean | undefined;
    watch: boolean;
    watchAll: boolean;
    watchman: boolean;
    watchPlugins: (string | [string, Record<string, unknown>])[];
}>;
