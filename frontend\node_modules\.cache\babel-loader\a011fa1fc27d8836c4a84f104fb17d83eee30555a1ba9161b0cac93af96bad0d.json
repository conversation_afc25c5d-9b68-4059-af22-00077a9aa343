{"ast": null, "code": "import * as React from \"react\";\nfunction PictureWrongOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PictureWrongOutline-PictureWrongOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PictureWrongOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"PictureWrongOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M26.6,41 C26.8209139,41 27,41.1790861 27,41.4 L27,43.6 C27,43.8209139 26.8209139,44 26.6,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,10 C4,6.6862915 6.6862915,4 10,4 L38,4 C41.3137085,4 44,6.6862915 44,10 L44,27.593 C44,27.8139139 43.8209139,27.993 43.6,27.993 L41.4,27.993 C41.1790861,27.993 41,27.8139139 41,27.593 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 M40.6719687,31.2652801 L42.2275262,32.8204117 C42.383855,32.9765022 42.3838897,33.2297682 42.2277014,33.3859993 C42.2276688,33.3860319 42.2276362,33.3860645 42.2275059,33.3859993 L38.9039017,36.7082163 C38.7475587,36.8642927 38.7475058,37.1175587 38.903683,37.273801 C38.9037165,37.2738346 38.9037501,37.2738682 38.9038844,37.273801 L42.2275232,40.5964595 C42.3838364,40.7525657 42.3838737,41.0058317 42.2276871,41.1620645 C42.2276603,41.1620913 42.2276334,41.1621181 42.2275262,41.1620645 L40.6719687,42.717196 C40.5156899,42.8732755 40.2624843,42.8732759 40.1062051,42.7171968 L36.7818822,39.3938032 C36.6256468,39.2376241 36.3723857,39.2376567 36.2161905,39.3938759 L32.8937222,42.7171241 C32.7375275,42.8733444 32.4842657,42.8733766 32.3280313,42.717196 L30.7724738,41.1620645 C30.6161986,41.0059202 30.6161639,40.7526542 30.7723523,40.5964231 C30.772367,40.5964084 30.7723817,40.5963938 30.7724404,40.5964231 L34.0951519,37.2738374 C34.2513926,37.1176586 34.2513974,36.8643926 34.0951907,36.70818 C34.0951813,36.7081706 34.095172,36.7081613 34.0951346,36.70818 L30.7724577,33.3860357 C30.6161741,33.2298999 30.6161538,32.9766339 30.772351,32.8204117 C30.7723715,32.8203912 30.7723919,32.8203707 30.7724738,32.8204117 L32.3280313,31.2652801 C32.4843206,31.1092247 32.7375129,31.1092166 32.8938123,31.265262 L36.2171004,34.5872142 C36.3734109,34.7432343 36.6265891,34.7432343 36.7828996,34.5872142 L40.1061877,31.265262 C40.2624871,31.1092166 40.5156794,31.1092247 40.6719687,31.2652801 Z M13,40.9990101 L28.6265729,25.372583 C30.18867,23.8104858 32.72133,23.8104858 34.2834271,25.372583 L36.2211004,27.310141 C36.3773147,27.4663461 36.3773223,27.7196121 36.2211172,27.8758264 C36.1461012,27.9508469 36.0443547,27.992993 35.9382628,27.9929921 L32.6494405,27.9929653 C31.9483786,27.412271 30.9237784,27.4203861 30.2317592,28.0173105 L30.1291748,28.1126218 L17.3591558,40.8818615 C17.284142,40.9568707 17.1824041,40.9990101 17.0763217,40.9990101 L13,40.9990101 L13,40.9990101 Z M16,12 C18.7614237,12 21,14.2385763 21,17 C21,19.7614237 18.7614237,22 16,22 C13.2385763,22 11,19.7614237 11,17 C11,14.2385763 13.2385763,12 16,12 Z M16,15 C14.8954305,15 14,15.8954305 14,17 C14,18.1045695 14.8954305,19 16,19 C17.1045695,19 18,18.1045695 18,17 C18,15.8954305 17.1045695,15 16,15 Z\",\n    id: \"PictureWrongOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default PictureWrongOutline;", "map": {"version": 3, "names": ["React", "PictureWrongOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/PictureWrongOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction PictureWrongOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PictureWrongOutline-PictureWrongOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PictureWrongOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"PictureWrongOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M26.6,41 C26.8209139,41 27,41.1790861 27,41.4 L27,43.6 C27,43.8209139 26.8209139,44 26.6,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,10 C4,6.6862915 6.6862915,4 10,4 L38,4 C41.3137085,4 44,6.6862915 44,10 L44,27.593 C44,27.8139139 43.8209139,27.993 43.6,27.993 L41.4,27.993 C41.1790861,27.993 41,27.8139139 41,27.593 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 M40.6719687,31.2652801 L42.2275262,32.8204117 C42.383855,32.9765022 42.3838897,33.2297682 42.2277014,33.3859993 C42.2276688,33.3860319 42.2276362,33.3860645 42.2275059,33.3859993 L38.9039017,36.7082163 C38.7475587,36.8642927 38.7475058,37.1175587 38.903683,37.273801 C38.9037165,37.2738346 38.9037501,37.2738682 38.9038844,37.273801 L42.2275232,40.5964595 C42.3838364,40.7525657 42.3838737,41.0058317 42.2276871,41.1620645 C42.2276603,41.1620913 42.2276334,41.1621181 42.2275262,41.1620645 L40.6719687,42.717196 C40.5156899,42.8732755 40.2624843,42.8732759 40.1062051,42.7171968 L36.7818822,39.3938032 C36.6256468,39.2376241 36.3723857,39.2376567 36.2161905,39.3938759 L32.8937222,42.7171241 C32.7375275,42.8733444 32.4842657,42.8733766 32.3280313,42.717196 L30.7724738,41.1620645 C30.6161986,41.0059202 30.6161639,40.7526542 30.7723523,40.5964231 C30.772367,40.5964084 30.7723817,40.5963938 30.7724404,40.5964231 L34.0951519,37.2738374 C34.2513926,37.1176586 34.2513974,36.8643926 34.0951907,36.70818 C34.0951813,36.7081706 34.095172,36.7081613 34.0951346,36.70818 L30.7724577,33.3860357 C30.6161741,33.2298999 30.6161538,32.9766339 30.772351,32.8204117 C30.7723715,32.8203912 30.7723919,32.8203707 30.7724738,32.8204117 L32.3280313,31.2652801 C32.4843206,31.1092247 32.7375129,31.1092166 32.8938123,31.265262 L36.2171004,34.5872142 C36.3734109,34.7432343 36.6265891,34.7432343 36.7828996,34.5872142 L40.1061877,31.265262 C40.2624871,31.1092166 40.5156794,31.1092247 40.6719687,31.2652801 Z M13,40.9990101 L28.6265729,25.372583 C30.18867,23.8104858 32.72133,23.8104858 34.2834271,25.372583 L36.2211004,27.310141 C36.3773147,27.4663461 36.3773223,27.7196121 36.2211172,27.8758264 C36.1461012,27.9508469 36.0443547,27.992993 35.9382628,27.9929921 L32.6494405,27.9929653 C31.9483786,27.412271 30.9237784,27.4203861 30.2317592,28.0173105 L30.1291748,28.1126218 L17.3591558,40.8818615 C17.284142,40.9568707 17.1824041,40.9990101 17.0763217,40.9990101 L13,40.9990101 L13,40.9990101 Z M16,12 C18.7614237,12 21,14.2385763 21,17 C21,19.7614237 18.7614237,22 16,22 C13.2385763,22 11,19.7614237 11,17 C11,14.2385763 13.2385763,12 16,12 Z M16,15 C14.8954305,15 14,15.8954305 14,17 C14,18.1045695 14.8954305,19 16,19 C17.1045695,19 18,18.1045695 18,17 C18,15.8954305 17.1045695,15 16,15 Z\",\n    id: \"PictureWrongOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default PictureWrongOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yCAAyC;IAC7CC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,kCAAkC;IACtCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,uvFAAuvF;IAC1vFR,EAAE,EAAE,kCAAkC;IACtCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}