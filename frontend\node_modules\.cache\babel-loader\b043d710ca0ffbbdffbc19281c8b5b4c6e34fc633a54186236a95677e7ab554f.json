{"ast": null, "code": "import React from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { CheckboxGroupContext } from './group-context';\nimport { usePropsValue } from '../../utils/use-props-value';\nconst defaultProps = {\n  disabled: false,\n  defaultValue: []\n};\nexport const Group = p => {\n  const props = mergeProps(defaultProps, p);\n  const [value, setValue] = usePropsValue(props);\n  return React.createElement(CheckboxGroupContext.Provider\n  // TODO: 性能优化\n  , {\n    // TODO: 性能优化\n    value: {\n      value: value,\n      disabled: props.disabled,\n      check: v => {\n        setValue([...value, v]);\n      },\n      uncheck: v => {\n        setValue(value.filter(item => item !== v));\n      }\n    }\n  }, props.children);\n};", "map": {"version": 3, "names": ["React", "mergeProps", "CheckboxGroupContext", "usePropsValue", "defaultProps", "disabled", "defaultValue", "Group", "p", "props", "value", "setValue", "createElement", "Provider", "check", "v", "uncheck", "filter", "item", "children"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/checkbox/group.js"], "sourcesContent": ["import React from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { CheckboxGroupContext } from './group-context';\nimport { usePropsValue } from '../../utils/use-props-value';\nconst defaultProps = {\n  disabled: false,\n  defaultValue: []\n};\nexport const Group = p => {\n  const props = mergeProps(defaultProps, p);\n  const [value, setValue] = usePropsValue(props);\n  return React.createElement(CheckboxGroupContext.Provider\n  // TODO: 性能优化\n  , {\n    // TODO: 性能优化\n    value: {\n      value: value,\n      disabled: props.disabled,\n      check: v => {\n        setValue([...value, v]);\n      },\n      uncheck: v => {\n        setValue(value.filter(item => item !== v));\n      }\n    }\n  }, props.children);\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,oBAAoB,QAAQ,iBAAiB;AACtD,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,MAAMC,YAAY,GAAG;EACnBC,QAAQ,EAAE,KAAK;EACfC,YAAY,EAAE;AAChB,CAAC;AACD,OAAO,MAAMC,KAAK,GAAGC,CAAC,IAAI;EACxB,MAAMC,KAAK,GAAGR,UAAU,CAACG,YAAY,EAAEI,CAAC,CAAC;EACzC,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAGR,aAAa,CAACM,KAAK,CAAC;EAC9C,OAAOT,KAAK,CAACY,aAAa,CAACV,oBAAoB,CAACW;EAChD;EAAA,EACE;IACA;IACAH,KAAK,EAAE;MACLA,KAAK,EAAEA,KAAK;MACZL,QAAQ,EAAEI,KAAK,CAACJ,QAAQ;MACxBS,KAAK,EAAEC,CAAC,IAAI;QACVJ,QAAQ,CAAC,CAAC,GAAGD,KAAK,EAAEK,CAAC,CAAC,CAAC;MACzB,CAAC;MACDC,OAAO,EAAED,CAAC,IAAI;QACZJ,QAAQ,CAACD,KAAK,CAACO,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKH,CAAC,CAAC,CAAC;MAC5C;IACF;EACF,CAAC,EAAEN,KAAK,CAACU,QAAQ,CAAC;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}