{"ast": null, "code": "import React from 'react';\nimport { findDOMNode } from 'react-dom';\nexport class <PERSON>rapper extends React.Component {\n  constructor() {\n    super(...arguments);\n    this.element = null;\n  }\n  componentDidMount() {\n    this.componentDidUpdate();\n  }\n  componentDidUpdate() {\n    // eslint-disable-next-line\n    const node = findDOMNode(this);\n    if (node instanceof Element) {\n      this.element = node;\n    } else {\n      this.element = null;\n    }\n  }\n  render() {\n    return React.Children.only(this.props.children);\n  }\n}", "map": {"version": 3, "names": ["React", "findDOMNode", "Wrapper", "Component", "constructor", "arguments", "element", "componentDidMount", "componentDidUpdate", "node", "Element", "render", "Children", "only", "props", "children"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/popover/wrapper.js"], "sourcesContent": ["import React from 'react';\nimport { findDOMNode } from 'react-dom';\nexport class <PERSON>rapper extends React.Component {\n  constructor() {\n    super(...arguments);\n    this.element = null;\n  }\n  componentDidMount() {\n    this.componentDidUpdate();\n  }\n  componentDidUpdate() {\n    // eslint-disable-next-line\n    const node = findDOMNode(this);\n    if (node instanceof Element) {\n      this.element = node;\n    } else {\n      this.element = null;\n    }\n  }\n  render() {\n    return React.Children.only(this.props.children);\n  }\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,WAAW;AACvC,OAAO,MAAMC,OAAO,SAASF,KAAK,CAACG,SAAS,CAAC;EAC3CC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;EACrB;EACAC,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC3B;EACAA,kBAAkBA,CAAA,EAAG;IACnB;IACA,MAAMC,IAAI,GAAGR,WAAW,CAAC,IAAI,CAAC;IAC9B,IAAIQ,IAAI,YAAYC,OAAO,EAAE;MAC3B,IAAI,CAACJ,OAAO,GAAGG,IAAI;IACrB,CAAC,MAAM;MACL,IAAI,CAACH,OAAO,GAAG,IAAI;IACrB;EACF;EACAK,MAAMA,CAAA,EAAG;IACP,OAAOX,KAAK,CAACY,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAC;EACjD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}