{"ast": null, "code": "import * as React from \"react\";\nfunction MessageFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MessageFill-MessageFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MessageFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"MessageFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,5 C35.0454654,5 44,13.5065417 44,23.9999335 C44,34.3816935 35.234974,42.8187175 24.3517967,42.9970537 L24,42.999934 C20.5361038,43.0086689 17.1256565,42.1499296 14.0827434,40.5027329 L9.12272736,42.6506288 C8.20180797,43.0490167 7.13075643,42.6289802 6.73046083,41.7124519 C6.57362597,41.3533578 6.53805635,40.953324 6.6290919,40.5723965 L7.89727419,35.2705244 C5.44727043,32.1174388 4,28.2188348 4,23.999959 C4,13.5065671 12.9545346,5 24,5 Z M16.2500001,22 C15.2835003,22 14.5,22.7835045 14.5,23.75 C14.5,24.7164996 15.2835044,25.5 16.2499999,25.5 L16.2499999,25.5 C17.2164997,25.5 18,24.7164955 18,23.75 C18,22.7835004 17.2164956,22 16.2500001,22 L16.2500001,22 Z M32.2500001,22 C31.2835003,22 30.5,22.7835045 30.5,23.75 C30.5,24.7164996 31.2835044,25.5 32.2499999,25.5 L32.2499999,25.5 C33.2164997,25.5 34,24.7164955 34,23.75 C34,22.7835004 33.2164956,22 32.2500001,22 L32.2500001,22 Z M24.2500001,22 C23.2835003,22 22.5,22.7835045 22.5,23.75 C22.5,24.7164996 23.2835044,25.5 24.2499999,25.5 L24.2499999,25.5 C25.2164997,25.5 26,24.7164955 26,23.75 C26,22.7835004 25.2164956,22 24.2500001,22 L24.2500001,22 Z\",\n    id: \"MessageFill-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default MessageFill;", "map": {"version": 3, "names": ["React", "MessageFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/MessageFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction MessageFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MessageFill-MessageFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MessageFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"MessageFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,5 C35.0454654,5 44,13.5065417 44,23.9999335 C44,34.3816935 35.234974,42.8187175 24.3517967,42.9970537 L24,42.999934 C20.5361038,43.0086689 17.1256565,42.1499296 14.0827434,40.5027329 L9.12272736,42.6506288 C8.20180797,43.0490167 7.13075643,42.6289802 6.73046083,41.7124519 C6.57362597,41.3533578 6.53805635,40.953324 6.6290919,40.5723965 L7.89727419,35.2705244 C5.44727043,32.1174388 4,28.2188348 4,23.999959 C4,13.5065671 12.9545346,5 24,5 Z M16.2500001,22 C15.2835003,22 14.5,22.7835045 14.5,23.75 C14.5,24.7164996 15.2835044,25.5 16.2499999,25.5 L16.2499999,25.5 C17.2164997,25.5 18,24.7164955 18,23.75 C18,22.7835004 17.2164956,22 16.2500001,22 L16.2500001,22 Z M32.2500001,22 C31.2835003,22 30.5,22.7835045 30.5,23.75 C30.5,24.7164996 31.2835044,25.5 32.2499999,25.5 L32.2499999,25.5 C33.2164997,25.5 34,24.7164955 34,23.75 C34,22.7835004 33.2164956,22 32.2500001,22 L32.2500001,22 Z M24.2500001,22 C23.2835003,22 22.5,22.7835045 22.5,23.75 C22.5,24.7164996 23.2835044,25.5 24.2499999,25.5 L24.2499999,25.5 C25.2164997,25.5 26,24.7164955 26,23.75 C26,22.7835004 25.2164956,22 24.2500001,22 L24.2500001,22 Z\",\n    id: \"MessageFill-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default MessageFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yBAAyB;IAC7BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,6lCAA6lC;IAChmCR,EAAE,EAAE,sCAAsC;IAC1CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}