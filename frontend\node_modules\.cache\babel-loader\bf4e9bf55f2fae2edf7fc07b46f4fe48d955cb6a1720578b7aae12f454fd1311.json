{"ast": null, "code": "import React, { forwardRef, useRef, useState, useImperativeHandle } from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { useThrottleFn } from 'ahooks';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { Sidebar } from './sidebar';\nimport { convertPx } from '../../utils/convert-px';\nimport { Panel } from './panel';\nimport { devWarning } from '../../utils/dev-log';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\nconst classPrefix = `adm-index-bar`;\nconst defaultProps = {\n  sticky: true\n};\nexport const IndexBar = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const titleHeight = convertPx(35);\n  const bodyRef = useRef(null);\n  const indexItems = [];\n  const panels = [];\n  traverseReactNode(props.children, child => {\n    var _a;\n    if (!React.isValidElement(child)) return;\n    if (child.type !== Panel) {\n      devWarning('IndexBar', 'The children of `IndexBar` must be `IndexBar.Panel` components.');\n      return;\n    }\n    indexItems.push({\n      index: child.props.index,\n      brief: (_a = child.props.brief) !== null && _a !== void 0 ? _a : child.props.index.charAt(0)\n    });\n    panels.push(withNativeProps(child.props, React.createElement(\"div\", {\n      key: child.props.index,\n      \"data-index\": child.props.index,\n      className: `${classPrefix}-anchor`\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-anchor-title`\n    }, child.props.title || child.props.index), child.props.children)));\n  });\n  const [activeIndex, setActiveIndex] = useState(() => {\n    const firstItem = indexItems[0];\n    return firstItem ? firstItem.index : null;\n  });\n  useImperativeHandle(ref, () => ({\n    scrollTo\n  }));\n  function scrollTo(index) {\n    var _a;\n    const body = bodyRef.current;\n    if (!body) return;\n    const children = body.children;\n    for (let i = 0; i < children.length; i++) {\n      const panel = children.item(i);\n      if (!panel) continue;\n      const panelIndex = panel.dataset['index'];\n      if (panelIndex === index) {\n        body.scrollTop = panel.offsetTop;\n        setActiveIndex(index);\n        activeIndex !== index && ((_a = props.onIndexChange) === null || _a === void 0 ? void 0 : _a.call(props, index));\n        return;\n      }\n    }\n  }\n  const {\n    run: checkActiveIndex\n  } = useThrottleFn(() => {\n    var _a;\n    const body = bodyRef.current;\n    if (!body) return;\n    const scrollTop = body.scrollTop;\n    const elements = body.getElementsByClassName(`${classPrefix}-anchor`);\n    for (let i = 0; i < elements.length; i++) {\n      const panel = elements.item(i);\n      if (!panel) continue;\n      const panelIndex = panel.dataset['index'];\n      if (!panelIndex) continue;\n      if (panel.offsetTop + panel.clientHeight - titleHeight > scrollTop) {\n        setActiveIndex(panelIndex);\n        activeIndex !== panelIndex && ((_a = props.onIndexChange) === null || _a === void 0 ? void 0 : _a.call(props, panelIndex));\n        return;\n      }\n    }\n  }, {\n    wait: 50,\n    trailing: true,\n    leading: true\n  });\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}`, {\n      [`${classPrefix}-sticky`]: props.sticky\n    })\n  }, React.createElement(Sidebar, {\n    indexItems: indexItems,\n    activeIndex: activeIndex,\n    onActive: index => {\n      scrollTo(index);\n    }\n  }), React.createElement(\"div\", {\n    className: `${classPrefix}-body`,\n    ref: bodyRef,\n    onScroll: checkActiveIndex\n  }, panels)));\n});", "map": {"version": 3, "names": ["React", "forwardRef", "useRef", "useState", "useImperativeHandle", "classNames", "withNativeProps", "useThrottleFn", "mergeProps", "Sidebar", "convertPx", "Panel", "dev<PERSON><PERSON><PERSON>", "traverseReactNode", "classPrefix", "defaultProps", "sticky", "IndexBar", "p", "ref", "props", "titleHeight", "bodyRef", "indexItems", "panels", "children", "child", "_a", "isValidElement", "type", "push", "index", "brief", "char<PERSON>t", "createElement", "key", "className", "title", "activeIndex", "setActiveIndex", "firstItem", "scrollTo", "body", "current", "i", "length", "panel", "item", "panelIndex", "dataset", "scrollTop", "offsetTop", "onIndexChange", "call", "run", "checkActiveIndex", "elements", "getElementsByClassName", "clientHeight", "wait", "trailing", "leading", "onActive", "onScroll"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/index-bar/index-bar.js"], "sourcesContent": ["import React, { forwardRef, useRef, useState, useImperativeHandle } from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { useThrottleFn } from 'ahooks';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { Sidebar } from './sidebar';\nimport { convertPx } from '../../utils/convert-px';\nimport { Panel } from './panel';\nimport { devWarning } from '../../utils/dev-log';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\nconst classPrefix = `adm-index-bar`;\nconst defaultProps = {\n  sticky: true\n};\nexport const IndexBar = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const titleHeight = convertPx(35);\n  const bodyRef = useRef(null);\n  const indexItems = [];\n  const panels = [];\n  traverseReactNode(props.children, child => {\n    var _a;\n    if (!React.isValidElement(child)) return;\n    if (child.type !== Panel) {\n      devWarning('IndexBar', 'The children of `IndexBar` must be `IndexBar.Panel` components.');\n      return;\n    }\n    indexItems.push({\n      index: child.props.index,\n      brief: (_a = child.props.brief) !== null && _a !== void 0 ? _a : child.props.index.charAt(0)\n    });\n    panels.push(withNativeProps(child.props, React.createElement(\"div\", {\n      key: child.props.index,\n      \"data-index\": child.props.index,\n      className: `${classPrefix}-anchor`\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-anchor-title`\n    }, child.props.title || child.props.index), child.props.children)));\n  });\n  const [activeIndex, setActiveIndex] = useState(() => {\n    const firstItem = indexItems[0];\n    return firstItem ? firstItem.index : null;\n  });\n  useImperativeHandle(ref, () => ({\n    scrollTo\n  }));\n  function scrollTo(index) {\n    var _a;\n    const body = bodyRef.current;\n    if (!body) return;\n    const children = body.children;\n    for (let i = 0; i < children.length; i++) {\n      const panel = children.item(i);\n      if (!panel) continue;\n      const panelIndex = panel.dataset['index'];\n      if (panelIndex === index) {\n        body.scrollTop = panel.offsetTop;\n        setActiveIndex(index);\n        activeIndex !== index && ((_a = props.onIndexChange) === null || _a === void 0 ? void 0 : _a.call(props, index));\n        return;\n      }\n    }\n  }\n  const {\n    run: checkActiveIndex\n  } = useThrottleFn(() => {\n    var _a;\n    const body = bodyRef.current;\n    if (!body) return;\n    const scrollTop = body.scrollTop;\n    const elements = body.getElementsByClassName(`${classPrefix}-anchor`);\n    for (let i = 0; i < elements.length; i++) {\n      const panel = elements.item(i);\n      if (!panel) continue;\n      const panelIndex = panel.dataset['index'];\n      if (!panelIndex) continue;\n      if (panel.offsetTop + panel.clientHeight - titleHeight > scrollTop) {\n        setActiveIndex(panelIndex);\n        activeIndex !== panelIndex && ((_a = props.onIndexChange) === null || _a === void 0 ? void 0 : _a.call(props, panelIndex));\n        return;\n      }\n    }\n  }, {\n    wait: 50,\n    trailing: true,\n    leading: true\n  });\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}`, {\n      [`${classPrefix}-sticky`]: props.sticky\n    })\n  }, React.createElement(Sidebar, {\n    indexItems: indexItems,\n    activeIndex: activeIndex,\n    onActive: index => {\n      scrollTo(index);\n    }\n  }), React.createElement(\"div\", {\n    className: `${classPrefix}-body`,\n    ref: bodyRef,\n    onScroll: checkActiveIndex\n  }, panels)));\n});"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,mBAAmB,QAAQ,OAAO;AAChF,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,QAAQ;AACtC,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,MAAMC,WAAW,GAAG,eAAe;AACnC,MAAMC,YAAY,GAAG;EACnBC,MAAM,EAAE;AACV,CAAC;AACD,OAAO,MAAMC,QAAQ,GAAGhB,UAAU,CAAC,CAACiB,CAAC,EAAEC,GAAG,KAAK;EAC7C,MAAMC,KAAK,GAAGZ,UAAU,CAACO,YAAY,EAAEG,CAAC,CAAC;EACzC,MAAMG,WAAW,GAAGX,SAAS,CAAC,EAAE,CAAC;EACjC,MAAMY,OAAO,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMqB,UAAU,GAAG,EAAE;EACrB,MAAMC,MAAM,GAAG,EAAE;EACjBX,iBAAiB,CAACO,KAAK,CAACK,QAAQ,EAAEC,KAAK,IAAI;IACzC,IAAIC,EAAE;IACN,IAAI,CAAC3B,KAAK,CAAC4B,cAAc,CAACF,KAAK,CAAC,EAAE;IAClC,IAAIA,KAAK,CAACG,IAAI,KAAKlB,KAAK,EAAE;MACxBC,UAAU,CAAC,UAAU,EAAE,iEAAiE,CAAC;MACzF;IACF;IACAW,UAAU,CAACO,IAAI,CAAC;MACdC,KAAK,EAAEL,KAAK,CAACN,KAAK,CAACW,KAAK;MACxBC,KAAK,EAAE,CAACL,EAAE,GAAGD,KAAK,CAACN,KAAK,CAACY,KAAK,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGD,KAAK,CAACN,KAAK,CAACW,KAAK,CAACE,MAAM,CAAC,CAAC;IAC7F,CAAC,CAAC;IACFT,MAAM,CAACM,IAAI,CAACxB,eAAe,CAACoB,KAAK,CAACN,KAAK,EAAEpB,KAAK,CAACkC,aAAa,CAAC,KAAK,EAAE;MAClEC,GAAG,EAAET,KAAK,CAACN,KAAK,CAACW,KAAK;MACtB,YAAY,EAAEL,KAAK,CAACN,KAAK,CAACW,KAAK;MAC/BK,SAAS,EAAE,GAAGtB,WAAW;IAC3B,CAAC,EAAEd,KAAK,CAACkC,aAAa,CAAC,KAAK,EAAE;MAC5BE,SAAS,EAAE,GAAGtB,WAAW;IAC3B,CAAC,EAAEY,KAAK,CAACN,KAAK,CAACiB,KAAK,IAAIX,KAAK,CAACN,KAAK,CAACW,KAAK,CAAC,EAAEL,KAAK,CAACN,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC;EACrE,CAAC,CAAC;EACF,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,MAAM;IACnD,MAAMqC,SAAS,GAAGjB,UAAU,CAAC,CAAC,CAAC;IAC/B,OAAOiB,SAAS,GAAGA,SAAS,CAACT,KAAK,GAAG,IAAI;EAC3C,CAAC,CAAC;EACF3B,mBAAmB,CAACe,GAAG,EAAE,OAAO;IAC9BsB;EACF,CAAC,CAAC,CAAC;EACH,SAASA,QAAQA,CAACV,KAAK,EAAE;IACvB,IAAIJ,EAAE;IACN,MAAMe,IAAI,GAAGpB,OAAO,CAACqB,OAAO;IAC5B,IAAI,CAACD,IAAI,EAAE;IACX,MAAMjB,QAAQ,GAAGiB,IAAI,CAACjB,QAAQ;IAC9B,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,QAAQ,CAACoB,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,MAAME,KAAK,GAAGrB,QAAQ,CAACsB,IAAI,CAACH,CAAC,CAAC;MAC9B,IAAI,CAACE,KAAK,EAAE;MACZ,MAAME,UAAU,GAAGF,KAAK,CAACG,OAAO,CAAC,OAAO,CAAC;MACzC,IAAID,UAAU,KAAKjB,KAAK,EAAE;QACxBW,IAAI,CAACQ,SAAS,GAAGJ,KAAK,CAACK,SAAS;QAChCZ,cAAc,CAACR,KAAK,CAAC;QACrBO,WAAW,KAAKP,KAAK,KAAK,CAACJ,EAAE,GAAGP,KAAK,CAACgC,aAAa,MAAM,IAAI,IAAIzB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0B,IAAI,CAACjC,KAAK,EAAEW,KAAK,CAAC,CAAC;QAChH;MACF;IACF;EACF;EACA,MAAM;IACJuB,GAAG,EAAEC;EACP,CAAC,GAAGhD,aAAa,CAAC,MAAM;IACtB,IAAIoB,EAAE;IACN,MAAMe,IAAI,GAAGpB,OAAO,CAACqB,OAAO;IAC5B,IAAI,CAACD,IAAI,EAAE;IACX,MAAMQ,SAAS,GAAGR,IAAI,CAACQ,SAAS;IAChC,MAAMM,QAAQ,GAAGd,IAAI,CAACe,sBAAsB,CAAC,GAAG3C,WAAW,SAAS,CAAC;IACrE,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,QAAQ,CAACX,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,MAAME,KAAK,GAAGU,QAAQ,CAACT,IAAI,CAACH,CAAC,CAAC;MAC9B,IAAI,CAACE,KAAK,EAAE;MACZ,MAAME,UAAU,GAAGF,KAAK,CAACG,OAAO,CAAC,OAAO,CAAC;MACzC,IAAI,CAACD,UAAU,EAAE;MACjB,IAAIF,KAAK,CAACK,SAAS,GAAGL,KAAK,CAACY,YAAY,GAAGrC,WAAW,GAAG6B,SAAS,EAAE;QAClEX,cAAc,CAACS,UAAU,CAAC;QAC1BV,WAAW,KAAKU,UAAU,KAAK,CAACrB,EAAE,GAAGP,KAAK,CAACgC,aAAa,MAAM,IAAI,IAAIzB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0B,IAAI,CAACjC,KAAK,EAAE4B,UAAU,CAAC,CAAC;QAC1H;MACF;IACF;EACF,CAAC,EAAE;IACDW,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,OAAOvD,eAAe,CAACc,KAAK,EAAEpB,KAAK,CAACkC,aAAa,CAAC,KAAK,EAAE;IACvDE,SAAS,EAAE/B,UAAU,CAAC,GAAGS,WAAW,EAAE,EAAE;MACtC,CAAC,GAAGA,WAAW,SAAS,GAAGM,KAAK,CAACJ;IACnC,CAAC;EACH,CAAC,EAAEhB,KAAK,CAACkC,aAAa,CAACzB,OAAO,EAAE;IAC9Bc,UAAU,EAAEA,UAAU;IACtBe,WAAW,EAAEA,WAAW;IACxBwB,QAAQ,EAAE/B,KAAK,IAAI;MACjBU,QAAQ,CAACV,KAAK,CAAC;IACjB;EACF,CAAC,CAAC,EAAE/B,KAAK,CAACkC,aAAa,CAAC,KAAK,EAAE;IAC7BE,SAAS,EAAE,GAAGtB,WAAW,OAAO;IAChCK,GAAG,EAAEG,OAAO;IACZyC,QAAQ,EAAER;EACZ,CAAC,EAAE/B,MAAM,CAAC,CAAC,CAAC;AACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}