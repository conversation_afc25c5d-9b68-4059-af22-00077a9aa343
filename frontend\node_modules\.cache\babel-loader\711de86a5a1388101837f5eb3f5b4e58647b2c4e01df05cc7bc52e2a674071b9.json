{"ast": null, "code": "import * as React from \"react\";\nfunction PhonebookOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PhonebookOutline-PhonebookOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PhonebookOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"PhonebookOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M43,8.43529471 L43,39.5647053 C43,42.5665336 40.5948309,45 37.627909,45 L13.3720913,45 C10.4051691,45 8,42.5665336 8,39.5647053 L8,8.43529968 C8,5.43347216 10.4051649,3 13.372091,3 L37.6279087,3 C40.5948309,3 43,5.43346212 43,8.43529471 Z M5.4,12 L8.6,12 C8.8209139,12 9,12.1790861 9,12.4 L9,14.6 C9,14.8209139 8.8209139,15 8.6,15 L5.4,15 C5.1790861,15 5,14.8209139 5,14.6 L5,12.4 C5,12.1790861 5.1790861,12 5.4,12 Z M5.4,22 L8.6,22 C8.8209139,22 9,22.1790861 9,22.4 L9,24.6 C9,24.8209139 8.8209139,25 8.6,25 L5.4,25 C5.1790861,25 5,24.8209139 5,24.6 L5,22.4 C5,22.1790861 5.1790861,22 5.4,22 Z M5.4,32 L8.6,32 C8.8209139,32 9,32.1790861 9,32.4 L9,34.6 C9,34.8209139 8.8209139,35 8.6,35 L5.4,35 C5.1790861,35 5,34.8209139 5,34.6 L5,32.4 C5,32.1790861 5.1790861,32 5.4,32 Z M13.7500017,5.99999999 C12.2938235,5.99999999 11.0900096,7.54633112 11.0045834,8.99999999 L11.0045834,39.2499962 C11.0045834,40.7063272 12.551766,41.9146511 14.0054435,42 L14.1666246,42 L36.4166077,42 C37.8727859,42 39.9145737,40.7036651 40,39.2499962 L40,8.99999999 C40,7.54366857 38.8648417,6.08534893 37.4111643,5.99999999 L37.2499836,5.99999999 L13.7500017,5.99999999 Z M23.7827066,15.0469175 L23.8157066,15.0744176 L24.0769569,15.3283344 L24.7800425,16.03692 L25.9313786,17.1845865 C26.6335447,17.8840026 27.08088,18.7667536 27.08088,19.8062548 C27.08088,20.8448408 26.6335447,21.7275918 25.9313786,22.4260926 L25.6756283,22.6827595 L25.8708786,22.9348432 C26.5886294,23.8157595 27.3201303,24.5252603 28.1102964,25.105511 L28.1597965,25.141261 L28.4292968,24.8735942 C29.0709628,24.2355935 29.8858832,23.8166777 30.8126343,23.7598429 L31.0298846,23.7534262 C32.1546359,23.7534262 33.0254717,24.246592 33.6790529,24.9469276 L35.7846401,27.03693 C37.2476418,28.4925969 37.2806418,30.5825994 35.9789738,32.0694359 L35.8396402,32.220686 C35.6627234,32.4086028 35.4913063,32.5781865 35.2648895,32.7945215 L35.0494727,33.0035217 C34.9698034,33.0812283 34.8918674,33.1606926 34.8157224,33.2418555 L34.6113057,33.4710223 C33.6689698,34.4830235 32.4424684,35.0000241 31.0500515,35.0000241 L30.9638849,35.0000241 L30.687968,34.9871907 C29.2634664,34.8964406 28.0259649,34.4894401 26.5583785,33.7936893 C23.9519346,32.5373549 21.6441663,30.7383802 19.7897188,28.5173358 C18.2533823,26.6775837 17.186381,24.9102468 16.475965,23.0008294 C15.9745491,21.6679931 15.7600489,20.4818265 15.856299,19.2809903 C15.9497324,18.1739867 16.4369234,17.1371581 17.2294657,16.3586537 L18.6063021,14.9891521 C19.2974681,14.3245666 20.1893887,13.9166509 21.1757203,13.9166509 C22.18862,13.9166509 23.0924558,14.3475015 23.7827066,15.0469175 Z M20.5120333,16.9719198 L19.1700317,18.3084213 C18.8354968,18.6312834 18.6312759,19.0658238 18.5961962,19.5294228 C18.5384461,20.2508389 18.6786963,21.0446746 19.050862,22.0319257 C19.6494474,23.6415928 20.556029,25.1421793 21.9017001,26.7555289 C23.5043982,28.6752939 25.49904,30.2301887 27.7518598,31.3159372 C28.5786955,31.7073542 29.6796121,32.167523 30.8630287,32.2426883 L31.0509454,32.2500217 C31.6898614,32.2500217 32.1839467,32.041938 32.6001124,31.5964361 C32.8384462,31.3122693 33.100613,31.0611008 33.3536133,30.81727 L33.6011136,30.5789363 L33.8339474,30.339686 C34.2739479,29.8831854 34.2776131,29.4166001 33.8449474,28.9857692 L31.6027795,26.7582666 C31.4625294,26.6280999 31.2645291,26.5034328 31.0298636,26.5034328 C30.8052799,26.5034328 30.5779479,26.6152664 30.3689476,26.8233497 L29.0186961,28.1662665 C28.958196,28.2276831 28.7702792,28.4110168 28.4815302,28.4110168 C28.3592667,28.4097845 28.23912,28.3789616 28.1313632,28.3211832 L27.9681965,28.2258496 C27.8779901,28.1762933 27.7866023,28.1289185 27.6941128,28.083766 L27.5364461,28.0040159 C27.4273833,27.9500023 27.3212121,27.8903384 27.2183637,27.8252664 C25.9625275,27.0314307 24.8212761,26.0010991 23.7386944,24.671928 C23.1364437,23.9175119 22.7496085,23.2859264 22.4718582,22.6213408 L22.4406914,22.5260072 C22.3829414,22.319757 22.3719413,22.0640067 22.6423582,21.7954216 L22.9971086,21.4672547 C23.2180254,21.256421 23.4325243,21.0400877 23.6561941,20.8145887 L23.9916945,20.4772549 C24.2153613,20.2545046 24.3308614,20.0280891 24.3308614,19.8053388 C24.3308615,19.5825886 24.2153613,19.3561731 23.9916945,19.1343381 L22.9164433,18.0636716 L22.5406094,17.6832546 L22.0639436,17.2056693 L21.8256098,16.9792525 C21.6202761,16.7711688 21.3966093,16.6666687 21.1766091,16.6666687 C20.952952,16.6666687 20.721953,16.7711695 20.5120333,16.9719198 Z\",\n    id: \"PhonebookOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default PhonebookOutline;", "map": {"version": 3, "names": ["React", "PhonebookOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/PhonebookOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction PhonebookOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PhonebookOutline-PhonebookOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PhonebookOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"PhonebookOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M43,8.43529471 L43,39.5647053 C43,42.5665336 40.5948309,45 37.627909,45 L13.3720913,45 C10.4051691,45 8,42.5665336 8,39.5647053 L8,8.43529968 C8,5.43347216 10.4051649,3 13.372091,3 L37.6279087,3 C40.5948309,3 43,5.43346212 43,8.43529471 Z M5.4,12 L8.6,12 C8.8209139,12 9,12.1790861 9,12.4 L9,14.6 C9,14.8209139 8.8209139,15 8.6,15 L5.4,15 C5.1790861,15 5,14.8209139 5,14.6 L5,12.4 C5,12.1790861 5.1790861,12 5.4,12 Z M5.4,22 L8.6,22 C8.8209139,22 9,22.1790861 9,22.4 L9,24.6 C9,24.8209139 8.8209139,25 8.6,25 L5.4,25 C5.1790861,25 5,24.8209139 5,24.6 L5,22.4 C5,22.1790861 5.1790861,22 5.4,22 Z M5.4,32 L8.6,32 C8.8209139,32 9,32.1790861 9,32.4 L9,34.6 C9,34.8209139 8.8209139,35 8.6,35 L5.4,35 C5.1790861,35 5,34.8209139 5,34.6 L5,32.4 C5,32.1790861 5.1790861,32 5.4,32 Z M13.7500017,5.99999999 C12.2938235,5.99999999 11.0900096,7.54633112 11.0045834,8.99999999 L11.0045834,39.2499962 C11.0045834,40.7063272 12.551766,41.9146511 14.0054435,42 L14.1666246,42 L36.4166077,42 C37.8727859,42 39.9145737,40.7036651 40,39.2499962 L40,8.99999999 C40,7.54366857 38.8648417,6.08534893 37.4111643,5.99999999 L37.2499836,5.99999999 L13.7500017,5.99999999 Z M23.7827066,15.0469175 L23.8157066,15.0744176 L24.0769569,15.3283344 L24.7800425,16.03692 L25.9313786,17.1845865 C26.6335447,17.8840026 27.08088,18.7667536 27.08088,19.8062548 C27.08088,20.8448408 26.6335447,21.7275918 25.9313786,22.4260926 L25.6756283,22.6827595 L25.8708786,22.9348432 C26.5886294,23.8157595 27.3201303,24.5252603 28.1102964,25.105511 L28.1597965,25.141261 L28.4292968,24.8735942 C29.0709628,24.2355935 29.8858832,23.8166777 30.8126343,23.7598429 L31.0298846,23.7534262 C32.1546359,23.7534262 33.0254717,24.246592 33.6790529,24.9469276 L35.7846401,27.03693 C37.2476418,28.4925969 37.2806418,30.5825994 35.9789738,32.0694359 L35.8396402,32.220686 C35.6627234,32.4086028 35.4913063,32.5781865 35.2648895,32.7945215 L35.0494727,33.0035217 C34.9698034,33.0812283 34.8918674,33.1606926 34.8157224,33.2418555 L34.6113057,33.4710223 C33.6689698,34.4830235 32.4424684,35.0000241 31.0500515,35.0000241 L30.9638849,35.0000241 L30.687968,34.9871907 C29.2634664,34.8964406 28.0259649,34.4894401 26.5583785,33.7936893 C23.9519346,32.5373549 21.6441663,30.7383802 19.7897188,28.5173358 C18.2533823,26.6775837 17.186381,24.9102468 16.475965,23.0008294 C15.9745491,21.6679931 15.7600489,20.4818265 15.856299,19.2809903 C15.9497324,18.1739867 16.4369234,17.1371581 17.2294657,16.3586537 L18.6063021,14.9891521 C19.2974681,14.3245666 20.1893887,13.9166509 21.1757203,13.9166509 C22.18862,13.9166509 23.0924558,14.3475015 23.7827066,15.0469175 Z M20.5120333,16.9719198 L19.1700317,18.3084213 C18.8354968,18.6312834 18.6312759,19.0658238 18.5961962,19.5294228 C18.5384461,20.2508389 18.6786963,21.0446746 19.050862,22.0319257 C19.6494474,23.6415928 20.556029,25.1421793 21.9017001,26.7555289 C23.5043982,28.6752939 25.49904,30.2301887 27.7518598,31.3159372 C28.5786955,31.7073542 29.6796121,32.167523 30.8630287,32.2426883 L31.0509454,32.2500217 C31.6898614,32.2500217 32.1839467,32.041938 32.6001124,31.5964361 C32.8384462,31.3122693 33.100613,31.0611008 33.3536133,30.81727 L33.6011136,30.5789363 L33.8339474,30.339686 C34.2739479,29.8831854 34.2776131,29.4166001 33.8449474,28.9857692 L31.6027795,26.7582666 C31.4625294,26.6280999 31.2645291,26.5034328 31.0298636,26.5034328 C30.8052799,26.5034328 30.5779479,26.6152664 30.3689476,26.8233497 L29.0186961,28.1662665 C28.958196,28.2276831 28.7702792,28.4110168 28.4815302,28.4110168 C28.3592667,28.4097845 28.23912,28.3789616 28.1313632,28.3211832 L27.9681965,28.2258496 C27.8779901,28.1762933 27.7866023,28.1289185 27.6941128,28.083766 L27.5364461,28.0040159 C27.4273833,27.9500023 27.3212121,27.8903384 27.2183637,27.8252664 C25.9625275,27.0314307 24.8212761,26.0010991 23.7386944,24.671928 C23.1364437,23.9175119 22.7496085,23.2859264 22.4718582,22.6213408 L22.4406914,22.5260072 C22.3829414,22.319757 22.3719413,22.0640067 22.6423582,21.7954216 L22.9971086,21.4672547 C23.2180254,21.256421 23.4325243,21.0400877 23.6561941,20.8145887 L23.9916945,20.4772549 C24.2153613,20.2545046 24.3308614,20.0280891 24.3308614,19.8053388 C24.3308615,19.5825886 24.2153613,19.3561731 23.9916945,19.1343381 L22.9164433,18.0636716 L22.5406094,17.6832546 L22.0639436,17.2056693 L21.8256098,16.9792525 C21.6202761,16.7711688 21.3966093,16.6666687 21.1766091,16.6666687 C20.952952,16.6666687 20.721953,16.7711695 20.5120333,16.9719198 Z\",\n    id: \"PhonebookOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default PhonebookOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,mCAAmC;IACvCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,+BAA+B;IACnCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,k0IAAk0I;IACr0IR,EAAE,EAAE,+BAA+B;IACnCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}