{"ast": null, "code": "import { createPortal } from 'react-dom';\nimport { resolveContainer } from './get-container';\nimport { canUseDom } from './can-use-dom';\nexport function renderToContainer(getContainer, node) {\n  if (canUseDom && getContainer) {\n    const container = resolveContainer(getContainer);\n    return createPortal(node, container);\n  }\n  return node;\n}", "map": {"version": 3, "names": ["createPortal", "resolveContainer", "canUseDom", "renderToContainer", "getContainer", "node", "container"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/render-to-container.js"], "sourcesContent": ["import { createPortal } from 'react-dom';\nimport { resolveContainer } from './get-container';\nimport { canUseDom } from './can-use-dom';\nexport function renderToContainer(getContainer, node) {\n  if (canUseDom && getContainer) {\n    const container = resolveContainer(getContainer);\n    return createPortal(node, container);\n  }\n  return node;\n}"], "mappings": "AAAA,SAASA,YAAY,QAAQ,WAAW;AACxC,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,SAAS,QAAQ,eAAe;AACzC,OAAO,SAASC,iBAAiBA,CAACC,YAAY,EAAEC,IAAI,EAAE;EACpD,IAAIH,SAAS,IAAIE,YAAY,EAAE;IAC7B,MAAME,SAAS,GAAGL,gBAAgB,CAACG,YAAY,CAAC;IAChD,OAAOJ,YAAY,CAACK,IAAI,EAAEC,SAAS,CAAC;EACtC;EACA,OAAOD,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}