{"ast": null, "code": "export const isDev = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';", "map": {"version": 3, "names": ["isDev", "process", "env", "NODE_ENV"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/is-dev.js"], "sourcesContent": ["export const isDev = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';"], "mappings": "AAAA,OAAO,MAAMA,KAAK,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAIF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}