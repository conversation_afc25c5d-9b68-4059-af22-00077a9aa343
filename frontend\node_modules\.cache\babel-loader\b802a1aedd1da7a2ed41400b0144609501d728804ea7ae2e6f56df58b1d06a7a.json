{"ast": null, "code": "import { useRef } from 'react';\nexport function useInitialized(check) {\n  const initializedRef = useRef(check);\n  if (check) {\n    initializedRef.current = true;\n  }\n  return !!initializedRef.current;\n}", "map": {"version": 3, "names": ["useRef", "useInitialized", "check", "initializedRef", "current"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/use-initialized.js"], "sourcesContent": ["import { useRef } from 'react';\nexport function useInitialized(check) {\n  const initializedRef = useRef(check);\n  if (check) {\n    initializedRef.current = true;\n  }\n  return !!initializedRef.current;\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,MAAMC,cAAc,GAAGH,MAAM,CAACE,KAAK,CAAC;EACpC,IAAIA,KAAK,EAAE;IACTC,cAAc,CAACC,OAAO,GAAG,IAAI;EAC/B;EACA,OAAO,CAAC,CAACD,cAAc,CAACC,OAAO;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}