{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = exports.HOOK_MARK = void 0;\nvar _warning = _interopRequireDefault(require(\"rc-util/lib/warning\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar HOOK_MARK = exports.HOOK_MARK = 'RC_FORM_INTERNAL_HOOKS';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nvar warningFunc = function warningFunc() {\n  (0, _warning.default)(false, 'Can not find FormContext. Please make sure you wrap Field under Form.');\n};\nvar Context = /*#__PURE__*/React.createContext({\n  getFieldValue: warningFunc,\n  getFieldsValue: warningFunc,\n  getFieldError: warningFunc,\n  getFieldWarning: warningFunc,\n  getFieldsError: warningFunc,\n  isFieldsTouched: warningFunc,\n  isFieldTouched: warningFunc,\n  isFieldValidating: warningFunc,\n  isFieldsValidating: warningFunc,\n  resetFields: warningFunc,\n  setFields: warningFunc,\n  setFieldValue: warningFunc,\n  setFieldsValue: warningFunc,\n  validateFields: warningFunc,\n  submit: warningFunc,\n  getInternalHooks: function getInternalHooks() {\n    warningFunc();\n    return {\n      dispatch: warningFunc,\n      initEntityValue: warningFunc,\n      registerField: warningFunc,\n      useSubscribe: warningFunc,\n      setInitialValues: warningFunc,\n      destroyForm: warningFunc,\n      setCallbacks: warningFunc,\n      registerWatch: warningFunc,\n      getFields: warningFunc,\n      setValidateMessages: warningFunc,\n      setPreserve: warningFunc,\n      getInitialValue: warningFunc\n    };\n  }\n});\nvar _default = exports.default = Context;", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "HOOK_MARK", "_warning", "React", "warningFunc", "Context", "createContext", "getFieldValue", "getFieldsValue", "getFieldError", "getFieldWarning", "getFieldsError", "isFieldsTouched", "isFieldTouched", "isFieldValidating", "isFieldsValidating", "resetFields", "setFields", "setFieldValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validateFields", "submit", "getInternalHooks", "dispatch", "initEntityValue", "registerField", "useSubscribe", "setInitialValues", "destroyForm", "setCallbacks", "registerWatch", "getFields", "setValidateMessages", "setPreserve", "getInitialValue", "_default"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/rc-field-form/lib/FieldContext.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = exports.HOOK_MARK = void 0;\nvar _warning = _interopRequireDefault(require(\"rc-util/lib/warning\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar HOOK_MARK = exports.HOOK_MARK = 'RC_FORM_INTERNAL_HOOKS';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nvar warningFunc = function warningFunc() {\n  (0, _warning.default)(false, 'Can not find FormContext. Please make sure you wrap Field under Form.');\n};\nvar Context = /*#__PURE__*/React.createContext({\n  getFieldValue: warningFunc,\n  getFieldsValue: warningFunc,\n  getFieldError: warningFunc,\n  getFieldWarning: warningFunc,\n  getFieldsError: warningFunc,\n  isFieldsTouched: warningFunc,\n  isFieldTouched: warningFunc,\n  isFieldValidating: warningFunc,\n  isFieldsValidating: warningFunc,\n  resetFields: warningFunc,\n  setFields: warningFunc,\n  setFieldValue: warningFunc,\n  setFieldsValue: warningFunc,\n  validateFields: warningFunc,\n  submit: warningFunc,\n  getInternalHooks: function getInternalHooks() {\n    warningFunc();\n    return {\n      dispatch: warningFunc,\n      initEntityValue: warningFunc,\n      registerField: warningFunc,\n      useSubscribe: warningFunc,\n      setInitialValues: warningFunc,\n      destroyForm: warningFunc,\n      setCallbacks: warningFunc,\n      registerWatch: warningFunc,\n      getFields: warningFunc,\n      setValidateMessages: warningFunc,\n      setPreserve: warningFunc,\n      getInitialValue: warningFunc\n    };\n  }\n});\nvar _default = exports.default = Context;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9F,IAAIC,sBAAsB,GAAGF,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACJ,OAAO,GAAGI,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC5C,IAAIC,QAAQ,GAAGN,sBAAsB,CAACF,OAAO,CAAC,qBAAqB,CAAC,CAAC;AACrE,IAAIS,KAAK,GAAGV,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIO,SAAS,GAAGF,OAAO,CAACE,SAAS,GAAG,wBAAwB;;AAE5D;AACA,IAAIG,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;EACvC,CAAC,CAAC,EAAEF,QAAQ,CAACP,OAAO,EAAE,KAAK,EAAE,uEAAuE,CAAC;AACvG,CAAC;AACD,IAAIU,OAAO,GAAG,aAAaF,KAAK,CAACG,aAAa,CAAC;EAC7CC,aAAa,EAAEH,WAAW;EAC1BI,cAAc,EAAEJ,WAAW;EAC3BK,aAAa,EAAEL,WAAW;EAC1BM,eAAe,EAAEN,WAAW;EAC5BO,cAAc,EAAEP,WAAW;EAC3BQ,eAAe,EAAER,WAAW;EAC5BS,cAAc,EAAET,WAAW;EAC3BU,iBAAiB,EAAEV,WAAW;EAC9BW,kBAAkB,EAAEX,WAAW;EAC/BY,WAAW,EAAEZ,WAAW;EACxBa,SAAS,EAAEb,WAAW;EACtBc,aAAa,EAAEd,WAAW;EAC1Be,cAAc,EAAEf,WAAW;EAC3BgB,cAAc,EAAEhB,WAAW;EAC3BiB,MAAM,EAAEjB,WAAW;EACnBkB,gBAAgB,EAAE,SAASA,gBAAgBA,CAAA,EAAG;IAC5ClB,WAAW,CAAC,CAAC;IACb,OAAO;MACLmB,QAAQ,EAAEnB,WAAW;MACrBoB,eAAe,EAAEpB,WAAW;MAC5BqB,aAAa,EAAErB,WAAW;MAC1BsB,YAAY,EAAEtB,WAAW;MACzBuB,gBAAgB,EAAEvB,WAAW;MAC7BwB,WAAW,EAAExB,WAAW;MACxByB,YAAY,EAAEzB,WAAW;MACzB0B,aAAa,EAAE1B,WAAW;MAC1B2B,SAAS,EAAE3B,WAAW;MACtB4B,mBAAmB,EAAE5B,WAAW;MAChC6B,WAAW,EAAE7B,WAAW;MACxB8B,eAAe,EAAE9B;IACnB,CAAC;EACH;AACF,CAAC,CAAC;AACF,IAAI+B,QAAQ,GAAGpC,OAAO,CAACJ,OAAO,GAAGU,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}