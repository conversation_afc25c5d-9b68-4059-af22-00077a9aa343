{"ast": null, "code": "import { useMemo } from 'react';\nimport isEqual from 'react-fast-compare';\nimport memoize from 'nano-memoize';\nexport function useCascaderValueExtend(options, fieldNames) {\n  const {\n    valueName,\n    childrenName\n  } = fieldNames;\n  const generateItems = useMemo(() => {\n    return memoize(val => {\n      const ret = [];\n      let currentOptions = options;\n      for (const v of val) {\n        const target = currentOptions.find(option => option[valueName] === v);\n        if (!target) {\n          break;\n        }\n        ret.push(target);\n        if (!target[childrenName]) break;\n        currentOptions = target[childrenName];\n      }\n      return ret;\n    }, {\n      equals: isEqual\n    });\n  }, [options]);\n  const generateIsLeaf = useMemo(() => {\n    return memoize(val => {\n      const children = val.reduce((currentOptions, v) => {\n        var _a;\n        return ((_a = currentOptions.find(option => option[valueName] === v)) === null || _a === void 0 ? void 0 : _a[childrenName]) || [];\n      }, options);\n      return children.length === 0;\n    }, {\n      equals: isEqual\n    });\n  }, [options]);\n  function generateValueExtend(val) {\n    return {\n      get items() {\n        return generateItems(val);\n      },\n      get isLeaf() {\n        return generateIsLeaf(val);\n      }\n    };\n  }\n  return generateValueExtend;\n}", "map": {"version": 3, "names": ["useMemo", "isEqual", "memoize", "useCascaderValueExtend", "options", "fieldNames", "valueName", "<PERSON><PERSON><PERSON>", "generateItems", "val", "ret", "currentOptions", "v", "target", "find", "option", "push", "equals", "generateIsLeaf", "children", "reduce", "_a", "length", "generateValueExtend", "items", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/cascader-view/use-cascader-value-extend.js"], "sourcesContent": ["import { useMemo } from 'react';\nimport isEqual from 'react-fast-compare';\nimport memoize from 'nano-memoize';\nexport function useCascaderValueExtend(options, fieldNames) {\n  const {\n    valueName,\n    childrenName\n  } = fieldNames;\n  const generateItems = useMemo(() => {\n    return memoize(val => {\n      const ret = [];\n      let currentOptions = options;\n      for (const v of val) {\n        const target = currentOptions.find(option => option[valueName] === v);\n        if (!target) {\n          break;\n        }\n        ret.push(target);\n        if (!target[childrenName]) break;\n        currentOptions = target[childrenName];\n      }\n      return ret;\n    }, {\n      equals: isEqual\n    });\n  }, [options]);\n  const generateIsLeaf = useMemo(() => {\n    return memoize(val => {\n      const children = val.reduce((currentOptions, v) => {\n        var _a;\n        return ((_a = currentOptions.find(option => option[valueName] === v)) === null || _a === void 0 ? void 0 : _a[childrenName]) || [];\n      }, options);\n      return children.length === 0;\n    }, {\n      equals: isEqual\n    });\n  }, [options]);\n  function generateValueExtend(val) {\n    return {\n      get items() {\n        return generateItems(val);\n      },\n      get isLeaf() {\n        return generateIsLeaf(val);\n      }\n    };\n  }\n  return generateValueExtend;\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAO,SAASC,sBAAsBA,CAACC,OAAO,EAAEC,UAAU,EAAE;EAC1D,MAAM;IACJC,SAAS;IACTC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,aAAa,GAAGR,OAAO,CAAC,MAAM;IAClC,OAAOE,OAAO,CAACO,GAAG,IAAI;MACpB,MAAMC,GAAG,GAAG,EAAE;MACd,IAAIC,cAAc,GAAGP,OAAO;MAC5B,KAAK,MAAMQ,CAAC,IAAIH,GAAG,EAAE;QACnB,MAAMI,MAAM,GAAGF,cAAc,CAACG,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACT,SAAS,CAAC,KAAKM,CAAC,CAAC;QACrE,IAAI,CAACC,MAAM,EAAE;UACX;QACF;QACAH,GAAG,CAACM,IAAI,CAACH,MAAM,CAAC;QAChB,IAAI,CAACA,MAAM,CAACN,YAAY,CAAC,EAAE;QAC3BI,cAAc,GAAGE,MAAM,CAACN,YAAY,CAAC;MACvC;MACA,OAAOG,GAAG;IACZ,CAAC,EAAE;MACDO,MAAM,EAAEhB;IACV,CAAC,CAAC;EACJ,CAAC,EAAE,CAACG,OAAO,CAAC,CAAC;EACb,MAAMc,cAAc,GAAGlB,OAAO,CAAC,MAAM;IACnC,OAAOE,OAAO,CAACO,GAAG,IAAI;MACpB,MAAMU,QAAQ,GAAGV,GAAG,CAACW,MAAM,CAAC,CAACT,cAAc,EAAEC,CAAC,KAAK;QACjD,IAAIS,EAAE;QACN,OAAO,CAAC,CAACA,EAAE,GAAGV,cAAc,CAACG,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACT,SAAS,CAAC,KAAKM,CAAC,CAAC,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACd,YAAY,CAAC,KAAK,EAAE;MACpI,CAAC,EAAEH,OAAO,CAAC;MACX,OAAOe,QAAQ,CAACG,MAAM,KAAK,CAAC;IAC9B,CAAC,EAAE;MACDL,MAAM,EAAEhB;IACV,CAAC,CAAC;EACJ,CAAC,EAAE,CAACG,OAAO,CAAC,CAAC;EACb,SAASmB,mBAAmBA,CAACd,GAAG,EAAE;IAChC,OAAO;MACL,IAAIe,KAAKA,CAAA,EAAG;QACV,OAAOhB,aAAa,CAACC,GAAG,CAAC;MAC3B,CAAC;MACD,IAAIgB,MAAMA,CAAA,EAAG;QACX,OAAOP,cAAc,CAACT,GAAG,CAAC;MAC5B;IACF,CAAC;EACH;EACA,OAAOc,mBAAmB;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}