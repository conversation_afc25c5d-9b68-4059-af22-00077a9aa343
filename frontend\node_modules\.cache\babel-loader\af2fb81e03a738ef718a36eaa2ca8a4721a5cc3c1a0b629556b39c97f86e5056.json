{"ast": null, "code": "import * as React from \"react\";\nfunction FrownOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FrownOutline-FrownOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FrownOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"FrownOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M24,26 C27.5130055,26 30.814464,26.9057398 33.6835926,28.4964361 C33.7148054,28.5137411 33.7527775,28.5350394 33.797509,28.5603311 C33.9230362,28.6312665 34.0006468,28.7642826 34.0006688,28.9084662 L34.0009735,31.4919246 L34.0009735,31.4919246 C34.000859,31.7127753 33.8218242,31.8918101 33.6009735,31.8918101 C33.5209035,31.8918101 33.4426773,31.8677729 33.3764232,31.8228106 C33.2335304,31.7258388 33.1330253,31.6589732 33.0749079,31.6222139 C30.4490543,29.9613603 27.336796,29 24,29 C20.6603127,29 17.5455525,29.963027 14.9182674,31.6265328 C14.8638743,31.6609727 14.7660014,31.7261796 14.6246487,31.8221536 C14.4418748,31.9461735 14.1931555,31.8986165 14.0690823,31.7158788 C14.0240816,31.6496009 14.0000226,31.5713358 14.0000226,31.4912244 L14.0000226,28.9078158 L14.0000226,28.9078158 C14.0001012,28.7634005 14.0779603,28.630203 14.2037966,28.559344 C14.2045801,28.5589029 14.2053572,28.5584655 14.2061278,28.5580318 C17.1008116,26.9291423 20.4418994,26 24,26 Z M16,18 C17.1045695,18 18,18.8954305 18,20 C18,21.1045695 17.1045695,22 16,22 C14.8954305,22 14,21.1045695 14,20 C14,18.8954305 14.8954305,18 16,18 Z M32,18 C33.1045695,18 34,18.8954305 34,20 C34,21.1045695 33.1045695,22 32,22 C30.8954305,22 30,21.1045695 30,20 C30,18.8954305 30.8954305,18 32,18 Z\",\n    id: \"FrownOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default FrownOutline;", "map": {"version": 3, "names": ["React", "FrownOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/FrownOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction FrownOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FrownOutline-FrownOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FrownOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"FrownOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M24,26 C27.5130055,26 30.814464,26.9057398 33.6835926,28.4964361 C33.7148054,28.5137411 33.7527775,28.5350394 33.797509,28.5603311 C33.9230362,28.6312665 34.0006468,28.7642826 34.0006688,28.9084662 L34.0009735,31.4919246 L34.0009735,31.4919246 C34.000859,31.7127753 33.8218242,31.8918101 33.6009735,31.8918101 C33.5209035,31.8918101 33.4426773,31.8677729 33.3764232,31.8228106 C33.2335304,31.7258388 33.1330253,31.6589732 33.0749079,31.6222139 C30.4490543,29.9613603 27.336796,29 24,29 C20.6603127,29 17.5455525,29.963027 14.9182674,31.6265328 C14.8638743,31.6609727 14.7660014,31.7261796 14.6246487,31.8221536 C14.4418748,31.9461735 14.1931555,31.8986165 14.0690823,31.7158788 C14.0240816,31.6496009 14.0000226,31.5713358 14.0000226,31.4912244 L14.0000226,28.9078158 L14.0000226,28.9078158 C14.0001012,28.7634005 14.0779603,28.630203 14.2037966,28.559344 C14.2045801,28.5589029 14.2053572,28.5584655 14.2061278,28.5580318 C17.1008116,26.9291423 20.4418994,26 24,26 Z M16,18 C17.1045695,18 18,18.8954305 18,20 C18,21.1045695 17.1045695,22 16,22 C14.8954305,22 14,21.1045695 14,20 C14,18.8954305 14.8954305,18 16,18 Z M32,18 C33.1045695,18 34,18.8954305 34,20 C34,21.1045695 33.1045695,22 32,22 C30.8954305,22 30,21.1045695 30,20 C30,18.8954305 30.8954305,18 32,18 Z\",\n    id: \"FrownOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default FrownOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,2BAA2B;IAC/BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,2BAA2B;IAC/BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,+gDAA+gD;IAClhDR,EAAE,EAAE,uCAAuC;IAC3CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}