{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Divider from '../divider';\nconst classPrefix = `adm-footer`;\nconst defaultProps = {\n  label: '',\n  links: [],\n  content: '',\n  chips: []\n};\nexport const Footer = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    label,\n    links,\n    content,\n    chips,\n    onChipClick,\n    onLinkClick\n  } = props;\n  const clickChipItem = (item, index) => {\n    if ((chips === null || chips === void 0 ? void 0 : chips.length) && item.type === 'link') {\n      onChipClick === null || onChipClick === void 0 ? void 0 : onChipClick(item, index);\n    }\n  };\n  const clickLinkItem = (item, index, e) => {\n    if (onLinkClick) {\n      e.preventDefault();\n      onLinkClick(item, index);\n    }\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix)\n  }, label && React.createElement(\"div\", {\n    className: `${classPrefix}-label`\n  }, React.createElement(Divider, null, label)), !!(links === null || links === void 0 ? void 0 : links.length) && React.createElement(\"div\", {\n    className: `${classPrefix}-links`\n  }, links.map((link, index) => React.createElement(React.Fragment, {\n    key: index\n  }, React.createElement(\"a\", {\n    href: link.href,\n    rel: 'noopener noreferrer',\n    onClick: event => clickLinkItem(link, index, event)\n  }, link.text), index !== links.length - 1 && React.createElement(Divider, {\n    direction: 'vertical'\n  })))), content && React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, content), chips && chips.length > 0 && React.createElement(\"div\", {\n    className: `${classPrefix}-chips`\n  }, chips.map((chip, index) => React.createElement(\"div\", {\n    key: index,\n    onClick: () => clickChipItem(chip, index),\n    className: classNames(`${classPrefix}-chip`, {\n      [`${classPrefix}-chip-link`]: chip.type === 'link'\n    })\n  }, chip.text)))));\n};", "map": {"version": 3, "names": ["React", "classNames", "withNativeProps", "mergeProps", "Divider", "classPrefix", "defaultProps", "label", "links", "content", "chips", "Footer", "p", "props", "onChipClick", "onLinkClick", "clickChipItem", "item", "index", "length", "type", "clickLinkItem", "e", "preventDefault", "createElement", "className", "map", "link", "Fragment", "key", "href", "rel", "onClick", "event", "text", "direction", "chip"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/footer/footer.js"], "sourcesContent": ["import React from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Divider from '../divider';\nconst classPrefix = `adm-footer`;\nconst defaultProps = {\n  label: '',\n  links: [],\n  content: '',\n  chips: []\n};\nexport const Footer = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    label,\n    links,\n    content,\n    chips,\n    onChipClick,\n    onLinkClick\n  } = props;\n  const clickChipItem = (item, index) => {\n    if ((chips === null || chips === void 0 ? void 0 : chips.length) && item.type === 'link') {\n      onChipClick === null || onChipClick === void 0 ? void 0 : onChipClick(item, index);\n    }\n  };\n  const clickLinkItem = (item, index, e) => {\n    if (onLinkClick) {\n      e.preventDefault();\n      onLinkClick(item, index);\n    }\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix)\n  }, label && React.createElement(\"div\", {\n    className: `${classPrefix}-label`\n  }, React.createElement(Divider, null, label)), !!(links === null || links === void 0 ? void 0 : links.length) && React.createElement(\"div\", {\n    className: `${classPrefix}-links`\n  }, links.map((link, index) => React.createElement(React.Fragment, {\n    key: index\n  }, React.createElement(\"a\", {\n    href: link.href,\n    rel: 'noopener noreferrer',\n    onClick: event => clickLinkItem(link, index, event)\n  }, link.text), index !== links.length - 1 && React.createElement(Divider, {\n    direction: 'vertical'\n  })))), content && React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, content), chips && chips.length > 0 && React.createElement(\"div\", {\n    className: `${classPrefix}-chips`\n  }, chips.map((chip, index) => React.createElement(\"div\", {\n    key: index,\n    onClick: () => clickChipItem(chip, index),\n    className: classNames(`${classPrefix}-chip`, {\n      [`${classPrefix}-chip-link`]: chip.type === 'link'\n    })\n  }, chip.text)))));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,OAAO,MAAM,YAAY;AAChC,MAAMC,WAAW,GAAG,YAAY;AAChC,MAAMC,YAAY,GAAG;EACnBC,KAAK,EAAE,EAAE;EACTC,KAAK,EAAE,EAAE;EACTC,OAAO,EAAE,EAAE;EACXC,KAAK,EAAE;AACT,CAAC;AACD,OAAO,MAAMC,MAAM,GAAGC,CAAC,IAAI;EACzB,MAAMC,KAAK,GAAGV,UAAU,CAACG,YAAY,EAAEM,CAAC,CAAC;EACzC,MAAM;IACJL,KAAK;IACLC,KAAK;IACLC,OAAO;IACPC,KAAK;IACLI,WAAW;IACXC;EACF,CAAC,GAAGF,KAAK;EACT,MAAMG,aAAa,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;IACrC,IAAI,CAACR,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACS,MAAM,KAAKF,IAAI,CAACG,IAAI,KAAK,MAAM,EAAE;MACxFN,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACG,IAAI,EAAEC,KAAK,CAAC;IACpF;EACF,CAAC;EACD,MAAMG,aAAa,GAAGA,CAACJ,IAAI,EAAEC,KAAK,EAAEI,CAAC,KAAK;IACxC,IAAIP,WAAW,EAAE;MACfO,CAAC,CAACC,cAAc,CAAC,CAAC;MAClBR,WAAW,CAACE,IAAI,EAAEC,KAAK,CAAC;IAC1B;EACF,CAAC;EACD,OAAOhB,eAAe,CAACW,KAAK,EAAEb,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAExB,UAAU,CAACI,WAAW;EACnC,CAAC,EAAEE,KAAK,IAAIP,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;IACrCC,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,EAAEL,KAAK,CAACwB,aAAa,CAACpB,OAAO,EAAE,IAAI,EAAEG,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACW,MAAM,CAAC,IAAInB,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;IAC1IC,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,EAAEG,KAAK,CAACkB,GAAG,CAAC,CAACC,IAAI,EAAET,KAAK,KAAKlB,KAAK,CAACwB,aAAa,CAACxB,KAAK,CAAC4B,QAAQ,EAAE;IAChEC,GAAG,EAAEX;EACP,CAAC,EAAElB,KAAK,CAACwB,aAAa,CAAC,GAAG,EAAE;IAC1BM,IAAI,EAAEH,IAAI,CAACG,IAAI;IACfC,GAAG,EAAE,qBAAqB;IAC1BC,OAAO,EAAEC,KAAK,IAAIZ,aAAa,CAACM,IAAI,EAAET,KAAK,EAAEe,KAAK;EACpD,CAAC,EAAEN,IAAI,CAACO,IAAI,CAAC,EAAEhB,KAAK,KAAKV,KAAK,CAACW,MAAM,GAAG,CAAC,IAAInB,KAAK,CAACwB,aAAa,CAACpB,OAAO,EAAE;IACxE+B,SAAS,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE1B,OAAO,IAAIT,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;IAC3CC,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,EAAEI,OAAO,CAAC,EAAEC,KAAK,IAAIA,KAAK,CAACS,MAAM,GAAG,CAAC,IAAInB,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;IACnEC,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,EAAEK,KAAK,CAACgB,GAAG,CAAC,CAACU,IAAI,EAAElB,KAAK,KAAKlB,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;IACvDK,GAAG,EAAEX,KAAK;IACVc,OAAO,EAAEA,CAAA,KAAMhB,aAAa,CAACoB,IAAI,EAAElB,KAAK,CAAC;IACzCO,SAAS,EAAExB,UAAU,CAAC,GAAGI,WAAW,OAAO,EAAE;MAC3C,CAAC,GAAGA,WAAW,YAAY,GAAG+B,IAAI,CAAChB,IAAI,KAAK;IAC9C,CAAC;EACH,CAAC,EAAEgB,IAAI,CAACF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}