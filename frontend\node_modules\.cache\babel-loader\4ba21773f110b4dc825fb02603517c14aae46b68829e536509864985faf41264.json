{"ast": null, "code": "import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport Popup from '../popup';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport CascaderView from '../cascader-view';\nimport { useConfig } from '../config-provider';\nimport { useCascaderValueExtend } from '../cascader-view/use-cascader-value-extend';\nimport { useFieldNames } from '../../hooks';\nconst classPrefix = `adm-cascader`;\nconst defaultProps = {\n  defaultValue: [],\n  destroyOnClose: true,\n  forceRender: false\n};\nexport const Cascader = forwardRef((p, ref) => {\n  var _a;\n  const {\n    locale\n  } = useConfig();\n  const props = mergeProps(defaultProps, {\n    confirmText: locale.common.confirm,\n    cancelText: locale.common.cancel,\n    placeholder: locale.Cascader.placeholder\n  }, p);\n  const [visible, setVisible] = usePropsValue({\n    value: props.visible,\n    defaultValue: false,\n    onChange: v => {\n      var _a;\n      if (v === false) {\n        (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n      }\n    }\n  });\n  const actions = {\n    toggle: () => {\n      setVisible(v => !v);\n    },\n    open: () => {\n      setVisible(true);\n    },\n    close: () => {\n      setVisible(false);\n    }\n  };\n  useImperativeHandle(ref, () => actions);\n  const [value, setValue] = usePropsValue(Object.assign(Object.assign({}, props), {\n    onChange: val => {\n      var _a;\n      (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(props, val, generateValueExtend(val));\n    }\n  }));\n  const [, valueName, childrenName] = useFieldNames(props.fieldNames);\n  const generateValueExtend = useCascaderValueExtend(props.options, {\n    valueName,\n    childrenName\n  });\n  const [innerValue, setInnerValue] = useState(value);\n  useEffect(() => {\n    if (!visible) {\n      setInnerValue(value);\n    }\n  }, [visible, value]);\n  const cascaderElement = withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-header`\n  }, React.createElement(\"a\", {\n    className: `${classPrefix}-header-button`,\n    onClick: () => {\n      var _a;\n      (_a = props.onCancel) === null || _a === void 0 ? void 0 : _a.call(props);\n      setVisible(false);\n    }\n  }, props.cancelText), React.createElement(\"div\", {\n    className: `${classPrefix}-header-title`\n  }, props.title), React.createElement(\"a\", {\n    className: `${classPrefix}-header-button`,\n    onClick: () => {\n      setValue(innerValue, true);\n      setVisible(false);\n    }\n  }, props.confirmText)), React.createElement(\"div\", {\n    className: `${classPrefix}-body`\n  }, React.createElement(CascaderView, Object.assign({}, props, {\n    value: innerValue,\n    onChange: (val, ext) => {\n      var _a;\n      setInnerValue(val);\n      if (visible) {\n        (_a = props.onSelect) === null || _a === void 0 ? void 0 : _a.call(props, val, ext);\n      }\n    }\n  })))));\n  const popupElement = React.createElement(Popup, {\n    visible: visible,\n    position: 'bottom',\n    onMaskClick: () => {\n      var _a;\n      (_a = props.onCancel) === null || _a === void 0 ? void 0 : _a.call(props);\n      setVisible(false);\n    },\n    getContainer: props.getContainer,\n    destroyOnClose: props.destroyOnClose,\n    forceRender: props.forceRender,\n    afterShow: props.afterShow,\n    afterClose: props.afterClose,\n    onClick: props.onClick,\n    stopPropagation: props.stopPropagation\n  }, cascaderElement);\n  return React.createElement(React.Fragment, null, popupElement, (_a = props.children) === null || _a === void 0 ? void 0 : _a.call(props, generateValueExtend(value).items, actions));\n});", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "Popup", "mergeProps", "withNativeProps", "usePropsValue", "Cascader<PERSON>iew", "useConfig", "useCascaderValueExtend", "useFieldNames", "classPrefix", "defaultProps", "defaultValue", "destroyOnClose", "forceRender", "<PERSON>r", "p", "ref", "_a", "locale", "props", "confirmText", "common", "confirm", "cancelText", "cancel", "placeholder", "visible", "setVisible", "value", "onChange", "v", "onClose", "call", "actions", "toggle", "open", "close", "setValue", "Object", "assign", "val", "onConfirm", "generateValueExtend", "valueName", "<PERSON><PERSON><PERSON>", "fieldNames", "options", "innerValue", "setInnerValue", "cascaderElement", "createElement", "className", "onClick", "onCancel", "title", "ext", "onSelect", "popupElement", "position", "onMaskClick", "getContainer", "afterShow", "afterClose", "stopPropagation", "Fragment", "children", "items"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/cascader/cascader.js"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport Popup from '../popup';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport CascaderView from '../cascader-view';\nimport { useConfig } from '../config-provider';\nimport { useCascaderValueExtend } from '../cascader-view/use-cascader-value-extend';\nimport { useFieldNames } from '../../hooks';\nconst classPrefix = `adm-cascader`;\nconst defaultProps = {\n  defaultValue: [],\n  destroyOnClose: true,\n  forceRender: false\n};\nexport const Cascader = forwardRef((p, ref) => {\n  var _a;\n  const {\n    locale\n  } = useConfig();\n  const props = mergeProps(defaultProps, {\n    confirmText: locale.common.confirm,\n    cancelText: locale.common.cancel,\n    placeholder: locale.Cascader.placeholder\n  }, p);\n  const [visible, setVisible] = usePropsValue({\n    value: props.visible,\n    defaultValue: false,\n    onChange: v => {\n      var _a;\n      if (v === false) {\n        (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n      }\n    }\n  });\n  const actions = {\n    toggle: () => {\n      setVisible(v => !v);\n    },\n    open: () => {\n      setVisible(true);\n    },\n    close: () => {\n      setVisible(false);\n    }\n  };\n  useImperativeHandle(ref, () => actions);\n  const [value, setValue] = usePropsValue(Object.assign(Object.assign({}, props), {\n    onChange: val => {\n      var _a;\n      (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(props, val, generateValueExtend(val));\n    }\n  }));\n  const [, valueName, childrenName] = useFieldNames(props.fieldNames);\n  const generateValueExtend = useCascaderValueExtend(props.options, {\n    valueName,\n    childrenName\n  });\n  const [innerValue, setInnerValue] = useState(value);\n  useEffect(() => {\n    if (!visible) {\n      setInnerValue(value);\n    }\n  }, [visible, value]);\n  const cascaderElement = withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-header`\n  }, React.createElement(\"a\", {\n    className: `${classPrefix}-header-button`,\n    onClick: () => {\n      var _a;\n      (_a = props.onCancel) === null || _a === void 0 ? void 0 : _a.call(props);\n      setVisible(false);\n    }\n  }, props.cancelText), React.createElement(\"div\", {\n    className: `${classPrefix}-header-title`\n  }, props.title), React.createElement(\"a\", {\n    className: `${classPrefix}-header-button`,\n    onClick: () => {\n      setValue(innerValue, true);\n      setVisible(false);\n    }\n  }, props.confirmText)), React.createElement(\"div\", {\n    className: `${classPrefix}-body`\n  }, React.createElement(CascaderView, Object.assign({}, props, {\n    value: innerValue,\n    onChange: (val, ext) => {\n      var _a;\n      setInnerValue(val);\n      if (visible) {\n        (_a = props.onSelect) === null || _a === void 0 ? void 0 : _a.call(props, val, ext);\n      }\n    }\n  })))));\n  const popupElement = React.createElement(Popup, {\n    visible: visible,\n    position: 'bottom',\n    onMaskClick: () => {\n      var _a;\n      (_a = props.onCancel) === null || _a === void 0 ? void 0 : _a.call(props);\n      setVisible(false);\n    },\n    getContainer: props.getContainer,\n    destroyOnClose: props.destroyOnClose,\n    forceRender: props.forceRender,\n    afterShow: props.afterShow,\n    afterClose: props.afterClose,\n    onClick: props.onClick,\n    stopPropagation: props.stopPropagation\n  }, cascaderElement);\n  return React.createElement(React.Fragment, null, popupElement, (_a = props.children) === null || _a === void 0 ? void 0 : _a.call(props, generateValueExtend(value).items, actions));\n});"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACnF,OAAOC,KAAK,MAAM,UAAU;AAC5B,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,OAAOC,YAAY,MAAM,kBAAkB;AAC3C,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,sBAAsB,QAAQ,4CAA4C;AACnF,SAASC,aAAa,QAAQ,aAAa;AAC3C,MAAMC,WAAW,GAAG,cAAc;AAClC,MAAMC,YAAY,GAAG;EACnBC,YAAY,EAAE,EAAE;EAChBC,cAAc,EAAE,IAAI;EACpBC,WAAW,EAAE;AACf,CAAC;AACD,OAAO,MAAMC,QAAQ,GAAGf,UAAU,CAAC,CAACgB,CAAC,EAAEC,GAAG,KAAK;EAC7C,IAAIC,EAAE;EACN,MAAM;IACJC;EACF,CAAC,GAAGZ,SAAS,CAAC,CAAC;EACf,MAAMa,KAAK,GAAGjB,UAAU,CAACQ,YAAY,EAAE;IACrCU,WAAW,EAAEF,MAAM,CAACG,MAAM,CAACC,OAAO;IAClCC,UAAU,EAAEL,MAAM,CAACG,MAAM,CAACG,MAAM;IAChCC,WAAW,EAAEP,MAAM,CAACJ,QAAQ,CAACW;EAC/B,CAAC,EAAEV,CAAC,CAAC;EACL,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGvB,aAAa,CAAC;IAC1CwB,KAAK,EAAET,KAAK,CAACO,OAAO;IACpBf,YAAY,EAAE,KAAK;IACnBkB,QAAQ,EAAEC,CAAC,IAAI;MACb,IAAIb,EAAE;MACN,IAAIa,CAAC,KAAK,KAAK,EAAE;QACf,CAACb,EAAE,GAAGE,KAAK,CAACY,OAAO,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,IAAI,CAACb,KAAK,CAAC;MAC1E;IACF;EACF,CAAC,CAAC;EACF,MAAMc,OAAO,GAAG;IACdC,MAAM,EAAEA,CAAA,KAAM;MACZP,UAAU,CAACG,CAAC,IAAI,CAACA,CAAC,CAAC;IACrB,CAAC;IACDK,IAAI,EAAEA,CAAA,KAAM;MACVR,UAAU,CAAC,IAAI,CAAC;IAClB,CAAC;IACDS,KAAK,EAAEA,CAAA,KAAM;MACXT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EACD3B,mBAAmB,CAACgB,GAAG,EAAE,MAAMiB,OAAO,CAAC;EACvC,MAAM,CAACL,KAAK,EAAES,QAAQ,CAAC,GAAGjC,aAAa,CAACkC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpB,KAAK,CAAC,EAAE;IAC9EU,QAAQ,EAAEW,GAAG,IAAI;MACf,IAAIvB,EAAE;MACN,CAACA,EAAE,GAAGE,KAAK,CAACsB,SAAS,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,IAAI,CAACb,KAAK,EAAEqB,GAAG,EAAEE,mBAAmB,CAACF,GAAG,CAAC,CAAC;IAC3G;EACF,CAAC,CAAC,CAAC;EACH,MAAM,GAAGG,SAAS,EAAEC,YAAY,CAAC,GAAGpC,aAAa,CAACW,KAAK,CAAC0B,UAAU,CAAC;EACnE,MAAMH,mBAAmB,GAAGnC,sBAAsB,CAACY,KAAK,CAAC2B,OAAO,EAAE;IAChEH,SAAS;IACTC;EACF,CAAC,CAAC;EACF,MAAM,CAACG,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC+B,KAAK,CAAC;EACnD9B,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4B,OAAO,EAAE;MACZsB,aAAa,CAACpB,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACF,OAAO,EAAEE,KAAK,CAAC,CAAC;EACpB,MAAMqB,eAAe,GAAG9C,eAAe,CAACgB,KAAK,EAAEvB,KAAK,CAACsD,aAAa,CAAC,KAAK,EAAE;IACxEC,SAAS,EAAE1C;EACb,CAAC,EAAEb,KAAK,CAACsD,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAG1C,WAAW;EAC3B,CAAC,EAAEb,KAAK,CAACsD,aAAa,CAAC,GAAG,EAAE;IAC1BC,SAAS,EAAE,GAAG1C,WAAW,gBAAgB;IACzC2C,OAAO,EAAEA,CAAA,KAAM;MACb,IAAInC,EAAE;MACN,CAACA,EAAE,GAAGE,KAAK,CAACkC,QAAQ,MAAM,IAAI,IAAIpC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,IAAI,CAACb,KAAK,CAAC;MACzEQ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAER,KAAK,CAACI,UAAU,CAAC,EAAE3B,KAAK,CAACsD,aAAa,CAAC,KAAK,EAAE;IAC/CC,SAAS,EAAE,GAAG1C,WAAW;EAC3B,CAAC,EAAEU,KAAK,CAACmC,KAAK,CAAC,EAAE1D,KAAK,CAACsD,aAAa,CAAC,GAAG,EAAE;IACxCC,SAAS,EAAE,GAAG1C,WAAW,gBAAgB;IACzC2C,OAAO,EAAEA,CAAA,KAAM;MACbf,QAAQ,CAACU,UAAU,EAAE,IAAI,CAAC;MAC1BpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAER,KAAK,CAACC,WAAW,CAAC,CAAC,EAAExB,KAAK,CAACsD,aAAa,CAAC,KAAK,EAAE;IACjDC,SAAS,EAAE,GAAG1C,WAAW;EAC3B,CAAC,EAAEb,KAAK,CAACsD,aAAa,CAAC7C,YAAY,EAAEiC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpB,KAAK,EAAE;IAC5DS,KAAK,EAAEmB,UAAU;IACjBlB,QAAQ,EAAEA,CAACW,GAAG,EAAEe,GAAG,KAAK;MACtB,IAAItC,EAAE;MACN+B,aAAa,CAACR,GAAG,CAAC;MAClB,IAAId,OAAO,EAAE;QACX,CAACT,EAAE,GAAGE,KAAK,CAACqC,QAAQ,MAAM,IAAI,IAAIvC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,IAAI,CAACb,KAAK,EAAEqB,GAAG,EAAEe,GAAG,CAAC;MACrF;IACF;EACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACN,MAAME,YAAY,GAAG7D,KAAK,CAACsD,aAAa,CAACjD,KAAK,EAAE;IAC9CyB,OAAO,EAAEA,OAAO;IAChBgC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAEA,CAAA,KAAM;MACjB,IAAI1C,EAAE;MACN,CAACA,EAAE,GAAGE,KAAK,CAACkC,QAAQ,MAAM,IAAI,IAAIpC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,IAAI,CAACb,KAAK,CAAC;MACzEQ,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IACDiC,YAAY,EAAEzC,KAAK,CAACyC,YAAY;IAChChD,cAAc,EAAEO,KAAK,CAACP,cAAc;IACpCC,WAAW,EAAEM,KAAK,CAACN,WAAW;IAC9BgD,SAAS,EAAE1C,KAAK,CAAC0C,SAAS;IAC1BC,UAAU,EAAE3C,KAAK,CAAC2C,UAAU;IAC5BV,OAAO,EAAEjC,KAAK,CAACiC,OAAO;IACtBW,eAAe,EAAE5C,KAAK,CAAC4C;EACzB,CAAC,EAAEd,eAAe,CAAC;EACnB,OAAOrD,KAAK,CAACsD,aAAa,CAACtD,KAAK,CAACoE,QAAQ,EAAE,IAAI,EAAEP,YAAY,EAAE,CAACxC,EAAE,GAAGE,KAAK,CAAC8C,QAAQ,MAAM,IAAI,IAAIhD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,IAAI,CAACb,KAAK,EAAEuB,mBAAmB,CAACd,KAAK,CAAC,CAACsC,KAAK,EAAEjC,OAAO,CAAC,CAAC;AACtL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}