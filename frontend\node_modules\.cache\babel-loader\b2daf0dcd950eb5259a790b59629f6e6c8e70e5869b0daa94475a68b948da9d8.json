{"ast": null, "code": "import * as React from \"react\";\nfunction BellMuteOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"BellMuteOutline-BellMuteOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"BellMuteOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"BellMuteOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M5.34549139,8 L8.621,8 L8.621,8 L40.9381573,40.3171573 C41.094367,40.473367 41.094367,40.726633 40.9381573,40.8828427 C40.8631427,40.9578573 40.7614012,41 40.6553146,41 L37.379,41 L37.379,41 L34.3776703,37.9996703 L30.9290789,38.0002251 C30.4437524,41.3924095 27.5263939,44 24,44 C20.4736061,44 17.5562476,41.3924095 17.0709211,38.0002251 L7,38 L7.00004975,37.9645627 C5.30387842,37.7219693 4,36.2632532 4,34.5 L4,31.8284271 C4,31.2979941 4.21071368,30.7892863 4.58578644,30.4142136 L7,28 L7,21 C7,18.0072179 7.77335171,15.1950618 9.13111818,12.7524687 L5.06260849,8.68280252 C4.90642097,8.5265706 4.90645696,8.27330462 5.06268888,8.1171171 C5.13770016,8.04212713 5.23942449,8 5.34549139,8 Z M27.8739632,38.000075 L20.1260368,38.000075 C20.5701057,39.7252642 22.1361876,41 24,41 C25.8638124,41 27.4298943,39.7252642 27.8739632,38.000075 Z M24,4 C33.3888407,4 41,11.6111593 41,21 L41,28 L43.4142136,30.4142136 C43.7892863,30.7892863 44,31.2979941 44,31.8284271 L44,34.5 C44,35.5360733 43.5498174,36.4670008 42.8343896,37.1078451 C42.7813026,37.1553978 42.7112692,37.211576 42.6242894,37.2763796 C42.4650544,37.395053 42.2428679,37.3789066 42.1024426,37.238479 C42.0574654,37.1935011 42.0194909,37.155526 41.9885191,37.1245536 C41.2363523,36.3723746 40.6590291,35.795042 40.2565496,35.3925559 C40.2565496,35.3925559 40.2565496,35.3925559 40.2565496,35.3925559 C40.1667346,35.3027395 40.1667357,35.1571194 40.2565522,35.0673044 C40.2997109,35.0241464 40.3582548,34.9999139 40.4192899,34.999944 L40.533,35 L40.533,35 L40.5753422,34.9947633 C40.7851759,34.9646902 40.9529292,34.7989963 40.9915889,34.5913076 L41,34.5 L41,32.243 L38,29.2426407 L38,21 C38,13.2680135 31.7319865,7 24,7 C20.8684082,7 17.9769656,8.02820105 15.6448473,9.76542817 C15.5690017,9.82192654 15.4721883,9.8967177 15.3544071,9.98980166 C15.1952321,10.1155955 14.9670906,10.1023035 14.8236033,9.95887349 C14.7885112,9.92379537 14.7583128,9.89360906 14.7330082,9.86831456 C14.1100249,9.24557996 13.6301441,8.76589073 13.2933658,8.42924689 C13.2821722,8.41805769 13.2701642,8.40605455 13.257342,8.39323747 C13.1009825,8.23709319 13.1009319,7.98375876 13.2571527,7.82747563 C13.2664937,7.81813084 13.276292,7.80925449 13.2865113,7.80087921 C13.3468698,7.75141247 13.3990102,7.70925714 13.4429325,7.67441321 C16.3427826,5.37394017 20.0109408,4 24,4 Z M11.3573032,14.9789091 C10.5293096,16.7143656 10.0481265,18.6470703 10.003422,20.687355 L10,21 L10,29.2426407 L7,32.242 L7,34.5 C7,34.7178229 7.14158244,34.9070074 7.33767609,34.9738837 L7.4247998,34.9947837 L7.466,35 L31.3776703,34.9996703 L11.3573032,14.9789091 Z\",\n    id: \"BellMuteOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default BellMuteOutline;", "map": {"version": 3, "names": ["React", "BellMuteOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/BellMuteOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction BellMuteOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"BellMuteOutline-BellMuteOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"BellMuteOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"BellMuteOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M5.34549139,8 L8.621,8 L8.621,8 L40.9381573,40.3171573 C41.094367,40.473367 41.094367,40.726633 40.9381573,40.8828427 C40.8631427,40.9578573 40.7614012,41 40.6553146,41 L37.379,41 L37.379,41 L34.3776703,37.9996703 L30.9290789,38.0002251 C30.4437524,41.3924095 27.5263939,44 24,44 C20.4736061,44 17.5562476,41.3924095 17.0709211,38.0002251 L7,38 L7.00004975,37.9645627 C5.30387842,37.7219693 4,36.2632532 4,34.5 L4,31.8284271 C4,31.2979941 4.21071368,30.7892863 4.58578644,30.4142136 L7,28 L7,21 C7,18.0072179 7.77335171,15.1950618 9.13111818,12.7524687 L5.06260849,8.68280252 C4.90642097,8.5265706 4.90645696,8.27330462 5.06268888,8.1171171 C5.13770016,8.04212713 5.23942449,8 5.34549139,8 Z M27.8739632,38.000075 L20.1260368,38.000075 C20.5701057,39.7252642 22.1361876,41 24,41 C25.8638124,41 27.4298943,39.7252642 27.8739632,38.000075 Z M24,4 C33.3888407,4 41,11.6111593 41,21 L41,28 L43.4142136,30.4142136 C43.7892863,30.7892863 44,31.2979941 44,31.8284271 L44,34.5 C44,35.5360733 43.5498174,36.4670008 42.8343896,37.1078451 C42.7813026,37.1553978 42.7112692,37.211576 42.6242894,37.2763796 C42.4650544,37.395053 42.2428679,37.3789066 42.1024426,37.238479 C42.0574654,37.1935011 42.0194909,37.155526 41.9885191,37.1245536 C41.2363523,36.3723746 40.6590291,35.795042 40.2565496,35.3925559 C40.2565496,35.3925559 40.2565496,35.3925559 40.2565496,35.3925559 C40.1667346,35.3027395 40.1667357,35.1571194 40.2565522,35.0673044 C40.2997109,35.0241464 40.3582548,34.9999139 40.4192899,34.999944 L40.533,35 L40.533,35 L40.5753422,34.9947633 C40.7851759,34.9646902 40.9529292,34.7989963 40.9915889,34.5913076 L41,34.5 L41,32.243 L38,29.2426407 L38,21 C38,13.2680135 31.7319865,7 24,7 C20.8684082,7 17.9769656,8.02820105 15.6448473,9.76542817 C15.5690017,9.82192654 15.4721883,9.8967177 15.3544071,9.98980166 C15.1952321,10.1155955 14.9670906,10.1023035 14.8236033,9.95887349 C14.7885112,9.92379537 14.7583128,9.89360906 14.7330082,9.86831456 C14.1100249,9.24557996 13.6301441,8.76589073 13.2933658,8.42924689 C13.2821722,8.41805769 13.2701642,8.40605455 13.257342,8.39323747 C13.1009825,8.23709319 13.1009319,7.98375876 13.2571527,7.82747563 C13.2664937,7.81813084 13.276292,7.80925449 13.2865113,7.80087921 C13.3468698,7.75141247 13.3990102,7.70925714 13.4429325,7.67441321 C16.3427826,5.37394017 20.0109408,4 24,4 Z M11.3573032,14.9789091 C10.5293096,16.7143656 10.0481265,18.6470703 10.003422,20.687355 L10,21 L10,29.2426407 L7,32.242 L7,34.5 C7,34.7178229 7.14158244,34.9070074 7.33767609,34.9738837 L7.4247998,34.9947837 L7.466,35 L31.3776703,34.9996703 L11.3573032,14.9789091 Z\",\n    id: \"BellMuteOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default BellMuteOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,iCAAiC;IACrCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,mhFAAmhF;IACthFR,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}