{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = `adm-space`;\nconst defaultProps = {\n  direction: 'horizontal'\n};\nexport const Space = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    direction,\n    onClick\n  } = props;\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-wrap`]: props.wrap,\n      [`${classPrefix}-block`]: props.block,\n      [`${classPrefix}-${direction}`]: true,\n      [`${classPrefix}-align-${props.align}`]: !!props.align,\n      [`${classPrefix}-justify-${props.justify}`]: !!props.justify\n    }),\n    onClick: onClick\n  }, React.Children.map(props.children, child => {\n    return child !== null && child !== undefined && React.createElement(\"div\", {\n      className: `${classPrefix}-item`\n    }, child);\n  })));\n};", "map": {"version": 3, "names": ["React", "classNames", "withNativeProps", "mergeProps", "classPrefix", "defaultProps", "direction", "Space", "p", "props", "onClick", "createElement", "className", "wrap", "block", "align", "justify", "Children", "map", "children", "child", "undefined"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/space/space.js"], "sourcesContent": ["import React from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = `adm-space`;\nconst defaultProps = {\n  direction: 'horizontal'\n};\nexport const Space = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    direction,\n    onClick\n  } = props;\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-wrap`]: props.wrap,\n      [`${classPrefix}-block`]: props.block,\n      [`${classPrefix}-${direction}`]: true,\n      [`${classPrefix}-align-${props.align}`]: !!props.align,\n      [`${classPrefix}-justify-${props.justify}`]: !!props.justify\n    }),\n    onClick: onClick\n  }, React.Children.map(props.children, child => {\n    return child !== null && child !== undefined && React.createElement(\"div\", {\n      className: `${classPrefix}-item`\n    }, child);\n  })));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,MAAMC,WAAW,GAAG,WAAW;AAC/B,MAAMC,YAAY,GAAG;EACnBC,SAAS,EAAE;AACb,CAAC;AACD,OAAO,MAAMC,KAAK,GAAGC,CAAC,IAAI;EACxB,MAAMC,KAAK,GAAGN,UAAU,CAACE,YAAY,EAAEG,CAAC,CAAC;EACzC,MAAM;IACJF,SAAS;IACTI;EACF,CAAC,GAAGD,KAAK;EACT,OAAOP,eAAe,CAACO,KAAK,EAAET,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEX,UAAU,CAACG,WAAW,EAAE;MACjC,CAAC,GAAGA,WAAW,OAAO,GAAGK,KAAK,CAACI,IAAI;MACnC,CAAC,GAAGT,WAAW,QAAQ,GAAGK,KAAK,CAACK,KAAK;MACrC,CAAC,GAAGV,WAAW,IAAIE,SAAS,EAAE,GAAG,IAAI;MACrC,CAAC,GAAGF,WAAW,UAAUK,KAAK,CAACM,KAAK,EAAE,GAAG,CAAC,CAACN,KAAK,CAACM,KAAK;MACtD,CAAC,GAAGX,WAAW,YAAYK,KAAK,CAACO,OAAO,EAAE,GAAG,CAAC,CAACP,KAAK,CAACO;IACvD,CAAC,CAAC;IACFN,OAAO,EAAEA;EACX,CAAC,EAAEV,KAAK,CAACiB,QAAQ,CAACC,GAAG,CAACT,KAAK,CAACU,QAAQ,EAAEC,KAAK,IAAI;IAC7C,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAIrB,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;MACzEC,SAAS,EAAE,GAAGR,WAAW;IAC3B,CAAC,EAAEgB,KAAK,CAAC;EACX,CAAC,CAAC,CAAC,CAAC;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}