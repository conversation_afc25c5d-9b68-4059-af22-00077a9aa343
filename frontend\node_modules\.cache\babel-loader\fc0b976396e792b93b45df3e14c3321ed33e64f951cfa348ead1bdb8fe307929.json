{"ast": null, "code": "import { withNativeProps } from '../../utils/native-props';\nimport React, { useMemo, useRef, useState } from 'react';\nimport { useUnmountedRef } from 'ahooks';\nimport { useLockScroll } from '../../utils/use-lock-scroll';\nimport { useSpring, animated } from '@react-spring/web';\nimport { renderToContainer } from '../../utils/render-to-container';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport { ShouldRender } from '../../utils/should-render';\nimport { withStopPropagation } from '../../utils/with-stop-propagation';\nconst classPrefix = `adm-mask`;\nconst opacityRecord = {\n  default: 0.55,\n  thin: 0.35,\n  thick: 0.75\n};\nconst colorRecord = {\n  black: '0, 0, 0',\n  white: '255, 255, 255'\n};\nconst defaultProps = {\n  visible: true,\n  destroyOnClose: false,\n  forceRender: false,\n  color: 'black',\n  opacity: 'default',\n  disableBodyScroll: true,\n  getContainer: null,\n  stopPropagation: ['click']\n};\nexport const Mask = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    locale\n  } = useConfig();\n  const ref = useRef(null);\n  useLockScroll(ref, props.visible && props.disableBodyScroll);\n  const background = useMemo(() => {\n    var _a;\n    const opacity = (_a = opacityRecord[props.opacity]) !== null && _a !== void 0 ? _a : props.opacity;\n    const rgb = colorRecord[props.color];\n    return rgb ? `rgba(${rgb}, ${opacity})` : props.color;\n  }, [props.color, props.opacity]);\n  const [active, setActive] = useState(props.visible);\n  const unmountedRef = useUnmountedRef();\n  const {\n    opacity\n  } = useSpring({\n    opacity: props.visible ? 1 : 0,\n    config: {\n      precision: 0.01,\n      mass: 1,\n      tension: 250,\n      friction: 30,\n      clamp: true\n    },\n    onStart: () => {\n      setActive(true);\n    },\n    onRest: () => {\n      var _a, _b;\n      if (unmountedRef.current) return;\n      setActive(props.visible);\n      if (props.visible) {\n        (_a = props.afterShow) === null || _a === void 0 ? void 0 : _a.call(props);\n      } else {\n        (_b = props.afterClose) === null || _b === void 0 ? void 0 : _b.call(props);\n      }\n    }\n  });\n  const node = withStopPropagation(props.stopPropagation, withNativeProps(props, React.createElement(animated.div, {\n    className: classPrefix,\n    ref: ref,\n    \"aria-hidden\": true,\n    style: Object.assign(Object.assign({}, props.style), {\n      background,\n      opacity,\n      display: active ? undefined : 'none'\n    }),\n    onClick: e => {\n      var _a;\n      if (e.target === e.currentTarget) {\n        (_a = props.onMaskClick) === null || _a === void 0 ? void 0 : _a.call(props, e);\n      }\n    }\n  }, props.onMaskClick && React.createElement(\"div\", {\n    className: `${classPrefix}-aria-button`,\n    role: 'button',\n    \"aria-label\": locale.Mask.name,\n    onClick: props.onMaskClick\n  }), React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, props.children))));\n  return React.createElement(ShouldRender, {\n    active: active,\n    forceRender: props.forceRender,\n    destroyOnClose: props.destroyOnClose\n  }, renderToContainer(props.getContainer, node));\n};", "map": {"version": 3, "names": ["withNativeProps", "React", "useMemo", "useRef", "useState", "useUnmountedRef", "useLockScroll", "useSpring", "animated", "renderToContainer", "mergeProps", "useConfig", "ShouldRender", "withStopPropagation", "classPrefix", "opacityRecord", "default", "thin", "thick", "colorRecord", "black", "white", "defaultProps", "visible", "destroyOnClose", "forceRender", "color", "opacity", "disableBodyScroll", "getContainer", "stopPropagation", "Mask", "p", "props", "locale", "ref", "background", "_a", "rgb", "active", "setActive", "unmountedRef", "config", "precision", "mass", "tension", "friction", "clamp", "onStart", "onRest", "_b", "current", "afterShow", "call", "afterClose", "node", "createElement", "div", "className", "style", "Object", "assign", "display", "undefined", "onClick", "e", "target", "currentTarget", "onMaskClick", "role", "name", "children"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/mask/mask.js"], "sourcesContent": ["import { withNativeProps } from '../../utils/native-props';\nimport React, { useMemo, useRef, useState } from 'react';\nimport { useUnmountedRef } from 'ahooks';\nimport { useLockScroll } from '../../utils/use-lock-scroll';\nimport { useSpring, animated } from '@react-spring/web';\nimport { renderToContainer } from '../../utils/render-to-container';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport { ShouldRender } from '../../utils/should-render';\nimport { withStopPropagation } from '../../utils/with-stop-propagation';\nconst classPrefix = `adm-mask`;\nconst opacityRecord = {\n  default: 0.55,\n  thin: 0.35,\n  thick: 0.75\n};\nconst colorRecord = {\n  black: '0, 0, 0',\n  white: '255, 255, 255'\n};\nconst defaultProps = {\n  visible: true,\n  destroyOnClose: false,\n  forceRender: false,\n  color: 'black',\n  opacity: 'default',\n  disableBodyScroll: true,\n  getContainer: null,\n  stopPropagation: ['click']\n};\nexport const Mask = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    locale\n  } = useConfig();\n  const ref = useRef(null);\n  useLockScroll(ref, props.visible && props.disableBodyScroll);\n  const background = useMemo(() => {\n    var _a;\n    const opacity = (_a = opacityRecord[props.opacity]) !== null && _a !== void 0 ? _a : props.opacity;\n    const rgb = colorRecord[props.color];\n    return rgb ? `rgba(${rgb}, ${opacity})` : props.color;\n  }, [props.color, props.opacity]);\n  const [active, setActive] = useState(props.visible);\n  const unmountedRef = useUnmountedRef();\n  const {\n    opacity\n  } = useSpring({\n    opacity: props.visible ? 1 : 0,\n    config: {\n      precision: 0.01,\n      mass: 1,\n      tension: 250,\n      friction: 30,\n      clamp: true\n    },\n    onStart: () => {\n      setActive(true);\n    },\n    onRest: () => {\n      var _a, _b;\n      if (unmountedRef.current) return;\n      setActive(props.visible);\n      if (props.visible) {\n        (_a = props.afterShow) === null || _a === void 0 ? void 0 : _a.call(props);\n      } else {\n        (_b = props.afterClose) === null || _b === void 0 ? void 0 : _b.call(props);\n      }\n    }\n  });\n  const node = withStopPropagation(props.stopPropagation, withNativeProps(props, React.createElement(animated.div, {\n    className: classPrefix,\n    ref: ref,\n    \"aria-hidden\": true,\n    style: Object.assign(Object.assign({}, props.style), {\n      background,\n      opacity,\n      display: active ? undefined : 'none'\n    }),\n    onClick: e => {\n      var _a;\n      if (e.target === e.currentTarget) {\n        (_a = props.onMaskClick) === null || _a === void 0 ? void 0 : _a.call(props, e);\n      }\n    }\n  }, props.onMaskClick && React.createElement(\"div\", {\n    className: `${classPrefix}-aria-button`,\n    role: 'button',\n    \"aria-label\": locale.Mask.name,\n    onClick: props.onMaskClick\n  }), React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, props.children))));\n  return React.createElement(ShouldRender, {\n    active: active,\n    forceRender: props.forceRender,\n    destroyOnClose: props.destroyOnClose\n  }, renderToContainer(props.getContainer, node));\n};"], "mappings": "AAAA,SAASA,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,KAAK,IAAIC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxD,SAASC,eAAe,QAAQ,QAAQ;AACxC,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,SAAS,EAAEC,QAAQ,QAAQ,mBAAmB;AACvD,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,MAAMC,WAAW,GAAG,UAAU;AAC9B,MAAMC,aAAa,GAAG;EACpBC,OAAO,EAAE,IAAI;EACbC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE;AACT,CAAC;AACD,MAAMC,WAAW,GAAG;EAClBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE;AACT,CAAC;AACD,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,IAAI;EACbC,cAAc,EAAE,KAAK;EACrBC,WAAW,EAAE,KAAK;EAClBC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE,SAAS;EAClBC,iBAAiB,EAAE,IAAI;EACvBC,YAAY,EAAE,IAAI;EAClBC,eAAe,EAAE,CAAC,OAAO;AAC3B,CAAC;AACD,OAAO,MAAMC,IAAI,GAAGC,CAAC,IAAI;EACvB,MAAMC,KAAK,GAAGvB,UAAU,CAACY,YAAY,EAAEU,CAAC,CAAC;EACzC,MAAM;IACJE;EACF,CAAC,GAAGvB,SAAS,CAAC,CAAC;EACf,MAAMwB,GAAG,GAAGhC,MAAM,CAAC,IAAI,CAAC;EACxBG,aAAa,CAAC6B,GAAG,EAAEF,KAAK,CAACV,OAAO,IAAIU,KAAK,CAACL,iBAAiB,CAAC;EAC5D,MAAMQ,UAAU,GAAGlC,OAAO,CAAC,MAAM;IAC/B,IAAImC,EAAE;IACN,MAAMV,OAAO,GAAG,CAACU,EAAE,GAAGtB,aAAa,CAACkB,KAAK,CAACN,OAAO,CAAC,MAAM,IAAI,IAAIU,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGJ,KAAK,CAACN,OAAO;IAClG,MAAMW,GAAG,GAAGnB,WAAW,CAACc,KAAK,CAACP,KAAK,CAAC;IACpC,OAAOY,GAAG,GAAG,QAAQA,GAAG,KAAKX,OAAO,GAAG,GAAGM,KAAK,CAACP,KAAK;EACvD,CAAC,EAAE,CAACO,KAAK,CAACP,KAAK,EAAEO,KAAK,CAACN,OAAO,CAAC,CAAC;EAChC,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAC6B,KAAK,CAACV,OAAO,CAAC;EACnD,MAAMkB,YAAY,GAAGpC,eAAe,CAAC,CAAC;EACtC,MAAM;IACJsB;EACF,CAAC,GAAGpB,SAAS,CAAC;IACZoB,OAAO,EAAEM,KAAK,CAACV,OAAO,GAAG,CAAC,GAAG,CAAC;IAC9BmB,MAAM,EAAE;MACNC,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE,CAAC;MACPC,OAAO,EAAE,GAAG;MACZC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAEA,CAAA,KAAM;MACbR,SAAS,CAAC,IAAI,CAAC;IACjB,CAAC;IACDS,MAAM,EAAEA,CAAA,KAAM;MACZ,IAAIZ,EAAE,EAAEa,EAAE;MACV,IAAIT,YAAY,CAACU,OAAO,EAAE;MAC1BX,SAAS,CAACP,KAAK,CAACV,OAAO,CAAC;MACxB,IAAIU,KAAK,CAACV,OAAO,EAAE;QACjB,CAACc,EAAE,GAAGJ,KAAK,CAACmB,SAAS,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgB,IAAI,CAACpB,KAAK,CAAC;MAC5E,CAAC,MAAM;QACL,CAACiB,EAAE,GAAGjB,KAAK,CAACqB,UAAU,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACpB,KAAK,CAAC;MAC7E;IACF;EACF,CAAC,CAAC;EACF,MAAMsB,IAAI,GAAG1C,mBAAmB,CAACoB,KAAK,CAACH,eAAe,EAAE9B,eAAe,CAACiC,KAAK,EAAEhC,KAAK,CAACuD,aAAa,CAAChD,QAAQ,CAACiD,GAAG,EAAE;IAC/GC,SAAS,EAAE5C,WAAW;IACtBqB,GAAG,EAAEA,GAAG;IACR,aAAa,EAAE,IAAI;IACnBwB,KAAK,EAAEC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE5B,KAAK,CAAC0B,KAAK,CAAC,EAAE;MACnDvB,UAAU;MACVT,OAAO;MACPmC,OAAO,EAAEvB,MAAM,GAAGwB,SAAS,GAAG;IAChC,CAAC,CAAC;IACFC,OAAO,EAAEC,CAAC,IAAI;MACZ,IAAI5B,EAAE;MACN,IAAI4B,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;QAChC,CAAC9B,EAAE,GAAGJ,KAAK,CAACmC,WAAW,MAAM,IAAI,IAAI/B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgB,IAAI,CAACpB,KAAK,EAAEgC,CAAC,CAAC;MACjF;IACF;EACF,CAAC,EAAEhC,KAAK,CAACmC,WAAW,IAAInE,KAAK,CAACuD,aAAa,CAAC,KAAK,EAAE;IACjDE,SAAS,EAAE,GAAG5C,WAAW,cAAc;IACvCuD,IAAI,EAAE,QAAQ;IACd,YAAY,EAAEnC,MAAM,CAACH,IAAI,CAACuC,IAAI;IAC9BN,OAAO,EAAE/B,KAAK,CAACmC;EACjB,CAAC,CAAC,EAAEnE,KAAK,CAACuD,aAAa,CAAC,KAAK,EAAE;IAC7BE,SAAS,EAAE,GAAG5C,WAAW;EAC3B,CAAC,EAAEmB,KAAK,CAACsC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACrB,OAAOtE,KAAK,CAACuD,aAAa,CAAC5C,YAAY,EAAE;IACvC2B,MAAM,EAAEA,MAAM;IACdd,WAAW,EAAEQ,KAAK,CAACR,WAAW;IAC9BD,cAAc,EAAES,KAAK,CAACT;EACxB,CAAC,EAAEf,iBAAiB,CAACwB,KAAK,CAACJ,YAAY,EAAE0B,IAAI,CAAC,CAAC;AACjD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}