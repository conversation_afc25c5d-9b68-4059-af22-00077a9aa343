{"ast": null, "code": "import * as React from \"react\";\nfunction EditSOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EditSOutline-EditSOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EditSOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"EditSOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M39.1558113,6.84476948 L39.1547876,6.84478452 C41.6150708,9.30533949 41.6150708,13.2945216 39.1547875,15.7550568 L14.2175882,40.6933641 C14.0205351,40.8899288 13.7535085,41 13.475183,41 L7.10015653,41 C5.94027729,41 5.00000168,40.0596889 5.00000168,38.8997532 L5.00000168,32.525535 C4.9995034,32.2468348 5.1098075,31.9793647 5.30661943,31.7820434 L30.2469685,6.84477025 C32.707296,4.38507671 36.6954838,4.38507671 39.1558113,6.84476948 Z M41.5764707,38 C41.8588237,38 42,38.1333332 42,38.4000001 L42,40.5999999 C42,40.8666668 41.8588237,41 41.5764707,41 L24.4235293,41 C24.1411763,41 24,40.8666668 24,40.5999999 L24,38.4000001 C24,38.1333332 24.1411763,38 24.4235293,38 L41.5764707,38 Z M28.172,13.371 L8.2730898,33.272167 C8.19433904,33.3510089 8.15023081,33.4579141 8.15023081,33.5693515 L8.15023081,37.4317018 C8.15023081,37.663779 8.33819467,37.8517506 8.57026108,37.8517506 L12.4303379,37.8517506 C12.5419214,37.8517506 12.6488431,37.8069757 12.7275092,37.7278363 L32.628,17.827 L28.172,13.371 Z M41.5555549,31 C41.8518518,31 42,31.1333332 42,31.4000001 L42,33.5999999 C42,33.8666668 41.8518518,34 41.5555549,34 L32.4444451,34 C32.1481482,34 32,33.8666668 32,33.5999999 L32,31.4000001 C32,31.1333332 32.1481482,31 32.4444451,31 L41.5555549,31 Z M32.607562,8.94503104 L32.4721022,9.07104573 L30.371,11.172 L34.827,15.628 L36.9275554,13.5288315 C38.1074041,12.3492947 38.1631188,10.4544078 37.0546144,9.20757614 L36.9286053,9.07316031 L36.9286254,9.07104573 C35.7488554,7.89144977 33.8540626,7.83619529 32.607562,8.94503104 Z\",\n    id: \"EditSOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default EditSOutline;", "map": {"version": 3, "names": ["React", "EditSOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/EditSOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction EditSOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EditSOutline-EditSOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EditSOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"EditSOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M39.1558113,6.84476948 L39.1547876,6.84478452 C41.6150708,9.30533949 41.6150708,13.2945216 39.1547875,15.7550568 L14.2175882,40.6933641 C14.0205351,40.8899288 13.7535085,41 13.475183,41 L7.10015653,41 C5.94027729,41 5.00000168,40.0596889 5.00000168,38.8997532 L5.00000168,32.525535 C4.9995034,32.2468348 5.1098075,31.9793647 5.30661943,31.7820434 L30.2469685,6.84477025 C32.707296,4.38507671 36.6954838,4.38507671 39.1558113,6.84476948 Z M41.5764707,38 C41.8588237,38 42,38.1333332 42,38.4000001 L42,40.5999999 C42,40.8666668 41.8588237,41 41.5764707,41 L24.4235293,41 C24.1411763,41 24,40.8666668 24,40.5999999 L24,38.4000001 C24,38.1333332 24.1411763,38 24.4235293,38 L41.5764707,38 Z M28.172,13.371 L8.2730898,33.272167 C8.19433904,33.3510089 8.15023081,33.4579141 8.15023081,33.5693515 L8.15023081,37.4317018 C8.15023081,37.663779 8.33819467,37.8517506 8.57026108,37.8517506 L12.4303379,37.8517506 C12.5419214,37.8517506 12.6488431,37.8069757 12.7275092,37.7278363 L32.628,17.827 L28.172,13.371 Z M41.5555549,31 C41.8518518,31 42,31.1333332 42,31.4000001 L42,33.5999999 C42,33.8666668 41.8518518,34 41.5555549,34 L32.4444451,34 C32.1481482,34 32,33.8666668 32,33.5999999 L32,31.4000001 C32,31.1333332 32.1481482,31 32.4444451,31 L41.5555549,31 Z M32.607562,8.94503104 L32.4721022,9.07104573 L30.371,11.172 L34.827,15.628 L36.9275554,13.5288315 C38.1074041,12.3492947 38.1631188,10.4544078 37.0546144,9.20757614 L36.9286053,9.07316031 L36.9286254,9.07104573 C35.7488554,7.89144977 33.8540626,7.83619529 32.607562,8.94503104 Z\",\n    id: \"EditSOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default EditSOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,2BAA2B;IAC/BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,2BAA2B;IAC/BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,0/CAA0/C;IAC7/CR,EAAE,EAAE,uCAAuC;IAC3CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}