{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nconst classPrefix = `adm-steps`;\nconst stepClassPrefix = `adm-step`;\nconst defaultIcon = React.createElement(\"span\", {\n  className: `${stepClassPrefix}-icon-dot`\n});\nconst defaultProps = {\n  current: 0,\n  direction: 'horizontal'\n};\nexport const Steps = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    direction,\n    current\n  } = props;\n  const classString = classNames(classPrefix, `${classPrefix}-${direction}`);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classString\n  }, React.Children.map(props.children, (child, index) => {\n    var _a;\n    if (!React.isValidElement(child)) {\n      return child;\n    }\n    const childProps = child.props;\n    let status = childProps.status || 'wait';\n    if (index < current) {\n      status = childProps.status || 'finish';\n    } else if (index === current) {\n      status = childProps.status || 'process';\n    }\n    const icon = (_a = childProps.icon) !== null && _a !== void 0 ? _a : defaultIcon;\n    return React.cloneElement(child, {\n      status,\n      icon\n    });\n  })));\n};", "map": {"version": 3, "names": ["React", "classNames", "mergeProps", "withNativeProps", "classPrefix", "stepClassPrefix", "defaultIcon", "createElement", "className", "defaultProps", "current", "direction", "Steps", "p", "props", "classString", "Children", "map", "children", "child", "index", "_a", "isValidElement", "childProps", "status", "icon", "cloneElement"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/steps/steps.js"], "sourcesContent": ["import React from 'react';\nimport classNames from 'classnames';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nconst classPrefix = `adm-steps`;\nconst stepClassPrefix = `adm-step`;\nconst defaultIcon = React.createElement(\"span\", {\n  className: `${stepClassPrefix}-icon-dot`\n});\nconst defaultProps = {\n  current: 0,\n  direction: 'horizontal'\n};\nexport const Steps = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    direction,\n    current\n  } = props;\n  const classString = classNames(classPrefix, `${classPrefix}-${direction}`);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classString\n  }, React.Children.map(props.children, (child, index) => {\n    var _a;\n    if (!React.isValidElement(child)) {\n      return child;\n    }\n    const childProps = child.props;\n    let status = childProps.status || 'wait';\n    if (index < current) {\n      status = childProps.status || 'finish';\n    } else if (index === current) {\n      status = childProps.status || 'process';\n    }\n    const icon = (_a = childProps.icon) !== null && _a !== void 0 ? _a : defaultIcon;\n    return React.cloneElement(child, {\n      status,\n      icon\n    });\n  })));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,MAAMC,WAAW,GAAG,WAAW;AAC/B,MAAMC,eAAe,GAAG,UAAU;AAClC,MAAMC,WAAW,GAAGN,KAAK,CAACO,aAAa,CAAC,MAAM,EAAE;EAC9CC,SAAS,EAAE,GAAGH,eAAe;AAC/B,CAAC,CAAC;AACF,MAAMI,YAAY,GAAG;EACnBC,OAAO,EAAE,CAAC;EACVC,SAAS,EAAE;AACb,CAAC;AACD,OAAO,MAAMC,KAAK,GAAGC,CAAC,IAAI;EACxB,MAAMC,KAAK,GAAGZ,UAAU,CAACO,YAAY,EAAEI,CAAC,CAAC;EACzC,MAAM;IACJF,SAAS;IACTD;EACF,CAAC,GAAGI,KAAK;EACT,MAAMC,WAAW,GAAGd,UAAU,CAACG,WAAW,EAAE,GAAGA,WAAW,IAAIO,SAAS,EAAE,CAAC;EAC1E,OAAOR,eAAe,CAACW,KAAK,EAAEd,KAAK,CAACO,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEO;EACb,CAAC,EAAEf,KAAK,CAACgB,QAAQ,CAACC,GAAG,CAACH,KAAK,CAACI,QAAQ,EAAE,CAACC,KAAK,EAAEC,KAAK,KAAK;IACtD,IAAIC,EAAE;IACN,IAAI,CAACrB,KAAK,CAACsB,cAAc,CAACH,KAAK,CAAC,EAAE;MAChC,OAAOA,KAAK;IACd;IACA,MAAMI,UAAU,GAAGJ,KAAK,CAACL,KAAK;IAC9B,IAAIU,MAAM,GAAGD,UAAU,CAACC,MAAM,IAAI,MAAM;IACxC,IAAIJ,KAAK,GAAGV,OAAO,EAAE;MACnBc,MAAM,GAAGD,UAAU,CAACC,MAAM,IAAI,QAAQ;IACxC,CAAC,MAAM,IAAIJ,KAAK,KAAKV,OAAO,EAAE;MAC5Bc,MAAM,GAAGD,UAAU,CAACC,MAAM,IAAI,SAAS;IACzC;IACA,MAAMC,IAAI,GAAG,CAACJ,EAAE,GAAGE,UAAU,CAACE,IAAI,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGf,WAAW;IAChF,OAAON,KAAK,CAAC0B,YAAY,CAACP,KAAK,EAAE;MAC/BK,MAAM;MACNC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC,CAAC;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}