{"ast": null, "code": "import * as React from \"react\";\nfunction ExclamationShieldFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ExclamationShieldFill-ExclamationShieldFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ExclamationShieldFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M23.5828289,4.01695412 L39.8608414,7.34925941 C41.6942831,7.74902106 43,9.35659657 43,11.2141357 L43,29.7665668 C43,32.6436189 41.4221454,35.2941108 38.8789667,36.6891094 L25.4394833,43.5124845 C24.2329985,44.1742723 22.7670015,44.1742723 21.5605167,43.5124845 L9.12103331,36.6891094 C6.57785456,35.2941108 5,32.6436189 5,29.7665668 L5,11.2141357 C5,9.35659657 6.30571695,7.74902106 8.1391586,7.34925941 L23.4173942,4.01800934 C23.471863,4.00613301 23.528213,4.00577358 23.5828289,4.01695412 Z M25.1,30.0057358 L22.9,30.0057358 C22.6790861,30.0057358 22.5,30.1848219 22.5,30.4057358 L22.5,30.4057358 L22.5,32.6063979 C22.5,32.8273118 22.6790861,33.0063979 22.9,33.0063979 L22.9,33.0063979 L25.1,33.0063979 C25.3209139,33.0063979 25.5,32.8273118 25.5,32.6063979 L25.5,32.6063979 L25.5,30.4057358 C25.5,30.1848219 25.3209139,30.0057358 25.1,30.0057358 L25.1,30.0057358 Z M25.1,14.0022049 L22.9,14.0022049 C22.6790861,14.0022049 22.5,14.181291 22.5,14.4022049 L22.5,14.4022049 L22.5,26.6050738 C22.5,26.8259877 22.6790861,27.0050738 22.9,27.0050738 L22.9,27.0050738 L25.1,27.0050738 C25.3209139,27.0050738 25.5,26.8259877 25.5,26.6050738 L25.5,26.6050738 L25.5,14.4022049 C25.5,14.181291 25.3209139,14.0022049 25.1,14.0022049 L25.1,14.0022049 Z\",\n    id: \"ExclamationShieldFill-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default ExclamationShieldFill;", "map": {"version": 3, "names": ["React", "ExclamationShieldFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/ExclamationShieldFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction ExclamationShieldFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ExclamationShieldFill-ExclamationShieldFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ExclamationShieldFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M23.5828289,4.01695412 L39.8608414,7.34925941 C41.6942831,7.74902106 43,9.35659657 43,11.2141357 L43,29.7665668 C43,32.6436189 41.4221454,35.2941108 38.8789667,36.6891094 L25.4394833,43.5124845 C24.2329985,44.1742723 22.7670015,44.1742723 21.5605167,43.5124845 L9.12103331,36.6891094 C6.57785456,35.2941108 5,32.6436189 5,29.7665668 L5,11.2141357 C5,9.35659657 6.30571695,7.74902106 8.1391586,7.34925941 L23.4173942,4.01800934 C23.471863,4.00613301 23.528213,4.00577358 23.5828289,4.01695412 Z M25.1,30.0057358 L22.9,30.0057358 C22.6790861,30.0057358 22.5,30.1848219 22.5,30.4057358 L22.5,30.4057358 L22.5,32.6063979 C22.5,32.8273118 22.6790861,33.0063979 22.9,33.0063979 L22.9,33.0063979 L25.1,33.0063979 C25.3209139,33.0063979 25.5,32.8273118 25.5,32.6063979 L25.5,32.6063979 L25.5,30.4057358 C25.5,30.1848219 25.3209139,30.0057358 25.1,30.0057358 L25.1,30.0057358 Z M25.1,14.0022049 L22.9,14.0022049 C22.6790861,14.0022049 22.5,14.181291 22.5,14.4022049 L22.5,14.4022049 L22.5,26.6050738 C22.5,26.8259877 22.6790861,27.0050738 22.9,27.0050738 L22.9,27.0050738 L25.1,27.0050738 C25.3209139,27.0050738 25.5,26.8259877 25.5,26.6050738 L25.5,26.6050738 L25.5,14.4022049 C25.5,14.181291 25.3209139,14.0022049 25.1,14.0022049 L25.1,14.0022049 Z\",\n    id: \"ExclamationShieldFill-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default ExclamationShieldFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,qBAAqBA,CAACC,KAAK,EAAE;EACpC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,6CAA6C;IACjDC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,aAAaH,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IACtFc,EAAE,EAAE,oCAAoC;IACxCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,2tCAA2tC;IAC9tCR,EAAE,EAAE,gDAAgD;IACpDG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}