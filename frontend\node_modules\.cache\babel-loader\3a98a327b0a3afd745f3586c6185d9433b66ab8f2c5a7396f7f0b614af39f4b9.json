{"ast": null, "code": "import * as React from \"react\";\nfunction MailFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MailFill-MailFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MailFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"MailFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37,5 L37,5 C41.4182665,5 45,8.78070315 45,13.4444499 L45,34.5555501 L45,34.5555501 C45,39.219282 41.4182806,43 37,43 L11,43 L11,43 C6.58173347,43 3,39.2192968 3,34.5555501 C3,34.5555501 3,34.5555501 3,34.5555501 L3,13.4444499 L3,13.4444512 C3,8.78071927 6.58171472,5 11,5 L37,5 L37,5 Z M41.9610038,14.0429492 L41.9610038,14.0429492 C41.866374,13.8322394 41.6278377,13.7423997 41.4282184,13.8422871 C41.4281472,13.8423228 41.4280761,13.8423584 41.4280049,13.8423941 L26.1500065,21.5236783 L25.9070067,21.6366228 L25.9070066,21.6366229 C24.6003534,22.2060383 23.1264129,22.1650052 21.8500127,21.5236784 L6.57201428,13.8434827 L6.57201428,13.8434827 C6.37256748,13.7432118 6.13387603,13.8325924 6.03888207,14.04312 C6.01316601,14.1001125 6.00001321,14.1624665 6.00001321,14.2255934 L6.00001321,16.7969234 L6.00001321,16.7969293 C6.00001321,16.9601035 6.08845264,17.1088738 6.22801905,17.1790366 L20.5610186,24.3821548 L20.5610193,24.3821552 C22.7366825,25.4755593 25.2633734,25.4755593 27.4390272,24.3821545 L41.7720267,17.1790362 L41.7720267,17.1790362 C41.9115922,17.1088714 42.0000274,16.9600996 42.0000274,16.7969255 L42.0000274,14.2255955 L42.0000274,14.2255954 C42.0000274,14.1627678 41.9866615,14.1007418 41.9610265,14.0440402 L41.9610038,14.0429492 Z\",\n    id: \"MailFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default MailFill;", "map": {"version": 3, "names": ["React", "MailFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/MailFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction MailFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MailFill-MailFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MailFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"MailFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37,5 L37,5 C41.4182665,5 45,8.78070315 45,13.4444499 L45,34.5555501 L45,34.5555501 C45,39.219282 41.4182806,43 37,43 L11,43 L11,43 C6.58173347,43 3,39.2192968 3,34.5555501 C3,34.5555501 3,34.5555501 3,34.5555501 L3,13.4444499 L3,13.4444512 C3,8.78071927 6.58171472,5 11,5 L37,5 L37,5 Z M41.9610038,14.0429492 L41.9610038,14.0429492 C41.866374,13.8322394 41.6278377,13.7423997 41.4282184,13.8422871 C41.4281472,13.8423228 41.4280761,13.8423584 41.4280049,13.8423941 L26.1500065,21.5236783 L25.9070067,21.6366228 L25.9070066,21.6366229 C24.6003534,22.2060383 23.1264129,22.1650052 21.8500127,21.5236784 L6.57201428,13.8434827 L6.57201428,13.8434827 C6.37256748,13.7432118 6.13387603,13.8325924 6.03888207,14.04312 C6.01316601,14.1001125 6.00001321,14.1624665 6.00001321,14.2255934 L6.00001321,16.7969234 L6.00001321,16.7969293 C6.00001321,16.9601035 6.08845264,17.1088738 6.22801905,17.1790366 L20.5610186,24.3821548 L20.5610193,24.3821552 C22.7366825,25.4755593 25.2633734,25.4755593 27.4390272,24.3821545 L41.7720267,17.1790362 L41.7720267,17.1790362 C41.9115922,17.1088714 42.0000274,16.9600996 42.0000274,16.7969255 L42.0000274,14.2255955 L42.0000274,14.2255954 C42.0000274,14.1627678 41.9866615,14.1007418 41.9610265,14.0440402 L41.9610038,14.0429492 Z\",\n    id: \"MailFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default MailFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,mBAAmB;IACvBC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,uBAAuB;IAC3BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,0uCAA0uC;IAC7uCR,EAAE,EAAE,uBAAuB;IAC3BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}