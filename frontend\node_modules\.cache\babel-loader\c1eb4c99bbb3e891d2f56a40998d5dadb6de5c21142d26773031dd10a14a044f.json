{"ast": null, "code": "import * as React from \"react\";\nfunction CalendarOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CalendarOutline-CalendarOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CalendarOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CalendarOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M18,4.4 L18,7 L18,7 L30,7 L30,4.4 C30,4.1790861 30.1790861,4 30.4,4 L32.6,4 C32.8209139,4 33,4.1790861 33,4.4 L33,7 L33,7 L37,7 C40.3137085,7 43,9.6862915 43,13 L43,38 C43,41.3137085 40.3137085,44 37,44 L11,44 C7.6862915,44 5,41.3137085 5,38 L5,13 C5,9.6862915 7.6862915,7 11,7 L15,7 L15,4.4 C15,4.1790861 15.1790861,4 15.4,4 L17.6,4 C17.8209139,4 18,4.1790861 18,4.4 Z M40,20 L8,20 L8,38 C8,39.5976809 9.24891996,40.9036609 10.8237272,40.9949073 L11,41 L37,41 C38.5976809,41 39.9036609,39.75108 39.9949073,38.1762728 L40,38 L40,20 Z M15,10 L11,10 C9.40231912,10 8.09633912,11.24892 8.00509269,12.8237272 L8,13 L8,17 L40,17 L40,13 C40,11.4023191 38.75108,10.0963391 37.1762728,10.0050927 L37,10 L33,10 L33,12.6 C33,12.8209139 32.8209139,13 32.6,13 L30.4,13 C30.1790861,13 30,12.8209139 30,12.6 L30,10 L30,10 L18,10 L18,12.6 C18,12.8209139 17.8209139,13 17.6,13 L15.4,13 C15.1790861,13 15,12.8209139 15,12.6 L15,10 L15,10 Z\",\n    id: \"CalendarOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default CalendarOutline;", "map": {"version": 3, "names": ["React", "CalendarOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/CalendarOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction CalendarOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CalendarOutline-CalendarOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CalendarOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CalendarOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M18,4.4 L18,7 L18,7 L30,7 L30,4.4 C30,4.1790861 30.1790861,4 30.4,4 L32.6,4 C32.8209139,4 33,4.1790861 33,4.4 L33,7 L33,7 L37,7 C40.3137085,7 43,9.6862915 43,13 L43,38 C43,41.3137085 40.3137085,44 37,44 L11,44 C7.6862915,44 5,41.3137085 5,38 L5,13 C5,9.6862915 7.6862915,7 11,7 L15,7 L15,4.4 C15,4.1790861 15.1790861,4 15.4,4 L17.6,4 C17.8209139,4 18,4.1790861 18,4.4 Z M40,20 L8,20 L8,38 C8,39.5976809 9.24891996,40.9036609 10.8237272,40.9949073 L11,41 L37,41 C38.5976809,41 39.9036609,39.75108 39.9949073,38.1762728 L40,38 L40,20 Z M15,10 L11,10 C9.40231912,10 8.09633912,11.24892 8.00509269,12.8237272 L8,13 L8,17 L40,17 L40,13 C40,11.4023191 38.75108,10.0963391 37.1762728,10.0050927 L37,10 L33,10 L33,12.6 C33,12.8209139 32.8209139,13 32.6,13 L30.4,13 C30.1790861,13 30,12.8209139 30,12.6 L30,10 L30,10 L18,10 L18,12.6 C18,12.8209139 17.8209139,13 17.6,13 L15.4,13 C15.1790861,13 15,12.8209139 15,12.6 L15,10 L15,10 Z\",\n    id: \"CalendarOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default CalendarOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,iCAAiC;IACrCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,45BAA45B;IAC/5BR,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}