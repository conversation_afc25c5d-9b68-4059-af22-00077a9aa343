{"ast": null, "code": "import React, { isValidElement, useRef } from 'react';\nimport classNames from 'classnames';\nimport { useSpring, animated } from '@react-spring/web';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { bound } from '../../utils/bound';\nimport { useThrottleFn, useIsomorphicLayoutEffect } from 'ahooks';\nimport { useMutationEffect } from '../../utils/use-mutation-effect';\nimport { useResizeEffect } from '../../utils/use-resize-effect';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useIsomorphicUpdateLayoutEffect } from '../../utils/use-isomorphic-update-layout-effect';\nimport { ShouldRender } from '../../utils/should-render';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\nconst classPrefix = `adm-tabs`;\nexport const Tab = () => {\n  return null;\n};\nconst defaultProps = {\n  activeLineMode: 'auto',\n  stretch: true,\n  direction: 'ltr'\n};\nexport const Tabs = p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  const tabListContainerRef = useRef(null);\n  const activeLineRef = useRef(null);\n  const keyToIndexRecord = {};\n  let firstActiveKey = null;\n  const panes = [];\n  const isRTL = props.direction === 'rtl';\n  traverseReactNode(props.children, (child, index) => {\n    if (!isValidElement(child)) return;\n    const key = child.key;\n    if (typeof key !== 'string') return;\n    if (index === 0) {\n      firstActiveKey = key;\n    }\n    const length = panes.push(child);\n    keyToIndexRecord[key] = length - 1;\n  });\n  const [activeKey, setActiveKey] = usePropsValue({\n    value: props.activeKey,\n    defaultValue: (_a = props.defaultActiveKey) !== null && _a !== void 0 ? _a : firstActiveKey,\n    onChange: v => {\n      var _a;\n      if (v === null) return;\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, v);\n    }\n  });\n  const [{\n    x,\n    width\n  }, inkApi] = useSpring(() => ({\n    x: 0,\n    width: 0,\n    config: {\n      tension: 300,\n      clamp: true\n    }\n  }));\n  const [{\n    scrollLeft\n  }, scrollApi] = useSpring(() => ({\n    scrollLeft: 0,\n    config: {\n      tension: 300,\n      clamp: true\n    }\n  }));\n  const [{\n    leftMaskOpacity,\n    rightMaskOpacity\n  }, maskApi] = useSpring(() => ({\n    leftMaskOpacity: 0,\n    rightMaskOpacity: 0,\n    config: {\n      clamp: true\n    }\n  }));\n  function animate(immediate = false, fromMutation = false) {\n    const container = tabListContainerRef.current;\n    if (!container) return;\n    const activeIndex = keyToIndexRecord[activeKey];\n    if (activeIndex === undefined) {\n      inkApi.start({\n        x: 0,\n        width: 0,\n        immediate: true\n      });\n      return;\n    }\n    const activeLine = activeLineRef.current;\n    if (!activeLine) return;\n    const activeTabWrapper = container.children.item(activeIndex + 1);\n    const activeTab = activeTabWrapper.children.item(0);\n    const activeTabLeft = activeTab.offsetLeft;\n    const activeTabWidth = activeTab.offsetWidth;\n    const activeTabWrapperLeft = activeTabWrapper.offsetLeft;\n    const activeTabWrapperWidth = activeTabWrapper.offsetWidth;\n    const containerWidth = container.offsetWidth;\n    const containerScrollWidth = container.scrollWidth;\n    const containerScrollLeft = container.scrollLeft;\n    const activeLineWidth = activeLine.offsetWidth;\n    let x = 0;\n    let width = 0;\n    if (props.activeLineMode === 'auto') {\n      x = activeTabLeft;\n      width = activeTabWidth;\n    } else if (props.activeLineMode === 'full') {\n      x = activeTabWrapperLeft;\n      width = activeTabWrapperWidth;\n    } else {\n      x = activeTabLeft + (activeTabWidth - activeLineWidth) / 2;\n    }\n    if (isRTL) {\n      /**\n       * In RTL mode, x equals the container width minus the x-coordinate of the current tab minus the width of the current tab.\n       * https://github.com/Fog3211/reproduce-codesandbox/blob/f0a3396a114cc00e88a51a67d3be60a746519b30/assets/images/antd_mobile_tabs_rtl_x.jpg?raw=true\n       */\n      const w = ['auto', 'full'].includes(props.activeLineMode) ? width : activeLineWidth;\n      x = -(containerWidth - x - w);\n    }\n    inkApi.start({\n      x,\n      width,\n      immediate\n    });\n    const maxScrollDistance = containerScrollWidth - containerWidth;\n    if (maxScrollDistance <= 0) return;\n    let nextScrollLeft = 0;\n    if (isRTL) {\n      /**\n       * 位移距离等于：activeTab的中心坐标距离容器中心坐标的距离，然后RTL取负数\n       * containerWidth / 2 - (activeTabLeft + (activeTabWidth - activeLineWidth) / 2) - activeLineWidth / 2,\n       */\n      nextScrollLeft = -bound(containerWidth / 2 - activeTabLeft + activeTabWidth / 2 - activeLineWidth, 0, maxScrollDistance);\n    } else {\n      nextScrollLeft = bound(activeTabLeft - (containerWidth - activeTabWidth) / 2, 0, maxScrollDistance);\n    }\n    if (!fromMutation || props.autoScroll !== false) {\n      scrollApi.start({\n        scrollLeft: nextScrollLeft,\n        from: {\n          scrollLeft: containerScrollLeft\n        },\n        immediate\n      });\n    }\n  }\n  useIsomorphicLayoutEffect(() => {\n    animate(!x.isAnimating);\n  }, []);\n  useIsomorphicUpdateLayoutEffect(() => {\n    animate();\n  }, [activeKey, isRTL, props.activeLineMode]);\n  useResizeEffect(() => {\n    animate(!x.isAnimating);\n  }, tabListContainerRef);\n  useMutationEffect(() => {\n    animate(!x.isAnimating, true);\n  }, tabListContainerRef, {\n    subtree: true,\n    childList: true,\n    characterData: true\n  });\n  const {\n    run: updateMask\n  } = useThrottleFn((immediate = false) => {\n    const container = tabListContainerRef.current;\n    if (!container) return;\n    const scrollLeft = container.scrollLeft;\n    let showLeftMask = false;\n    let showRightMask = false;\n    if (isRTL) {\n      /**\n       * RTL模式下，只要滑动过，scrollLeft就再也回不到0（chrome是0.5）\n       * 所以要加round才能终止触发条件\n       * round(443.5) + 375 < 819\n       */\n      showLeftMask = Math.round(-scrollLeft) + container.offsetWidth < container.scrollWidth;\n      showRightMask = scrollLeft < 0;\n    } else {\n      showLeftMask = scrollLeft > 0;\n      showRightMask = scrollLeft + container.offsetWidth < container.scrollWidth;\n    }\n    maskApi.start({\n      leftMaskOpacity: showLeftMask ? 1 : 0,\n      rightMaskOpacity: showRightMask ? 1 : 0,\n      immediate\n    });\n  }, {\n    wait: 100,\n    trailing: true,\n    leading: true\n  });\n  useIsomorphicLayoutEffect(() => {\n    updateMask(true);\n  }, []);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix,\n    style: {\n      direction: props.direction\n    }\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-header`\n  }, React.createElement(animated.div, {\n    className: classNames(`${classPrefix}-header-mask`, `${classPrefix}-header-mask-left`),\n    style: {\n      opacity: leftMaskOpacity\n    }\n  }), React.createElement(animated.div, {\n    className: classNames(`${classPrefix}-header-mask`, `${classPrefix}-header-mask-right`),\n    style: {\n      opacity: rightMaskOpacity\n    }\n  }), React.createElement(animated.div, {\n    className: `${classPrefix}-tab-list`,\n    ref: tabListContainerRef,\n    scrollLeft: scrollLeft,\n    onScroll: updateMask,\n    role: 'tablist'\n  }, React.createElement(animated.div, {\n    ref: activeLineRef,\n    className: `${classPrefix}-tab-line`,\n    style: {\n      width: props.activeLineMode === 'fixed' ? 'var(--fixed-active-line-width, 30px)' : width,\n      x\n    }\n  }), panes.map(pane => withNativeProps(pane.props, React.createElement(\"div\", {\n    key: pane.key,\n    className: classNames(`${classPrefix}-tab-wrapper`, {\n      [`${classPrefix}-tab-wrapper-stretch`]: props.stretch\n    })\n  }, React.createElement(\"div\", {\n    onClick: () => {\n      const {\n        key\n      } = pane;\n      if (pane.props.disabled) return;\n      if (key === undefined || key === null) {\n        return;\n      }\n      setActiveKey(key.toString());\n    },\n    className: classNames(`${classPrefix}-tab`, {\n      [`${classPrefix}-tab-active`]: pane.key === activeKey,\n      [`${classPrefix}-tab-disabled`]: pane.props.disabled\n    }),\n    role: 'tab',\n    \"aria-selected\": pane.key === activeKey\n  }, pane.props.title)))))), panes.map(pane => {\n    if (pane.props.children === undefined) {\n      return null;\n    }\n    const active = pane.key === activeKey;\n    return React.createElement(ShouldRender, {\n      key: pane.key,\n      active: active,\n      forceRender: pane.props.forceRender,\n      destroyOnClose: pane.props.destroyOnClose\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-content`,\n      style: {\n        display: active ? 'block' : 'none'\n      }\n    }, pane.props.children));\n  })));\n};", "map": {"version": 3, "names": ["React", "isValidElement", "useRef", "classNames", "useSpring", "animated", "withNativeProps", "usePropsValue", "bound", "useThrottleFn", "useIsomorphicLayoutEffect", "useMutationEffect", "useResizeEffect", "mergeProps", "useIsomorphicUpdateLayoutEffect", "ShouldRender", "traverseReactNode", "classPrefix", "Tab", "defaultProps", "activeLineMode", "stretch", "direction", "Tabs", "p", "_a", "props", "tabListContainerRef", "activeLineRef", "keyToIndexRecord", "firstActiveKey", "panes", "isRTL", "children", "child", "index", "key", "length", "push", "active<PERSON><PERSON>", "setActiveKey", "value", "defaultValue", "defaultActiveKey", "onChange", "v", "call", "x", "width", "inkApi", "config", "tension", "clamp", "scrollLeft", "scrollApi", "leftMaskOpacity", "rightMaskOpacity", "<PERSON><PERSON><PERSON>", "animate", "immediate", "fromMutation", "container", "current", "activeIndex", "undefined", "start", "activeLine", "activeTabWrapper", "item", "activeTab", "activeTabLeft", "offsetLeft", "activeTabWidth", "offsetWidth", "activeTabWrapperLeft", "activeTabWrapperWidth", "containerWidth", "containerScrollWidth", "scrollWidth", "containerScrollLeft", "activeLineWidth", "w", "includes", "maxScrollDistance", "nextScrollLeft", "autoScroll", "from", "isAnimating", "subtree", "childList", "characterData", "run", "updateMask", "showLeftMask", "showRightMask", "Math", "round", "wait", "trailing", "leading", "createElement", "className", "style", "div", "opacity", "ref", "onScroll", "role", "map", "pane", "onClick", "disabled", "toString", "title", "active", "forceRender", "destroyOnClose", "display"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/tabs/tabs.js"], "sourcesContent": ["import React, { isValidElement, useRef } from 'react';\nimport classNames from 'classnames';\nimport { useSpring, animated } from '@react-spring/web';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { bound } from '../../utils/bound';\nimport { useThrottleFn, useIsomorphicLayoutEffect } from 'ahooks';\nimport { useMutationEffect } from '../../utils/use-mutation-effect';\nimport { useResizeEffect } from '../../utils/use-resize-effect';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useIsomorphicUpdateLayoutEffect } from '../../utils/use-isomorphic-update-layout-effect';\nimport { ShouldRender } from '../../utils/should-render';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\nconst classPrefix = `adm-tabs`;\nexport const Tab = () => {\n  return null;\n};\nconst defaultProps = {\n  activeLineMode: 'auto',\n  stretch: true,\n  direction: 'ltr'\n};\nexport const Tabs = p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  const tabListContainerRef = useRef(null);\n  const activeLineRef = useRef(null);\n  const keyToIndexRecord = {};\n  let firstActiveKey = null;\n  const panes = [];\n  const isRTL = props.direction === 'rtl';\n  traverseReactNode(props.children, (child, index) => {\n    if (!isValidElement(child)) return;\n    const key = child.key;\n    if (typeof key !== 'string') return;\n    if (index === 0) {\n      firstActiveKey = key;\n    }\n    const length = panes.push(child);\n    keyToIndexRecord[key] = length - 1;\n  });\n  const [activeKey, setActiveKey] = usePropsValue({\n    value: props.activeKey,\n    defaultValue: (_a = props.defaultActiveKey) !== null && _a !== void 0 ? _a : firstActiveKey,\n    onChange: v => {\n      var _a;\n      if (v === null) return;\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, v);\n    }\n  });\n  const [{\n    x,\n    width\n  }, inkApi] = useSpring(() => ({\n    x: 0,\n    width: 0,\n    config: {\n      tension: 300,\n      clamp: true\n    }\n  }));\n  const [{\n    scrollLeft\n  }, scrollApi] = useSpring(() => ({\n    scrollLeft: 0,\n    config: {\n      tension: 300,\n      clamp: true\n    }\n  }));\n  const [{\n    leftMaskOpacity,\n    rightMaskOpacity\n  }, maskApi] = useSpring(() => ({\n    leftMaskOpacity: 0,\n    rightMaskOpacity: 0,\n    config: {\n      clamp: true\n    }\n  }));\n  function animate(immediate = false, fromMutation = false) {\n    const container = tabListContainerRef.current;\n    if (!container) return;\n    const activeIndex = keyToIndexRecord[activeKey];\n    if (activeIndex === undefined) {\n      inkApi.start({\n        x: 0,\n        width: 0,\n        immediate: true\n      });\n      return;\n    }\n    const activeLine = activeLineRef.current;\n    if (!activeLine) return;\n    const activeTabWrapper = container.children.item(activeIndex + 1);\n    const activeTab = activeTabWrapper.children.item(0);\n    const activeTabLeft = activeTab.offsetLeft;\n    const activeTabWidth = activeTab.offsetWidth;\n    const activeTabWrapperLeft = activeTabWrapper.offsetLeft;\n    const activeTabWrapperWidth = activeTabWrapper.offsetWidth;\n    const containerWidth = container.offsetWidth;\n    const containerScrollWidth = container.scrollWidth;\n    const containerScrollLeft = container.scrollLeft;\n    const activeLineWidth = activeLine.offsetWidth;\n    let x = 0;\n    let width = 0;\n    if (props.activeLineMode === 'auto') {\n      x = activeTabLeft;\n      width = activeTabWidth;\n    } else if (props.activeLineMode === 'full') {\n      x = activeTabWrapperLeft;\n      width = activeTabWrapperWidth;\n    } else {\n      x = activeTabLeft + (activeTabWidth - activeLineWidth) / 2;\n    }\n    if (isRTL) {\n      /**\n       * In RTL mode, x equals the container width minus the x-coordinate of the current tab minus the width of the current tab.\n       * https://github.com/Fog3211/reproduce-codesandbox/blob/f0a3396a114cc00e88a51a67d3be60a746519b30/assets/images/antd_mobile_tabs_rtl_x.jpg?raw=true\n       */\n      const w = ['auto', 'full'].includes(props.activeLineMode) ? width : activeLineWidth;\n      x = -(containerWidth - x - w);\n    }\n    inkApi.start({\n      x,\n      width,\n      immediate\n    });\n    const maxScrollDistance = containerScrollWidth - containerWidth;\n    if (maxScrollDistance <= 0) return;\n    let nextScrollLeft = 0;\n    if (isRTL) {\n      /**\n       * 位移距离等于：activeTab的中心坐标距离容器中心坐标的距离，然后RTL取负数\n       * containerWidth / 2 - (activeTabLeft + (activeTabWidth - activeLineWidth) / 2) - activeLineWidth / 2,\n       */\n      nextScrollLeft = -bound(containerWidth / 2 - activeTabLeft + activeTabWidth / 2 - activeLineWidth, 0, maxScrollDistance);\n    } else {\n      nextScrollLeft = bound(activeTabLeft - (containerWidth - activeTabWidth) / 2, 0, maxScrollDistance);\n    }\n    if (!fromMutation || props.autoScroll !== false) {\n      scrollApi.start({\n        scrollLeft: nextScrollLeft,\n        from: {\n          scrollLeft: containerScrollLeft\n        },\n        immediate\n      });\n    }\n  }\n  useIsomorphicLayoutEffect(() => {\n    animate(!x.isAnimating);\n  }, []);\n  useIsomorphicUpdateLayoutEffect(() => {\n    animate();\n  }, [activeKey, isRTL, props.activeLineMode]);\n  useResizeEffect(() => {\n    animate(!x.isAnimating);\n  }, tabListContainerRef);\n  useMutationEffect(() => {\n    animate(!x.isAnimating, true);\n  }, tabListContainerRef, {\n    subtree: true,\n    childList: true,\n    characterData: true\n  });\n  const {\n    run: updateMask\n  } = useThrottleFn((immediate = false) => {\n    const container = tabListContainerRef.current;\n    if (!container) return;\n    const scrollLeft = container.scrollLeft;\n    let showLeftMask = false;\n    let showRightMask = false;\n    if (isRTL) {\n      /**\n       * RTL模式下，只要滑动过，scrollLeft就再也回不到0（chrome是0.5）\n       * 所以要加round才能终止触发条件\n       * round(443.5) + 375 < 819\n       */\n      showLeftMask = Math.round(-scrollLeft) + container.offsetWidth < container.scrollWidth;\n      showRightMask = scrollLeft < 0;\n    } else {\n      showLeftMask = scrollLeft > 0;\n      showRightMask = scrollLeft + container.offsetWidth < container.scrollWidth;\n    }\n    maskApi.start({\n      leftMaskOpacity: showLeftMask ? 1 : 0,\n      rightMaskOpacity: showRightMask ? 1 : 0,\n      immediate\n    });\n  }, {\n    wait: 100,\n    trailing: true,\n    leading: true\n  });\n  useIsomorphicLayoutEffect(() => {\n    updateMask(true);\n  }, []);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix,\n    style: {\n      direction: props.direction\n    }\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-header`\n  }, React.createElement(animated.div, {\n    className: classNames(`${classPrefix}-header-mask`, `${classPrefix}-header-mask-left`),\n    style: {\n      opacity: leftMaskOpacity\n    }\n  }), React.createElement(animated.div, {\n    className: classNames(`${classPrefix}-header-mask`, `${classPrefix}-header-mask-right`),\n    style: {\n      opacity: rightMaskOpacity\n    }\n  }), React.createElement(animated.div, {\n    className: `${classPrefix}-tab-list`,\n    ref: tabListContainerRef,\n    scrollLeft: scrollLeft,\n    onScroll: updateMask,\n    role: 'tablist'\n  }, React.createElement(animated.div, {\n    ref: activeLineRef,\n    className: `${classPrefix}-tab-line`,\n    style: {\n      width: props.activeLineMode === 'fixed' ? 'var(--fixed-active-line-width, 30px)' : width,\n      x\n    }\n  }), panes.map(pane => withNativeProps(pane.props, React.createElement(\"div\", {\n    key: pane.key,\n    className: classNames(`${classPrefix}-tab-wrapper`, {\n      [`${classPrefix}-tab-wrapper-stretch`]: props.stretch\n    })\n  }, React.createElement(\"div\", {\n    onClick: () => {\n      const {\n        key\n      } = pane;\n      if (pane.props.disabled) return;\n      if (key === undefined || key === null) {\n        return;\n      }\n      setActiveKey(key.toString());\n    },\n    className: classNames(`${classPrefix}-tab`, {\n      [`${classPrefix}-tab-active`]: pane.key === activeKey,\n      [`${classPrefix}-tab-disabled`]: pane.props.disabled\n    }),\n    role: 'tab',\n    \"aria-selected\": pane.key === activeKey\n  }, pane.props.title)))))), panes.map(pane => {\n    if (pane.props.children === undefined) {\n      return null;\n    }\n    const active = pane.key === activeKey;\n    return React.createElement(ShouldRender, {\n      key: pane.key,\n      active: active,\n      forceRender: pane.props.forceRender,\n      destroyOnClose: pane.props.destroyOnClose\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-content`,\n      style: {\n        display: active ? 'block' : 'none'\n      }\n    }, pane.props.children));\n  })));\n};"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,cAAc,EAAEC,MAAM,QAAQ,OAAO;AACrD,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,mBAAmB;AACvD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,KAAK,QAAQ,mBAAmB;AACzC,SAASC,aAAa,EAAEC,yBAAyB,QAAQ,QAAQ;AACjE,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,+BAA+B,QAAQ,iDAAiD;AACjG,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,MAAMC,WAAW,GAAG,UAAU;AAC9B,OAAO,MAAMC,GAAG,GAAGA,CAAA,KAAM;EACvB,OAAO,IAAI;AACb,CAAC;AACD,MAAMC,YAAY,GAAG;EACnBC,cAAc,EAAE,MAAM;EACtBC,OAAO,EAAE,IAAI;EACbC,SAAS,EAAE;AACb,CAAC;AACD,OAAO,MAAMC,IAAI,GAAGC,CAAC,IAAI;EACvB,IAAIC,EAAE;EACN,MAAMC,KAAK,GAAGb,UAAU,CAACM,YAAY,EAAEK,CAAC,CAAC;EACzC,MAAMG,mBAAmB,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM0B,aAAa,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM2B,gBAAgB,GAAG,CAAC,CAAC;EAC3B,IAAIC,cAAc,GAAG,IAAI;EACzB,MAAMC,KAAK,GAAG,EAAE;EAChB,MAAMC,KAAK,GAAGN,KAAK,CAACJ,SAAS,KAAK,KAAK;EACvCN,iBAAiB,CAACU,KAAK,CAACO,QAAQ,EAAE,CAACC,KAAK,EAAEC,KAAK,KAAK;IAClD,IAAI,CAAClC,cAAc,CAACiC,KAAK,CAAC,EAAE;IAC5B,MAAME,GAAG,GAAGF,KAAK,CAACE,GAAG;IACrB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC7B,IAAID,KAAK,KAAK,CAAC,EAAE;MACfL,cAAc,GAAGM,GAAG;IACtB;IACA,MAAMC,MAAM,GAAGN,KAAK,CAACO,IAAI,CAACJ,KAAK,CAAC;IAChCL,gBAAgB,CAACO,GAAG,CAAC,GAAGC,MAAM,GAAG,CAAC;EACpC,CAAC,CAAC;EACF,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAGjC,aAAa,CAAC;IAC9CkC,KAAK,EAAEf,KAAK,CAACa,SAAS;IACtBG,YAAY,EAAE,CAACjB,EAAE,GAAGC,KAAK,CAACiB,gBAAgB,MAAM,IAAI,IAAIlB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGK,cAAc;IAC3Fc,QAAQ,EAAEC,CAAC,IAAI;MACb,IAAIpB,EAAE;MACN,IAAIoB,CAAC,KAAK,IAAI,EAAE;MAChB,CAACpB,EAAE,GAAGC,KAAK,CAACkB,QAAQ,MAAM,IAAI,IAAInB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqB,IAAI,CAACpB,KAAK,EAAEmB,CAAC,CAAC;IAC9E;EACF,CAAC,CAAC;EACF,MAAM,CAAC;IACLE,CAAC;IACDC;EACF,CAAC,EAAEC,MAAM,CAAC,GAAG7C,SAAS,CAAC,OAAO;IAC5B2C,CAAC,EAAE,CAAC;IACJC,KAAK,EAAE,CAAC;IACRE,MAAM,EAAE;MACNC,OAAO,EAAE,GAAG;MACZC,KAAK,EAAE;IACT;EACF,CAAC,CAAC,CAAC;EACH,MAAM,CAAC;IACLC;EACF,CAAC,EAAEC,SAAS,CAAC,GAAGlD,SAAS,CAAC,OAAO;IAC/BiD,UAAU,EAAE,CAAC;IACbH,MAAM,EAAE;MACNC,OAAO,EAAE,GAAG;MACZC,KAAK,EAAE;IACT;EACF,CAAC,CAAC,CAAC;EACH,MAAM,CAAC;IACLG,eAAe;IACfC;EACF,CAAC,EAAEC,OAAO,CAAC,GAAGrD,SAAS,CAAC,OAAO;IAC7BmD,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBN,MAAM,EAAE;MACNE,KAAK,EAAE;IACT;EACF,CAAC,CAAC,CAAC;EACH,SAASM,OAAOA,CAACC,SAAS,GAAG,KAAK,EAAEC,YAAY,GAAG,KAAK,EAAE;IACxD,MAAMC,SAAS,GAAGlC,mBAAmB,CAACmC,OAAO;IAC7C,IAAI,CAACD,SAAS,EAAE;IAChB,MAAME,WAAW,GAAGlC,gBAAgB,CAACU,SAAS,CAAC;IAC/C,IAAIwB,WAAW,KAAKC,SAAS,EAAE;MAC7Bf,MAAM,CAACgB,KAAK,CAAC;QACXlB,CAAC,EAAE,CAAC;QACJC,KAAK,EAAE,CAAC;QACRW,SAAS,EAAE;MACb,CAAC,CAAC;MACF;IACF;IACA,MAAMO,UAAU,GAAGtC,aAAa,CAACkC,OAAO;IACxC,IAAI,CAACI,UAAU,EAAE;IACjB,MAAMC,gBAAgB,GAAGN,SAAS,CAAC5B,QAAQ,CAACmC,IAAI,CAACL,WAAW,GAAG,CAAC,CAAC;IACjE,MAAMM,SAAS,GAAGF,gBAAgB,CAAClC,QAAQ,CAACmC,IAAI,CAAC,CAAC,CAAC;IACnD,MAAME,aAAa,GAAGD,SAAS,CAACE,UAAU;IAC1C,MAAMC,cAAc,GAAGH,SAAS,CAACI,WAAW;IAC5C,MAAMC,oBAAoB,GAAGP,gBAAgB,CAACI,UAAU;IACxD,MAAMI,qBAAqB,GAAGR,gBAAgB,CAACM,WAAW;IAC1D,MAAMG,cAAc,GAAGf,SAAS,CAACY,WAAW;IAC5C,MAAMI,oBAAoB,GAAGhB,SAAS,CAACiB,WAAW;IAClD,MAAMC,mBAAmB,GAAGlB,SAAS,CAACR,UAAU;IAChD,MAAM2B,eAAe,GAAGd,UAAU,CAACO,WAAW;IAC9C,IAAI1B,CAAC,GAAG,CAAC;IACT,IAAIC,KAAK,GAAG,CAAC;IACb,IAAItB,KAAK,CAACN,cAAc,KAAK,MAAM,EAAE;MACnC2B,CAAC,GAAGuB,aAAa;MACjBtB,KAAK,GAAGwB,cAAc;IACxB,CAAC,MAAM,IAAI9C,KAAK,CAACN,cAAc,KAAK,MAAM,EAAE;MAC1C2B,CAAC,GAAG2B,oBAAoB;MACxB1B,KAAK,GAAG2B,qBAAqB;IAC/B,CAAC,MAAM;MACL5B,CAAC,GAAGuB,aAAa,GAAG,CAACE,cAAc,GAAGQ,eAAe,IAAI,CAAC;IAC5D;IACA,IAAIhD,KAAK,EAAE;MACT;AACN;AACA;AACA;MACM,MAAMiD,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACxD,KAAK,CAACN,cAAc,CAAC,GAAG4B,KAAK,GAAGgC,eAAe;MACnFjC,CAAC,GAAG,EAAE6B,cAAc,GAAG7B,CAAC,GAAGkC,CAAC,CAAC;IAC/B;IACAhC,MAAM,CAACgB,KAAK,CAAC;MACXlB,CAAC;MACDC,KAAK;MACLW;IACF,CAAC,CAAC;IACF,MAAMwB,iBAAiB,GAAGN,oBAAoB,GAAGD,cAAc;IAC/D,IAAIO,iBAAiB,IAAI,CAAC,EAAE;IAC5B,IAAIC,cAAc,GAAG,CAAC;IACtB,IAAIpD,KAAK,EAAE;MACT;AACN;AACA;AACA;MACMoD,cAAc,GAAG,CAAC5E,KAAK,CAACoE,cAAc,GAAG,CAAC,GAAGN,aAAa,GAAGE,cAAc,GAAG,CAAC,GAAGQ,eAAe,EAAE,CAAC,EAAEG,iBAAiB,CAAC;IAC1H,CAAC,MAAM;MACLC,cAAc,GAAG5E,KAAK,CAAC8D,aAAa,GAAG,CAACM,cAAc,GAAGJ,cAAc,IAAI,CAAC,EAAE,CAAC,EAAEW,iBAAiB,CAAC;IACrG;IACA,IAAI,CAACvB,YAAY,IAAIlC,KAAK,CAAC2D,UAAU,KAAK,KAAK,EAAE;MAC/C/B,SAAS,CAACW,KAAK,CAAC;QACdZ,UAAU,EAAE+B,cAAc;QAC1BE,IAAI,EAAE;UACJjC,UAAU,EAAE0B;QACd,CAAC;QACDpB;MACF,CAAC,CAAC;IACJ;EACF;EACAjD,yBAAyB,CAAC,MAAM;IAC9BgD,OAAO,CAAC,CAACX,CAAC,CAACwC,WAAW,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EACNzE,+BAA+B,CAAC,MAAM;IACpC4C,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,CAACnB,SAAS,EAAEP,KAAK,EAAEN,KAAK,CAACN,cAAc,CAAC,CAAC;EAC5CR,eAAe,CAAC,MAAM;IACpB8C,OAAO,CAAC,CAACX,CAAC,CAACwC,WAAW,CAAC;EACzB,CAAC,EAAE5D,mBAAmB,CAAC;EACvBhB,iBAAiB,CAAC,MAAM;IACtB+C,OAAO,CAAC,CAACX,CAAC,CAACwC,WAAW,EAAE,IAAI,CAAC;EAC/B,CAAC,EAAE5D,mBAAmB,EAAE;IACtB6D,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM;IACJC,GAAG,EAAEC;EACP,CAAC,GAAGnF,aAAa,CAAC,CAACkD,SAAS,GAAG,KAAK,KAAK;IACvC,MAAME,SAAS,GAAGlC,mBAAmB,CAACmC,OAAO;IAC7C,IAAI,CAACD,SAAS,EAAE;IAChB,MAAMR,UAAU,GAAGQ,SAAS,CAACR,UAAU;IACvC,IAAIwC,YAAY,GAAG,KAAK;IACxB,IAAIC,aAAa,GAAG,KAAK;IACzB,IAAI9D,KAAK,EAAE;MACT;AACN;AACA;AACA;AACA;MACM6D,YAAY,GAAGE,IAAI,CAACC,KAAK,CAAC,CAAC3C,UAAU,CAAC,GAAGQ,SAAS,CAACY,WAAW,GAAGZ,SAAS,CAACiB,WAAW;MACtFgB,aAAa,GAAGzC,UAAU,GAAG,CAAC;IAChC,CAAC,MAAM;MACLwC,YAAY,GAAGxC,UAAU,GAAG,CAAC;MAC7ByC,aAAa,GAAGzC,UAAU,GAAGQ,SAAS,CAACY,WAAW,GAAGZ,SAAS,CAACiB,WAAW;IAC5E;IACArB,OAAO,CAACQ,KAAK,CAAC;MACZV,eAAe,EAAEsC,YAAY,GAAG,CAAC,GAAG,CAAC;MACrCrC,gBAAgB,EAAEsC,aAAa,GAAG,CAAC,GAAG,CAAC;MACvCnC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE;IACDsC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE;EACX,CAAC,CAAC;EACFzF,yBAAyB,CAAC,MAAM;IAC9BkF,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EACN,OAAOtF,eAAe,CAACoB,KAAK,EAAE1B,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEpF,WAAW;IACtBqF,KAAK,EAAE;MACLhF,SAAS,EAAEI,KAAK,CAACJ;IACnB;EACF,CAAC,EAAEtB,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGpF,WAAW;EAC3B,CAAC,EAAEjB,KAAK,CAACoG,aAAa,CAAC/F,QAAQ,CAACkG,GAAG,EAAE;IACnCF,SAAS,EAAElG,UAAU,CAAC,GAAGc,WAAW,cAAc,EAAE,GAAGA,WAAW,mBAAmB,CAAC;IACtFqF,KAAK,EAAE;MACLE,OAAO,EAAEjD;IACX;EACF,CAAC,CAAC,EAAEvD,KAAK,CAACoG,aAAa,CAAC/F,QAAQ,CAACkG,GAAG,EAAE;IACpCF,SAAS,EAAElG,UAAU,CAAC,GAAGc,WAAW,cAAc,EAAE,GAAGA,WAAW,oBAAoB,CAAC;IACvFqF,KAAK,EAAE;MACLE,OAAO,EAAEhD;IACX;EACF,CAAC,CAAC,EAAExD,KAAK,CAACoG,aAAa,CAAC/F,QAAQ,CAACkG,GAAG,EAAE;IACpCF,SAAS,EAAE,GAAGpF,WAAW,WAAW;IACpCwF,GAAG,EAAE9E,mBAAmB;IACxB0B,UAAU,EAAEA,UAAU;IACtBqD,QAAQ,EAAEd,UAAU;IACpBe,IAAI,EAAE;EACR,CAAC,EAAE3G,KAAK,CAACoG,aAAa,CAAC/F,QAAQ,CAACkG,GAAG,EAAE;IACnCE,GAAG,EAAE7E,aAAa;IAClByE,SAAS,EAAE,GAAGpF,WAAW,WAAW;IACpCqF,KAAK,EAAE;MACLtD,KAAK,EAAEtB,KAAK,CAACN,cAAc,KAAK,OAAO,GAAG,sCAAsC,GAAG4B,KAAK;MACxFD;IACF;EACF,CAAC,CAAC,EAAEhB,KAAK,CAAC6E,GAAG,CAACC,IAAI,IAAIvG,eAAe,CAACuG,IAAI,CAACnF,KAAK,EAAE1B,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAE;IAC3EhE,GAAG,EAAEyE,IAAI,CAACzE,GAAG;IACbiE,SAAS,EAAElG,UAAU,CAAC,GAAGc,WAAW,cAAc,EAAE;MAClD,CAAC,GAAGA,WAAW,sBAAsB,GAAGS,KAAK,CAACL;IAChD,CAAC;EACH,CAAC,EAAErB,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAE;IAC5BU,OAAO,EAAEA,CAAA,KAAM;MACb,MAAM;QACJ1E;MACF,CAAC,GAAGyE,IAAI;MACR,IAAIA,IAAI,CAACnF,KAAK,CAACqF,QAAQ,EAAE;MACzB,IAAI3E,GAAG,KAAK4B,SAAS,IAAI5B,GAAG,KAAK,IAAI,EAAE;QACrC;MACF;MACAI,YAAY,CAACJ,GAAG,CAAC4E,QAAQ,CAAC,CAAC,CAAC;IAC9B,CAAC;IACDX,SAAS,EAAElG,UAAU,CAAC,GAAGc,WAAW,MAAM,EAAE;MAC1C,CAAC,GAAGA,WAAW,aAAa,GAAG4F,IAAI,CAACzE,GAAG,KAAKG,SAAS;MACrD,CAAC,GAAGtB,WAAW,eAAe,GAAG4F,IAAI,CAACnF,KAAK,CAACqF;IAC9C,CAAC,CAAC;IACFJ,IAAI,EAAE,KAAK;IACX,eAAe,EAAEE,IAAI,CAACzE,GAAG,KAAKG;EAChC,CAAC,EAAEsE,IAAI,CAACnF,KAAK,CAACuF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAElF,KAAK,CAAC6E,GAAG,CAACC,IAAI,IAAI;IAC3C,IAAIA,IAAI,CAACnF,KAAK,CAACO,QAAQ,KAAK+B,SAAS,EAAE;MACrC,OAAO,IAAI;IACb;IACA,MAAMkD,MAAM,GAAGL,IAAI,CAACzE,GAAG,KAAKG,SAAS;IACrC,OAAOvC,KAAK,CAACoG,aAAa,CAACrF,YAAY,EAAE;MACvCqB,GAAG,EAAEyE,IAAI,CAACzE,GAAG;MACb8E,MAAM,EAAEA,MAAM;MACdC,WAAW,EAAEN,IAAI,CAACnF,KAAK,CAACyF,WAAW;MACnCC,cAAc,EAAEP,IAAI,CAACnF,KAAK,CAAC0F;IAC7B,CAAC,EAAEpH,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAE;MAC5BC,SAAS,EAAE,GAAGpF,WAAW,UAAU;MACnCqF,KAAK,EAAE;QACLe,OAAO,EAAEH,MAAM,GAAG,OAAO,GAAG;MAC9B;IACF,CAAC,EAAEL,IAAI,CAACnF,KAAK,CAACO,QAAQ,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}