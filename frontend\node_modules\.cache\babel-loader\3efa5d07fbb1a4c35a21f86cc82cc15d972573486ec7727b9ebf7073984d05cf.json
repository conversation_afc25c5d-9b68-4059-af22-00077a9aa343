{"ast": null, "code": "import * as React from \"react\";\nfunction StarOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"StarOutline-StarOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"StarOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"StarOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M25.845596,4.44900551 C26.6681364,4.87129848 27.3339158,5.56388467 27.7398638,6.41954369 L31.6608321,14.6841788 L40.4283838,16.0094747 C42.70793,16.354049 44.2873463,18.5557239 43.9561089,20.9270533 C43.8242084,21.8713291 43.3967517,22.7440386 42.7399141,23.4100762 L36.3956541,29.8431975 L37.8933307,38.9269105 C38.2827237,41.2886581 36.7579187,43.531608 34.4875834,43.9366794 C33.5835239,44.097981 32.6535618,43.9447589 31.8416658,43.5007338 L23.9997292,39.2119863 L16.1577926,43.5007338 C14.1189044,44.6157998 11.5971068,43.8003441 10.5251999,41.6793627 C10.0983609,40.8347767 9.95106936,39.8673708 10.1061277,38.9269105 L11.6038043,29.8431975 L5.25954433,23.4100762 C3.61004918,21.7374773 3.57629754,18.9905481 5.18415792,17.274638 C5.82441637,16.5913537 6.66334736,16.146686 7.57107463,16.0094747 L16.3386263,14.6841788 L20.2595946,6.41954369 C21.2790387,4.27075478 23.7799767,3.38851478 25.845596,4.44900551 Z M24.4649084,7.19036662 C23.9817424,6.94156666 23.4040248,7.11599174 23.1183508,7.57685715 L23.0577862,7.68823318 L18.3736364,17.5910426 L7.89955916,19.179032 C7.67090127,19.2136991 7.45957327,19.3260467 7.29829119,19.498682 C6.92442428,19.8988667 6.90290967,20.5210094 7.2286221,20.9467347 L7.31728114,21.0488319 L14.8963946,28.757094 L13.1072086,39.6413349 C13.0681492,39.878947 13.1052522,40.1233671 13.2127736,40.336756 C13.4635018,40.8343559 14.0291474,41.0475463 14.5198909,40.8498968 L14.6316323,40.7969348 L23.9999318,35.6580933 L33.3682313,40.7969348 C33.5727491,40.90912 33.807008,40.9478323 34.034742,40.9070787 C34.5657928,40.8120455 34.9349139,40.3166295 34.9064008,39.7685831 L34.8926549,39.6413349 L33.1034689,28.757094 L40.6825824,21.0488319 C40.8480408,20.880554 40.9557178,20.6600595 40.9889438,20.4214834 C41.066423,19.8651498 40.7289068,19.3457755 40.2205412,19.2047137 L40.1003044,19.179032 L29.6262272,17.5910426 L24.9420774,7.68823318 C24.8602703,7.51528387 24.7365739,7.36870333 24.5842817,7.2623615 L24.4649084,7.19036662 Z\",\n    id: \"StarOutline-\\u661F\\u5F62\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default StarOutline;", "map": {"version": 3, "names": ["React", "StarOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/StarOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction StarOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"StarOutline-StarOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"StarOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"StarOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M25.845596,4.44900551 C26.6681364,4.87129848 27.3339158,5.56388467 27.7398638,6.41954369 L31.6608321,14.6841788 L40.4283838,16.0094747 C42.70793,16.354049 44.2873463,18.5557239 43.9561089,20.9270533 C43.8242084,21.8713291 43.3967517,22.7440386 42.7399141,23.4100762 L36.3956541,29.8431975 L37.8933307,38.9269105 C38.2827237,41.2886581 36.7579187,43.531608 34.4875834,43.9366794 C33.5835239,44.097981 32.6535618,43.9447589 31.8416658,43.5007338 L23.9997292,39.2119863 L16.1577926,43.5007338 C14.1189044,44.6157998 11.5971068,43.8003441 10.5251999,41.6793627 C10.0983609,40.8347767 9.95106936,39.8673708 10.1061277,38.9269105 L11.6038043,29.8431975 L5.25954433,23.4100762 C3.61004918,21.7374773 3.57629754,18.9905481 5.18415792,17.274638 C5.82441637,16.5913537 6.66334736,16.146686 7.57107463,16.0094747 L16.3386263,14.6841788 L20.2595946,6.41954369 C21.2790387,4.27075478 23.7799767,3.38851478 25.845596,4.44900551 Z M24.4649084,7.19036662 C23.9817424,6.94156666 23.4040248,7.11599174 23.1183508,7.57685715 L23.0577862,7.68823318 L18.3736364,17.5910426 L7.89955916,19.179032 C7.67090127,19.2136991 7.45957327,19.3260467 7.29829119,19.498682 C6.92442428,19.8988667 6.90290967,20.5210094 7.2286221,20.9467347 L7.31728114,21.0488319 L14.8963946,28.757094 L13.1072086,39.6413349 C13.0681492,39.878947 13.1052522,40.1233671 13.2127736,40.336756 C13.4635018,40.8343559 14.0291474,41.0475463 14.5198909,40.8498968 L14.6316323,40.7969348 L23.9999318,35.6580933 L33.3682313,40.7969348 C33.5727491,40.90912 33.807008,40.9478323 34.034742,40.9070787 C34.5657928,40.8120455 34.9349139,40.3166295 34.9064008,39.7685831 L34.8926549,39.6413349 L33.1034689,28.757094 L40.6825824,21.0488319 C40.8480408,20.880554 40.9557178,20.6600595 40.9889438,20.4214834 C41.066423,19.8651498 40.7289068,19.3457755 40.2205412,19.2047137 L40.1003044,19.179032 L29.6262272,17.5910426 L24.9420774,7.68823318 C24.8602703,7.51528387 24.7365739,7.36870333 24.5842817,7.2623615 L24.4649084,7.19036662 Z\",\n    id: \"StarOutline-\\u661F\\u5F62\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default StarOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yBAAyB;IAC7BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,66DAA66D;IACh7DR,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}