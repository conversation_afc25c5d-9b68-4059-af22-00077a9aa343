{"ast": null, "code": "import { animated, useSpring } from '@react-spring/web';\nimport { useDrag } from '@use-gesture/react';\nimport { useMemoizedFn } from 'ahooks';\nimport classNames from 'classnames';\nimport React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { nearest } from '../../utils/nearest';\nimport { supportsPassive } from '../../utils/supports-passive';\nimport { useLockScroll } from '../../utils/use-lock-scroll';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = 'adm-floating-panel';\nconst defaultProps = {\n  handleDraggingOfContent: true\n};\nexport const FloatingPanel = forwardRef((p, ref) => {\n  var _a, _b;\n  const props = mergeProps(defaultProps, p);\n  const {\n    anchors,\n    placement = 'bottom'\n  } = props;\n  const maxHeight = (_a = anchors[anchors.length - 1]) !== null && _a !== void 0 ? _a : window.innerHeight;\n  const isBottomPlacement = placement !== 'top';\n  const possibles = isBottomPlacement ? anchors.map(x => -x) : anchors;\n  const elementRef = useRef(null);\n  const headerRef = useRef(null);\n  const contentRef = useRef(null);\n  const [pulling, setPulling] = useState(false);\n  const pullingRef = useRef(false);\n  const bounds = {\n    top: Math.min(...possibles),\n    bottom: Math.max(...possibles)\n  };\n  const onHeightChange = useMemoizedFn((_b = props.onHeightChange) !== null && _b !== void 0 ? _b : () => {});\n  const [{\n    y\n  }, api] = useSpring(() => ({\n    y: isBottomPlacement ? bounds.bottom : bounds.top,\n    config: {\n      tension: 300\n    },\n    onChange: result => {\n      onHeightChange(-result.value.y, y.isAnimating);\n    }\n  }));\n  useDrag(state => {\n    const [, offsetY] = state.offset;\n    if (state.first) {\n      const target = state.event.target;\n      const header = headerRef.current;\n      if (header === target || (header === null || header === void 0 ? void 0 : header.contains(target))) {\n        pullingRef.current = true;\n      } else {\n        if (!props.handleDraggingOfContent) return;\n        const reachedTop = y.goal <= bounds.top;\n        const content = contentRef.current;\n        if (!content) return;\n        if (reachedTop) {\n          if (content.scrollTop <= 0 && state.direction[1] > 0) {\n            pullingRef.current = true;\n          }\n        } else {\n          pullingRef.current = true;\n        }\n      }\n    }\n    setPulling(pullingRef.current);\n    if (!pullingRef.current) return;\n    const {\n      event\n    } = state;\n    if (event.cancelable && supportsPassive) {\n      event.preventDefault();\n    }\n    event.stopPropagation();\n    let nextY = offsetY;\n    if (state.last) {\n      pullingRef.current = false;\n      setPulling(false);\n      nextY = nearest(possibles, offsetY);\n    }\n    api.start({\n      y: nextY\n    });\n  }, {\n    axis: 'y',\n    bounds,\n    rubberband: true,\n    from: () => [0, y.get()],\n    pointer: {\n      touch: true\n    },\n    target: elementRef,\n    eventOptions: supportsPassive ? {\n      passive: false\n    } : undefined\n  });\n  useImperativeHandle(ref, () => ({\n    setHeight: (height, options) => {\n      api.start({\n        y: -height,\n        immediate: options === null || options === void 0 ? void 0 : options.immediate\n      });\n    }\n  }), [api]);\n  useLockScroll(elementRef, true);\n  const HeaderNode = React.createElement(\"div\", {\n    className: `${classPrefix}-header`,\n    ref: headerRef\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-bar`\n  }));\n  return withNativeProps(props, React.createElement(animated.div, {\n    ref: elementRef,\n    className: classNames(classPrefix, `${classPrefix}-${placement}`),\n    style: {\n      height: Math.round(maxHeight),\n      translateY: y.to(y => {\n        if (isBottomPlacement) {\n          return `calc(100% + (${Math.round(y)}px))`;\n        }\n        if (placement === 'top') {\n          return `calc(-100% + (${Math.round(y)}px))`;\n        }\n        return y;\n      })\n    }\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-mask`,\n    style: {\n      display: pulling ? 'block' : 'none'\n    }\n  }), isBottomPlacement && HeaderNode, React.createElement(\"div\", {\n    className: `${classPrefix}-content`,\n    ref: contentRef\n  }, props.children), placement === 'top' && HeaderNode));\n});", "map": {"version": 3, "names": ["animated", "useSpring", "useDrag", "useMemoizedFn", "classNames", "React", "forwardRef", "useImperativeHandle", "useRef", "useState", "withNativeProps", "nearest", "supportsPassive", "useLockScroll", "mergeProps", "classPrefix", "defaultProps", "handleDraggingOfContent", "FloatingPanel", "p", "ref", "_a", "_b", "props", "anchors", "placement", "maxHeight", "length", "window", "innerHeight", "isBottomPlacement", "possibles", "map", "x", "elementRef", "headerRef", "contentRef", "pulling", "setPulling", "pullingRef", "bounds", "top", "Math", "min", "bottom", "max", "onHeightChange", "y", "api", "config", "tension", "onChange", "result", "value", "isAnimating", "state", "offsetY", "offset", "first", "target", "event", "header", "current", "contains", "reachedTop", "goal", "content", "scrollTop", "direction", "cancelable", "preventDefault", "stopPropagation", "nextY", "last", "start", "axis", "rubberband", "from", "get", "pointer", "touch", "eventOptions", "passive", "undefined", "setHeight", "height", "options", "immediate", "HeaderNode", "createElement", "className", "div", "style", "round", "translateY", "to", "display", "children"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/floating-panel/floating-panel.js"], "sourcesContent": ["import { animated, useSpring } from '@react-spring/web';\nimport { useDrag } from '@use-gesture/react';\nimport { useMemoizedFn } from 'ahooks';\nimport classNames from 'classnames';\nimport React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { nearest } from '../../utils/nearest';\nimport { supportsPassive } from '../../utils/supports-passive';\nimport { useLockScroll } from '../../utils/use-lock-scroll';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = 'adm-floating-panel';\nconst defaultProps = {\n  handleDraggingOfContent: true\n};\nexport const FloatingPanel = forwardRef((p, ref) => {\n  var _a, _b;\n  const props = mergeProps(defaultProps, p);\n  const {\n    anchors,\n    placement = 'bottom'\n  } = props;\n  const maxHeight = (_a = anchors[anchors.length - 1]) !== null && _a !== void 0 ? _a : window.innerHeight;\n  const isBottomPlacement = placement !== 'top';\n  const possibles = isBottomPlacement ? anchors.map(x => -x) : anchors;\n  const elementRef = useRef(null);\n  const headerRef = useRef(null);\n  const contentRef = useRef(null);\n  const [pulling, setPulling] = useState(false);\n  const pullingRef = useRef(false);\n  const bounds = {\n    top: Math.min(...possibles),\n    bottom: Math.max(...possibles)\n  };\n  const onHeightChange = useMemoizedFn((_b = props.onHeightChange) !== null && _b !== void 0 ? _b : () => {});\n  const [{\n    y\n  }, api] = useSpring(() => ({\n    y: isBottomPlacement ? bounds.bottom : bounds.top,\n    config: {\n      tension: 300\n    },\n    onChange: result => {\n      onHeightChange(-result.value.y, y.isAnimating);\n    }\n  }));\n  useDrag(state => {\n    const [, offsetY] = state.offset;\n    if (state.first) {\n      const target = state.event.target;\n      const header = headerRef.current;\n      if (header === target || (header === null || header === void 0 ? void 0 : header.contains(target))) {\n        pullingRef.current = true;\n      } else {\n        if (!props.handleDraggingOfContent) return;\n        const reachedTop = y.goal <= bounds.top;\n        const content = contentRef.current;\n        if (!content) return;\n        if (reachedTop) {\n          if (content.scrollTop <= 0 && state.direction[1] > 0) {\n            pullingRef.current = true;\n          }\n        } else {\n          pullingRef.current = true;\n        }\n      }\n    }\n    setPulling(pullingRef.current);\n    if (!pullingRef.current) return;\n    const {\n      event\n    } = state;\n    if (event.cancelable && supportsPassive) {\n      event.preventDefault();\n    }\n    event.stopPropagation();\n    let nextY = offsetY;\n    if (state.last) {\n      pullingRef.current = false;\n      setPulling(false);\n      nextY = nearest(possibles, offsetY);\n    }\n    api.start({\n      y: nextY\n    });\n  }, {\n    axis: 'y',\n    bounds,\n    rubberband: true,\n    from: () => [0, y.get()],\n    pointer: {\n      touch: true\n    },\n    target: elementRef,\n    eventOptions: supportsPassive ? {\n      passive: false\n    } : undefined\n  });\n  useImperativeHandle(ref, () => ({\n    setHeight: (height, options) => {\n      api.start({\n        y: -height,\n        immediate: options === null || options === void 0 ? void 0 : options.immediate\n      });\n    }\n  }), [api]);\n  useLockScroll(elementRef, true);\n  const HeaderNode = React.createElement(\"div\", {\n    className: `${classPrefix}-header`,\n    ref: headerRef\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-bar`\n  }));\n  return withNativeProps(props, React.createElement(animated.div, {\n    ref: elementRef,\n    className: classNames(classPrefix, `${classPrefix}-${placement}`),\n    style: {\n      height: Math.round(maxHeight),\n      translateY: y.to(y => {\n        if (isBottomPlacement) {\n          return `calc(100% + (${Math.round(y)}px))`;\n        }\n        if (placement === 'top') {\n          return `calc(-100% + (${Math.round(y)}px))`;\n        }\n        return y;\n      })\n    }\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-mask`,\n    style: {\n      display: pulling ? 'block' : 'none'\n    }\n  }), isBottomPlacement && HeaderNode, React.createElement(\"div\", {\n    className: `${classPrefix}-content`,\n    ref: contentRef\n  }, props.children), placement === 'top' && HeaderNode));\n});"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,mBAAmB;AACvD,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,aAAa,QAAQ,QAAQ;AACtC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,UAAU,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAChF,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,MAAMC,WAAW,GAAG,oBAAoB;AACxC,MAAMC,YAAY,GAAG;EACnBC,uBAAuB,EAAE;AAC3B,CAAC;AACD,OAAO,MAAMC,aAAa,GAAGZ,UAAU,CAAC,CAACa,CAAC,EAAEC,GAAG,KAAK;EAClD,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAMC,KAAK,GAAGT,UAAU,CAACE,YAAY,EAAEG,CAAC,CAAC;EACzC,MAAM;IACJK,OAAO;IACPC,SAAS,GAAG;EACd,CAAC,GAAGF,KAAK;EACT,MAAMG,SAAS,GAAG,CAACL,EAAE,GAAGG,OAAO,CAACA,OAAO,CAACG,MAAM,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGO,MAAM,CAACC,WAAW;EACxG,MAAMC,iBAAiB,GAAGL,SAAS,KAAK,KAAK;EAC7C,MAAMM,SAAS,GAAGD,iBAAiB,GAAGN,OAAO,CAACQ,GAAG,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC,GAAGT,OAAO;EACpE,MAAMU,UAAU,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM2B,SAAS,GAAG3B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM4B,UAAU,GAAG5B,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM8B,UAAU,GAAG/B,MAAM,CAAC,KAAK,CAAC;EAChC,MAAMgC,MAAM,GAAG;IACbC,GAAG,EAAEC,IAAI,CAACC,GAAG,CAAC,GAAGZ,SAAS,CAAC;IAC3Ba,MAAM,EAAEF,IAAI,CAACG,GAAG,CAAC,GAAGd,SAAS;EAC/B,CAAC;EACD,MAAMe,cAAc,GAAG3C,aAAa,CAAC,CAACmB,EAAE,GAAGC,KAAK,CAACuB,cAAc,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC;EAC3G,MAAM,CAAC;IACLyB;EACF,CAAC,EAAEC,GAAG,CAAC,GAAG/C,SAAS,CAAC,OAAO;IACzB8C,CAAC,EAAEjB,iBAAiB,GAAGU,MAAM,CAACI,MAAM,GAAGJ,MAAM,CAACC,GAAG;IACjDQ,MAAM,EAAE;MACNC,OAAO,EAAE;IACX,CAAC;IACDC,QAAQ,EAAEC,MAAM,IAAI;MAClBN,cAAc,CAAC,CAACM,MAAM,CAACC,KAAK,CAACN,CAAC,EAAEA,CAAC,CAACO,WAAW,CAAC;IAChD;EACF,CAAC,CAAC,CAAC;EACHpD,OAAO,CAACqD,KAAK,IAAI;IACf,MAAM,GAAGC,OAAO,CAAC,GAAGD,KAAK,CAACE,MAAM;IAChC,IAAIF,KAAK,CAACG,KAAK,EAAE;MACf,MAAMC,MAAM,GAAGJ,KAAK,CAACK,KAAK,CAACD,MAAM;MACjC,MAAME,MAAM,GAAG1B,SAAS,CAAC2B,OAAO;MAChC,IAAID,MAAM,KAAKF,MAAM,KAAKE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACE,QAAQ,CAACJ,MAAM,CAAC,CAAC,EAAE;QAClGpB,UAAU,CAACuB,OAAO,GAAG,IAAI;MAC3B,CAAC,MAAM;QACL,IAAI,CAACvC,KAAK,CAACN,uBAAuB,EAAE;QACpC,MAAM+C,UAAU,GAAGjB,CAAC,CAACkB,IAAI,IAAIzB,MAAM,CAACC,GAAG;QACvC,MAAMyB,OAAO,GAAG9B,UAAU,CAAC0B,OAAO;QAClC,IAAI,CAACI,OAAO,EAAE;QACd,IAAIF,UAAU,EAAE;UACd,IAAIE,OAAO,CAACC,SAAS,IAAI,CAAC,IAAIZ,KAAK,CAACa,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;YACpD7B,UAAU,CAACuB,OAAO,GAAG,IAAI;UAC3B;QACF,CAAC,MAAM;UACLvB,UAAU,CAACuB,OAAO,GAAG,IAAI;QAC3B;MACF;IACF;IACAxB,UAAU,CAACC,UAAU,CAACuB,OAAO,CAAC;IAC9B,IAAI,CAACvB,UAAU,CAACuB,OAAO,EAAE;IACzB,MAAM;MACJF;IACF,CAAC,GAAGL,KAAK;IACT,IAAIK,KAAK,CAACS,UAAU,IAAIzD,eAAe,EAAE;MACvCgD,KAAK,CAACU,cAAc,CAAC,CAAC;IACxB;IACAV,KAAK,CAACW,eAAe,CAAC,CAAC;IACvB,IAAIC,KAAK,GAAGhB,OAAO;IACnB,IAAID,KAAK,CAACkB,IAAI,EAAE;MACdlC,UAAU,CAACuB,OAAO,GAAG,KAAK;MAC1BxB,UAAU,CAAC,KAAK,CAAC;MACjBkC,KAAK,GAAG7D,OAAO,CAACoB,SAAS,EAAEyB,OAAO,CAAC;IACrC;IACAR,GAAG,CAAC0B,KAAK,CAAC;MACR3B,CAAC,EAAEyB;IACL,CAAC,CAAC;EACJ,CAAC,EAAE;IACDG,IAAI,EAAE,GAAG;IACTnC,MAAM;IACNoC,UAAU,EAAE,IAAI;IAChBC,IAAI,EAAEA,CAAA,KAAM,CAAC,CAAC,EAAE9B,CAAC,CAAC+B,GAAG,CAAC,CAAC,CAAC;IACxBC,OAAO,EAAE;MACPC,KAAK,EAAE;IACT,CAAC;IACDrB,MAAM,EAAEzB,UAAU;IAClB+C,YAAY,EAAErE,eAAe,GAAG;MAC9BsE,OAAO,EAAE;IACX,CAAC,GAAGC;EACN,CAAC,CAAC;EACF5E,mBAAmB,CAACa,GAAG,EAAE,OAAO;IAC9BgE,SAAS,EAAEA,CAACC,MAAM,EAAEC,OAAO,KAAK;MAC9BtC,GAAG,CAAC0B,KAAK,CAAC;QACR3B,CAAC,EAAE,CAACsC,MAAM;QACVE,SAAS,EAAED,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC;MACvE,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,EAAE,CAACvC,GAAG,CAAC,CAAC;EACVnC,aAAa,CAACqB,UAAU,EAAE,IAAI,CAAC;EAC/B,MAAMsD,UAAU,GAAGnF,KAAK,CAACoF,aAAa,CAAC,KAAK,EAAE;IAC5CC,SAAS,EAAE,GAAG3E,WAAW,SAAS;IAClCK,GAAG,EAAEe;EACP,CAAC,EAAE9B,KAAK,CAACoF,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAG3E,WAAW;EAC3B,CAAC,CAAC,CAAC;EACH,OAAOL,eAAe,CAACa,KAAK,EAAElB,KAAK,CAACoF,aAAa,CAACzF,QAAQ,CAAC2F,GAAG,EAAE;IAC9DvE,GAAG,EAAEc,UAAU;IACfwD,SAAS,EAAEtF,UAAU,CAACW,WAAW,EAAE,GAAGA,WAAW,IAAIU,SAAS,EAAE,CAAC;IACjEmE,KAAK,EAAE;MACLP,MAAM,EAAE3C,IAAI,CAACmD,KAAK,CAACnE,SAAS,CAAC;MAC7BoE,UAAU,EAAE/C,CAAC,CAACgD,EAAE,CAAChD,CAAC,IAAI;QACpB,IAAIjB,iBAAiB,EAAE;UACrB,OAAO,gBAAgBY,IAAI,CAACmD,KAAK,CAAC9C,CAAC,CAAC,MAAM;QAC5C;QACA,IAAItB,SAAS,KAAK,KAAK,EAAE;UACvB,OAAO,iBAAiBiB,IAAI,CAACmD,KAAK,CAAC9C,CAAC,CAAC,MAAM;QAC7C;QACA,OAAOA,CAAC;MACV,CAAC;IACH;EACF,CAAC,EAAE1C,KAAK,CAACoF,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAG3E,WAAW,OAAO;IAChC6E,KAAK,EAAE;MACLI,OAAO,EAAE3D,OAAO,GAAG,OAAO,GAAG;IAC/B;EACF,CAAC,CAAC,EAAEP,iBAAiB,IAAI0D,UAAU,EAAEnF,KAAK,CAACoF,aAAa,CAAC,KAAK,EAAE;IAC9DC,SAAS,EAAE,GAAG3E,WAAW,UAAU;IACnCK,GAAG,EAAEgB;EACP,CAAC,EAAEb,KAAK,CAAC0E,QAAQ,CAAC,EAAExE,SAAS,KAAK,KAAK,IAAI+D,UAAU,CAAC,CAAC;AACzD,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}