{"ast": null, "code": "import { canUseDom } from './can-use-dom';\nimport { isDev } from './is-dev';\nimport { devError } from './dev-log';\nlet tenPxTester = null;\nlet tester = null;\nif (canUseDom) {\n  tenPxTester = document.createElement('div');\n  tenPxTester.className = 'adm-px-tester';\n  tenPxTester.style.setProperty('--size', '10');\n  document.body.appendChild(tenPxTester);\n  tester = document.createElement('div');\n  tester.className = 'adm-px-tester';\n  document.body.appendChild(tester);\n  if (isDev) {\n    if (window.getComputedStyle(tester).position !== 'fixed') {\n      devError('Global', 'The px tester is not rendering properly. Please make sure you have imported `antd-mobile/es/global`.');\n    }\n  }\n}\nexport function convertPx(px) {\n  if (tenPxTester === null || tester === null) return px;\n  if (tenPxTester.getBoundingClientRect().height === 10) {\n    return px;\n  }\n  tester.style.setProperty('--size', px.toString());\n  return tester.getBoundingClientRect().height;\n}", "map": {"version": 3, "names": ["canUseDom", "isDev", "dev<PERSON><PERSON><PERSON>", "tenPxTester", "tester", "document", "createElement", "className", "style", "setProperty", "body", "append<PERSON><PERSON><PERSON>", "window", "getComputedStyle", "position", "convertPx", "px", "getBoundingClientRect", "height", "toString"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/convert-px.js"], "sourcesContent": ["import { canUseDom } from './can-use-dom';\nimport { isDev } from './is-dev';\nimport { devError } from './dev-log';\nlet tenPxTester = null;\nlet tester = null;\nif (canUseDom) {\n  tenPxTester = document.createElement('div');\n  tenPxTester.className = 'adm-px-tester';\n  tenPxTester.style.setProperty('--size', '10');\n  document.body.appendChild(tenPxTester);\n  tester = document.createElement('div');\n  tester.className = 'adm-px-tester';\n  document.body.appendChild(tester);\n  if (isDev) {\n    if (window.getComputedStyle(tester).position !== 'fixed') {\n      devError('Global', 'The px tester is not rendering properly. Please make sure you have imported `antd-mobile/es/global`.');\n    }\n  }\n}\nexport function convertPx(px) {\n  if (tenPxTester === null || tester === null) return px;\n  if (tenPxTester.getBoundingClientRect().height === 10) {\n    return px;\n  }\n  tester.style.setProperty('--size', px.toString());\n  return tester.getBoundingClientRect().height;\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,SAASC,KAAK,QAAQ,UAAU;AAChC,SAASC,QAAQ,QAAQ,WAAW;AACpC,IAAIC,WAAW,GAAG,IAAI;AACtB,IAAIC,MAAM,GAAG,IAAI;AACjB,IAAIJ,SAAS,EAAE;EACbG,WAAW,GAAGE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC3CH,WAAW,CAACI,SAAS,GAAG,eAAe;EACvCJ,WAAW,CAACK,KAAK,CAACC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC;EAC7CJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACR,WAAW,CAAC;EACtCC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACtCF,MAAM,CAACG,SAAS,GAAG,eAAe;EAClCF,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,MAAM,CAAC;EACjC,IAAIH,KAAK,EAAE;IACT,IAAIW,MAAM,CAACC,gBAAgB,CAACT,MAAM,CAAC,CAACU,QAAQ,KAAK,OAAO,EAAE;MACxDZ,QAAQ,CAAC,QAAQ,EAAE,sGAAsG,CAAC;IAC5H;EACF;AACF;AACA,OAAO,SAASa,SAASA,CAACC,EAAE,EAAE;EAC5B,IAAIb,WAAW,KAAK,IAAI,IAAIC,MAAM,KAAK,IAAI,EAAE,OAAOY,EAAE;EACtD,IAAIb,WAAW,CAACc,qBAAqB,CAAC,CAAC,CAACC,MAAM,KAAK,EAAE,EAAE;IACrD,OAAOF,EAAE;EACX;EACAZ,MAAM,CAACI,KAAK,CAACC,WAAW,CAAC,QAAQ,EAAEO,EAAE,CAACG,QAAQ,CAAC,CAAC,CAAC;EACjD,OAAOf,MAAM,CAACa,qBAAqB,CAAC,CAAC,CAACC,MAAM;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}