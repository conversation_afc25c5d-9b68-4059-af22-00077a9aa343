{"ast": null, "code": "import React, { memo } from 'react';\nexport const CheckMark = memo(() => {\n  return React.createElement(\"svg\", {\n    width: '17px',\n    height: '13px',\n    viewBox: '0 0 17 13',\n    version: '1.1',\n    xmlns: 'http://www.w3.org/2000/svg'\n  }, React.createElement(\"g\", {\n    stroke: 'none',\n    strokeWidth: '1',\n    fill: 'none',\n    fillRule: 'evenodd',\n    strokeLinecap: 'round',\n    strokeLinejoin: 'round'\n  }, React.createElement(\"g\", {\n    transform: 'translate(-2832.000000, -1103.000000)',\n    stroke: '#FFFFFF',\n    strokeWidth: '3'\n  }, React.createElement(\"g\", {\n    transform: 'translate(2610.000000, 955.000000)'\n  }, React.createElement(\"g\", {\n    transform: 'translate(24.000000, 91.000000)'\n  }, React.createElement(\"g\", {\n    transform: 'translate(179.177408, 36.687816)'\n  }, React.createElement(\"polyline\", {\n    points: '34.2767388 22 24.797043 31.4796958 21 27.6826527'\n  })))))));\n});", "map": {"version": 3, "names": ["React", "memo", "CheckMark", "createElement", "width", "height", "viewBox", "version", "xmlns", "stroke", "strokeWidth", "fill", "fillRule", "strokeLinecap", "strokeLinejoin", "transform", "points"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/selector/check-mark.js"], "sourcesContent": ["import React, { memo } from 'react';\nexport const CheckMark = memo(() => {\n  return React.createElement(\"svg\", {\n    width: '17px',\n    height: '13px',\n    viewBox: '0 0 17 13',\n    version: '1.1',\n    xmlns: 'http://www.w3.org/2000/svg'\n  }, React.createElement(\"g\", {\n    stroke: 'none',\n    strokeWidth: '1',\n    fill: 'none',\n    fillRule: 'evenodd',\n    strokeLinecap: 'round',\n    strokeLinejoin: 'round'\n  }, React.createElement(\"g\", {\n    transform: 'translate(-2832.000000, -1103.000000)',\n    stroke: '#FFFFFF',\n    strokeWidth: '3'\n  }, React.createElement(\"g\", {\n    transform: 'translate(2610.000000, 955.000000)'\n  }, React.createElement(\"g\", {\n    transform: 'translate(24.000000, 91.000000)'\n  }, React.createElement(\"g\", {\n    transform: 'translate(179.177408, 36.687816)'\n  }, React.createElement(\"polyline\", {\n    points: '34.2767388 22 24.797043 31.4796958 21 27.6826527'\n  })))))));\n});"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,IAAI,QAAQ,OAAO;AACnC,OAAO,MAAMC,SAAS,GAAGD,IAAI,CAAC,MAAM;EAClC,OAAOD,KAAK,CAACG,aAAa,CAAC,KAAK,EAAE;IAChCC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,OAAO,EAAE,WAAW;IACpBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC,EAAER,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IAC1BM,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,GAAG;IAChBC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,SAAS;IACnBC,aAAa,EAAE,OAAO;IACtBC,cAAc,EAAE;EAClB,CAAC,EAAEd,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IAC1BY,SAAS,EAAE,uCAAuC;IAClDN,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE;EACf,CAAC,EAAEV,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IAC1BY,SAAS,EAAE;EACb,CAAC,EAAEf,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IAC1BY,SAAS,EAAE;EACb,CAAC,EAAEf,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IAC1BY,SAAS,EAAE;EACb,CAAC,EAAEf,KAAK,CAACG,aAAa,CAAC,UAAU,EAAE;IACjCa,MAAM,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}