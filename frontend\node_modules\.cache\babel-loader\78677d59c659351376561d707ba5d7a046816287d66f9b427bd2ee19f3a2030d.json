{"ast": null, "code": "import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport classNames from 'classnames';\nimport { isNodeWithContent } from '../../utils/is-node-with-content';\nconst classPrefix = `adm-progress-bar`;\nconst defaultProps = {\n  percent: 0,\n  rounded: true,\n  text: false\n};\nexport const ProgressBar = p => {\n  const props = mergeProps(defaultProps, p);\n  const fillStyle = {\n    width: `${props.percent}%`\n  };\n  const textElement = function () {\n    if (props.text === true) {\n      return `${props.percent}%`;\n    }\n    if (typeof props.text === 'function') {\n      return props.text(props.percent);\n    }\n    return props.text;\n  }();\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, props.rounded && `${classPrefix}-rounded`)\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-trail`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-fill`,\n    style: fillStyle\n  })), isNodeWithContent(textElement) && React.createElement(\"div\", {\n    className: `${classPrefix}-text`\n  }, textElement)));\n};", "map": {"version": 3, "names": ["React", "withNativeProps", "mergeProps", "classNames", "isNodeWithContent", "classPrefix", "defaultProps", "percent", "rounded", "text", "ProgressBar", "p", "props", "fillStyle", "width", "textElement", "createElement", "className", "style"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/progress-bar/progress-bar.js"], "sourcesContent": ["import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport classNames from 'classnames';\nimport { isNodeWithContent } from '../../utils/is-node-with-content';\nconst classPrefix = `adm-progress-bar`;\nconst defaultProps = {\n  percent: 0,\n  rounded: true,\n  text: false\n};\nexport const ProgressBar = p => {\n  const props = mergeProps(defaultProps, p);\n  const fillStyle = {\n    width: `${props.percent}%`\n  };\n  const textElement = function () {\n    if (props.text === true) {\n      return `${props.percent}%`;\n    }\n    if (typeof props.text === 'function') {\n      return props.text(props.percent);\n    }\n    return props.text;\n  }();\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, props.rounded && `${classPrefix}-rounded`)\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-trail`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-fill`,\n    style: fillStyle\n  })), isNodeWithContent(textElement) && React.createElement(\"div\", {\n    className: `${classPrefix}-text`\n  }, textElement)));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,MAAMC,WAAW,GAAG,kBAAkB;AACtC,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,IAAI;EACbC,IAAI,EAAE;AACR,CAAC;AACD,OAAO,MAAMC,WAAW,GAAGC,CAAC,IAAI;EAC9B,MAAMC,KAAK,GAAGV,UAAU,CAACI,YAAY,EAAEK,CAAC,CAAC;EACzC,MAAME,SAAS,GAAG;IAChBC,KAAK,EAAE,GAAGF,KAAK,CAACL,OAAO;EACzB,CAAC;EACD,MAAMQ,WAAW,GAAG,YAAY;IAC9B,IAAIH,KAAK,CAACH,IAAI,KAAK,IAAI,EAAE;MACvB,OAAO,GAAGG,KAAK,CAACL,OAAO,GAAG;IAC5B;IACA,IAAI,OAAOK,KAAK,CAACH,IAAI,KAAK,UAAU,EAAE;MACpC,OAAOG,KAAK,CAACH,IAAI,CAACG,KAAK,CAACL,OAAO,CAAC;IAClC;IACA,OAAOK,KAAK,CAACH,IAAI;EACnB,CAAC,CAAC,CAAC;EACH,OAAOR,eAAe,CAACW,KAAK,EAAEZ,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEd,UAAU,CAACE,WAAW,EAAEO,KAAK,CAACJ,OAAO,IAAI,GAAGH,WAAW,UAAU;EAC9E,CAAC,EAAEL,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGZ,WAAW;EAC3B,CAAC,EAAEL,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGZ,WAAW,OAAO;IAChCa,KAAK,EAAEL;EACT,CAAC,CAAC,CAAC,EAAET,iBAAiB,CAACW,WAAW,CAAC,IAAIf,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAChEC,SAAS,EAAE,GAAGZ,WAAW;EAC3B,CAAC,EAAEU,WAAW,CAAC,CAAC,CAAC;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}