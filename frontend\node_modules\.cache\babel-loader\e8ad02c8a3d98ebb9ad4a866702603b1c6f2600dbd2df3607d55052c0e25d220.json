{"ast": null, "code": "const record = {\n  'topLeft': 'top-start',\n  'topRight': 'top-end',\n  'bottomLeft': 'bottom-start',\n  'bottomRight': 'bottom-end',\n  'leftTop': 'left-start',\n  'leftBottom': 'left-end',\n  'rightTop': 'right-start',\n  'rightBottom': 'right-end'\n};\nexport function normalizePlacement(placement) {\n  var _a;\n  return (_a = record[placement]) !== null && _a !== void 0 ? _a : placement;\n}", "map": {"version": 3, "names": ["record", "normalizePlacement", "placement", "_a"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/popover/normalize-placement.js"], "sourcesContent": ["const record = {\n  'topLeft': 'top-start',\n  'topRight': 'top-end',\n  'bottomLeft': 'bottom-start',\n  'bottomRight': 'bottom-end',\n  'leftTop': 'left-start',\n  'leftBottom': 'left-end',\n  'rightTop': 'right-start',\n  'rightBottom': 'right-end'\n};\nexport function normalizePlacement(placement) {\n  var _a;\n  return (_a = record[placement]) !== null && _a !== void 0 ? _a : placement;\n}"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACb,SAAS,EAAE,WAAW;EACtB,UAAU,EAAE,SAAS;EACrB,YAAY,EAAE,cAAc;EAC5B,aAAa,EAAE,YAAY;EAC3B,SAAS,EAAE,YAAY;EACvB,YAAY,EAAE,UAAU;EACxB,UAAU,EAAE,aAAa;EACzB,aAAa,EAAE;AACjB,CAAC;AACD,OAAO,SAASC,kBAAkBA,CAACC,SAAS,EAAE;EAC5C,IAAIC,EAAE;EACN,OAAO,CAACA,EAAE,GAAGH,MAAM,CAACE,SAAS,CAAC,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGD,SAAS;AAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}