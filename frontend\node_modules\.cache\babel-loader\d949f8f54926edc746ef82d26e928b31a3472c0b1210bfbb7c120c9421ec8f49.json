{"ast": null, "code": "import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nconst classPrefix = `adm-result-page-card`;\nexport const ResultPageCard = props => {\n  return withNativeProps(props, React.createElement('div', {\n    className: classNames(`${classPrefix}`)\n  }, props.children));\n};", "map": {"version": 3, "names": ["React", "withNativeProps", "classNames", "classPrefix", "ResultPageCard", "props", "createElement", "className", "children"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/result-page/result-page-card.js"], "sourcesContent": ["import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nconst classPrefix = `adm-result-page-card`;\nexport const ResultPageCard = props => {\n  return withNativeProps(props, React.createElement('div', {\n    className: classNames(`${classPrefix}`)\n  }, props.children));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,UAAU,MAAM,YAAY;AACnC,MAAMC,WAAW,GAAG,sBAAsB;AAC1C,OAAO,MAAMC,cAAc,GAAGC,KAAK,IAAI;EACrC,OAAOJ,eAAe,CAACI,KAAK,EAAEL,KAAK,CAACM,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEL,UAAU,CAAC,GAAGC,WAAW,EAAE;EACxC,CAAC,EAAEE,KAAK,CAACG,QAAQ,CAAC,CAAC;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}