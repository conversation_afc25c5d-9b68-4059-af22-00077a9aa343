{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = `adm-divider`;\nconst defaultProps = {\n  contentPosition: 'center',\n  direction: 'horizontal'\n};\nexport const Divider = p => {\n  const props = mergeProps(defaultProps, p);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, `${classPrefix}-${props.direction}`, `${classPrefix}-${props.contentPosition}`)\n  }, props.children && React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, props.children)));\n};", "map": {"version": 3, "names": ["React", "classNames", "withNativeProps", "mergeProps", "classPrefix", "defaultProps", "contentPosition", "direction", "Divider", "p", "props", "createElement", "className", "children"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/divider/divider.js"], "sourcesContent": ["import React from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = `adm-divider`;\nconst defaultProps = {\n  contentPosition: 'center',\n  direction: 'horizontal'\n};\nexport const Divider = p => {\n  const props = mergeProps(defaultProps, p);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, `${classPrefix}-${props.direction}`, `${classPrefix}-${props.contentPosition}`)\n  }, props.children && React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, props.children)));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,MAAMC,WAAW,GAAG,aAAa;AACjC,MAAMC,YAAY,GAAG;EACnBC,eAAe,EAAE,QAAQ;EACzBC,SAAS,EAAE;AACb,CAAC;AACD,OAAO,MAAMC,OAAO,GAAGC,CAAC,IAAI;EAC1B,MAAMC,KAAK,GAAGP,UAAU,CAACE,YAAY,EAAEI,CAAC,CAAC;EACzC,OAAOP,eAAe,CAACQ,KAAK,EAAEV,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEX,UAAU,CAACG,WAAW,EAAE,GAAGA,WAAW,IAAIM,KAAK,CAACH,SAAS,EAAE,EAAE,GAAGH,WAAW,IAAIM,KAAK,CAACJ,eAAe,EAAE;EACnH,CAAC,EAAEI,KAAK,CAACG,QAAQ,IAAIb,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;IAC9CC,SAAS,EAAE,GAAGR,WAAW;EAC3B,CAAC,EAAEM,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}