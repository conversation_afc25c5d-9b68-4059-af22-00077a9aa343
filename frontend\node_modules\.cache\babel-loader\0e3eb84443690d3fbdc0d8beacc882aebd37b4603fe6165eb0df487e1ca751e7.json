{"ast": null, "code": "import React from 'react';\nexport const Star = () => {\n  return React.createElement(\"svg\", {\n    viewBox: '0 0 42 40',\n    height: '1em',\n    xmlns: 'http://www.w3.org/2000/svg',\n    style: {\n      verticalAlign: '-0.125em'\n    }\n  }, React.createElement(\"path\", {\n    d: 'm21 34-10.52 5.53a2 2 0 0 1-2.902-2.108l2.01-11.714-8.511-8.296a2 2 0 0 1 1.108-3.411l11.762-1.71 5.26-10.657a2 2 0 0 1 3.586 0l5.26 10.658L39.815 14a2 2 0 0 1 1.108 3.411l-8.51 8.296 2.009 11.714a2 2 0 0 1-2.902 2.109L21 34Z',\n    fill: 'currentColor',\n    fillRule: 'evenodd'\n  }));\n};", "map": {"version": 3, "names": ["React", "Star", "createElement", "viewBox", "height", "xmlns", "style", "verticalAlign", "d", "fill", "fillRule"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/rate/star.js"], "sourcesContent": ["import React from 'react';\nexport const Star = () => {\n  return React.createElement(\"svg\", {\n    viewBox: '0 0 42 40',\n    height: '1em',\n    xmlns: 'http://www.w3.org/2000/svg',\n    style: {\n      verticalAlign: '-0.125em'\n    }\n  }, React.createElement(\"path\", {\n    d: 'm21 34-10.52 5.53a2 2 0 0 1-2.902-2.108l2.01-11.714-8.511-8.296a2 2 0 0 1 1.108-3.411l11.762-1.71 5.26-10.657a2 2 0 0 1 3.586 0l5.26 10.658L39.815 14a2 2 0 0 1 1.108 3.411l-8.51 8.296 2.009 11.714a2 2 0 0 1-2.902 2.109L21 34Z',\n    fill: 'currentColor',\n    fillRule: 'evenodd'\n  }));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACxB,OAAOD,KAAK,CAACE,aAAa,CAAC,KAAK,EAAE;IAChCC,OAAO,EAAE,WAAW;IACpBC,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,4BAA4B;IACnCC,KAAK,EAAE;MACLC,aAAa,EAAE;IACjB;EACF,CAAC,EAAEP,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;IAC7BM,CAAC,EAAE,mOAAmO;IACtOC,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}