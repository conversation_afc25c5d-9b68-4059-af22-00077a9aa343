{"ast": null, "code": "import { createUpdateEffect, useIsomorphicLayoutEffect } from 'ahooks';\nexport const useIsomorphicUpdateLayoutEffect = createUpdateEffect(useIsomorphicLayoutEffect);", "map": {"version": 3, "names": ["createUpdateEffect", "useIsomorphicLayoutEffect", "useIsomorphicUpdateLayoutEffect"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/use-isomorphic-update-layout-effect.js"], "sourcesContent": ["import { createUpdateEffect, useIsomorphicLayoutEffect } from 'ahooks';\nexport const useIsomorphicUpdateLayoutEffect = createUpdateEffect(useIsomorphicLayoutEffect);"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,yBAAyB,QAAQ,QAAQ;AACtE,OAAO,MAAMC,+BAA+B,GAAGF,kBAAkB,CAACC,yBAAyB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}