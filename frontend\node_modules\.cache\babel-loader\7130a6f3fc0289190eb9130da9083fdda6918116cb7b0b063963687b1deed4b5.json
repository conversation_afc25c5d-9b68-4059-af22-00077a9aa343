{"ast": null, "code": "import { useMemo } from 'react';\nexport const useFieldNames = (fieldNames = {}) => {\n  const fields = useMemo(() => {\n    const {\n      label = 'label',\n      value = 'value',\n      disabled = 'disabled',\n      children = 'children'\n    } = fieldNames;\n    return [label, value, children, disabled];\n  }, [JSON.stringify(fieldNames)]);\n  return fields;\n};", "map": {"version": 3, "names": ["useMemo", "useFieldNames", "fieldNames", "fields", "label", "value", "disabled", "children", "JSON", "stringify"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/hooks/useFieldNames.js"], "sourcesContent": ["import { useMemo } from 'react';\nexport const useFieldNames = (fieldNames = {}) => {\n  const fields = useMemo(() => {\n    const {\n      label = 'label',\n      value = 'value',\n      disabled = 'disabled',\n      children = 'children'\n    } = fieldNames;\n    return [label, value, children, disabled];\n  }, [JSON.stringify(fieldNames)]);\n  return fields;\n};"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B,OAAO,MAAMC,aAAa,GAAGA,CAACC,UAAU,GAAG,CAAC,CAAC,KAAK;EAChD,MAAMC,MAAM,GAAGH,OAAO,CAAC,MAAM;IAC3B,MAAM;MACJI,KAAK,GAAG,OAAO;MACfC,KAAK,GAAG,OAAO;MACfC,QAAQ,GAAG,UAAU;MACrBC,QAAQ,GAAG;IACb,CAAC,GAAGL,UAAU;IACd,OAAO,CAACE,KAAK,EAAEC,KAAK,EAAEE,QAAQ,EAAED,QAAQ,CAAC;EAC3C,CAAC,EAAE,CAACE,IAAI,CAACC,SAAS,CAACP,UAAU,CAAC,CAAC,CAAC;EAChC,OAAOC,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}