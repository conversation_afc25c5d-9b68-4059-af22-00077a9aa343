{"ast": null, "code": "import { __awaiter } from \"tslib\";\nimport React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';\nimport classNames from 'classnames';\nimport DotLoading from '../dot-loading';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nimport { isPromise } from '../../utils/validate';\nconst classPrefix = `adm-button`;\nconst defaultProps = {\n  color: 'default',\n  fill: 'solid',\n  block: false,\n  loading: false,\n  loadingIcon: React.createElement(DotLoading, {\n    color: 'currentColor'\n  }),\n  type: 'button',\n  shape: 'default',\n  size: 'middle'\n};\nexport const Button = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const [innerLoading, setInnerLoading] = useState(false);\n  const nativeButtonRef = useRef(null);\n  const loading = props.loading === 'auto' ? innerLoading : props.loading;\n  const disabled = props.disabled || loading;\n  useImperativeHandle(ref, () => ({\n    get nativeElement() {\n      return nativeButtonRef.current;\n    }\n  }));\n  const handleClick = e => __awaiter(void 0, void 0, void 0, function* () {\n    if (!props.onClick) return;\n    const promise = props.onClick(e);\n    if (isPromise(promise)) {\n      try {\n        setInnerLoading(true);\n        yield promise;\n        setInnerLoading(false);\n      } catch (e) {\n        setInnerLoading(false);\n        throw e;\n      }\n    }\n  });\n  return withNativeProps(props, React.createElement(\"button\", {\n    ref: nativeButtonRef,\n    type: props.type,\n    onClick: handleClick,\n    className: classNames(classPrefix, {\n      [`${classPrefix}-${props.color}`]: props.color,\n      [`${classPrefix}-block`]: props.block,\n      [`${classPrefix}-disabled`]: disabled,\n      [`${classPrefix}-fill-outline`]: props.fill === 'outline',\n      [`${classPrefix}-fill-none`]: props.fill === 'none',\n      [`${classPrefix}-mini`]: props.size === 'mini',\n      [`${classPrefix}-small`]: props.size === 'small',\n      [`${classPrefix}-large`]: props.size === 'large',\n      [`${classPrefix}-loading`]: loading\n    }, `${classPrefix}-shape-${props.shape}`),\n    disabled: disabled,\n    onMouseDown: props.onMouseDown,\n    onMouseUp: props.onMouseUp,\n    onTouchStart: props.onTouchStart,\n    onTouchEnd: props.onTouchEnd\n  }, loading ? React.createElement(\"div\", {\n    className: `${classPrefix}-loading-wrapper`\n  }, props.loadingIcon, props.loadingText) : React.createElement(\"span\", null, props.children)));\n});", "map": {"version": 3, "names": ["__awaiter", "React", "forwardRef", "useImperativeHandle", "useRef", "useState", "classNames", "DotLoading", "mergeProps", "withNativeProps", "isPromise", "classPrefix", "defaultProps", "color", "fill", "block", "loading", "loadingIcon", "createElement", "type", "shape", "size", "<PERSON><PERSON>", "p", "ref", "props", "innerLoading", "setInnerLoading", "nativeButtonRef", "disabled", "nativeElement", "current", "handleClick", "e", "onClick", "promise", "className", "onMouseDown", "onMouseUp", "onTouchStart", "onTouchEnd", "loadingText", "children"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/button/button.js"], "sourcesContent": ["import { __awaiter } from \"tslib\";\nimport React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';\nimport classNames from 'classnames';\nimport DotLoading from '../dot-loading';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nimport { isPromise } from '../../utils/validate';\nconst classPrefix = `adm-button`;\nconst defaultProps = {\n  color: 'default',\n  fill: 'solid',\n  block: false,\n  loading: false,\n  loadingIcon: React.createElement(DotLoading, {\n    color: 'currentColor'\n  }),\n  type: 'button',\n  shape: 'default',\n  size: 'middle'\n};\nexport const Button = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const [innerLoading, setInnerLoading] = useState(false);\n  const nativeButtonRef = useRef(null);\n  const loading = props.loading === 'auto' ? innerLoading : props.loading;\n  const disabled = props.disabled || loading;\n  useImperativeHandle(ref, () => ({\n    get nativeElement() {\n      return nativeButtonRef.current;\n    }\n  }));\n  const handleClick = e => __awaiter(void 0, void 0, void 0, function* () {\n    if (!props.onClick) return;\n    const promise = props.onClick(e);\n    if (isPromise(promise)) {\n      try {\n        setInnerLoading(true);\n        yield promise;\n        setInnerLoading(false);\n      } catch (e) {\n        setInnerLoading(false);\n        throw e;\n      }\n    }\n  });\n  return withNativeProps(props, React.createElement(\"button\", {\n    ref: nativeButtonRef,\n    type: props.type,\n    onClick: handleClick,\n    className: classNames(classPrefix, {\n      [`${classPrefix}-${props.color}`]: props.color,\n      [`${classPrefix}-block`]: props.block,\n      [`${classPrefix}-disabled`]: disabled,\n      [`${classPrefix}-fill-outline`]: props.fill === 'outline',\n      [`${classPrefix}-fill-none`]: props.fill === 'none',\n      [`${classPrefix}-mini`]: props.size === 'mini',\n      [`${classPrefix}-small`]: props.size === 'small',\n      [`${classPrefix}-large`]: props.size === 'large',\n      [`${classPrefix}-loading`]: loading\n    }, `${classPrefix}-shape-${props.shape}`),\n    disabled: disabled,\n    onMouseDown: props.onMouseDown,\n    onMouseUp: props.onMouseUp,\n    onTouchStart: props.onTouchStart,\n    onTouchEnd: props.onTouchEnd\n  }, loading ? React.createElement(\"div\", {\n    className: `${classPrefix}-loading-wrapper`\n  }, props.loadingIcon, props.loadingText) : React.createElement(\"span\", null, props.children)));\n});"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,KAAK,IAAIC,UAAU,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAChF,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,UAAU,MAAM,gBAAgB;AACvC,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,SAAS,QAAQ,sBAAsB;AAChD,MAAMC,WAAW,GAAG,YAAY;AAChC,MAAMC,YAAY,GAAG;EACnBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,KAAK;EACZC,OAAO,EAAE,KAAK;EACdC,WAAW,EAAEhB,KAAK,CAACiB,aAAa,CAACX,UAAU,EAAE;IAC3CM,KAAK,EAAE;EACT,CAAC,CAAC;EACFM,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE;AACR,CAAC;AACD,OAAO,MAAMC,MAAM,GAAGpB,UAAU,CAAC,CAACqB,CAAC,EAAEC,GAAG,KAAK;EAC3C,MAAMC,KAAK,GAAGjB,UAAU,CAACI,YAAY,EAAEW,CAAC,CAAC;EACzC,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMuB,eAAe,GAAGxB,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMY,OAAO,GAAGS,KAAK,CAACT,OAAO,KAAK,MAAM,GAAGU,YAAY,GAAGD,KAAK,CAACT,OAAO;EACvE,MAAMa,QAAQ,GAAGJ,KAAK,CAACI,QAAQ,IAAIb,OAAO;EAC1Cb,mBAAmB,CAACqB,GAAG,EAAE,OAAO;IAC9B,IAAIM,aAAaA,CAAA,EAAG;MAClB,OAAOF,eAAe,CAACG,OAAO;IAChC;EACF,CAAC,CAAC,CAAC;EACH,MAAMC,WAAW,GAAGC,CAAC,IAAIjC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;IACtE,IAAI,CAACyB,KAAK,CAACS,OAAO,EAAE;IACpB,MAAMC,OAAO,GAAGV,KAAK,CAACS,OAAO,CAACD,CAAC,CAAC;IAChC,IAAIvB,SAAS,CAACyB,OAAO,CAAC,EAAE;MACtB,IAAI;QACFR,eAAe,CAAC,IAAI,CAAC;QACrB,MAAMQ,OAAO;QACbR,eAAe,CAAC,KAAK,CAAC;MACxB,CAAC,CAAC,OAAOM,CAAC,EAAE;QACVN,eAAe,CAAC,KAAK,CAAC;QACtB,MAAMM,CAAC;MACT;IACF;EACF,CAAC,CAAC;EACF,OAAOxB,eAAe,CAACgB,KAAK,EAAExB,KAAK,CAACiB,aAAa,CAAC,QAAQ,EAAE;IAC1DM,GAAG,EAAEI,eAAe;IACpBT,IAAI,EAAEM,KAAK,CAACN,IAAI;IAChBe,OAAO,EAAEF,WAAW;IACpBI,SAAS,EAAE9B,UAAU,CAACK,WAAW,EAAE;MACjC,CAAC,GAAGA,WAAW,IAAIc,KAAK,CAACZ,KAAK,EAAE,GAAGY,KAAK,CAACZ,KAAK;MAC9C,CAAC,GAAGF,WAAW,QAAQ,GAAGc,KAAK,CAACV,KAAK;MACrC,CAAC,GAAGJ,WAAW,WAAW,GAAGkB,QAAQ;MACrC,CAAC,GAAGlB,WAAW,eAAe,GAAGc,KAAK,CAACX,IAAI,KAAK,SAAS;MACzD,CAAC,GAAGH,WAAW,YAAY,GAAGc,KAAK,CAACX,IAAI,KAAK,MAAM;MACnD,CAAC,GAAGH,WAAW,OAAO,GAAGc,KAAK,CAACJ,IAAI,KAAK,MAAM;MAC9C,CAAC,GAAGV,WAAW,QAAQ,GAAGc,KAAK,CAACJ,IAAI,KAAK,OAAO;MAChD,CAAC,GAAGV,WAAW,QAAQ,GAAGc,KAAK,CAACJ,IAAI,KAAK,OAAO;MAChD,CAAC,GAAGV,WAAW,UAAU,GAAGK;IAC9B,CAAC,EAAE,GAAGL,WAAW,UAAUc,KAAK,CAACL,KAAK,EAAE,CAAC;IACzCS,QAAQ,EAAEA,QAAQ;IAClBQ,WAAW,EAAEZ,KAAK,CAACY,WAAW;IAC9BC,SAAS,EAAEb,KAAK,CAACa,SAAS;IAC1BC,YAAY,EAAEd,KAAK,CAACc,YAAY;IAChCC,UAAU,EAAEf,KAAK,CAACe;EACpB,CAAC,EAAExB,OAAO,GAAGf,KAAK,CAACiB,aAAa,CAAC,KAAK,EAAE;IACtCkB,SAAS,EAAE,GAAGzB,WAAW;EAC3B,CAAC,EAAEc,KAAK,CAACR,WAAW,EAAEQ,KAAK,CAACgB,WAAW,CAAC,GAAGxC,KAAK,CAACiB,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEO,KAAK,CAACiB,QAAQ,CAAC,CAAC,CAAC;AAChG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}