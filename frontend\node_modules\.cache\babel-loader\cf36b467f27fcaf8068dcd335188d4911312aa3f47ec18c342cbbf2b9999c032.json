{"ast": null, "code": "import classNames from 'classnames';\nimport React, { useEffect, useMemo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { getTreeDeep } from '../../utils/tree';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Checkbox from '../checkbox';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { devWarning } from '../../utils/dev-log';\nimport { useFieldNames } from '../../hooks';\nconst classPrefix = `adm-tree-select-multiple`;\nexport const Multiple = p => {\n  const props = mergeProps({\n    options: [],\n    fieldNames: {},\n    allSelectText: [],\n    defaultExpandKeys: [],\n    defaultValue: []\n  }, p);\n  useEffect(() => {\n    devWarning('TreeSelect', 'TreeSelect.Multiple has been deprecated.');\n  }, []);\n  const [labelName, valueName, childrenName] = useFieldNames(props.fieldNames);\n  // 打开的 keys\n  const [expandKeys, setExpandKeys] = usePropsValue({\n    value: props.expandKeys,\n    defaultValue: props.defaultExpandKeys\n  });\n  // 选中的 value（聚合后）\n  const [value, setValue] = usePropsValue({\n    value: props.value,\n    defaultValue: props.defaultValue\n  });\n  // 获取目标所有叶子节点 key 集合\n  const getLeafKeys = option => {\n    const keys = [];\n    const walker = op => {\n      var _a;\n      if (!op) {\n        return;\n      }\n      if ((_a = op[childrenName]) === null || _a === void 0 ? void 0 : _a.length) {\n        op[childrenName].forEach(i => walker(i));\n      } else {\n        keys.push(op[valueName]);\n      }\n    };\n    walker(option);\n    return keys;\n  };\n  const [deep, optionsMap, optionsParentMap] = useMemo(() => {\n    const deep = getTreeDeep(props.options, childrenName);\n    const optionsMap = new Map();\n    const optionsParentMap = new Map();\n    function traverse(current, children) {\n      children.forEach(item => {\n        optionsParentMap.set(item[valueName], current);\n        optionsMap.set(item[valueName], item);\n        if (item[childrenName]) {\n          traverse(item, item[childrenName]);\n        }\n      });\n    }\n    traverse(undefined, props.options);\n    return [deep, optionsMap, optionsParentMap];\n  }, [props.options]);\n  // 将聚合的 value 拆分开，获得叶子节点的 value 集合\n  const allSelectedLeafKeys = useMemo(() => {\n    let leafKeys = [];\n    value.forEach(v => {\n      const option = optionsMap.get(v);\n      leafKeys = leafKeys.concat(getLeafKeys(option));\n    });\n    return leafKeys;\n  }, [value, optionsMap]);\n  // 子级有被选中的节点集合\n  const dotMap = useMemo(() => {\n    const map = new Map();\n    // 遍历 allChildrenValues, 向上递归\n    const walker = key => {\n      const parentOption = optionsParentMap.get(key);\n      if (!parentOption) {\n        return;\n      }\n      map.set(parentOption[valueName], true);\n      walker(parentOption[valueName]);\n    };\n    allSelectedLeafKeys.forEach(key => {\n      map.set(key, true);\n      walker(key);\n    });\n    return map;\n  }, [optionsParentMap, value]);\n  const onChange = targetKeys => {\n    var _a;\n    let groupKeys = [...targetKeys];\n    let unusedKeys = [];\n    const walker = keys => {\n      keys.forEach(key => {\n        var _a;\n        if (unusedKeys.includes(key)) {\n          return;\n        }\n        const parent = optionsParentMap.get(key);\n        if (!parent) {\n          return;\n        }\n        const childrenKeys = ((_a = parent[childrenName]) === null || _a === void 0 ? void 0 : _a.map(i => i[valueName])) || [];\n        if (childrenKeys.every(i => groupKeys.includes(i))) {\n          groupKeys.push(parent[valueName]);\n          unusedKeys = unusedKeys.concat(childrenKeys);\n        }\n      });\n    };\n    // 遍历 deep 次 groupKeys，每次往上聚合一层\n    for (let i = 0; i < deep; i++) {\n      walker(groupKeys);\n    }\n    groupKeys = groupKeys.filter(i => !unusedKeys.includes(i));\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const groupOptions = groupKeys.map(i => optionsMap.get(i));\n    setValue(groupKeys);\n    (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, groupKeys, groupOptions);\n  };\n  const onItemSelect = option => {\n    var _a;\n    const parentNodes = [];\n    let current = option;\n    while (current) {\n      parentNodes.unshift(current);\n      const next = optionsParentMap.get(current[valueName]);\n      current = next;\n    }\n    const keys = parentNodes.map(i => i[valueName]);\n    setExpandKeys(keys);\n    (_a = props.onExpand) === null || _a === void 0 ? void 0 : _a.call(props, keys, parentNodes);\n  };\n  // 渲染全选节点\n  const renderSelectAllItem = (columnOptions, index) => {\n    var _a;\n    const text = (_a = props.selectAllText) === null || _a === void 0 ? void 0 : _a[index];\n    if (!text) {\n      return;\n    }\n    let currentLeafKeys = [];\n    columnOptions.forEach(option => {\n      currentLeafKeys = currentLeafKeys.concat(getLeafKeys(option));\n    });\n    const allSelected = currentLeafKeys.every(i => allSelectedLeafKeys.includes(i));\n    return React.createElement(\"div\", {\n      onClick: () => {\n        if (allSelected) {\n          onChange(allSelectedLeafKeys.filter(i => !currentLeafKeys.includes(i)));\n        } else {\n          onChange(allSelectedLeafKeys.concat(currentLeafKeys));\n        }\n      },\n      className: `${classPrefix}-item`\n    }, text);\n  };\n  // 渲染\n  const renderSelectAllLeafItem = (columnOptions, index) => {\n    var _a;\n    const text = (_a = props.selectAllText) === null || _a === void 0 ? void 0 : _a[index];\n    if (!text) {\n      return;\n    }\n    const currentLeafKeys = columnOptions.map(i => i[valueName]);\n    const allSelected = currentLeafKeys.every(i => allSelectedLeafKeys.includes(i));\n    const halfSelected = allSelected ? false : currentLeafKeys.some(i => allSelectedLeafKeys.includes(i));\n    return React.createElement(\"div\", {\n      onClick: () => {\n        if (allSelected) {\n          onChange(allSelectedLeafKeys.filter(i => !currentLeafKeys.includes(i)));\n        } else {\n          onChange(allSelectedLeafKeys.concat(currentLeafKeys));\n        }\n      },\n      className: classNames(`${classPrefix}-item`, `${classPrefix}-item-leaf`)\n    }, React.createElement(Checkbox, {\n      className: `${classPrefix}-item-checkbox`,\n      checked: allSelected,\n      indeterminate: halfSelected\n    }), text);\n  };\n  // 渲染节点\n  const renderItem = option => {\n    const isExpand = expandKeys.includes(option[valueName]);\n    return React.createElement(\"div\", {\n      key: option[valueName],\n      onClick: () => {\n        if (!isExpand) {\n          onItemSelect(option);\n        }\n      },\n      className: classNames(`${classPrefix}-item`, {\n        [`${classPrefix}-item-expand`]: isExpand\n      })\n    }, option[labelName], !!dotMap.get(option[valueName]) && React.createElement(\"div\", {\n      className: `${classPrefix}-dot`\n    }));\n  };\n  // 渲染叶子节点\n  const renderLeafItem = option => {\n    const isSelected = allSelectedLeafKeys.includes(option[valueName]);\n    return React.createElement(\"div\", {\n      key: option[valueName],\n      onClick: () => {\n        if (isSelected) {\n          onChange(allSelectedLeafKeys.filter(val => val !== option[valueName]));\n        } else {\n          onChange([...allSelectedLeafKeys, option[valueName]]);\n        }\n      },\n      className: classNames(`${classPrefix}-item`, `${classPrefix}-item-leaf`)\n    }, React.createElement(Checkbox, {\n      className: `${classPrefix}-item-checkbox`,\n      checked: isSelected\n    }), option[labelName]);\n  };\n  const renderItems = (columnOptions = [], index) => {\n    if (columnOptions.length === 0) {\n      return;\n    }\n    const isLeaf = deep === index + 1;\n    if (isLeaf) {\n      return React.createElement(React.Fragment, null, renderSelectAllLeafItem(columnOptions, index), columnOptions.map(option => renderLeafItem(option)));\n    }\n    return React.createElement(React.Fragment, null, renderSelectAllItem(columnOptions, index), columnOptions.map(option => renderItem(option)));\n  };\n  const renderColumns = () => {\n    var _a;\n    const columns = [];\n    for (let i = 0; i < deep; i++) {\n      let width = `${100 / deep}%`;\n      // 两列的第一列宽度为 33.33，两列的第二列为 66.67%\n      if (deep === 2 && i === 0) {\n        width = `33.33%`;\n      }\n      if (deep === 2 && i === 1) {\n        width = `66.67%`;\n      }\n      const column = React.createElement(\"div\", {\n        key: i,\n        className: classNames(`${classPrefix}-column`),\n        style: {\n          width\n        }\n      }, renderItems(i === 0 ? props.options : (_a = optionsMap.get(expandKeys[i - 1])) === null || _a === void 0 ? void 0 : _a[childrenName], i));\n      columns.push(column);\n    }\n    return columns;\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, renderColumns()));\n};", "map": {"version": 3, "names": ["classNames", "React", "useEffect", "useMemo", "withNativeProps", "getTreeDeep", "mergeProps", "Checkbox", "usePropsValue", "dev<PERSON><PERSON><PERSON>", "useFieldNames", "classPrefix", "Multiple", "p", "props", "options", "fieldNames", "allSelectText", "defaultExpandKeys", "defaultValue", "labelName", "valueName", "<PERSON><PERSON><PERSON>", "expandKeys", "setExpandKeys", "value", "setValue", "getLeafKeys", "option", "keys", "walker", "op", "_a", "length", "for<PERSON>ach", "i", "push", "deep", "optionsMap", "optionsParentMap", "Map", "traverse", "current", "children", "item", "set", "undefined", "allSelectedLeafKeys", "leafKeys", "v", "get", "concat", "dotMap", "map", "key", "parentOption", "onChange", "targetKeys", "groupKeys", "unused<PERSON>eys", "includes", "parent", "children<PERSON>eys", "every", "filter", "groupOptions", "call", "onItemSelect", "parentNodes", "unshift", "next", "onExpand", "renderSelectAllItem", "columnOptions", "index", "text", "selectAllText", "currentLeafKeys", "allSelected", "createElement", "onClick", "className", "renderSelectAllLeafItem", "halfSelected", "some", "checked", "indeterminate", "renderItem", "isExpand", "renderLeafItem", "isSelected", "val", "renderItems", "<PERSON><PERSON><PERSON><PERSON>", "Fragment", "renderColumns", "columns", "width", "column", "style"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/tree-select/multiple.js"], "sourcesContent": ["import classNames from 'classnames';\nimport React, { useEffect, useMemo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { getTreeDeep } from '../../utils/tree';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Checkbox from '../checkbox';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { devWarning } from '../../utils/dev-log';\nimport { useFieldNames } from '../../hooks';\nconst classPrefix = `adm-tree-select-multiple`;\nexport const Multiple = p => {\n  const props = mergeProps({\n    options: [],\n    fieldNames: {},\n    allSelectText: [],\n    defaultExpandKeys: [],\n    defaultValue: []\n  }, p);\n  useEffect(() => {\n    devWarning('TreeSelect', 'TreeSelect.Multiple has been deprecated.');\n  }, []);\n  const [labelName, valueName, childrenName] = useFieldNames(props.fieldNames);\n  // 打开的 keys\n  const [expandKeys, setExpandKeys] = usePropsValue({\n    value: props.expandKeys,\n    defaultValue: props.defaultExpandKeys\n  });\n  // 选中的 value（聚合后）\n  const [value, setValue] = usePropsValue({\n    value: props.value,\n    defaultValue: props.defaultValue\n  });\n  // 获取目标所有叶子节点 key 集合\n  const getLeafKeys = option => {\n    const keys = [];\n    const walker = op => {\n      var _a;\n      if (!op) {\n        return;\n      }\n      if ((_a = op[childrenName]) === null || _a === void 0 ? void 0 : _a.length) {\n        op[childrenName].forEach(i => walker(i));\n      } else {\n        keys.push(op[valueName]);\n      }\n    };\n    walker(option);\n    return keys;\n  };\n  const [deep, optionsMap, optionsParentMap] = useMemo(() => {\n    const deep = getTreeDeep(props.options, childrenName);\n    const optionsMap = new Map();\n    const optionsParentMap = new Map();\n    function traverse(current, children) {\n      children.forEach(item => {\n        optionsParentMap.set(item[valueName], current);\n        optionsMap.set(item[valueName], item);\n        if (item[childrenName]) {\n          traverse(item, item[childrenName]);\n        }\n      });\n    }\n    traverse(undefined, props.options);\n    return [deep, optionsMap, optionsParentMap];\n  }, [props.options]);\n  // 将聚合的 value 拆分开，获得叶子节点的 value 集合\n  const allSelectedLeafKeys = useMemo(() => {\n    let leafKeys = [];\n    value.forEach(v => {\n      const option = optionsMap.get(v);\n      leafKeys = leafKeys.concat(getLeafKeys(option));\n    });\n    return leafKeys;\n  }, [value, optionsMap]);\n  // 子级有被选中的节点集合\n  const dotMap = useMemo(() => {\n    const map = new Map();\n    // 遍历 allChildrenValues, 向上递归\n    const walker = key => {\n      const parentOption = optionsParentMap.get(key);\n      if (!parentOption) {\n        return;\n      }\n      map.set(parentOption[valueName], true);\n      walker(parentOption[valueName]);\n    };\n    allSelectedLeafKeys.forEach(key => {\n      map.set(key, true);\n      walker(key);\n    });\n    return map;\n  }, [optionsParentMap, value]);\n  const onChange = targetKeys => {\n    var _a;\n    let groupKeys = [...targetKeys];\n    let unusedKeys = [];\n    const walker = keys => {\n      keys.forEach(key => {\n        var _a;\n        if (unusedKeys.includes(key)) {\n          return;\n        }\n        const parent = optionsParentMap.get(key);\n        if (!parent) {\n          return;\n        }\n        const childrenKeys = ((_a = parent[childrenName]) === null || _a === void 0 ? void 0 : _a.map(i => i[valueName])) || [];\n        if (childrenKeys.every(i => groupKeys.includes(i))) {\n          groupKeys.push(parent[valueName]);\n          unusedKeys = unusedKeys.concat(childrenKeys);\n        }\n      });\n    };\n    // 遍历 deep 次 groupKeys，每次往上聚合一层\n    for (let i = 0; i < deep; i++) {\n      walker(groupKeys);\n    }\n    groupKeys = groupKeys.filter(i => !unusedKeys.includes(i));\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const groupOptions = groupKeys.map(i => optionsMap.get(i));\n    setValue(groupKeys);\n    (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, groupKeys, groupOptions);\n  };\n  const onItemSelect = option => {\n    var _a;\n    const parentNodes = [];\n    let current = option;\n    while (current) {\n      parentNodes.unshift(current);\n      const next = optionsParentMap.get(current[valueName]);\n      current = next;\n    }\n    const keys = parentNodes.map(i => i[valueName]);\n    setExpandKeys(keys);\n    (_a = props.onExpand) === null || _a === void 0 ? void 0 : _a.call(props, keys, parentNodes);\n  };\n  // 渲染全选节点\n  const renderSelectAllItem = (columnOptions, index) => {\n    var _a;\n    const text = (_a = props.selectAllText) === null || _a === void 0 ? void 0 : _a[index];\n    if (!text) {\n      return;\n    }\n    let currentLeafKeys = [];\n    columnOptions.forEach(option => {\n      currentLeafKeys = currentLeafKeys.concat(getLeafKeys(option));\n    });\n    const allSelected = currentLeafKeys.every(i => allSelectedLeafKeys.includes(i));\n    return React.createElement(\"div\", {\n      onClick: () => {\n        if (allSelected) {\n          onChange(allSelectedLeafKeys.filter(i => !currentLeafKeys.includes(i)));\n        } else {\n          onChange(allSelectedLeafKeys.concat(currentLeafKeys));\n        }\n      },\n      className: `${classPrefix}-item`\n    }, text);\n  };\n  // 渲染\n  const renderSelectAllLeafItem = (columnOptions, index) => {\n    var _a;\n    const text = (_a = props.selectAllText) === null || _a === void 0 ? void 0 : _a[index];\n    if (!text) {\n      return;\n    }\n    const currentLeafKeys = columnOptions.map(i => i[valueName]);\n    const allSelected = currentLeafKeys.every(i => allSelectedLeafKeys.includes(i));\n    const halfSelected = allSelected ? false : currentLeafKeys.some(i => allSelectedLeafKeys.includes(i));\n    return React.createElement(\"div\", {\n      onClick: () => {\n        if (allSelected) {\n          onChange(allSelectedLeafKeys.filter(i => !currentLeafKeys.includes(i)));\n        } else {\n          onChange(allSelectedLeafKeys.concat(currentLeafKeys));\n        }\n      },\n      className: classNames(`${classPrefix}-item`, `${classPrefix}-item-leaf`)\n    }, React.createElement(Checkbox, {\n      className: `${classPrefix}-item-checkbox`,\n      checked: allSelected,\n      indeterminate: halfSelected\n    }), text);\n  };\n  // 渲染节点\n  const renderItem = option => {\n    const isExpand = expandKeys.includes(option[valueName]);\n    return React.createElement(\"div\", {\n      key: option[valueName],\n      onClick: () => {\n        if (!isExpand) {\n          onItemSelect(option);\n        }\n      },\n      className: classNames(`${classPrefix}-item`, {\n        [`${classPrefix}-item-expand`]: isExpand\n      })\n    }, option[labelName], !!dotMap.get(option[valueName]) && React.createElement(\"div\", {\n      className: `${classPrefix}-dot`\n    }));\n  };\n  // 渲染叶子节点\n  const renderLeafItem = option => {\n    const isSelected = allSelectedLeafKeys.includes(option[valueName]);\n    return React.createElement(\"div\", {\n      key: option[valueName],\n      onClick: () => {\n        if (isSelected) {\n          onChange(allSelectedLeafKeys.filter(val => val !== option[valueName]));\n        } else {\n          onChange([...allSelectedLeafKeys, option[valueName]]);\n        }\n      },\n      className: classNames(`${classPrefix}-item`, `${classPrefix}-item-leaf`)\n    }, React.createElement(Checkbox, {\n      className: `${classPrefix}-item-checkbox`,\n      checked: isSelected\n    }), option[labelName]);\n  };\n  const renderItems = (columnOptions = [], index) => {\n    if (columnOptions.length === 0) {\n      return;\n    }\n    const isLeaf = deep === index + 1;\n    if (isLeaf) {\n      return React.createElement(React.Fragment, null, renderSelectAllLeafItem(columnOptions, index), columnOptions.map(option => renderLeafItem(option)));\n    }\n    return React.createElement(React.Fragment, null, renderSelectAllItem(columnOptions, index), columnOptions.map(option => renderItem(option)));\n  };\n  const renderColumns = () => {\n    var _a;\n    const columns = [];\n    for (let i = 0; i < deep; i++) {\n      let width = `${100 / deep}%`;\n      // 两列的第一列宽度为 33.33，两列的第二列为 66.67%\n      if (deep === 2 && i === 0) {\n        width = `33.33%`;\n      }\n      if (deep === 2 && i === 1) {\n        width = `66.67%`;\n      }\n      const column = React.createElement(\"div\", {\n        key: i,\n        className: classNames(`${classPrefix}-column`),\n        style: {\n          width\n        }\n      }, renderItems(i === 0 ? props.options : (_a = optionsMap.get(expandKeys[i - 1])) === null || _a === void 0 ? void 0 : _a[childrenName], i));\n      columns.push(column);\n    }\n    return columns;\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, renderColumns()));\n};"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AACjD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,QAAQ,MAAM,aAAa;AAClC,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,aAAa,QAAQ,aAAa;AAC3C,MAAMC,WAAW,GAAG,0BAA0B;AAC9C,OAAO,MAAMC,QAAQ,GAAGC,CAAC,IAAI;EAC3B,MAAMC,KAAK,GAAGR,UAAU,CAAC;IACvBS,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE,CAAC,CAAC;IACdC,aAAa,EAAE,EAAE;IACjBC,iBAAiB,EAAE,EAAE;IACrBC,YAAY,EAAE;EAChB,CAAC,EAAEN,CAAC,CAAC;EACLX,SAAS,CAAC,MAAM;IACdO,UAAU,CAAC,YAAY,EAAE,0CAA0C,CAAC;EACtE,CAAC,EAAE,EAAE,CAAC;EACN,MAAM,CAACW,SAAS,EAAEC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,aAAa,CAACI,KAAK,CAACE,UAAU,CAAC;EAC5E;EACA,MAAM,CAACO,UAAU,EAAEC,aAAa,CAAC,GAAGhB,aAAa,CAAC;IAChDiB,KAAK,EAAEX,KAAK,CAACS,UAAU;IACvBJ,YAAY,EAAEL,KAAK,CAACI;EACtB,CAAC,CAAC;EACF;EACA,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,aAAa,CAAC;IACtCiB,KAAK,EAAEX,KAAK,CAACW,KAAK;IAClBN,YAAY,EAAEL,KAAK,CAACK;EACtB,CAAC,CAAC;EACF;EACA,MAAMQ,WAAW,GAAGC,MAAM,IAAI;IAC5B,MAAMC,IAAI,GAAG,EAAE;IACf,MAAMC,MAAM,GAAGC,EAAE,IAAI;MACnB,IAAIC,EAAE;MACN,IAAI,CAACD,EAAE,EAAE;QACP;MACF;MACA,IAAI,CAACC,EAAE,GAAGD,EAAE,CAACT,YAAY,CAAC,MAAM,IAAI,IAAIU,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,MAAM,EAAE;QAC1EF,EAAE,CAACT,YAAY,CAAC,CAACY,OAAO,CAACC,CAAC,IAAIL,MAAM,CAACK,CAAC,CAAC,CAAC;MAC1C,CAAC,MAAM;QACLN,IAAI,CAACO,IAAI,CAACL,EAAE,CAACV,SAAS,CAAC,CAAC;MAC1B;IACF,CAAC;IACDS,MAAM,CAACF,MAAM,CAAC;IACd,OAAOC,IAAI;EACb,CAAC;EACD,MAAM,CAACQ,IAAI,EAAEC,UAAU,EAAEC,gBAAgB,CAAC,GAAGpC,OAAO,CAAC,MAAM;IACzD,MAAMkC,IAAI,GAAGhC,WAAW,CAACS,KAAK,CAACC,OAAO,EAAEO,YAAY,CAAC;IACrD,MAAMgB,UAAU,GAAG,IAAIE,GAAG,CAAC,CAAC;IAC5B,MAAMD,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAClC,SAASC,QAAQA,CAACC,OAAO,EAAEC,QAAQ,EAAE;MACnCA,QAAQ,CAACT,OAAO,CAACU,IAAI,IAAI;QACvBL,gBAAgB,CAACM,GAAG,CAACD,IAAI,CAACvB,SAAS,CAAC,EAAEqB,OAAO,CAAC;QAC9CJ,UAAU,CAACO,GAAG,CAACD,IAAI,CAACvB,SAAS,CAAC,EAAEuB,IAAI,CAAC;QACrC,IAAIA,IAAI,CAACtB,YAAY,CAAC,EAAE;UACtBmB,QAAQ,CAACG,IAAI,EAAEA,IAAI,CAACtB,YAAY,CAAC,CAAC;QACpC;MACF,CAAC,CAAC;IACJ;IACAmB,QAAQ,CAACK,SAAS,EAAEhC,KAAK,CAACC,OAAO,CAAC;IAClC,OAAO,CAACsB,IAAI,EAAEC,UAAU,EAAEC,gBAAgB,CAAC;EAC7C,CAAC,EAAE,CAACzB,KAAK,CAACC,OAAO,CAAC,CAAC;EACnB;EACA,MAAMgC,mBAAmB,GAAG5C,OAAO,CAAC,MAAM;IACxC,IAAI6C,QAAQ,GAAG,EAAE;IACjBvB,KAAK,CAACS,OAAO,CAACe,CAAC,IAAI;MACjB,MAAMrB,MAAM,GAAGU,UAAU,CAACY,GAAG,CAACD,CAAC,CAAC;MAChCD,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACxB,WAAW,CAACC,MAAM,CAAC,CAAC;IACjD,CAAC,CAAC;IACF,OAAOoB,QAAQ;EACjB,CAAC,EAAE,CAACvB,KAAK,EAAEa,UAAU,CAAC,CAAC;EACvB;EACA,MAAMc,MAAM,GAAGjD,OAAO,CAAC,MAAM;IAC3B,MAAMkD,GAAG,GAAG,IAAIb,GAAG,CAAC,CAAC;IACrB;IACA,MAAMV,MAAM,GAAGwB,GAAG,IAAI;MACpB,MAAMC,YAAY,GAAGhB,gBAAgB,CAACW,GAAG,CAACI,GAAG,CAAC;MAC9C,IAAI,CAACC,YAAY,EAAE;QACjB;MACF;MACAF,GAAG,CAACR,GAAG,CAACU,YAAY,CAAClC,SAAS,CAAC,EAAE,IAAI,CAAC;MACtCS,MAAM,CAACyB,YAAY,CAAClC,SAAS,CAAC,CAAC;IACjC,CAAC;IACD0B,mBAAmB,CAACb,OAAO,CAACoB,GAAG,IAAI;MACjCD,GAAG,CAACR,GAAG,CAACS,GAAG,EAAE,IAAI,CAAC;MAClBxB,MAAM,CAACwB,GAAG,CAAC;IACb,CAAC,CAAC;IACF,OAAOD,GAAG;EACZ,CAAC,EAAE,CAACd,gBAAgB,EAAEd,KAAK,CAAC,CAAC;EAC7B,MAAM+B,QAAQ,GAAGC,UAAU,IAAI;IAC7B,IAAIzB,EAAE;IACN,IAAI0B,SAAS,GAAG,CAAC,GAAGD,UAAU,CAAC;IAC/B,IAAIE,UAAU,GAAG,EAAE;IACnB,MAAM7B,MAAM,GAAGD,IAAI,IAAI;MACrBA,IAAI,CAACK,OAAO,CAACoB,GAAG,IAAI;QAClB,IAAItB,EAAE;QACN,IAAI2B,UAAU,CAACC,QAAQ,CAACN,GAAG,CAAC,EAAE;UAC5B;QACF;QACA,MAAMO,MAAM,GAAGtB,gBAAgB,CAACW,GAAG,CAACI,GAAG,CAAC;QACxC,IAAI,CAACO,MAAM,EAAE;UACX;QACF;QACA,MAAMC,YAAY,GAAG,CAAC,CAAC9B,EAAE,GAAG6B,MAAM,CAACvC,YAAY,CAAC,MAAM,IAAI,IAAIU,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqB,GAAG,CAAClB,CAAC,IAAIA,CAAC,CAACd,SAAS,CAAC,CAAC,KAAK,EAAE;QACvH,IAAIyC,YAAY,CAACC,KAAK,CAAC5B,CAAC,IAAIuB,SAAS,CAACE,QAAQ,CAACzB,CAAC,CAAC,CAAC,EAAE;UAClDuB,SAAS,CAACtB,IAAI,CAACyB,MAAM,CAACxC,SAAS,CAAC,CAAC;UACjCsC,UAAU,GAAGA,UAAU,CAACR,MAAM,CAACW,YAAY,CAAC;QAC9C;MACF,CAAC,CAAC;IACJ,CAAC;IACD;IACA,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,IAAI,EAAEF,CAAC,EAAE,EAAE;MAC7BL,MAAM,CAAC4B,SAAS,CAAC;IACnB;IACAA,SAAS,GAAGA,SAAS,CAACM,MAAM,CAAC7B,CAAC,IAAI,CAACwB,UAAU,CAACC,QAAQ,CAACzB,CAAC,CAAC,CAAC;IAC1D;IACA,MAAM8B,YAAY,GAAGP,SAAS,CAACL,GAAG,CAAClB,CAAC,IAAIG,UAAU,CAACY,GAAG,CAACf,CAAC,CAAC,CAAC;IAC1DT,QAAQ,CAACgC,SAAS,CAAC;IACnB,CAAC1B,EAAE,GAAGlB,KAAK,CAAC0C,QAAQ,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkC,IAAI,CAACpD,KAAK,EAAE4C,SAAS,EAAEO,YAAY,CAAC;EACpG,CAAC;EACD,MAAME,YAAY,GAAGvC,MAAM,IAAI;IAC7B,IAAII,EAAE;IACN,MAAMoC,WAAW,GAAG,EAAE;IACtB,IAAI1B,OAAO,GAAGd,MAAM;IACpB,OAAOc,OAAO,EAAE;MACd0B,WAAW,CAACC,OAAO,CAAC3B,OAAO,CAAC;MAC5B,MAAM4B,IAAI,GAAG/B,gBAAgB,CAACW,GAAG,CAACR,OAAO,CAACrB,SAAS,CAAC,CAAC;MACrDqB,OAAO,GAAG4B,IAAI;IAChB;IACA,MAAMzC,IAAI,GAAGuC,WAAW,CAACf,GAAG,CAAClB,CAAC,IAAIA,CAAC,CAACd,SAAS,CAAC,CAAC;IAC/CG,aAAa,CAACK,IAAI,CAAC;IACnB,CAACG,EAAE,GAAGlB,KAAK,CAACyD,QAAQ,MAAM,IAAI,IAAIvC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkC,IAAI,CAACpD,KAAK,EAAEe,IAAI,EAAEuC,WAAW,CAAC;EAC9F,CAAC;EACD;EACA,MAAMI,mBAAmB,GAAGA,CAACC,aAAa,EAAEC,KAAK,KAAK;IACpD,IAAI1C,EAAE;IACN,MAAM2C,IAAI,GAAG,CAAC3C,EAAE,GAAGlB,KAAK,CAAC8D,aAAa,MAAM,IAAI,IAAI5C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0C,KAAK,CAAC;IACtF,IAAI,CAACC,IAAI,EAAE;MACT;IACF;IACA,IAAIE,eAAe,GAAG,EAAE;IACxBJ,aAAa,CAACvC,OAAO,CAACN,MAAM,IAAI;MAC9BiD,eAAe,GAAGA,eAAe,CAAC1B,MAAM,CAACxB,WAAW,CAACC,MAAM,CAAC,CAAC;IAC/D,CAAC,CAAC;IACF,MAAMkD,WAAW,GAAGD,eAAe,CAACd,KAAK,CAAC5B,CAAC,IAAIY,mBAAmB,CAACa,QAAQ,CAACzB,CAAC,CAAC,CAAC;IAC/E,OAAOlC,KAAK,CAAC8E,aAAa,CAAC,KAAK,EAAE;MAChCC,OAAO,EAAEA,CAAA,KAAM;QACb,IAAIF,WAAW,EAAE;UACftB,QAAQ,CAACT,mBAAmB,CAACiB,MAAM,CAAC7B,CAAC,IAAI,CAAC0C,eAAe,CAACjB,QAAQ,CAACzB,CAAC,CAAC,CAAC,CAAC;QACzE,CAAC,MAAM;UACLqB,QAAQ,CAACT,mBAAmB,CAACI,MAAM,CAAC0B,eAAe,CAAC,CAAC;QACvD;MACF,CAAC;MACDI,SAAS,EAAE,GAAGtE,WAAW;IAC3B,CAAC,EAAEgE,IAAI,CAAC;EACV,CAAC;EACD;EACA,MAAMO,uBAAuB,GAAGA,CAACT,aAAa,EAAEC,KAAK,KAAK;IACxD,IAAI1C,EAAE;IACN,MAAM2C,IAAI,GAAG,CAAC3C,EAAE,GAAGlB,KAAK,CAAC8D,aAAa,MAAM,IAAI,IAAI5C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0C,KAAK,CAAC;IACtF,IAAI,CAACC,IAAI,EAAE;MACT;IACF;IACA,MAAME,eAAe,GAAGJ,aAAa,CAACpB,GAAG,CAAClB,CAAC,IAAIA,CAAC,CAACd,SAAS,CAAC,CAAC;IAC5D,MAAMyD,WAAW,GAAGD,eAAe,CAACd,KAAK,CAAC5B,CAAC,IAAIY,mBAAmB,CAACa,QAAQ,CAACzB,CAAC,CAAC,CAAC;IAC/E,MAAMgD,YAAY,GAAGL,WAAW,GAAG,KAAK,GAAGD,eAAe,CAACO,IAAI,CAACjD,CAAC,IAAIY,mBAAmB,CAACa,QAAQ,CAACzB,CAAC,CAAC,CAAC;IACrG,OAAOlC,KAAK,CAAC8E,aAAa,CAAC,KAAK,EAAE;MAChCC,OAAO,EAAEA,CAAA,KAAM;QACb,IAAIF,WAAW,EAAE;UACftB,QAAQ,CAACT,mBAAmB,CAACiB,MAAM,CAAC7B,CAAC,IAAI,CAAC0C,eAAe,CAACjB,QAAQ,CAACzB,CAAC,CAAC,CAAC,CAAC;QACzE,CAAC,MAAM;UACLqB,QAAQ,CAACT,mBAAmB,CAACI,MAAM,CAAC0B,eAAe,CAAC,CAAC;QACvD;MACF,CAAC;MACDI,SAAS,EAAEjF,UAAU,CAAC,GAAGW,WAAW,OAAO,EAAE,GAAGA,WAAW,YAAY;IACzE,CAAC,EAAEV,KAAK,CAAC8E,aAAa,CAACxE,QAAQ,EAAE;MAC/B0E,SAAS,EAAE,GAAGtE,WAAW,gBAAgB;MACzC0E,OAAO,EAAEP,WAAW;MACpBQ,aAAa,EAAEH;IACjB,CAAC,CAAC,EAAER,IAAI,CAAC;EACX,CAAC;EACD;EACA,MAAMY,UAAU,GAAG3D,MAAM,IAAI;IAC3B,MAAM4D,QAAQ,GAAGjE,UAAU,CAACqC,QAAQ,CAAChC,MAAM,CAACP,SAAS,CAAC,CAAC;IACvD,OAAOpB,KAAK,CAAC8E,aAAa,CAAC,KAAK,EAAE;MAChCzB,GAAG,EAAE1B,MAAM,CAACP,SAAS,CAAC;MACtB2D,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,CAACQ,QAAQ,EAAE;UACbrB,YAAY,CAACvC,MAAM,CAAC;QACtB;MACF,CAAC;MACDqD,SAAS,EAAEjF,UAAU,CAAC,GAAGW,WAAW,OAAO,EAAE;QAC3C,CAAC,GAAGA,WAAW,cAAc,GAAG6E;MAClC,CAAC;IACH,CAAC,EAAE5D,MAAM,CAACR,SAAS,CAAC,EAAE,CAAC,CAACgC,MAAM,CAACF,GAAG,CAACtB,MAAM,CAACP,SAAS,CAAC,CAAC,IAAIpB,KAAK,CAAC8E,aAAa,CAAC,KAAK,EAAE;MAClFE,SAAS,EAAE,GAAGtE,WAAW;IAC3B,CAAC,CAAC,CAAC;EACL,CAAC;EACD;EACA,MAAM8E,cAAc,GAAG7D,MAAM,IAAI;IAC/B,MAAM8D,UAAU,GAAG3C,mBAAmB,CAACa,QAAQ,CAAChC,MAAM,CAACP,SAAS,CAAC,CAAC;IAClE,OAAOpB,KAAK,CAAC8E,aAAa,CAAC,KAAK,EAAE;MAChCzB,GAAG,EAAE1B,MAAM,CAACP,SAAS,CAAC;MACtB2D,OAAO,EAAEA,CAAA,KAAM;QACb,IAAIU,UAAU,EAAE;UACdlC,QAAQ,CAACT,mBAAmB,CAACiB,MAAM,CAAC2B,GAAG,IAAIA,GAAG,KAAK/D,MAAM,CAACP,SAAS,CAAC,CAAC,CAAC;QACxE,CAAC,MAAM;UACLmC,QAAQ,CAAC,CAAC,GAAGT,mBAAmB,EAAEnB,MAAM,CAACP,SAAS,CAAC,CAAC,CAAC;QACvD;MACF,CAAC;MACD4D,SAAS,EAAEjF,UAAU,CAAC,GAAGW,WAAW,OAAO,EAAE,GAAGA,WAAW,YAAY;IACzE,CAAC,EAAEV,KAAK,CAAC8E,aAAa,CAACxE,QAAQ,EAAE;MAC/B0E,SAAS,EAAE,GAAGtE,WAAW,gBAAgB;MACzC0E,OAAO,EAAEK;IACX,CAAC,CAAC,EAAE9D,MAAM,CAACR,SAAS,CAAC,CAAC;EACxB,CAAC;EACD,MAAMwE,WAAW,GAAGA,CAACnB,aAAa,GAAG,EAAE,EAAEC,KAAK,KAAK;IACjD,IAAID,aAAa,CAACxC,MAAM,KAAK,CAAC,EAAE;MAC9B;IACF;IACA,MAAM4D,MAAM,GAAGxD,IAAI,KAAKqC,KAAK,GAAG,CAAC;IACjC,IAAImB,MAAM,EAAE;MACV,OAAO5F,KAAK,CAAC8E,aAAa,CAAC9E,KAAK,CAAC6F,QAAQ,EAAE,IAAI,EAAEZ,uBAAuB,CAACT,aAAa,EAAEC,KAAK,CAAC,EAAED,aAAa,CAACpB,GAAG,CAACzB,MAAM,IAAI6D,cAAc,CAAC7D,MAAM,CAAC,CAAC,CAAC;IACtJ;IACA,OAAO3B,KAAK,CAAC8E,aAAa,CAAC9E,KAAK,CAAC6F,QAAQ,EAAE,IAAI,EAAEtB,mBAAmB,CAACC,aAAa,EAAEC,KAAK,CAAC,EAAED,aAAa,CAACpB,GAAG,CAACzB,MAAM,IAAI2D,UAAU,CAAC3D,MAAM,CAAC,CAAC,CAAC;EAC9I,CAAC;EACD,MAAMmE,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI/D,EAAE;IACN,MAAMgE,OAAO,GAAG,EAAE;IAClB,KAAK,IAAI7D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,IAAI,EAAEF,CAAC,EAAE,EAAE;MAC7B,IAAI8D,KAAK,GAAG,GAAG,GAAG,GAAG5D,IAAI,GAAG;MAC5B;MACA,IAAIA,IAAI,KAAK,CAAC,IAAIF,CAAC,KAAK,CAAC,EAAE;QACzB8D,KAAK,GAAG,QAAQ;MAClB;MACA,IAAI5D,IAAI,KAAK,CAAC,IAAIF,CAAC,KAAK,CAAC,EAAE;QACzB8D,KAAK,GAAG,QAAQ;MAClB;MACA,MAAMC,MAAM,GAAGjG,KAAK,CAAC8E,aAAa,CAAC,KAAK,EAAE;QACxCzB,GAAG,EAAEnB,CAAC;QACN8C,SAAS,EAAEjF,UAAU,CAAC,GAAGW,WAAW,SAAS,CAAC;QAC9CwF,KAAK,EAAE;UACLF;QACF;MACF,CAAC,EAAEL,WAAW,CAACzD,CAAC,KAAK,CAAC,GAAGrB,KAAK,CAACC,OAAO,GAAG,CAACiB,EAAE,GAAGM,UAAU,CAACY,GAAG,CAAC3B,UAAU,CAACY,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACV,YAAY,CAAC,EAAEa,CAAC,CAAC,CAAC;MAC5I6D,OAAO,CAAC5D,IAAI,CAAC8D,MAAM,CAAC;IACtB;IACA,OAAOF,OAAO;EAChB,CAAC;EACD,OAAO5F,eAAe,CAACU,KAAK,EAAEb,KAAK,CAAC8E,aAAa,CAAC,KAAK,EAAE;IACvDE,SAAS,EAAEtE;EACb,CAAC,EAAEoF,aAAa,CAAC,CAAC,CAAC,CAAC;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}