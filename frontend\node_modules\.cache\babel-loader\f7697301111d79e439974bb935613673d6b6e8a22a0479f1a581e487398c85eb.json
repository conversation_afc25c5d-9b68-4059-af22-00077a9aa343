{"ast": null, "code": "import * as React from \"react\";\nfunction PhoneFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PhoneFill-PhoneFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PhoneFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"PhoneFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M15.1766716,31.9445426 C8.09652665,24.7479935 4.37345146,18.0644855 4.00844409,11.8899316 L4.0084441,11.8899317 C3.92661639,10.5018215 4.44249252,9.1450022 5.42588939,8.16187843 L8.87520905,4.71264482 L8.875209,4.71264487 C9.82549238,3.76245173 11.3661372,3.76245173 12.3164158,4.71264476 L17.4782284,9.87432857 L17.4782283,9.87432851 C18.4284452,10.8245882 18.4284452,12.3651945 17.4782284,13.3154495 L14.1657865,16.628821 L14.1657866,16.628821 C13.8067263,16.9875805 13.7081153,17.5315453 13.9183928,17.9935105 C15.7748593,22.0642535 17.9030566,25.2995591 20.301982,27.6984247 C22.7008932,30.097276 25.9362605,32.2254155 30.0071383,34.0818547 L30.0071383,34.0818546 C30.4691155,34.2921269 31.01309,34.1935184 31.3718619,33.8344671 L34.6853161,30.5221078 L34.685316,30.5221079 C35.6355994,29.5719147 37.1762442,29.5719147 38.1265228,30.5221078 L43.2873373,35.6837916 L43.2873374,35.6837916 C44.2375542,36.6340513 44.2375542,38.1746576 43.2873373,39.1249125 L39.83903,42.5741462 L39.8390299,42.5741463 C38.8558816,43.5575172 37.4990332,44.0733814 36.1108835,43.9915562 C29.8114647,43.6194609 22.9787453,39.7504807 15.6147216,32.3856094 L15.1766716,31.9445426 Z\",\n    id: \"PhoneFill-\\u8DEF\\u5F84\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default PhoneFill;", "map": {"version": 3, "names": ["React", "PhoneFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/PhoneFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction PhoneFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PhoneFill-PhoneFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PhoneFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"PhoneFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M15.1766716,31.9445426 C8.09652665,24.7479935 4.37345146,18.0644855 4.00844409,11.8899316 L4.0084441,11.8899317 C3.92661639,10.5018215 4.44249252,9.1450022 5.42588939,8.16187843 L8.87520905,4.71264482 L8.875209,4.71264487 C9.82549238,3.76245173 11.3661372,3.76245173 12.3164158,4.71264476 L17.4782284,9.87432857 L17.4782283,9.87432851 C18.4284452,10.8245882 18.4284452,12.3651945 17.4782284,13.3154495 L14.1657865,16.628821 L14.1657866,16.628821 C13.8067263,16.9875805 13.7081153,17.5315453 13.9183928,17.9935105 C15.7748593,22.0642535 17.9030566,25.2995591 20.301982,27.6984247 C22.7008932,30.097276 25.9362605,32.2254155 30.0071383,34.0818547 L30.0071383,34.0818546 C30.4691155,34.2921269 31.01309,34.1935184 31.3718619,33.8344671 L34.6853161,30.5221078 L34.685316,30.5221079 C35.6355994,29.5719147 37.1762442,29.5719147 38.1265228,30.5221078 L43.2873373,35.6837916 L43.2873374,35.6837916 C44.2375542,36.6340513 44.2375542,38.1746576 43.2873373,39.1249125 L39.83903,42.5741462 L39.8390299,42.5741463 C38.8558816,43.5575172 37.4990332,44.0733814 36.1108835,43.9915562 C29.8114647,43.6194609 22.9787453,39.7504807 15.6147216,32.3856094 L15.1766716,31.9445426 Z\",\n    id: \"PhoneFill-\\u8DEF\\u5F84\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default PhoneFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,qBAAqB;IACzBC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,wBAAwB;IAC5BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,0oCAA0oC;IAC7oCR,EAAE,EAAE,wBAAwB;IAC5BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}