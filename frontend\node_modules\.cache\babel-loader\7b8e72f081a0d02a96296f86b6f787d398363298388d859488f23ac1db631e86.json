{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\components\\\\CameraTestPage.js\",\n  _s = $RefreshSig$();\n/**\n * 相機系統測試頁面\n * 用於測試新的環境自適應相機功能\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Button, Card, Space, Toast } from 'antd-mobile';\nimport { CameraOutline, CheckOutline } from 'antd-mobile-icons';\nimport { getCameraManager } from '../utils/cameraManager';\nimport { getEnvironmentInfo } from '../utils/deviceDetector';\nimport MobileCameraModal from './MobileCameraModal';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CameraTestPage = () => {\n  _s();\n  const [cameraManager, setCameraManager] = useState(null);\n  const [deviceInfo, setDeviceInfo] = useState(null);\n  const [cameraStatus, setCameraStatus] = useState('未初始化');\n  const [showMobileCamera, setShowMobileCamera] = useState(false);\n  const [capturedImage, setCapturedImage] = useState(null);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // 初始化\n  useEffect(() => {\n    const initializeSystem = async () => {\n      try {\n        // 獲取設備信息\n        const envInfo = await getEnvironmentInfo();\n        setDeviceInfo(envInfo);\n        setIsMobile(envInfo.deviceType === 'mobile' || envInfo.deviceType === 'tablet');\n\n        // 初始化相機管理器\n        const manager = getCameraManager();\n        setCameraManager(manager);\n        setCameraStatus('已初始化');\n        console.log('設備信息:', envInfo);\n        console.log('相機管理器:', manager);\n      } catch (error) {\n        console.error('初始化失敗:', error);\n        setCameraStatus('初始化失敗');\n        Toast.show({\n          content: '系統初始化失敗',\n          position: 'center'\n        });\n      }\n    };\n    initializeSystem();\n  }, []);\n\n  // 測試相機啟動\n  const handleTestCamera = async () => {\n    if (!cameraManager) {\n      Toast.show({\n        content: '相機管理器未初始化',\n        position: 'center'\n      });\n      return;\n    }\n    try {\n      setCameraStatus('正在啟動相機...');\n      if (isMobile) {\n        // 移動端：使用全屏相機\n        setShowMobileCamera(true);\n      } else {\n        // Web端：直接啟動相機\n        await cameraManager.startCamera('back');\n        setCameraStatus('相機已啟動 (Web模式)');\n        Toast.show({\n          content: '相機啟動成功！',\n          position: 'center'\n        });\n      }\n    } catch (error) {\n      console.error('相機啟動失敗:', error);\n      setCameraStatus('相機啟動失敗');\n      Toast.show({\n        content: '相機啟動失敗，請檢查權限',\n        position: 'center'\n      });\n    }\n  };\n\n  // 測試拍照\n  const handleTestPhoto = async () => {\n    if (!cameraManager) return;\n    try {\n      setCameraStatus('正在拍照...');\n      const result = await cameraManager.takePhoto();\n      if (result && result.file) {\n        const reader = new FileReader();\n        reader.onload = e => {\n          setCapturedImage(e.target.result);\n        };\n        reader.readAsDataURL(result.file);\n        setCameraStatus('拍照成功');\n        Toast.show({\n          content: '拍照成功！',\n          position: 'center'\n        });\n      }\n    } catch (error) {\n      console.error('拍照失敗:', error);\n      setCameraStatus('拍照失敗');\n      Toast.show({\n        content: '拍照失敗，請重試',\n        position: 'center'\n      });\n    }\n  };\n\n  // 停止相機\n  const handleStopCamera = () => {\n    if (cameraManager) {\n      cameraManager.stopCamera();\n      setCameraStatus('相機已停止');\n    }\n  };\n\n  // 移動端拍照完成回調\n  const handleMobilePhotoTaken = data => {\n    if (data && data.file) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        setCapturedImage(e.target.result);\n      };\n      reader.readAsDataURL(data.file);\n      setCameraStatus('拍照成功 (移動端模式)');\n      Toast.show({\n        content: '拍照成功！',\n        position: 'center'\n      });\n    }\n    setShowMobileCamera(false);\n  };\n\n  // 關閉移動端相機\n  const handleCloseMobileCamera = () => {\n    setShowMobileCamera(false);\n    setCameraStatus('相機已關閉');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '16px',\n      minHeight: '100vh',\n      background: '#f5f5f5'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u76F8\\u6A5F\\u7CFB\\u7D71\\u6E2C\\u8A66\",\n      style: {\n        marginBottom: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u72C0\\u614B\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), \" \", cameraStatus]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), deviceInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8A2D\\u5099\\u985E\\u578B\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this), \" \", deviceInfo.deviceType, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u76F8\\u6A5F\\u6A21\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this), \" \", isMobile ? '移動端全屏' : 'Web端Modal', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u76F8\\u6A5F\\u652F\\u6301\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this), \" \", deviceInfo.camera.hasCamera ? '是' : '否', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u53EF\\u7528\\u76F8\\u6A5F\\u6578\\u91CF\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this), \" \", deviceInfo.camera.availableCameras.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u76F8\\u6A5F\\u63A7\\u5236\",\n      style: {\n        marginBottom: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          color: \"primary\",\n          size: \"large\",\n          block: true,\n          onClick: handleTestCamera,\n          disabled: !cameraManager,\n          children: [/*#__PURE__*/_jsxDEV(CameraOutline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), \" \\u555F\\u52D5\\u76F8\\u6A5F\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), !isMobile && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            color: \"primary\",\n            size: \"large\",\n            block: true,\n            onClick: handleTestPhoto,\n            disabled: !cameraManager || cameraStatus !== '相機已啟動 (Web模式)',\n            children: [/*#__PURE__*/_jsxDEV(CheckOutline, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), \" \\u62CD\\u7167\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"default\",\n            size: \"large\",\n            block: true,\n            onClick: handleStopCamera,\n            disabled: !cameraManager,\n            children: \"\\u505C\\u6B62\\u76F8\\u6A5F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this), capturedImage && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u62CD\\u651D\\u7D50\\u679C\",\n      style: {\n        marginBottom: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: capturedImage,\n        alt: \"\\u62CD\\u651D\\u7684\\u7167\\u7247\",\n        style: {\n          width: '100%',\n          height: '350px',\n          objectFit: 'cover',\n          borderRadius: '8px',\n          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 9\n    }, this), deviceInfo && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u8A73\\u7D30\\u8A2D\\u5099\\u4FE1\\u606F\",\n      children: /*#__PURE__*/_jsxDEV(\"pre\", {\n        style: {\n          fontSize: '12px',\n          background: '#f0f0f0',\n          padding: '8px',\n          borderRadius: '4px',\n          overflow: 'auto'\n        },\n        children: JSON.stringify(deviceInfo, null, 2)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 9\n    }, this), isMobile && /*#__PURE__*/_jsxDEV(MobileCameraModal, {\n      visible: showMobileCamera,\n      onClose: handleCloseMobileCamera,\n      onPhotoTaken: handleMobilePhotoTaken,\n      cameraManager: cameraManager,\n      target: \"back\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n};\n_s(CameraTestPage, \"ab/l83dNVxiFnCUb5E+XXt+gFfQ=\");\n_c = CameraTestPage;\nexport default CameraTestPage;\nvar _c;\n$RefreshReg$(_c, \"CameraTestPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "Card", "Space", "Toast", "CameraOutline", "CheckOutline", "getCameraManager", "getEnvironmentInfo", "MobileCameraModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CameraTestPage", "_s", "cameraManager", "setCameraManager", "deviceInfo", "setDeviceInfo", "cameraStatus", "setCameraStatus", "showMobileCamera", "setShowMobileCamera", "capturedImage", "setCapturedImage", "isMobile", "setIsMobile", "initializeSystem", "envInfo", "deviceType", "manager", "console", "log", "error", "show", "content", "position", "handleTestCamera", "startCamera", "handleTestPhoto", "result", "<PERSON><PERSON><PERSON><PERSON>", "file", "reader", "FileReader", "onload", "e", "target", "readAsDataURL", "handleStopCamera", "stopCamera", "handleMobilePhotoTaken", "data", "handleCloseMobileCamera", "style", "padding", "minHeight", "background", "children", "title", "marginBottom", "direction", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "camera", "hasCamera", "availableCameras", "length", "color", "size", "block", "onClick", "disabled", "src", "alt", "height", "objectFit", "borderRadius", "boxShadow", "fontSize", "overflow", "JSON", "stringify", "visible", "onClose", "onPhotoTaken", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/CameraTestPage.js"], "sourcesContent": ["/**\n * 相機系統測試頁面\n * 用於測試新的環境自適應相機功能\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Button, Card, Space, Toast } from 'antd-mobile';\nimport { CameraOutline, CheckOutline } from 'antd-mobile-icons';\nimport { getCameraManager } from '../utils/cameraManager';\nimport { getEnvironmentInfo } from '../utils/deviceDetector';\nimport MobileCameraModal from './MobileCameraModal';\n\nconst CameraTestPage = () => {\n  const [cameraManager, setCameraManager] = useState(null);\n  const [deviceInfo, setDeviceInfo] = useState(null);\n  const [cameraStatus, setCameraStatus] = useState('未初始化');\n  const [showMobileCamera, setShowMobileCamera] = useState(false);\n  const [capturedImage, setCapturedImage] = useState(null);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // 初始化\n  useEffect(() => {\n    const initializeSystem = async () => {\n      try {\n        // 獲取設備信息\n        const envInfo = await getEnvironmentInfo();\n        setDeviceInfo(envInfo);\n        setIsMobile(envInfo.deviceType === 'mobile' || envInfo.deviceType === 'tablet');\n        \n        // 初始化相機管理器\n        const manager = getCameraManager();\n        setCameraManager(manager);\n        \n        setCameraStatus('已初始化');\n        \n        console.log('設備信息:', envInfo);\n        console.log('相機管理器:', manager);\n        \n      } catch (error) {\n        console.error('初始化失敗:', error);\n        setCameraStatus('初始化失敗');\n        Toast.show({\n          content: '系統初始化失敗',\n          position: 'center'\n        });\n      }\n    };\n    \n    initializeSystem();\n  }, []);\n\n  // 測試相機啟動\n  const handleTestCamera = async () => {\n    if (!cameraManager) {\n      Toast.show({\n        content: '相機管理器未初始化',\n        position: 'center'\n      });\n      return;\n    }\n\n    try {\n      setCameraStatus('正在啟動相機...');\n      \n      if (isMobile) {\n        // 移動端：使用全屏相機\n        setShowMobileCamera(true);\n      } else {\n        // Web端：直接啟動相機\n        await cameraManager.startCamera('back');\n        setCameraStatus('相機已啟動 (Web模式)');\n        \n        Toast.show({\n          content: '相機啟動成功！',\n          position: 'center'\n        });\n      }\n    } catch (error) {\n      console.error('相機啟動失敗:', error);\n      setCameraStatus('相機啟動失敗');\n      Toast.show({\n        content: '相機啟動失敗，請檢查權限',\n        position: 'center'\n      });\n    }\n  };\n\n  // 測試拍照\n  const handleTestPhoto = async () => {\n    if (!cameraManager) return;\n\n    try {\n      setCameraStatus('正在拍照...');\n      const result = await cameraManager.takePhoto();\n      \n      if (result && result.file) {\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          setCapturedImage(e.target.result);\n        };\n        reader.readAsDataURL(result.file);\n        \n        setCameraStatus('拍照成功');\n        Toast.show({\n          content: '拍照成功！',\n          position: 'center'\n        });\n      }\n    } catch (error) {\n      console.error('拍照失敗:', error);\n      setCameraStatus('拍照失敗');\n      Toast.show({\n        content: '拍照失敗，請重試',\n        position: 'center'\n      });\n    }\n  };\n\n  // 停止相機\n  const handleStopCamera = () => {\n    if (cameraManager) {\n      cameraManager.stopCamera();\n      setCameraStatus('相機已停止');\n    }\n  };\n\n  // 移動端拍照完成回調\n  const handleMobilePhotoTaken = (data) => {\n    if (data && data.file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setCapturedImage(e.target.result);\n      };\n      reader.readAsDataURL(data.file);\n      \n      setCameraStatus('拍照成功 (移動端模式)');\n      Toast.show({\n        content: '拍照成功！',\n        position: 'center'\n      });\n    }\n    setShowMobileCamera(false);\n  };\n\n  // 關閉移動端相機\n  const handleCloseMobileCamera = () => {\n    setShowMobileCamera(false);\n    setCameraStatus('相機已關閉');\n  };\n\n  return (\n    <div style={{ padding: '16px', minHeight: '100vh', background: '#f5f5f5' }}>\n      <Card title=\"相機系統測試\" style={{ marginBottom: '16px' }}>\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <div>\n            <strong>狀態：</strong> {cameraStatus}\n          </div>\n          \n          {deviceInfo && (\n            <div>\n              <strong>設備類型：</strong> {deviceInfo.deviceType}\n              <br />\n              <strong>相機模式：</strong> {isMobile ? '移動端全屏' : 'Web端Modal'}\n              <br />\n              <strong>相機支持：</strong> {deviceInfo.camera.hasCamera ? '是' : '否'}\n              <br />\n              <strong>可用相機數量：</strong> {deviceInfo.camera.availableCameras.length}\n            </div>\n          )}\n        </Space>\n      </Card>\n\n      <Card title=\"相機控制\" style={{ marginBottom: '16px' }}>\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <Button \n            color=\"primary\" \n            size=\"large\" \n            block \n            onClick={handleTestCamera}\n            disabled={!cameraManager}\n          >\n            <CameraOutline /> 啟動相機\n          </Button>\n          \n          {!isMobile && (\n            <>\n              <Button \n                color=\"primary\" \n                size=\"large\" \n                block \n                onClick={handleTestPhoto}\n                disabled={!cameraManager || cameraStatus !== '相機已啟動 (Web模式)'}\n              >\n                <CheckOutline /> 拍照\n              </Button>\n              \n              <Button \n                color=\"default\" \n                size=\"large\" \n                block \n                onClick={handleStopCamera}\n                disabled={!cameraManager}\n              >\n                停止相機\n              </Button>\n            </>\n          )}\n        </Space>\n      </Card>\n\n      {capturedImage && (\n        <Card title=\"拍攝結果\" style={{ marginBottom: '16px' }}>\n          <img\n            src={capturedImage}\n            alt=\"拍攝的照片\"\n            style={{\n              width: '100%',\n              height: '350px',\n              objectFit: 'cover',\n              borderRadius: '8px',\n              boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n            }}\n          />\n        </Card>\n      )}\n\n      {deviceInfo && (\n        <Card title=\"詳細設備信息\">\n          <pre style={{ \n            fontSize: '12px', \n            background: '#f0f0f0', \n            padding: '8px', \n            borderRadius: '4px',\n            overflow: 'auto'\n          }}>\n            {JSON.stringify(deviceInfo, null, 2)}\n          </pre>\n        </Card>\n      )}\n\n      {/* 移動端全屏相機 */}\n      {isMobile && (\n        <MobileCameraModal\n          visible={showMobileCamera}\n          onClose={handleCloseMobileCamera}\n          onPhotoTaken={handleMobilePhotoTaken}\n          cameraManager={cameraManager}\n          target=\"back\"\n        />\n      )}\n    </div>\n  );\n};\n\nexport default CameraTestPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,QAAQ,aAAa;AACxD,SAASC,aAAa,EAAEC,YAAY,QAAQ,mBAAmB;AAC/D,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,MAAM,CAAC;EACxD,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACAC,SAAS,CAAC,MAAM;IACd,MAAM4B,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF;QACA,MAAMC,OAAO,GAAG,MAAMrB,kBAAkB,CAAC,CAAC;QAC1CW,aAAa,CAACU,OAAO,CAAC;QACtBF,WAAW,CAACE,OAAO,CAACC,UAAU,KAAK,QAAQ,IAAID,OAAO,CAACC,UAAU,KAAK,QAAQ,CAAC;;QAE/E;QACA,MAAMC,OAAO,GAAGxB,gBAAgB,CAAC,CAAC;QAClCU,gBAAgB,CAACc,OAAO,CAAC;QAEzBV,eAAe,CAAC,MAAM,CAAC;QAEvBW,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEJ,OAAO,CAAC;QAC7BG,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEF,OAAO,CAAC;MAEhC,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;QAC9Bb,eAAe,CAAC,OAAO,CAAC;QACxBjB,KAAK,CAAC+B,IAAI,CAAC;UACTC,OAAO,EAAE,SAAS;UAClBC,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC;IAEDT,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACtB,aAAa,EAAE;MAClBZ,KAAK,CAAC+B,IAAI,CAAC;QACTC,OAAO,EAAE,WAAW;QACpBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;IAEA,IAAI;MACFhB,eAAe,CAAC,WAAW,CAAC;MAE5B,IAAIK,QAAQ,EAAE;QACZ;QACAH,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAMP,aAAa,CAACuB,WAAW,CAAC,MAAM,CAAC;QACvClB,eAAe,CAAC,eAAe,CAAC;QAEhCjB,KAAK,CAAC+B,IAAI,CAAC;UACTC,OAAO,EAAE,SAAS;UAClBC,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/Bb,eAAe,CAAC,QAAQ,CAAC;MACzBjB,KAAK,CAAC+B,IAAI,CAAC;QACTC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMG,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACxB,aAAa,EAAE;IAEpB,IAAI;MACFK,eAAe,CAAC,SAAS,CAAC;MAC1B,MAAMoB,MAAM,GAAG,MAAMzB,aAAa,CAAC0B,SAAS,CAAC,CAAC;MAE9C,IAAID,MAAM,IAAIA,MAAM,CAACE,IAAI,EAAE;QACzB,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;UACrBtB,gBAAgB,CAACsB,CAAC,CAACC,MAAM,CAACP,MAAM,CAAC;QACnC,CAAC;QACDG,MAAM,CAACK,aAAa,CAACR,MAAM,CAACE,IAAI,CAAC;QAEjCtB,eAAe,CAAC,MAAM,CAAC;QACvBjB,KAAK,CAAC+B,IAAI,CAAC;UACTC,OAAO,EAAE,OAAO;UAChBC,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7Bb,eAAe,CAAC,MAAM,CAAC;MACvBjB,KAAK,CAAC+B,IAAI,CAAC;QACTC,OAAO,EAAE,UAAU;QACnBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMa,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIlC,aAAa,EAAE;MACjBA,aAAa,CAACmC,UAAU,CAAC,CAAC;MAC1B9B,eAAe,CAAC,OAAO,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAM+B,sBAAsB,GAAIC,IAAI,IAAK;IACvC,IAAIA,IAAI,IAAIA,IAAI,CAACV,IAAI,EAAE;MACrB,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;QACrBtB,gBAAgB,CAACsB,CAAC,CAACC,MAAM,CAACP,MAAM,CAAC;MACnC,CAAC;MACDG,MAAM,CAACK,aAAa,CAACI,IAAI,CAACV,IAAI,CAAC;MAE/BtB,eAAe,CAAC,cAAc,CAAC;MAC/BjB,KAAK,CAAC+B,IAAI,CAAC;QACTC,OAAO,EAAE,OAAO;QAChBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACAd,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM+B,uBAAuB,GAAGA,CAAA,KAAM;IACpC/B,mBAAmB,CAAC,KAAK,CAAC;IAC1BF,eAAe,CAAC,OAAO,CAAC;EAC1B,CAAC;EAED,oBACEV,OAAA;IAAK4C,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,SAAS,EAAE,OAAO;MAAEC,UAAU,EAAE;IAAU,CAAE;IAAAC,QAAA,gBACzEhD,OAAA,CAACT,IAAI;MAAC0D,KAAK,EAAC,sCAAQ;MAACL,KAAK,EAAE;QAAEM,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,eACnDhD,OAAA,CAACR,KAAK;QAAC2D,SAAS,EAAC,UAAU;QAACP,KAAK,EAAE;UAAEQ,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACnDhD,OAAA;UAAAgD,QAAA,gBACEhD,OAAA;YAAAgD,QAAA,EAAQ;UAAG;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC/C,YAAY;QAAA;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EAELjD,UAAU,iBACTP,OAAA;UAAAgD,QAAA,gBACEhD,OAAA;YAAAgD,QAAA,EAAQ;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACjD,UAAU,CAACY,UAAU,eAC7CnB,OAAA;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxD,OAAA;YAAAgD,QAAA,EAAQ;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACzC,QAAQ,GAAG,OAAO,GAAG,WAAW,eACxDf,OAAA;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxD,OAAA;YAAAgD,QAAA,EAAQ;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACjD,UAAU,CAACkD,MAAM,CAACC,SAAS,GAAG,GAAG,GAAG,GAAG,eAC/D1D,OAAA;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxD,OAAA;YAAAgD,QAAA,EAAQ;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACjD,UAAU,CAACkD,MAAM,CAACE,gBAAgB,CAACC,MAAM;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEPxD,OAAA,CAACT,IAAI;MAAC0D,KAAK,EAAC,0BAAM;MAACL,KAAK,EAAE;QAAEM,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,eACjDhD,OAAA,CAACR,KAAK;QAAC2D,SAAS,EAAC,UAAU;QAACP,KAAK,EAAE;UAAEQ,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACnDhD,OAAA,CAACV,MAAM;UACLuE,KAAK,EAAC,SAAS;UACfC,IAAI,EAAC,OAAO;UACZC,KAAK;UACLC,OAAO,EAAErC,gBAAiB;UAC1BsC,QAAQ,EAAE,CAAC5D,aAAc;UAAA2C,QAAA,gBAEzBhD,OAAA,CAACN,aAAa;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BACnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAER,CAACzC,QAAQ,iBACRf,OAAA,CAAAE,SAAA;UAAA8C,QAAA,gBACEhD,OAAA,CAACV,MAAM;YACLuE,KAAK,EAAC,SAAS;YACfC,IAAI,EAAC,OAAO;YACZC,KAAK;YACLC,OAAO,EAAEnC,eAAgB;YACzBoC,QAAQ,EAAE,CAAC5D,aAAa,IAAII,YAAY,KAAK,eAAgB;YAAAuC,QAAA,gBAE7DhD,OAAA,CAACL,YAAY;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAClB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETxD,OAAA,CAACV,MAAM;YACLuE,KAAK,EAAC,SAAS;YACfC,IAAI,EAAC,OAAO;YACZC,KAAK;YACLC,OAAO,EAAEzB,gBAAiB;YAC1B0B,QAAQ,EAAE,CAAC5D,aAAc;YAAA2C,QAAA,EAC1B;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAEN3C,aAAa,iBACZb,OAAA,CAACT,IAAI;MAAC0D,KAAK,EAAC,0BAAM;MAACL,KAAK,EAAE;QAAEM,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,eACjDhD,OAAA;QACEkE,GAAG,EAAErD,aAAc;QACnBsD,GAAG,EAAC,gCAAO;QACXvB,KAAK,EAAE;UACLQ,KAAK,EAAE,MAAM;UACbgB,MAAM,EAAE,OAAO;UACfC,SAAS,EAAE,OAAO;UAClBC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE;QACb;MAAE;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,EAEAjD,UAAU,iBACTP,OAAA,CAACT,IAAI;MAAC0D,KAAK,EAAC,sCAAQ;MAAAD,QAAA,eAClBhD,OAAA;QAAK4C,KAAK,EAAE;UACV4B,QAAQ,EAAE,MAAM;UAChBzB,UAAU,EAAE,SAAS;UACrBF,OAAO,EAAE,KAAK;UACdyB,YAAY,EAAE,KAAK;UACnBG,QAAQ,EAAE;QACZ,CAAE;QAAAzB,QAAA,EACC0B,IAAI,CAACC,SAAS,CAACpE,UAAU,EAAE,IAAI,EAAE,CAAC;MAAC;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP,EAGAzC,QAAQ,iBACPf,OAAA,CAACF,iBAAiB;MAChB8E,OAAO,EAAEjE,gBAAiB;MAC1BkE,OAAO,EAAElC,uBAAwB;MACjCmC,YAAY,EAAErC,sBAAuB;MACrCpC,aAAa,EAAEA,aAAc;MAC7BgC,MAAM,EAAC;IAAM;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpD,EAAA,CAhPID,cAAc;AAAA4E,EAAA,GAAd5E,cAAc;AAkPpB,eAAeA,cAAc;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}