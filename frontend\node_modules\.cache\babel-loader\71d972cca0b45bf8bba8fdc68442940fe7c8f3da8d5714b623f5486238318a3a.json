{"ast": null, "code": "import * as React from \"react\";\nfunction ExclamationTriangleOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ExclamationTriangleOutline-ExclamationTriangleOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ExclamationTriangleOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ExclamationTriangleOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M25.4232976,5.01280751 C26.0290095,5.35966735 26.5307215,5.8639161 26.8758363,6.47269051 L43.9897723,36.6612652 C45.0521651,38.5353008 44.4018414,40.9201009 42.5372336,41.9878653 C41.9511231,42.3235005 41.2881721,42.5 40.6136012,42.5 L6.38572933,42.5 C4.23970028,42.5 2.5,40.7515035 2.5,38.5946238 C2.5,37.9166421 2.6756116,37.2503391 3.00955827,36.6612652 L20.1234942,6.47269051 C21.185887,4.59865494 23.5586898,3.94504306 25.4232976,5.01280751 Z M22.7183146,7.89574441 L22.6566056,7.99100253 L5.55751165,38.0467393 C5.47409738,38.1933598 5.43023256,38.3592025 5.43023256,38.5279521 C5.43023256,39.0264531 5.80491925,39.4373099 6.28763114,39.4934603 L6.40082242,39.5 L40.5990104,39.5 C40.7675068,39.5 40.9331008,39.4560693 41.0795014,39.3725297 C41.5119814,39.1257465 41.6829358,38.5963108 41.492683,38.1484724 L41.4423211,38.0467393 L24.3432272,7.99100253 C24.2570233,7.83947862 24.131704,7.71397114 23.9804074,7.62763775 C23.5479274,7.38085458 23.0059953,7.50350061 22.7183146,7.89574441 Z M24.9651163,32.9 L24.9651163,35.1 C24.9651163,35.3209139 24.7860302,35.5 24.5651163,35.5 L22.4348837,35.5 C22.2139698,35.5 22.0348837,35.3209139 22.0348837,35.1 L22.0348837,32.9 C22.0348837,32.6790861 22.2139698,32.5 22.4348837,32.5 L24.5651163,32.5 C24.7860302,32.5 24.9651163,32.6790861 24.9651163,32.9 Z M24.9651163,18.9 L24.9651163,29.1 C24.9651163,29.3209139 24.7860302,29.5 24.5651163,29.5 L22.4348837,29.5 C22.2139698,29.5 22.0348837,29.3209139 22.0348837,29.1 L22.0348837,18.9 C22.0348837,18.6790861 22.2139698,18.5 22.4348837,18.5 L24.5651163,18.5 C24.7860302,18.5 24.9651163,18.6790861 24.9651163,18.9 Z\",\n    id: \"ExclamationTriangleOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default ExclamationTriangleOutline;", "map": {"version": 3, "names": ["React", "ExclamationTriangleOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/ExclamationTriangleOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction ExclamationTriangleOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ExclamationTriangleOutline-ExclamationTriangleOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ExclamationTriangleOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ExclamationTriangleOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M25.4232976,5.01280751 C26.0290095,5.35966735 26.5307215,5.8639161 26.8758363,6.47269051 L43.9897723,36.6612652 C45.0521651,38.5353008 44.4018414,40.9201009 42.5372336,41.9878653 C41.9511231,42.3235005 41.2881721,42.5 40.6136012,42.5 L6.38572933,42.5 C4.23970028,42.5 2.5,40.7515035 2.5,38.5946238 C2.5,37.9166421 2.6756116,37.2503391 3.00955827,36.6612652 L20.1234942,6.47269051 C21.185887,4.59865494 23.5586898,3.94504306 25.4232976,5.01280751 Z M22.7183146,7.89574441 L22.6566056,7.99100253 L5.55751165,38.0467393 C5.47409738,38.1933598 5.43023256,38.3592025 5.43023256,38.5279521 C5.43023256,39.0264531 5.80491925,39.4373099 6.28763114,39.4934603 L6.40082242,39.5 L40.5990104,39.5 C40.7675068,39.5 40.9331008,39.4560693 41.0795014,39.3725297 C41.5119814,39.1257465 41.6829358,38.5963108 41.492683,38.1484724 L41.4423211,38.0467393 L24.3432272,7.99100253 C24.2570233,7.83947862 24.131704,7.71397114 23.9804074,7.62763775 C23.5479274,7.38085458 23.0059953,7.50350061 22.7183146,7.89574441 Z M24.9651163,32.9 L24.9651163,35.1 C24.9651163,35.3209139 24.7860302,35.5 24.5651163,35.5 L22.4348837,35.5 C22.2139698,35.5 22.0348837,35.3209139 22.0348837,35.1 L22.0348837,32.9 C22.0348837,32.6790861 22.2139698,32.5 22.4348837,32.5 L24.5651163,32.5 C24.7860302,32.5 24.9651163,32.6790861 24.9651163,32.9 Z M24.9651163,18.9 L24.9651163,29.1 C24.9651163,29.3209139 24.7860302,29.5 24.5651163,29.5 L22.4348837,29.5 C22.2139698,29.5 22.0348837,29.3209139 22.0348837,29.1 L22.0348837,18.9 C22.0348837,18.6790861 22.2139698,18.5 22.4348837,18.5 L24.5651163,18.5 C24.7860302,18.5 24.9651163,18.6790861 24.9651163,18.9 Z\",\n    id: \"ExclamationTriangleOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default ExclamationTriangleOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,0BAA0BA,CAACC,KAAK,EAAE;EACzC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,uDAAuD;IAC3DC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,yCAAyC;IAC7CG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,wkDAAwkD;IAC3kDR,EAAE,EAAE,yCAAyC;IAC7CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}