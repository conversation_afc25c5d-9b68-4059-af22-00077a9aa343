{"ast": null, "code": "import React from 'react';\nimport { CloseOutline } from 'antd-mobile-icons';\nexport const defaultPopupBaseProps = {\n  closeOnMaskClick: false,\n  closeIcon: React.createElement(CloseOutline, null),\n  destroyOnClose: false,\n  disableBodyScroll: true,\n  forceRender: false,\n  getContainer: () => document.body,\n  mask: true,\n  showCloseButton: false,\n  stopPropagation: ['click'],\n  visible: false\n};", "map": {"version": 3, "names": ["React", "CloseOutline", "defaultPopupBaseProps", "closeOnMaskClick", "closeIcon", "createElement", "destroyOnClose", "disableBodyScroll", "forceRender", "getContainer", "document", "body", "mask", "showCloseButton", "stopPropagation", "visible"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/popup/popup-base-props.js"], "sourcesContent": ["import React from 'react';\nimport { CloseOutline } from 'antd-mobile-icons';\nexport const defaultPopupBaseProps = {\n  closeOnMaskClick: false,\n  closeIcon: React.createElement(CloseOutline, null),\n  destroyOnClose: false,\n  disableBodyScroll: true,\n  forceRender: false,\n  getContainer: () => document.body,\n  mask: true,\n  showCloseButton: false,\n  stopPropagation: ['click'],\n  visible: false\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,mBAAmB;AAChD,OAAO,MAAMC,qBAAqB,GAAG;EACnCC,gBAAgB,EAAE,KAAK;EACvBC,SAAS,EAAEJ,KAAK,CAACK,aAAa,CAACJ,YAAY,EAAE,IAAI,CAAC;EAClDK,cAAc,EAAE,KAAK;EACrBC,iBAAiB,EAAE,IAAI;EACvBC,WAAW,EAAE,KAAK;EAClBC,YAAY,EAAEA,CAAA,KAAMC,QAAQ,CAACC,IAAI;EACjCC,IAAI,EAAE,IAAI;EACVC,eAAe,EAAE,KAAK;EACtBC,eAAe,EAAE,CAAC,OAAO,CAAC;EAC1BC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}