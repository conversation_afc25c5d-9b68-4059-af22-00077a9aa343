{"ast": null, "code": "import { useRef } from 'react';\nimport { useMemoizedFn, useUpdate } from 'ahooks';\nexport function usePropsValue(options) {\n  const {\n    value,\n    defaultValue,\n    onChange\n  } = options;\n  const update = useUpdate();\n  const stateRef = useRef(value !== undefined ? value : defaultValue);\n  if (value !== undefined) {\n    stateRef.current = value;\n  }\n  const setState = useMemoizedFn((v, forceTrigger = false) => {\n    // `forceTrigger` means trigger `onChange` even if `v` is the same as `stateRef.current`\n    const nextValue = typeof v === 'function' ? v(stateRef.current) : v;\n    if (!forceTrigger && nextValue === stateRef.current) return;\n    stateRef.current = nextValue;\n    update();\n    return onChange === null || onChange === void 0 ? void 0 : onChange(nextValue);\n  });\n  return [stateRef.current, setState];\n}", "map": {"version": 3, "names": ["useRef", "useMemoizedFn", "useUpdate", "usePropsValue", "options", "value", "defaultValue", "onChange", "update", "stateRef", "undefined", "current", "setState", "v", "forceTrigger", "nextValue"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/use-props-value.js"], "sourcesContent": ["import { useRef } from 'react';\nimport { useMemoizedFn, useUpdate } from 'ahooks';\nexport function usePropsValue(options) {\n  const {\n    value,\n    defaultValue,\n    onChange\n  } = options;\n  const update = useUpdate();\n  const stateRef = useRef(value !== undefined ? value : defaultValue);\n  if (value !== undefined) {\n    stateRef.current = value;\n  }\n  const setState = useMemoizedFn((v, forceTrigger = false) => {\n    // `forceTrigger` means trigger `onChange` even if `v` is the same as `stateRef.current`\n    const nextValue = typeof v === 'function' ? v(stateRef.current) : v;\n    if (!forceTrigger && nextValue === stateRef.current) return;\n    stateRef.current = nextValue;\n    update();\n    return onChange === null || onChange === void 0 ? void 0 : onChange(nextValue);\n  });\n  return [stateRef.current, setState];\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,aAAa,EAAEC,SAAS,QAAQ,QAAQ;AACjD,OAAO,SAASC,aAAaA,CAACC,OAAO,EAAE;EACrC,MAAM;IACJC,KAAK;IACLC,YAAY;IACZC;EACF,CAAC,GAAGH,OAAO;EACX,MAAMI,MAAM,GAAGN,SAAS,CAAC,CAAC;EAC1B,MAAMO,QAAQ,GAAGT,MAAM,CAACK,KAAK,KAAKK,SAAS,GAAGL,KAAK,GAAGC,YAAY,CAAC;EACnE,IAAID,KAAK,KAAKK,SAAS,EAAE;IACvBD,QAAQ,CAACE,OAAO,GAAGN,KAAK;EAC1B;EACA,MAAMO,QAAQ,GAAGX,aAAa,CAAC,CAACY,CAAC,EAAEC,YAAY,GAAG,KAAK,KAAK;IAC1D;IACA,MAAMC,SAAS,GAAG,OAAOF,CAAC,KAAK,UAAU,GAAGA,CAAC,CAACJ,QAAQ,CAACE,OAAO,CAAC,GAAGE,CAAC;IACnE,IAAI,CAACC,YAAY,IAAIC,SAAS,KAAKN,QAAQ,CAACE,OAAO,EAAE;IACrDF,QAAQ,CAACE,OAAO,GAAGI,SAAS;IAC5BP,MAAM,CAAC,CAAC;IACR,OAAOD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACQ,SAAS,CAAC;EAChF,CAAC,CAAC;EACF,OAAO,CAACN,QAAQ,CAACE,OAAO,EAAEC,QAAQ,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}