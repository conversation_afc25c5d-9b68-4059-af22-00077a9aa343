{"ast": null, "code": "import * as React from \"react\";\nfunction FireFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FireFill-FireFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FireFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"FireFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M28.8108503,7.56598922 C30.6177257,11.2247948 29.7435561,14.4566853 29.3686834,16.547422 C28.9938108,18.6381586 27.8320537,21.667736 26.811586,22.9814259 C25.4534588,24.7297988 25.096928,26.8463629 27.1867256,27.9151175 C30.7994494,29.0194852 35.136931,20.7401744 32.9972358,12.4787872 L32.9972358,12.375382 C38.2946065,17.7800278 40.9640387,23.7306532 41,30.2286368 C41,30.53058 40.9308437,30.8256296 40.9059475,31.1289515 C40.0608578,41.5122137 31.1757433,46 24.9695754,46 C18.7634076,46 5.11895825,43.6837232 7.21715963,26.29786 C8.10965742,20.0019947 13.6519693,16.3384878 16.5060011,13.0395238 C18.1058217,11.1902976 20.2628465,8.94509773 23.0862649,2 C26.0599847,3.53866958 27.8638693,5.6256273 28.8108503,7.56598922 Z\",\n    id: \"FireFill-\\u8499\\u7248\\u5907\\u4EFD-2\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default FireFill;", "map": {"version": 3, "names": ["React", "FireFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/FireFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction FireFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FireFill-FireFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FireFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"FireFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M28.8108503,7.56598922 C30.6177257,11.2247948 29.7435561,14.4566853 29.3686834,16.547422 C28.9938108,18.6381586 27.8320537,21.667736 26.811586,22.9814259 C25.4534588,24.7297988 25.096928,26.8463629 27.1867256,27.9151175 C30.7994494,29.0194852 35.136931,20.7401744 32.9972358,12.4787872 L32.9972358,12.375382 C38.2946065,17.7800278 40.9640387,23.7306532 41,30.2286368 C41,30.53058 40.9308437,30.8256296 40.9059475,31.1289515 C40.0608578,41.5122137 31.1757433,46 24.9695754,46 C18.7634076,46 5.11895825,43.6837232 7.21715963,26.29786 C8.10965742,20.0019947 13.6519693,16.3384878 16.5060011,13.0395238 C18.1058217,11.1902976 20.2628465,8.94509773 23.0862649,2 C26.0599847,3.53866958 27.8638693,5.6256273 28.8108503,7.56598922 Z\",\n    id: \"FireFill-\\u8499\\u7248\\u5907\\u4EFD-2\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default FireFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,mBAAmB;IACvBC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,uBAAuB;IAC3BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,stBAAstB;IACztBR,EAAE,EAAE,qCAAqC;IACzCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}