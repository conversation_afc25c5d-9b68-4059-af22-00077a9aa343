{"ast": null, "code": "import { isDev } from './is-dev';\nexport function devWarning(component, message) {\n  if (isDev) {\n    console.warn(`[antd-mobile: ${component}] ${message}`);\n  }\n}\nexport function devError(component, message) {\n  if (isDev) {\n    console.error(`[antd-mobile: ${component}] ${message}`);\n  }\n}\nlet infoBox;\nexport function devPrint(...message) {\n  if (isDev) {\n    if (!infoBox) {\n      infoBox = document.createElement('textarea');\n      document.body.append(infoBox);\n      infoBox.style.position = 'fixed';\n      infoBox.style.top = '0';\n      infoBox.style.left = '0';\n      infoBox.style.width = '100vw';\n      infoBox.style.height = '100vh';\n      infoBox.style.zIndex = '999999';\n      infoBox.style.fontSize = '12px';\n      // infoBox.style.opacity = '0.85'\n      infoBox.style.pointerEvents = 'none';\n      infoBox.style.background = 'transparent';\n      infoBox.style.textShadow = '-1px -1px 0 #FFF, -1px 1px 0 #FFF, 1px -1px 0 #FFF, 1px 1px 0 #FFF';\n    }\n    infoBox.value = `${infoBox.value}\\n${message.join(' ')}`.trim();\n    infoBox.scrollTop = infoBox.scrollHeight;\n  }\n}", "map": {"version": 3, "names": ["isDev", "dev<PERSON><PERSON><PERSON>", "component", "message", "console", "warn", "dev<PERSON><PERSON><PERSON>", "error", "infoBox", "devPrint", "document", "createElement", "body", "append", "style", "position", "top", "left", "width", "height", "zIndex", "fontSize", "pointerEvents", "background", "textShadow", "value", "join", "trim", "scrollTop", "scrollHeight"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/dev-log.js"], "sourcesContent": ["import { isDev } from './is-dev';\nexport function devWarning(component, message) {\n  if (isDev) {\n    console.warn(`[antd-mobile: ${component}] ${message}`);\n  }\n}\nexport function devError(component, message) {\n  if (isDev) {\n    console.error(`[antd-mobile: ${component}] ${message}`);\n  }\n}\nlet infoBox;\nexport function devPrint(...message) {\n  if (isDev) {\n    if (!infoBox) {\n      infoBox = document.createElement('textarea');\n      document.body.append(infoBox);\n      infoBox.style.position = 'fixed';\n      infoBox.style.top = '0';\n      infoBox.style.left = '0';\n      infoBox.style.width = '100vw';\n      infoBox.style.height = '100vh';\n      infoBox.style.zIndex = '999999';\n      infoBox.style.fontSize = '12px';\n      // infoBox.style.opacity = '0.85'\n      infoBox.style.pointerEvents = 'none';\n      infoBox.style.background = 'transparent';\n      infoBox.style.textShadow = '-1px -1px 0 #FFF, -1px 1px 0 #FFF, 1px -1px 0 #FFF, 1px 1px 0 #FFF';\n    }\n    infoBox.value = `${infoBox.value}\\n${message.join(' ')}`.trim();\n    infoBox.scrollTop = infoBox.scrollHeight;\n  }\n}"], "mappings": "AAAA,SAASA,KAAK,QAAQ,UAAU;AAChC,OAAO,SAASC,UAAUA,CAACC,SAAS,EAAEC,OAAO,EAAE;EAC7C,IAAIH,KAAK,EAAE;IACTI,OAAO,CAACC,IAAI,CAAC,iBAAiBH,SAAS,KAAKC,OAAO,EAAE,CAAC;EACxD;AACF;AACA,OAAO,SAASG,QAAQA,CAACJ,SAAS,EAAEC,OAAO,EAAE;EAC3C,IAAIH,KAAK,EAAE;IACTI,OAAO,CAACG,KAAK,CAAC,iBAAiBL,SAAS,KAAKC,OAAO,EAAE,CAAC;EACzD;AACF;AACA,IAAIK,OAAO;AACX,OAAO,SAASC,QAAQA,CAAC,GAAGN,OAAO,EAAE;EACnC,IAAIH,KAAK,EAAE;IACT,IAAI,CAACQ,OAAO,EAAE;MACZA,OAAO,GAAGE,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;MAC5CD,QAAQ,CAACE,IAAI,CAACC,MAAM,CAACL,OAAO,CAAC;MAC7BA,OAAO,CAACM,KAAK,CAACC,QAAQ,GAAG,OAAO;MAChCP,OAAO,CAACM,KAAK,CAACE,GAAG,GAAG,GAAG;MACvBR,OAAO,CAACM,KAAK,CAACG,IAAI,GAAG,GAAG;MACxBT,OAAO,CAACM,KAAK,CAACI,KAAK,GAAG,OAAO;MAC7BV,OAAO,CAACM,KAAK,CAACK,MAAM,GAAG,OAAO;MAC9BX,OAAO,CAACM,KAAK,CAACM,MAAM,GAAG,QAAQ;MAC/BZ,OAAO,CAACM,KAAK,CAACO,QAAQ,GAAG,MAAM;MAC/B;MACAb,OAAO,CAACM,KAAK,CAACQ,aAAa,GAAG,MAAM;MACpCd,OAAO,CAACM,KAAK,CAACS,UAAU,GAAG,aAAa;MACxCf,OAAO,CAACM,KAAK,CAACU,UAAU,GAAG,oEAAoE;IACjG;IACAhB,OAAO,CAACiB,KAAK,GAAG,GAAGjB,OAAO,CAACiB,KAAK,KAAKtB,OAAO,CAACuB,IAAI,CAAC,GAAG,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC;IAC/DnB,OAAO,CAACoB,SAAS,GAAGpB,OAAO,CAACqB,YAAY;EAC1C;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}