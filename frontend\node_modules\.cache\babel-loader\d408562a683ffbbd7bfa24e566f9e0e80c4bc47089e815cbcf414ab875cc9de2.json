{"ast": null, "code": "import * as React from \"react\";\nfunction UnlockOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UnlockOutline-UnlockOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UnlockOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UnlockOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,4 C30.627417,4 36,9.372583 36,16 L36,22 L37,22 C39.209139,22 41,23.790861 41,26 L41,40 C41,42.209139 39.209139,44 37,44 L11,44 C8.790861,44 7,42.209139 7,40 L7,26 C7,23.790861 8.790861,22 11,22 L33,22 L33,16 C33,11.1181973 29.1131863,7.14420858 24.2653623,7.00383711 L24,7 C19.1181973,7 15.1442086,10.8868137 15.0038371,15.7346377 L15,16 L15,18.6 C15,18.8209139 14.8209139,19 14.6,19 L12.4,19 C12.1790861,19 12,18.8209139 12,18.6 L12,16 L12,16 C12,9.372583 17.372583,4 24,4 Z M37,25 L11,25 C10.4871642,25 10.0644928,25.3860402 10.0067277,25.8833789 L10,26 L10,40 C10,40.5128358 10.3860402,40.9355072 10.8833789,40.9932723 L11,41 L37,41 C37.5128358,41 37.9355072,40.6139598 37.9932723,40.1166211 L38,40 L38,26 C38,25.4871642 37.6139598,25.0644928 37.1166211,25.0067277 L37,25 Z M26,30.4 L26,35.6 C26,35.8209139 25.8209139,36 25.6,36 L23.4,36 C23.1790861,36 23,35.8209139 23,35.6 L23,30.4 C23,30.1790861 23.1790861,30 23.4,30 L25.6,30 C25.8209139,30 26,30.1790861 26,30.4 Z\",\n    id: \"UnlockOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default UnlockOutline;", "map": {"version": 3, "names": ["React", "UnlockOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/UnlockOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction UnlockOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UnlockOutline-UnlockOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UnlockOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UnlockOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,4 C30.627417,4 36,9.372583 36,16 L36,22 L37,22 C39.209139,22 41,23.790861 41,26 L41,40 C41,42.209139 39.209139,44 37,44 L11,44 C8.790861,44 7,42.209139 7,40 L7,26 C7,23.790861 8.790861,22 11,22 L33,22 L33,16 C33,11.1181973 29.1131863,7.14420858 24.2653623,7.00383711 L24,7 C19.1181973,7 15.1442086,10.8868137 15.0038371,15.7346377 L15,16 L15,18.6 C15,18.8209139 14.8209139,19 14.6,19 L12.4,19 C12.1790861,19 12,18.8209139 12,18.6 L12,16 L12,16 C12,9.372583 17.372583,4 24,4 Z M37,25 L11,25 C10.4871642,25 10.0644928,25.3860402 10.0067277,25.8833789 L10,26 L10,40 C10,40.5128358 10.3860402,40.9355072 10.8833789,40.9932723 L11,41 L37,41 C37.5128358,41 37.9355072,40.6139598 37.9932723,40.1166211 L38,40 L38,26 C38,25.4871642 37.6139598,25.0644928 37.1166211,25.0067277 L37,25 Z M26,30.4 L26,35.6 C26,35.8209139 25.8209139,36 25.6,36 L23.4,36 C23.1790861,36 23,35.8209139 23,35.6 L23,30.4 C23,30.1790861 23.1790861,30 23.4,30 L25.6,30 C25.8209139,30 26,30.1790861 26,30.4 Z\",\n    id: \"UnlockOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default UnlockOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,6BAA6B;IACjCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,i9BAAi9B;IACp9BR,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}