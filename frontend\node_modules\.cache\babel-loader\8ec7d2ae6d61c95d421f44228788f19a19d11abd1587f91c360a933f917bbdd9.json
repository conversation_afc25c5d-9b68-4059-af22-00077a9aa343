{"ast": null, "code": "// 移植自 field-form https://github.com/react-component/field-form/blob/master/src/utils/validateUtil.ts#L21\nexport function replaceMessage(template, kv) {\n  return template.replace(/\\$\\{\\w+\\}/g, str => {\n    const key = str.slice(2, -1);\n    return kv[key];\n  });\n}", "map": {"version": 3, "names": ["replaceMessage", "template", "kv", "replace", "str", "key", "slice"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/replace-message.js"], "sourcesContent": ["// 移植自 field-form https://github.com/react-component/field-form/blob/master/src/utils/validateUtil.ts#L21\nexport function replaceMessage(template, kv) {\n  return template.replace(/\\$\\{\\w+\\}/g, str => {\n    const key = str.slice(2, -1);\n    return kv[key];\n  });\n}"], "mappings": "AAAA;AACA,OAAO,SAASA,cAAcA,CAACC,QAAQ,EAAEC,EAAE,EAAE;EAC3C,OAAOD,QAAQ,CAACE,OAAO,CAAC,YAAY,EAAEC,GAAG,IAAI;IAC3C,MAAMC,GAAG,GAAGD,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5B,OAAOJ,EAAE,CAACG,GAAG,CAAC;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}