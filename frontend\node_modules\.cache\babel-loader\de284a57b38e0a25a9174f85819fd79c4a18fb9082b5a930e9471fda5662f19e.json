{"ast": null, "code": "import { __awaiter } from \"tslib\";\nimport { show } from './show';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { getDefaultConfig } from '../config-provider';\nconst defaultProps = {\n  confirmText: '确认',\n  cancelText: '取消'\n};\nexport function confirm(p) {\n  const {\n    locale\n  } = getDefaultConfig();\n  const props = mergeProps(defaultProps, {\n    confirmText: locale.common.confirm,\n    cancelText: locale.common.cancel\n  }, p);\n  return new Promise(resolve => {\n    show(Object.assign(Object.assign({}, props), {\n      closeOnAction: true,\n      onClose: () => {\n        var _a;\n        (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n        resolve(false);\n      },\n      actions: [[{\n        key: 'cancel',\n        text: props.cancelText,\n        onClick: () => __awaiter(this, void 0, void 0, function* () {\n          var _a;\n          yield (_a = props.onCancel) === null || _a === void 0 ? void 0 : _a.call(props);\n          resolve(false);\n        })\n      }, {\n        key: 'confirm',\n        text: props.confirmText,\n        bold: true,\n        onClick: () => __awaiter(this, void 0, void 0, function* () {\n          var _b;\n          yield (_b = props.onConfirm) === null || _b === void 0 ? void 0 : _b.call(props);\n          resolve(true);\n        })\n      }]]\n    }));\n  });\n}", "map": {"version": 3, "names": ["__awaiter", "show", "mergeProps", "getDefaultConfig", "defaultProps", "confirmText", "cancelText", "confirm", "p", "locale", "props", "common", "cancel", "Promise", "resolve", "Object", "assign", "closeOnAction", "onClose", "_a", "call", "actions", "key", "text", "onClick", "onCancel", "bold", "_b", "onConfirm"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/dialog/confirm.js"], "sourcesContent": ["import { __awaiter } from \"tslib\";\nimport { show } from './show';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { getDefaultConfig } from '../config-provider';\nconst defaultProps = {\n  confirmText: '确认',\n  cancelText: '取消'\n};\nexport function confirm(p) {\n  const {\n    locale\n  } = getDefaultConfig();\n  const props = mergeProps(defaultProps, {\n    confirmText: locale.common.confirm,\n    cancelText: locale.common.cancel\n  }, p);\n  return new Promise(resolve => {\n    show(Object.assign(Object.assign({}, props), {\n      closeOnAction: true,\n      onClose: () => {\n        var _a;\n        (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n        resolve(false);\n      },\n      actions: [[{\n        key: 'cancel',\n        text: props.cancelText,\n        onClick: () => __awaiter(this, void 0, void 0, function* () {\n          var _a;\n          yield (_a = props.onCancel) === null || _a === void 0 ? void 0 : _a.call(props);\n          resolve(false);\n        })\n      }, {\n        key: 'confirm',\n        text: props.confirmText,\n        bold: true,\n        onClick: () => __awaiter(this, void 0, void 0, function* () {\n          var _b;\n          yield (_b = props.onConfirm) === null || _b === void 0 ? void 0 : _b.call(props);\n          resolve(true);\n        })\n      }]]\n    }));\n  });\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,MAAMC,YAAY,GAAG;EACnBC,WAAW,EAAE,IAAI;EACjBC,UAAU,EAAE;AACd,CAAC;AACD,OAAO,SAASC,OAAOA,CAACC,CAAC,EAAE;EACzB,MAAM;IACJC;EACF,CAAC,GAAGN,gBAAgB,CAAC,CAAC;EACtB,MAAMO,KAAK,GAAGR,UAAU,CAACE,YAAY,EAAE;IACrCC,WAAW,EAAEI,MAAM,CAACE,MAAM,CAACJ,OAAO;IAClCD,UAAU,EAAEG,MAAM,CAACE,MAAM,CAACC;EAC5B,CAAC,EAAEJ,CAAC,CAAC;EACL,OAAO,IAAIK,OAAO,CAACC,OAAO,IAAI;IAC5Bb,IAAI,CAACc,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,KAAK,CAAC,EAAE;MAC3CO,aAAa,EAAE,IAAI;MACnBC,OAAO,EAAEA,CAAA,KAAM;QACb,IAAIC,EAAE;QACN,CAACA,EAAE,GAAGT,KAAK,CAACQ,OAAO,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACV,KAAK,CAAC;QACxEI,OAAO,CAAC,KAAK,CAAC;MAChB,CAAC;MACDO,OAAO,EAAE,CAAC,CAAC;QACTC,GAAG,EAAE,QAAQ;QACbC,IAAI,EAAEb,KAAK,CAACJ,UAAU;QACtBkB,OAAO,EAAEA,CAAA,KAAMxB,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;UAC1D,IAAImB,EAAE;UACN,MAAM,CAACA,EAAE,GAAGT,KAAK,CAACe,QAAQ,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACV,KAAK,CAAC;UAC/EI,OAAO,CAAC,KAAK,CAAC;QAChB,CAAC;MACH,CAAC,EAAE;QACDQ,GAAG,EAAE,SAAS;QACdC,IAAI,EAAEb,KAAK,CAACL,WAAW;QACvBqB,IAAI,EAAE,IAAI;QACVF,OAAO,EAAEA,CAAA,KAAMxB,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;UAC1D,IAAI2B,EAAE;UACN,MAAM,CAACA,EAAE,GAAGjB,KAAK,CAACkB,SAAS,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACP,IAAI,CAACV,KAAK,CAAC;UAChFI,OAAO,CAAC,IAAI,CAAC;QACf,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}