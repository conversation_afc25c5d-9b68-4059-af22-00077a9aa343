{"ast": null, "code": "import React from 'react';\nexport const defaultFormContext = {\n  name: undefined,\n  hasFeedback: true,\n  layout: 'vertical',\n  requiredMarkStyle: 'asterisk',\n  disabled: false\n};\nexport const FormContext = React.createContext(defaultFormContext);\nexport const NoStyleItemContext = React.createContext(null);", "map": {"version": 3, "names": ["React", "defaultFormContext", "name", "undefined", "hasFeedback", "layout", "requiredMarkStyle", "disabled", "FormContext", "createContext", "NoStyleItemContext"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/form/context.js"], "sourcesContent": ["import React from 'react';\nexport const defaultFormContext = {\n  name: undefined,\n  hasFeedback: true,\n  layout: 'vertical',\n  requiredMarkStyle: 'asterisk',\n  disabled: false\n};\nexport const FormContext = React.createContext(defaultFormContext);\nexport const NoStyleItemContext = React.createContext(null);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,MAAMC,kBAAkB,GAAG;EAChCC,IAAI,EAAEC,SAAS;EACfC,WAAW,EAAE,IAAI;EACjBC,MAAM,EAAE,UAAU;EAClBC,iBAAiB,EAAE,UAAU;EAC7BC,QAAQ,EAAE;AACZ,CAAC;AACD,OAAO,MAAMC,WAAW,GAAGR,KAAK,CAACS,aAAa,CAACR,kBAAkB,CAAC;AAClE,OAAO,MAAMS,kBAAkB,GAAGV,KAAK,CAACS,aAAa,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}