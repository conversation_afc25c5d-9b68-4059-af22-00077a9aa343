{"ast": null, "code": "import React, { memo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nexport const CheckIcon = memo(props => {\n  return withNativeProps(props, React.createElement(\"svg\", {\n    viewBox: '0 0 40 40'\n  }, React.createElement(\"path\", {\n    d: 'M31.5541766,15 L28.0892433,15 L28.0892434,15 C27.971052,15 27.8576674,15.044522 27.7740471,15.1239792 L18.2724722,24.1625319 L13.2248725,19.3630279 L13.2248725,19.3630279 C13.1417074,19.2834412 13.0287551,19.2384807 12.9107898,19.2380079 L9.44474455,19.2380079 L9.44474454,19.2380079 C9.19869815,19.2384085 8.99957935,19.4284738 9,19.66253 C9,19.7747587 9.04719253,19.8823283 9.13066188,19.9616418 L17.0907466,27.5338228 C17.4170809,27.8442545 17.8447695,28 18.2713393,28 L18.2980697,28 C18.7168464,27.993643 19.133396,27.8378975 19.4530492,27.5338228 L31.8693384,15.7236361 L31.8693384,15.7236361 C32.0434167,15.5582251 32.0435739,15.2898919 31.8696892,15.1242941 C31.7860402,15.0446329 31.6725052,15 31.5541421,15 L31.5541766,15 Z',\n    fill: 'currentColor'\n  })));\n});", "map": {"version": 3, "names": ["React", "memo", "withNativeProps", "CheckIcon", "props", "createElement", "viewBox", "d", "fill"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/checkbox/check-icon.js"], "sourcesContent": ["import React, { memo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nexport const CheckIcon = memo(props => {\n  return withNativeProps(props, React.createElement(\"svg\", {\n    viewBox: '0 0 40 40'\n  }, React.createElement(\"path\", {\n    d: 'M31.5541766,15 L28.0892433,15 L28.0892434,15 C27.971052,15 27.8576674,15.044522 27.7740471,15.1239792 L18.2724722,24.1625319 L13.2248725,19.3630279 L13.2248725,19.3630279 C13.1417074,19.2834412 13.0287551,19.2384807 12.9107898,19.2380079 L9.44474455,19.2380079 L9.44474454,19.2380079 C9.19869815,19.2384085 8.99957935,19.4284738 9,19.66253 C9,19.7747587 9.04719253,19.8823283 9.13066188,19.9616418 L17.0907466,27.5338228 C17.4170809,27.8442545 17.8447695,28 18.2713393,28 L18.2980697,28 C18.7168464,27.993643 19.133396,27.8378975 19.4530492,27.5338228 L31.8693384,15.7236361 L31.8693384,15.7236361 C32.0434167,15.5582251 32.0435739,15.2898919 31.8696892,15.1242941 C31.7860402,15.0446329 31.6725052,15 31.5541421,15 L31.5541766,15 Z',\n    fill: 'currentColor'\n  })));\n});"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,IAAI,QAAQ,OAAO;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,SAAS,GAAGF,IAAI,CAACG,KAAK,IAAI;EACrC,OAAOF,eAAe,CAACE,KAAK,EAAEJ,KAAK,CAACK,aAAa,CAAC,KAAK,EAAE;IACvDC,OAAO,EAAE;EACX,CAAC,EAAEN,KAAK,CAACK,aAAa,CAAC,MAAM,EAAE;IAC7BE,CAAC,EAAE,8tBAA8tB;IACjuBC,IAAI,EAAE;EACR,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}