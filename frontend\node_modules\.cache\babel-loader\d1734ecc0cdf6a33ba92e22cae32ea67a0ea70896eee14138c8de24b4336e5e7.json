{"ast": null, "code": "import * as React from \"react\";\nfunction PayCircleOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PayCircleOutline-PayCircleOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PayCircleOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"PayCircleOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M29.481684,12.6536905 L29.5554769,12.6868007 L31.4514212,13.7775476 C31.4517209,13.77772 31.4520203,13.7778928 31.4523195,13.778066 L31.5180198,13.8254803 C31.6372256,13.9314105 31.682089,14.0995545 31.6314989,14.2507889 L31.5981516,14.3246307 L26.5546134,23.0374511 L31.1,23.0381798 C31.3209139,23.0381798 31.5,23.2172659 31.5,23.4381798 L31.5,25.6305439 C31.5,25.8514578 31.3209139,26.0305439 31.1,26.0305439 L25.4996134,26.0304511 L25.4996134,28.0254511 L31.1,28.0254532 C31.3209139,28.0254532 31.5,28.2045393 31.5,28.4254532 L31.5,30.6178173 C31.5,30.8387312 31.3209139,31.0178173 31.1,31.0178173 L25.4996134,31.0174511 L25.5,35.6 C25.5,35.8209139 25.3209139,36 25.1,36 L22.9,36 C22.6790861,36 22.5,35.8209139 22.5,35.6 L22.4996134,31.0174511 L16.9,31.0178173 C16.6790861,31.0178173 16.5,30.8387312 16.5,30.6178173 L16.5,28.4254532 C16.5,28.2045393 16.6790861,28.0254532 16.9,28.0254532 L22.4996134,28.0254511 L22.4996134,26.0304511 L16.9,26.0305439 C16.6790861,26.0305439 16.5,25.8514578 16.5,25.6305439 L16.5,23.4381798 C16.5,23.2172659 16.6790861,23.0381798 16.9,23.0381798 L21.3286134,23.0374511 L16.2864814,14.3246307 C16.1758221,14.1334306 16.2411134,13.8887252 16.4323135,13.778066 L16.4327625,13.7778065 L18.329156,12.6868007 C18.5202914,12.5768395 18.764366,12.6423015 18.8748225,12.8331512 L23.9416134,21.5884511 L29.0098105,12.8331512 C29.1064599,12.6661577 29.3054107,12.5951642 29.481684,12.6536905 Z\",\n    id: \"PayCircleOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default PayCircleOutline;", "map": {"version": 3, "names": ["React", "PayCircleOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/PayCircleOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction PayCircleOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PayCircleOutline-PayCircleOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PayCircleOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"PayCircleOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M29.481684,12.6536905 L29.5554769,12.6868007 L31.4514212,13.7775476 C31.4517209,13.77772 31.4520203,13.7778928 31.4523195,13.778066 L31.5180198,13.8254803 C31.6372256,13.9314105 31.682089,14.0995545 31.6314989,14.2507889 L31.5981516,14.3246307 L26.5546134,23.0374511 L31.1,23.0381798 C31.3209139,23.0381798 31.5,23.2172659 31.5,23.4381798 L31.5,25.6305439 C31.5,25.8514578 31.3209139,26.0305439 31.1,26.0305439 L25.4996134,26.0304511 L25.4996134,28.0254511 L31.1,28.0254532 C31.3209139,28.0254532 31.5,28.2045393 31.5,28.4254532 L31.5,30.6178173 C31.5,30.8387312 31.3209139,31.0178173 31.1,31.0178173 L25.4996134,31.0174511 L25.5,35.6 C25.5,35.8209139 25.3209139,36 25.1,36 L22.9,36 C22.6790861,36 22.5,35.8209139 22.5,35.6 L22.4996134,31.0174511 L16.9,31.0178173 C16.6790861,31.0178173 16.5,30.8387312 16.5,30.6178173 L16.5,28.4254532 C16.5,28.2045393 16.6790861,28.0254532 16.9,28.0254532 L22.4996134,28.0254511 L22.4996134,26.0304511 L16.9,26.0305439 C16.6790861,26.0305439 16.5,25.8514578 16.5,25.6305439 L16.5,23.4381798 C16.5,23.2172659 16.6790861,23.0381798 16.9,23.0381798 L21.3286134,23.0374511 L16.2864814,14.3246307 C16.1758221,14.1334306 16.2411134,13.8887252 16.4323135,13.778066 L16.4327625,13.7778065 L18.329156,12.6868007 C18.5202914,12.5768395 18.764366,12.6423015 18.8748225,12.8331512 L23.9416134,21.5884511 L29.0098105,12.8331512 C29.1064599,12.6661577 29.3054107,12.5951642 29.481684,12.6536905 Z\",\n    id: \"PayCircleOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default PayCircleOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,mCAAmC;IACvCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,+BAA+B;IACnCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,uqDAAuqD;IAC1qDR,EAAE,EAAE,2CAA2C;IAC/CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}