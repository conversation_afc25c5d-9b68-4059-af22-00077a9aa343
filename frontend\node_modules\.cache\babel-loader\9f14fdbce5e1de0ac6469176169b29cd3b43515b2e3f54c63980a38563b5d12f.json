{"ast": null, "code": "import React, { useContext } from 'react';\nimport List from '../list';\nimport { withNativeProps } from '../../utils/native-props';\nimport { CheckListContext } from './context';\nimport { devWarning } from '../../utils/dev-log';\nimport classNames from 'classnames';\nconst classPrefix = `adm-check-list-item`;\nexport const CheckListItem = props => {\n  const context = useContext(CheckListContext);\n  if (context === null) {\n    devWarning('CheckList.Item', 'CheckList.Item can only be used inside CheckList.');\n    return null;\n  }\n  const active = context.value.includes(props.value);\n  const readOnly = props.readOnly || context.readOnly;\n  const defaultExtra = active ? context.activeIcon : null;\n  const renderExtra = context.extra ? context.extra(active) : defaultExtra;\n  const extra = React.createElement(\"div\", {\n    className: `${classPrefix}-extra`\n  }, renderExtra);\n  return withNativeProps(props, React.createElement(List.Item, {\n    title: props.title,\n    className: classNames(classPrefix, readOnly && `${classPrefix}-readonly`, active && `${classPrefix}-active`),\n    description: props.description,\n    prefix: props.prefix,\n    onClick: e => {\n      var _a;\n      if (readOnly) return;\n      if (active) {\n        context.uncheck(props.value);\n      } else {\n        context.check(props.value);\n      }\n      (_a = props.onClick) === null || _a === void 0 ? void 0 : _a.call(props, e);\n    },\n    arrow: false,\n    clickable: !readOnly,\n    extra: extra,\n    disabled: props.disabled || context.disabled\n  }, props.children));\n};", "map": {"version": 3, "names": ["React", "useContext", "List", "withNativeProps", "CheckListContext", "dev<PERSON><PERSON><PERSON>", "classNames", "classPrefix", "CheckListItem", "props", "context", "active", "value", "includes", "readOnly", "defaultExtra", "activeIcon", "renderExtra", "extra", "createElement", "className", "<PERSON><PERSON>", "title", "description", "prefix", "onClick", "e", "_a", "uncheck", "check", "call", "arrow", "clickable", "disabled", "children"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/check-list/check-list-item.js"], "sourcesContent": ["import React, { useContext } from 'react';\nimport List from '../list';\nimport { withNativeProps } from '../../utils/native-props';\nimport { CheckListContext } from './context';\nimport { devWarning } from '../../utils/dev-log';\nimport classNames from 'classnames';\nconst classPrefix = `adm-check-list-item`;\nexport const CheckListItem = props => {\n  const context = useContext(CheckListContext);\n  if (context === null) {\n    devWarning('CheckList.Item', 'CheckList.Item can only be used inside CheckList.');\n    return null;\n  }\n  const active = context.value.includes(props.value);\n  const readOnly = props.readOnly || context.readOnly;\n  const defaultExtra = active ? context.activeIcon : null;\n  const renderExtra = context.extra ? context.extra(active) : defaultExtra;\n  const extra = React.createElement(\"div\", {\n    className: `${classPrefix}-extra`\n  }, renderExtra);\n  return withNativeProps(props, React.createElement(List.Item, {\n    title: props.title,\n    className: classNames(classPrefix, readOnly && `${classPrefix}-readonly`, active && `${classPrefix}-active`),\n    description: props.description,\n    prefix: props.prefix,\n    onClick: e => {\n      var _a;\n      if (readOnly) return;\n      if (active) {\n        context.uncheck(props.value);\n      } else {\n        context.check(props.value);\n      }\n      (_a = props.onClick) === null || _a === void 0 ? void 0 : _a.call(props, e);\n    },\n    arrow: false,\n    clickable: !readOnly,\n    extra: extra,\n    disabled: props.disabled || context.disabled\n  }, props.children));\n};"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAOC,IAAI,MAAM,SAAS;AAC1B,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,WAAW;AAC5C,SAASC,UAAU,QAAQ,qBAAqB;AAChD,OAAOC,UAAU,MAAM,YAAY;AACnC,MAAMC,WAAW,GAAG,qBAAqB;AACzC,OAAO,MAAMC,aAAa,GAAGC,KAAK,IAAI;EACpC,MAAMC,OAAO,GAAGT,UAAU,CAACG,gBAAgB,CAAC;EAC5C,IAAIM,OAAO,KAAK,IAAI,EAAE;IACpBL,UAAU,CAAC,gBAAgB,EAAE,mDAAmD,CAAC;IACjF,OAAO,IAAI;EACb;EACA,MAAMM,MAAM,GAAGD,OAAO,CAACE,KAAK,CAACC,QAAQ,CAACJ,KAAK,CAACG,KAAK,CAAC;EAClD,MAAME,QAAQ,GAAGL,KAAK,CAACK,QAAQ,IAAIJ,OAAO,CAACI,QAAQ;EACnD,MAAMC,YAAY,GAAGJ,MAAM,GAAGD,OAAO,CAACM,UAAU,GAAG,IAAI;EACvD,MAAMC,WAAW,GAAGP,OAAO,CAACQ,KAAK,GAAGR,OAAO,CAACQ,KAAK,CAACP,MAAM,CAAC,GAAGI,YAAY;EACxE,MAAMG,KAAK,GAAGlB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACvCC,SAAS,EAAE,GAAGb,WAAW;EAC3B,CAAC,EAAEU,WAAW,CAAC;EACf,OAAOd,eAAe,CAACM,KAAK,EAAET,KAAK,CAACmB,aAAa,CAACjB,IAAI,CAACmB,IAAI,EAAE;IAC3DC,KAAK,EAAEb,KAAK,CAACa,KAAK;IAClBF,SAAS,EAAEd,UAAU,CAACC,WAAW,EAAEO,QAAQ,IAAI,GAAGP,WAAW,WAAW,EAAEI,MAAM,IAAI,GAAGJ,WAAW,SAAS,CAAC;IAC5GgB,WAAW,EAAEd,KAAK,CAACc,WAAW;IAC9BC,MAAM,EAAEf,KAAK,CAACe,MAAM;IACpBC,OAAO,EAAEC,CAAC,IAAI;MACZ,IAAIC,EAAE;MACN,IAAIb,QAAQ,EAAE;MACd,IAAIH,MAAM,EAAE;QACVD,OAAO,CAACkB,OAAO,CAACnB,KAAK,CAACG,KAAK,CAAC;MAC9B,CAAC,MAAM;QACLF,OAAO,CAACmB,KAAK,CAACpB,KAAK,CAACG,KAAK,CAAC;MAC5B;MACA,CAACe,EAAE,GAAGlB,KAAK,CAACgB,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACrB,KAAK,EAAEiB,CAAC,CAAC;IAC7E,CAAC;IACDK,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,CAAClB,QAAQ;IACpBI,KAAK,EAAEA,KAAK;IACZe,QAAQ,EAAExB,KAAK,CAACwB,QAAQ,IAAIvB,OAAO,CAACuB;EACtC,CAAC,EAAExB,KAAK,CAACyB,QAAQ,CAAC,CAAC;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}