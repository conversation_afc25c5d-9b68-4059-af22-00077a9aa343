{"ast": null, "code": "import { animated, useSpring } from '@react-spring/web';\nimport { useDrag } from '@use-gesture/react';\nimport React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { nearest } from '../../utils/nearest';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withStopPropagation } from '../../utils/with-stop-propagation';\nimport Button from '../button';\nconst classPrefix = `adm-swipe-action`;\nconst defaultProps = {\n  rightActions: [],\n  leftActions: [],\n  closeOnTouchOutside: true,\n  closeOnAction: true,\n  stopPropagation: []\n};\nexport const SwipeAction = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const rootRef = useRef(null);\n  const leftRef = useRef(null);\n  const rightRef = useRef(null);\n  function getWidth(ref) {\n    const element = ref.current;\n    if (!element) return 0;\n    return element.offsetWidth;\n  }\n  function getLeftWidth() {\n    return getWidth(leftRef);\n  }\n  function getRightWidth() {\n    return getWidth(rightRef);\n  }\n  const [{\n    x\n  }, api] = useSpring(() => ({\n    x: 0,\n    config: {\n      tension: 200,\n      friction: 30\n    }\n  }), []);\n  const draggingRef = useRef(false);\n  const dragCancelRef = useRef(null);\n  function forceCancelDrag() {\n    var _a;\n    (_a = dragCancelRef.current) === null || _a === void 0 ? void 0 : _a.call(dragCancelRef);\n    draggingRef.current = false;\n  }\n  const bind = useDrag(state => {\n    var _a;\n    dragCancelRef.current = state.cancel;\n    if (!state.intentional) return;\n    if (state.down) {\n      draggingRef.current = true;\n    }\n    if (!draggingRef.current) return;\n    const [offsetX] = state.offset;\n    if (state.last) {\n      const leftWidth = getLeftWidth();\n      const rightWidth = getRightWidth();\n      let position = offsetX + state.velocity[0] * state.direction[0] * 50;\n      if (offsetX > 0) {\n        position = Math.max(0, position);\n      } else if (offsetX < 0) {\n        position = Math.min(0, position);\n      } else {\n        position = 0;\n      }\n      const targetX = nearest([-rightWidth, 0, leftWidth], position);\n      api.start({\n        x: targetX\n      });\n      if (targetX !== 0) {\n        (_a = p.onActionsReveal) === null || _a === void 0 ? void 0 : _a.call(p, targetX > 0 ? 'left' : 'right');\n      }\n      window.setTimeout(() => {\n        draggingRef.current = false;\n      });\n    } else {\n      api.start({\n        x: offsetX,\n        immediate: true\n      });\n    }\n  }, {\n    from: () => [x.get(), 0],\n    bounds: () => {\n      const leftWidth = getLeftWidth();\n      const rightWidth = getRightWidth();\n      return {\n        left: -rightWidth,\n        right: leftWidth\n      };\n    },\n    axis: 'x',\n    preventScroll: true,\n    pointer: {\n      touch: true\n    },\n    triggerAllEvents: true\n  });\n  const close = () => {\n    var _a;\n    api.start({\n      x: 0\n    });\n    forceCancelDrag();\n    (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n  };\n  useImperativeHandle(ref, () => ({\n    show: (side = 'right') => {\n      var _a;\n      if (side === 'right') {\n        api.start({\n          x: -getRightWidth()\n        });\n      } else if (side === 'left') {\n        api.start({\n          x: getLeftWidth()\n        });\n      }\n      (_a = p.onActionsReveal) === null || _a === void 0 ? void 0 : _a.call(p, side);\n    },\n    close\n  }));\n  useEffect(() => {\n    if (!props.closeOnTouchOutside) return;\n    function handle(e) {\n      if (x.get() === 0) {\n        return;\n      }\n      const root = rootRef.current;\n      if (root && !root.contains(e.target)) {\n        close();\n      }\n    }\n    document.addEventListener('touchstart', handle);\n    return () => {\n      document.removeEventListener('touchstart', handle);\n    };\n  }, [props.closeOnTouchOutside]);\n  function renderAction(action) {\n    var _a, _b;\n    const color = (_a = action.color) !== null && _a !== void 0 ? _a : 'light';\n    return React.createElement(Button, {\n      key: action.key,\n      className: `${classPrefix}-action-button`,\n      style: {\n        '--background-color': (_b = colorRecord[color]) !== null && _b !== void 0 ? _b : color\n      },\n      onClick: e => {\n        var _a, _b;\n        if (props.closeOnAction) {\n          close();\n        }\n        (_a = action.onClick) === null || _a === void 0 ? void 0 : _a.call(action, e);\n        (_b = props.onAction) === null || _b === void 0 ? void 0 : _b.call(props, action, e);\n      }\n    }, action.text);\n  }\n  return withNativeProps(props, React.createElement(\"div\", Object.assign({\n    className: classPrefix\n  }, bind(), {\n    ref: rootRef,\n    onClickCapture: e => {\n      if (draggingRef.current) {\n        e.stopPropagation();\n        e.preventDefault();\n      }\n    }\n  }), React.createElement(animated.div, {\n    className: `${classPrefix}-track`,\n    style: {\n      x\n    }\n  }, withStopPropagation(props.stopPropagation, React.createElement(\"div\", {\n    className: `${classPrefix}-actions ${classPrefix}-actions-left`,\n    ref: leftRef\n  }, props.leftActions.map(renderAction))), React.createElement(\"div\", {\n    className: `${classPrefix}-content`,\n    onClickCapture: e => {\n      if (x.goal !== 0) {\n        e.preventDefault();\n        e.stopPropagation();\n        close();\n      }\n    }\n  }, React.createElement(animated.div, {\n    style: {\n      pointerEvents: x.to(v => v !== 0 && x.goal !== 0 ? 'none' : 'auto')\n    }\n  }, props.children)), withStopPropagation(props.stopPropagation, React.createElement(\"div\", {\n    className: `${classPrefix}-actions ${classPrefix}-actions-right`,\n    ref: rightRef\n  }, props.rightActions.map(renderAction))))));\n});\nconst colorRecord = {\n  light: 'var(--adm-color-light)',\n  weak: 'var(--adm-color-weak)',\n  primary: 'var(--adm-color-primary)',\n  success: 'var(--adm-color-success)',\n  warning: 'var(--adm-color-warning)',\n  danger: 'var(--adm-color-danger)'\n};", "map": {"version": 3, "names": ["animated", "useSpring", "useDrag", "React", "forwardRef", "useEffect", "useImperativeHandle", "useRef", "withNativeProps", "nearest", "mergeProps", "withStopPropagation", "<PERSON><PERSON>", "classPrefix", "defaultProps", "rightActions", "leftActions", "closeOnTouchOutside", "closeOnAction", "stopPropagation", "SwipeAction", "p", "ref", "props", "rootRef", "leftRef", "rightRef", "getWidth", "element", "current", "offsetWidth", "getLeftWidth", "getRightWidth", "x", "api", "config", "tension", "friction", "draggingRef", "dragCancelRef", "forceCancelDrag", "_a", "call", "bind", "state", "cancel", "intentional", "down", "offsetX", "offset", "last", "leftWidth", "rightWidth", "position", "velocity", "direction", "Math", "max", "min", "targetX", "start", "onActionsReveal", "window", "setTimeout", "immediate", "from", "get", "bounds", "left", "right", "axis", "preventScroll", "pointer", "touch", "triggerAllEvents", "close", "onClose", "show", "side", "handle", "e", "root", "contains", "target", "document", "addEventListener", "removeEventListener", "renderAction", "action", "_b", "color", "createElement", "key", "className", "style", "colorRecord", "onClick", "onAction", "text", "Object", "assign", "onClickCapture", "preventDefault", "div", "map", "goal", "pointerEvents", "to", "v", "children", "light", "weak", "primary", "success", "warning", "danger"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/swipe-action/swipe-action.js"], "sourcesContent": ["import { animated, useSpring } from '@react-spring/web';\nimport { useDrag } from '@use-gesture/react';\nimport React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { nearest } from '../../utils/nearest';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withStopPropagation } from '../../utils/with-stop-propagation';\nimport Button from '../button';\nconst classPrefix = `adm-swipe-action`;\nconst defaultProps = {\n  rightActions: [],\n  leftActions: [],\n  closeOnTouchOutside: true,\n  closeOnAction: true,\n  stopPropagation: []\n};\nexport const SwipeAction = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const rootRef = useRef(null);\n  const leftRef = useRef(null);\n  const rightRef = useRef(null);\n  function getWidth(ref) {\n    const element = ref.current;\n    if (!element) return 0;\n    return element.offsetWidth;\n  }\n  function getLeftWidth() {\n    return getWidth(leftRef);\n  }\n  function getRightWidth() {\n    return getWidth(rightRef);\n  }\n  const [{\n    x\n  }, api] = useSpring(() => ({\n    x: 0,\n    config: {\n      tension: 200,\n      friction: 30\n    }\n  }), []);\n  const draggingRef = useRef(false);\n  const dragCancelRef = useRef(null);\n  function forceCancelDrag() {\n    var _a;\n    (_a = dragCancelRef.current) === null || _a === void 0 ? void 0 : _a.call(dragCancelRef);\n    draggingRef.current = false;\n  }\n  const bind = useDrag(state => {\n    var _a;\n    dragCancelRef.current = state.cancel;\n    if (!state.intentional) return;\n    if (state.down) {\n      draggingRef.current = true;\n    }\n    if (!draggingRef.current) return;\n    const [offsetX] = state.offset;\n    if (state.last) {\n      const leftWidth = getLeftWidth();\n      const rightWidth = getRightWidth();\n      let position = offsetX + state.velocity[0] * state.direction[0] * 50;\n      if (offsetX > 0) {\n        position = Math.max(0, position);\n      } else if (offsetX < 0) {\n        position = Math.min(0, position);\n      } else {\n        position = 0;\n      }\n      const targetX = nearest([-rightWidth, 0, leftWidth], position);\n      api.start({\n        x: targetX\n      });\n      if (targetX !== 0) {\n        (_a = p.onActionsReveal) === null || _a === void 0 ? void 0 : _a.call(p, targetX > 0 ? 'left' : 'right');\n      }\n      window.setTimeout(() => {\n        draggingRef.current = false;\n      });\n    } else {\n      api.start({\n        x: offsetX,\n        immediate: true\n      });\n    }\n  }, {\n    from: () => [x.get(), 0],\n    bounds: () => {\n      const leftWidth = getLeftWidth();\n      const rightWidth = getRightWidth();\n      return {\n        left: -rightWidth,\n        right: leftWidth\n      };\n    },\n    axis: 'x',\n    preventScroll: true,\n    pointer: {\n      touch: true\n    },\n    triggerAllEvents: true\n  });\n  const close = () => {\n    var _a;\n    api.start({\n      x: 0\n    });\n    forceCancelDrag();\n    (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n  };\n  useImperativeHandle(ref, () => ({\n    show: (side = 'right') => {\n      var _a;\n      if (side === 'right') {\n        api.start({\n          x: -getRightWidth()\n        });\n      } else if (side === 'left') {\n        api.start({\n          x: getLeftWidth()\n        });\n      }\n      (_a = p.onActionsReveal) === null || _a === void 0 ? void 0 : _a.call(p, side);\n    },\n    close\n  }));\n  useEffect(() => {\n    if (!props.closeOnTouchOutside) return;\n    function handle(e) {\n      if (x.get() === 0) {\n        return;\n      }\n      const root = rootRef.current;\n      if (root && !root.contains(e.target)) {\n        close();\n      }\n    }\n    document.addEventListener('touchstart', handle);\n    return () => {\n      document.removeEventListener('touchstart', handle);\n    };\n  }, [props.closeOnTouchOutside]);\n  function renderAction(action) {\n    var _a, _b;\n    const color = (_a = action.color) !== null && _a !== void 0 ? _a : 'light';\n    return React.createElement(Button, {\n      key: action.key,\n      className: `${classPrefix}-action-button`,\n      style: {\n        '--background-color': (_b = colorRecord[color]) !== null && _b !== void 0 ? _b : color\n      },\n      onClick: e => {\n        var _a, _b;\n        if (props.closeOnAction) {\n          close();\n        }\n        (_a = action.onClick) === null || _a === void 0 ? void 0 : _a.call(action, e);\n        (_b = props.onAction) === null || _b === void 0 ? void 0 : _b.call(props, action, e);\n      }\n    }, action.text);\n  }\n  return withNativeProps(props, React.createElement(\"div\", Object.assign({\n    className: classPrefix\n  }, bind(), {\n    ref: rootRef,\n    onClickCapture: e => {\n      if (draggingRef.current) {\n        e.stopPropagation();\n        e.preventDefault();\n      }\n    }\n  }), React.createElement(animated.div, {\n    className: `${classPrefix}-track`,\n    style: {\n      x\n    }\n  }, withStopPropagation(props.stopPropagation, React.createElement(\"div\", {\n    className: `${classPrefix}-actions ${classPrefix}-actions-left`,\n    ref: leftRef\n  }, props.leftActions.map(renderAction))), React.createElement(\"div\", {\n    className: `${classPrefix}-content`,\n    onClickCapture: e => {\n      if (x.goal !== 0) {\n        e.preventDefault();\n        e.stopPropagation();\n        close();\n      }\n    }\n  }, React.createElement(animated.div, {\n    style: {\n      pointerEvents: x.to(v => v !== 0 && x.goal !== 0 ? 'none' : 'auto')\n    }\n  }, props.children)), withStopPropagation(props.stopPropagation, React.createElement(\"div\", {\n    className: `${classPrefix}-actions ${classPrefix}-actions-right`,\n    ref: rightRef\n  }, props.rightActions.map(renderAction))))));\n});\nconst colorRecord = {\n  light: 'var(--adm-color-light)',\n  weak: 'var(--adm-color-weak)',\n  primary: 'var(--adm-color-primary)',\n  success: 'var(--adm-color-success)',\n  warning: 'var(--adm-color-warning)',\n  danger: 'var(--adm-color-danger)'\n};"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,mBAAmB;AACvD,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,MAAM,QAAQ,OAAO;AACjF,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,OAAOC,MAAM,MAAM,WAAW;AAC9B,MAAMC,WAAW,GAAG,kBAAkB;AACtC,MAAMC,YAAY,GAAG;EACnBC,YAAY,EAAE,EAAE;EAChBC,WAAW,EAAE,EAAE;EACfC,mBAAmB,EAAE,IAAI;EACzBC,aAAa,EAAE,IAAI;EACnBC,eAAe,EAAE;AACnB,CAAC;AACD,OAAO,MAAMC,WAAW,GAAGhB,UAAU,CAAC,CAACiB,CAAC,EAAEC,GAAG,KAAK;EAChD,MAAMC,KAAK,GAAGb,UAAU,CAACI,YAAY,EAAEO,CAAC,CAAC;EACzC,MAAMG,OAAO,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMkB,OAAO,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMmB,QAAQ,GAAGnB,MAAM,CAAC,IAAI,CAAC;EAC7B,SAASoB,QAAQA,CAACL,GAAG,EAAE;IACrB,MAAMM,OAAO,GAAGN,GAAG,CAACO,OAAO;IAC3B,IAAI,CAACD,OAAO,EAAE,OAAO,CAAC;IACtB,OAAOA,OAAO,CAACE,WAAW;EAC5B;EACA,SAASC,YAAYA,CAAA,EAAG;IACtB,OAAOJ,QAAQ,CAACF,OAAO,CAAC;EAC1B;EACA,SAASO,aAAaA,CAAA,EAAG;IACvB,OAAOL,QAAQ,CAACD,QAAQ,CAAC;EAC3B;EACA,MAAM,CAAC;IACLO;EACF,CAAC,EAAEC,GAAG,CAAC,GAAGjC,SAAS,CAAC,OAAO;IACzBgC,CAAC,EAAE,CAAC;IACJE,MAAM,EAAE;MACNC,OAAO,EAAE,GAAG;MACZC,QAAQ,EAAE;IACZ;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,MAAMC,WAAW,GAAG/B,MAAM,CAAC,KAAK,CAAC;EACjC,MAAMgC,aAAa,GAAGhC,MAAM,CAAC,IAAI,CAAC;EAClC,SAASiC,eAAeA,CAAA,EAAG;IACzB,IAAIC,EAAE;IACN,CAACA,EAAE,GAAGF,aAAa,CAACV,OAAO,MAAM,IAAI,IAAIY,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACH,aAAa,CAAC;IACxFD,WAAW,CAACT,OAAO,GAAG,KAAK;EAC7B;EACA,MAAMc,IAAI,GAAGzC,OAAO,CAAC0C,KAAK,IAAI;IAC5B,IAAIH,EAAE;IACNF,aAAa,CAACV,OAAO,GAAGe,KAAK,CAACC,MAAM;IACpC,IAAI,CAACD,KAAK,CAACE,WAAW,EAAE;IACxB,IAAIF,KAAK,CAACG,IAAI,EAAE;MACdT,WAAW,CAACT,OAAO,GAAG,IAAI;IAC5B;IACA,IAAI,CAACS,WAAW,CAACT,OAAO,EAAE;IAC1B,MAAM,CAACmB,OAAO,CAAC,GAAGJ,KAAK,CAACK,MAAM;IAC9B,IAAIL,KAAK,CAACM,IAAI,EAAE;MACd,MAAMC,SAAS,GAAGpB,YAAY,CAAC,CAAC;MAChC,MAAMqB,UAAU,GAAGpB,aAAa,CAAC,CAAC;MAClC,IAAIqB,QAAQ,GAAGL,OAAO,GAAGJ,KAAK,CAACU,QAAQ,CAAC,CAAC,CAAC,GAAGV,KAAK,CAACW,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;MACpE,IAAIP,OAAO,GAAG,CAAC,EAAE;QACfK,QAAQ,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,QAAQ,CAAC;MAClC,CAAC,MAAM,IAAIL,OAAO,GAAG,CAAC,EAAE;QACtBK,QAAQ,GAAGG,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEL,QAAQ,CAAC;MAClC,CAAC,MAAM;QACLA,QAAQ,GAAG,CAAC;MACd;MACA,MAAMM,OAAO,GAAGlD,OAAO,CAAC,CAAC,CAAC2C,UAAU,EAAE,CAAC,EAAED,SAAS,CAAC,EAAEE,QAAQ,CAAC;MAC9DnB,GAAG,CAAC0B,KAAK,CAAC;QACR3B,CAAC,EAAE0B;MACL,CAAC,CAAC;MACF,IAAIA,OAAO,KAAK,CAAC,EAAE;QACjB,CAAClB,EAAE,GAAGpB,CAAC,CAACwC,eAAe,MAAM,IAAI,IAAIpB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACrB,CAAC,EAAEsC,OAAO,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC;MAC1G;MACAG,MAAM,CAACC,UAAU,CAAC,MAAM;QACtBzB,WAAW,CAACT,OAAO,GAAG,KAAK;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLK,GAAG,CAAC0B,KAAK,CAAC;QACR3B,CAAC,EAAEe,OAAO;QACVgB,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDC,IAAI,EAAEA,CAAA,KAAM,CAAChC,CAAC,CAACiC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACxBC,MAAM,EAAEA,CAAA,KAAM;MACZ,MAAMhB,SAAS,GAAGpB,YAAY,CAAC,CAAC;MAChC,MAAMqB,UAAU,GAAGpB,aAAa,CAAC,CAAC;MAClC,OAAO;QACLoC,IAAI,EAAE,CAAChB,UAAU;QACjBiB,KAAK,EAAElB;MACT,CAAC;IACH,CAAC;IACDmB,IAAI,EAAE,GAAG;IACTC,aAAa,EAAE,IAAI;IACnBC,OAAO,EAAE;MACPC,KAAK,EAAE;IACT,CAAC;IACDC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EACF,MAAMC,KAAK,GAAGA,CAAA,KAAM;IAClB,IAAIlC,EAAE;IACNP,GAAG,CAAC0B,KAAK,CAAC;MACR3B,CAAC,EAAE;IACL,CAAC,CAAC;IACFO,eAAe,CAAC,CAAC;IACjB,CAACC,EAAE,GAAGlB,KAAK,CAACqD,OAAO,MAAM,IAAI,IAAInC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACnB,KAAK,CAAC;EAC1E,CAAC;EACDjB,mBAAmB,CAACgB,GAAG,EAAE,OAAO;IAC9BuD,IAAI,EAAEA,CAACC,IAAI,GAAG,OAAO,KAAK;MACxB,IAAIrC,EAAE;MACN,IAAIqC,IAAI,KAAK,OAAO,EAAE;QACpB5C,GAAG,CAAC0B,KAAK,CAAC;UACR3B,CAAC,EAAE,CAACD,aAAa,CAAC;QACpB,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI8C,IAAI,KAAK,MAAM,EAAE;QAC1B5C,GAAG,CAAC0B,KAAK,CAAC;UACR3B,CAAC,EAAEF,YAAY,CAAC;QAClB,CAAC,CAAC;MACJ;MACA,CAACU,EAAE,GAAGpB,CAAC,CAACwC,eAAe,MAAM,IAAI,IAAIpB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACrB,CAAC,EAAEyD,IAAI,CAAC;IAChF,CAAC;IACDH;EACF,CAAC,CAAC,CAAC;EACHtE,SAAS,CAAC,MAAM;IACd,IAAI,CAACkB,KAAK,CAACN,mBAAmB,EAAE;IAChC,SAAS8D,MAAMA,CAACC,CAAC,EAAE;MACjB,IAAI/C,CAAC,CAACiC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;QACjB;MACF;MACA,MAAMe,IAAI,GAAGzD,OAAO,CAACK,OAAO;MAC5B,IAAIoD,IAAI,IAAI,CAACA,IAAI,CAACC,QAAQ,CAACF,CAAC,CAACG,MAAM,CAAC,EAAE;QACpCR,KAAK,CAAC,CAAC;MACT;IACF;IACAS,QAAQ,CAACC,gBAAgB,CAAC,YAAY,EAAEN,MAAM,CAAC;IAC/C,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,YAAY,EAAEP,MAAM,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,CAACxD,KAAK,CAACN,mBAAmB,CAAC,CAAC;EAC/B,SAASsE,YAAYA,CAACC,MAAM,EAAE;IAC5B,IAAI/C,EAAE,EAAEgD,EAAE;IACV,MAAMC,KAAK,GAAG,CAACjD,EAAE,GAAG+C,MAAM,CAACE,KAAK,MAAM,IAAI,IAAIjD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,OAAO;IAC1E,OAAOtC,KAAK,CAACwF,aAAa,CAAC/E,MAAM,EAAE;MACjCgF,GAAG,EAAEJ,MAAM,CAACI,GAAG;MACfC,SAAS,EAAE,GAAGhF,WAAW,gBAAgB;MACzCiF,KAAK,EAAE;QACL,oBAAoB,EAAE,CAACL,EAAE,GAAGM,WAAW,CAACL,KAAK,CAAC,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGC;MACnF,CAAC;MACDM,OAAO,EAAEhB,CAAC,IAAI;QACZ,IAAIvC,EAAE,EAAEgD,EAAE;QACV,IAAIlE,KAAK,CAACL,aAAa,EAAE;UACvByD,KAAK,CAAC,CAAC;QACT;QACA,CAAClC,EAAE,GAAG+C,MAAM,CAACQ,OAAO,MAAM,IAAI,IAAIvD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAAC8C,MAAM,EAAER,CAAC,CAAC;QAC7E,CAACS,EAAE,GAAGlE,KAAK,CAAC0E,QAAQ,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC/C,IAAI,CAACnB,KAAK,EAAEiE,MAAM,EAAER,CAAC,CAAC;MACtF;IACF,CAAC,EAAEQ,MAAM,CAACU,IAAI,CAAC;EACjB;EACA,OAAO1F,eAAe,CAACe,KAAK,EAAEpB,KAAK,CAACwF,aAAa,CAAC,KAAK,EAAEQ,MAAM,CAACC,MAAM,CAAC;IACrEP,SAAS,EAAEhF;EACb,CAAC,EAAE8B,IAAI,CAAC,CAAC,EAAE;IACTrB,GAAG,EAAEE,OAAO;IACZ6E,cAAc,EAAErB,CAAC,IAAI;MACnB,IAAI1C,WAAW,CAACT,OAAO,EAAE;QACvBmD,CAAC,CAAC7D,eAAe,CAAC,CAAC;QACnB6D,CAAC,CAACsB,cAAc,CAAC,CAAC;MACpB;IACF;EACF,CAAC,CAAC,EAAEnG,KAAK,CAACwF,aAAa,CAAC3F,QAAQ,CAACuG,GAAG,EAAE;IACpCV,SAAS,EAAE,GAAGhF,WAAW,QAAQ;IACjCiF,KAAK,EAAE;MACL7D;IACF;EACF,CAAC,EAAEtB,mBAAmB,CAACY,KAAK,CAACJ,eAAe,EAAEhB,KAAK,CAACwF,aAAa,CAAC,KAAK,EAAE;IACvEE,SAAS,EAAE,GAAGhF,WAAW,YAAYA,WAAW,eAAe;IAC/DS,GAAG,EAAEG;EACP,CAAC,EAAEF,KAAK,CAACP,WAAW,CAACwF,GAAG,CAACjB,YAAY,CAAC,CAAC,CAAC,EAAEpF,KAAK,CAACwF,aAAa,CAAC,KAAK,EAAE;IACnEE,SAAS,EAAE,GAAGhF,WAAW,UAAU;IACnCwF,cAAc,EAAErB,CAAC,IAAI;MACnB,IAAI/C,CAAC,CAACwE,IAAI,KAAK,CAAC,EAAE;QAChBzB,CAAC,CAACsB,cAAc,CAAC,CAAC;QAClBtB,CAAC,CAAC7D,eAAe,CAAC,CAAC;QACnBwD,KAAK,CAAC,CAAC;MACT;IACF;EACF,CAAC,EAAExE,KAAK,CAACwF,aAAa,CAAC3F,QAAQ,CAACuG,GAAG,EAAE;IACnCT,KAAK,EAAE;MACLY,aAAa,EAAEzE,CAAC,CAAC0E,EAAE,CAACC,CAAC,IAAIA,CAAC,KAAK,CAAC,IAAI3E,CAAC,CAACwE,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM;IACpE;EACF,CAAC,EAAElF,KAAK,CAACsF,QAAQ,CAAC,CAAC,EAAElG,mBAAmB,CAACY,KAAK,CAACJ,eAAe,EAAEhB,KAAK,CAACwF,aAAa,CAAC,KAAK,EAAE;IACzFE,SAAS,EAAE,GAAGhF,WAAW,YAAYA,WAAW,gBAAgB;IAChES,GAAG,EAAEI;EACP,CAAC,EAAEH,KAAK,CAACR,YAAY,CAACyF,GAAG,CAACjB,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC;AACF,MAAMQ,WAAW,GAAG;EAClBe,KAAK,EAAE,wBAAwB;EAC/BC,IAAI,EAAE,uBAAuB;EAC7BC,OAAO,EAAE,0BAA0B;EACnCC,OAAO,EAAE,0BAA0B;EACnCC,OAAO,EAAE,0BAA0B;EACnCC,MAAM,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}