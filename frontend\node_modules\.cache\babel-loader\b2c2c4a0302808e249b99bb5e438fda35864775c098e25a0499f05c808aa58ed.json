{"ast": null, "code": "import React, { memo } from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nconst classPrefix = `adm-dot-loading`;\nconst colorRecord = {\n  default: 'var(--adm-color-weak)',\n  primary: 'var(--adm-color-primary)',\n  white: 'var(--adm-color-white)'\n};\nconst defaultProps = {\n  color: 'default'\n};\nexport const DotLoading = memo(p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  return withNativeProps(props, React.createElement(\"div\", {\n    style: {\n      color: (_a = colorRecord[props.color]) !== null && _a !== void 0 ? _a : props.color\n    },\n    className: classNames('adm-loading', classPrefix)\n  }, React.createElement(\"svg\", {\n    height: '1em',\n    viewBox: '0 0 100 40',\n    style: {\n      verticalAlign: '-0.125em'\n    }\n  }, React.createElement(\"g\", {\n    stroke: 'none',\n    strokeWidth: '1',\n    fill: 'none',\n    fillRule: 'evenodd'\n  }, React.createElement(\"g\", {\n    transform: 'translate(-100.000000, -71.000000)'\n  }, React.createElement(\"g\", {\n    transform: 'translate(95.000000, 71.000000)'\n  }, React.createElement(\"g\", {\n    transform: 'translate(5.000000, 0.000000)'\n  }, [0, 1, 2].map(i => React.createElement(\"rect\", {\n    key: i,\n    fill: 'currentColor',\n    x: 20 + i * 26,\n    y: '16',\n    width: '8',\n    height: '8',\n    rx: '2'\n  }, React.createElement(\"animate\", {\n    attributeName: 'y',\n    from: '16',\n    to: '16',\n    dur: '2s',\n    begin: `${i * 0.2}s`,\n    repeatCount: 'indefinite',\n    values: '16; 6; 26; 16; 16',\n    keyTimes: '0; 0.1; 0.3; 0.4; 1'\n  }))))))))));\n});", "map": {"version": 3, "names": ["React", "memo", "mergeProps", "withNativeProps", "classNames", "classPrefix", "colorRecord", "default", "primary", "white", "defaultProps", "color", "DotLoading", "p", "_a", "props", "createElement", "style", "className", "height", "viewBox", "verticalAlign", "stroke", "strokeWidth", "fill", "fillRule", "transform", "map", "i", "key", "x", "y", "width", "rx", "attributeName", "from", "to", "dur", "begin", "repeatCount", "values", "keyTimes"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/dot-loading/dot-loading.js"], "sourcesContent": ["import React, { memo } from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nconst classPrefix = `adm-dot-loading`;\nconst colorRecord = {\n  default: 'var(--adm-color-weak)',\n  primary: 'var(--adm-color-primary)',\n  white: 'var(--adm-color-white)'\n};\nconst defaultProps = {\n  color: 'default'\n};\nexport const DotLoading = memo(p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  return withNativeProps(props, React.createElement(\"div\", {\n    style: {\n      color: (_a = colorRecord[props.color]) !== null && _a !== void 0 ? _a : props.color\n    },\n    className: classNames('adm-loading', classPrefix)\n  }, React.createElement(\"svg\", {\n    height: '1em',\n    viewBox: '0 0 100 40',\n    style: {\n      verticalAlign: '-0.125em'\n    }\n  }, React.createElement(\"g\", {\n    stroke: 'none',\n    strokeWidth: '1',\n    fill: 'none',\n    fillRule: 'evenodd'\n  }, React.createElement(\"g\", {\n    transform: 'translate(-100.000000, -71.000000)'\n  }, React.createElement(\"g\", {\n    transform: 'translate(95.000000, 71.000000)'\n  }, React.createElement(\"g\", {\n    transform: 'translate(5.000000, 0.000000)'\n  }, [0, 1, 2].map(i => React.createElement(\"rect\", {\n    key: i,\n    fill: 'currentColor',\n    x: 20 + i * 26,\n    y: '16',\n    width: '8',\n    height: '8',\n    rx: '2'\n  }, React.createElement(\"animate\", {\n    attributeName: 'y',\n    from: '16',\n    to: '16',\n    dur: '2s',\n    begin: `${i * 0.2}s`,\n    repeatCount: 'indefinite',\n    values: '16; 6; 26; 16; 16',\n    keyTimes: '0; 0.1; 0.3; 0.4; 1'\n  }))))))))));\n});"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,IAAI,QAAQ,OAAO;AACnC,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,UAAU,MAAM,YAAY;AACnC,MAAMC,WAAW,GAAG,iBAAiB;AACrC,MAAMC,WAAW,GAAG;EAClBC,OAAO,EAAE,uBAAuB;EAChCC,OAAO,EAAE,0BAA0B;EACnCC,KAAK,EAAE;AACT,CAAC;AACD,MAAMC,YAAY,GAAG;EACnBC,KAAK,EAAE;AACT,CAAC;AACD,OAAO,MAAMC,UAAU,GAAGX,IAAI,CAACY,CAAC,IAAI;EAClC,IAAIC,EAAE;EACN,MAAMC,KAAK,GAAGb,UAAU,CAACQ,YAAY,EAAEG,CAAC,CAAC;EACzC,OAAOV,eAAe,CAACY,KAAK,EAAEf,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IACvDC,KAAK,EAAE;MACLN,KAAK,EAAE,CAACG,EAAE,GAAGR,WAAW,CAACS,KAAK,CAACJ,KAAK,CAAC,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGC,KAAK,CAACJ;IAChF,CAAC;IACDO,SAAS,EAAEd,UAAU,CAAC,aAAa,EAAEC,WAAW;EAClD,CAAC,EAAEL,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAC5BG,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,YAAY;IACrBH,KAAK,EAAE;MACLI,aAAa,EAAE;IACjB;EACF,CAAC,EAAErB,KAAK,CAACgB,aAAa,CAAC,GAAG,EAAE;IAC1BM,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,GAAG;IAChBC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAEzB,KAAK,CAACgB,aAAa,CAAC,GAAG,EAAE;IAC1BU,SAAS,EAAE;EACb,CAAC,EAAE1B,KAAK,CAACgB,aAAa,CAAC,GAAG,EAAE;IAC1BU,SAAS,EAAE;EACb,CAAC,EAAE1B,KAAK,CAACgB,aAAa,CAAC,GAAG,EAAE;IAC1BU,SAAS,EAAE;EACb,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,CAAC,IAAI5B,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAE;IAChDa,GAAG,EAAED,CAAC;IACNJ,IAAI,EAAE,cAAc;IACpBM,CAAC,EAAE,EAAE,GAAGF,CAAC,GAAG,EAAE;IACdG,CAAC,EAAE,IAAI;IACPC,KAAK,EAAE,GAAG;IACVb,MAAM,EAAE,GAAG;IACXc,EAAE,EAAE;EACN,CAAC,EAAEjC,KAAK,CAACgB,aAAa,CAAC,SAAS,EAAE;IAChCkB,aAAa,EAAE,GAAG;IAClBC,IAAI,EAAE,IAAI;IACVC,EAAE,EAAE,IAAI;IACRC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,GAAGV,CAAC,GAAG,GAAG,GAAG;IACpBW,WAAW,EAAE,YAAY;IACzBC,MAAM,EAAE,mBAAmB;IAC3BC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}