{"ast": null, "code": "import * as React from \"react\";\nfunction AudioFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AudioFill-AudioFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AudioFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"AudioFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M10.5570651,24.9533238 L10.5570651,24.9533238 C10.7519532,24.9533238 10.9169474,25.0936511 10.9437445,25.2819275 L10.9437444,25.2819266 C10.9734425,25.4994999 11.0082779,25.7163772 11.0482224,25.9323867 C12.2043562,31.8819424 17.5651521,36.3809619 24.0000131,36.3809619 C30.4466375,36.3809619 35.8122714,31.8676121 36.9567014,25.9047527 C36.9859953,25.7533241 37.0191949,25.5466575 37.0563006,25.2818954 L37.0563006,25.2818953 C37.0830977,25.0936189 37.2480919,24.9533238 37.44298,24.9533238 L39.609753,24.9533238 L39.609753,24.9533238 C39.8254673,24.9533238 40,25.1242118 40,25.3346056 C40,25.3491352 39.9991218,25.3636523 39.9974086,25.3780856 C39.9700677,25.608562 39.9456559,25.7933237 39.9241739,25.9314206 C38.8080766,33.0057089 32.8565191,38.5247734 25.464709,39.1742826 L25.464709,43.6190476 L25.464709,43.6190476 C25.464709,43.8294419 25.2898382,44 25.0741239,44 L22.9259067,44 L22.9259067,44 C22.7101924,44 22.5353216,43.8294419 22.5353216,43.6190476 C22.5353216,43.6190476 22.5353216,43.6190476 22.5353216,43.6190476 L22.5343452,39.1742826 C15.1425351,38.5238046 9.18900934,33.0028517 8.07584146,25.9266438 L8.07584119,25.926642 C8.0481635,25.7442138 8.02374841,25.5613289 8.00260646,25.3780703 L8.00260646,25.3780703 C7.97796764,25.1696381 8.13076403,24.9810113 8.34436868,24.9561653 L8.3678038,24.9542605 L10.5570324,24.9542605 L10.5570651,24.9533238 Z M24.000032,4 L24.0000316,4 C29.1232586,4 33.2764459,8.05075595 33.2764459,13.0476373 L33.2764459,23.5238465 L33.2764459,23.5232109 C33.2764459,28.5200878 29.1232586,32.5708482 24.0000316,32.5708482 C18.8773081,32.5708482 14.7243497,28.5208646 14.7236173,23.5245056 L14.7236173,13.0482964 L14.7236173,13.0476626 C14.7236173,8.05078574 18.8768,4 24.0000316,4 C24.0002486,4 24.0004657,4 24.0006827,4 L24.000032,4 Z\",\n    id: \"AudioFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default AudioFill;", "map": {"version": 3, "names": ["React", "AudioFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/AudioFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction AudioFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AudioFill-AudioFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AudioFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"AudioFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M10.5570651,24.9533238 L10.5570651,24.9533238 C10.7519532,24.9533238 10.9169474,25.0936511 10.9437445,25.2819275 L10.9437444,25.2819266 C10.9734425,25.4994999 11.0082779,25.7163772 11.0482224,25.9323867 C12.2043562,31.8819424 17.5651521,36.3809619 24.0000131,36.3809619 C30.4466375,36.3809619 35.8122714,31.8676121 36.9567014,25.9047527 C36.9859953,25.7533241 37.0191949,25.5466575 37.0563006,25.2818954 L37.0563006,25.2818953 C37.0830977,25.0936189 37.2480919,24.9533238 37.44298,24.9533238 L39.609753,24.9533238 L39.609753,24.9533238 C39.8254673,24.9533238 40,25.1242118 40,25.3346056 C40,25.3491352 39.9991218,25.3636523 39.9974086,25.3780856 C39.9700677,25.608562 39.9456559,25.7933237 39.9241739,25.9314206 C38.8080766,33.0057089 32.8565191,38.5247734 25.464709,39.1742826 L25.464709,43.6190476 L25.464709,43.6190476 C25.464709,43.8294419 25.2898382,44 25.0741239,44 L22.9259067,44 L22.9259067,44 C22.7101924,44 22.5353216,43.8294419 22.5353216,43.6190476 C22.5353216,43.6190476 22.5353216,43.6190476 22.5353216,43.6190476 L22.5343452,39.1742826 C15.1425351,38.5238046 9.18900934,33.0028517 8.07584146,25.9266438 L8.07584119,25.926642 C8.0481635,25.7442138 8.02374841,25.5613289 8.00260646,25.3780703 L8.00260646,25.3780703 C7.97796764,25.1696381 8.13076403,24.9810113 8.34436868,24.9561653 L8.3678038,24.9542605 L10.5570324,24.9542605 L10.5570651,24.9533238 Z M24.000032,4 L24.0000316,4 C29.1232586,4 33.2764459,8.05075595 33.2764459,13.0476373 L33.2764459,23.5238465 L33.2764459,23.5232109 C33.2764459,28.5200878 29.1232586,32.5708482 24.0000316,32.5708482 C18.8773081,32.5708482 14.7243497,28.5208646 14.7236173,23.5245056 L14.7236173,13.0482964 L14.7236173,13.0476626 C14.7236173,8.05078574 18.8768,4 24.0000316,4 C24.0002486,4 24.0004657,4 24.0006827,4 L24.000032,4 Z\",\n    id: \"AudioFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default AudioFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,qBAAqB;IACzBC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,wBAAwB;IAC5BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,mvDAAmvD;IACtvDR,EAAE,EAAE,wBAAwB;IAC5BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}