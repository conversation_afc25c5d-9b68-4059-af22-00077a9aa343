{"ast": null, "code": "import React, { useRef, useEffect } from 'react';\nimport classNames from 'classnames';\nimport { animated, useSpring } from '@react-spring/web';\nimport { useThrottleFn } from 'ahooks';\nconst classPrefix = `adm-scroll-mask`;\nexport const ScrollMask = props => {\n  const maskRef = useRef(null);\n  const [{\n    leftMaskOpacity,\n    rightMaskOpacity\n  }, api] = useSpring(() => ({\n    leftMaskOpacity: 0,\n    rightMaskOpacity: 0,\n    config: {\n      clamp: true\n    }\n  }));\n  const {\n    run: updateMask\n  } = useThrottleFn((immediate = false) => {\n    const mask = maskRef.current;\n    if (!mask) return;\n    const scrollEl = props.scrollTrackRef.current;\n    if (!scrollEl) return;\n    const scrollLeft = scrollEl.scrollLeft;\n    const showLeftMask = scrollLeft > 0;\n    const showRightMask = scrollLeft + scrollEl.offsetWidth < scrollEl.scrollWidth;\n    api.start({\n      leftMaskOpacity: showLeftMask ? 1 : 0,\n      rightMaskOpacity: showRightMask ? 1 : 0,\n      immediate\n    });\n  }, {\n    wait: 100,\n    trailing: true,\n    leading: true\n  });\n  useEffect(() => {\n    updateMask(true);\n  }, []);\n  useEffect(() => {\n    const scrollEl = props.scrollTrackRef.current;\n    if (!scrollEl) return;\n    scrollEl.addEventListener('scroll', updateMask);\n    return () => scrollEl.removeEventListener('scroll', updateMask);\n  }, []);\n  return React.createElement(React.Fragment, null, React.createElement(animated.div, {\n    ref: maskRef,\n    className: classNames(classPrefix, `${classPrefix}-left`),\n    style: {\n      opacity: leftMaskOpacity\n    }\n  }), React.createElement(animated.div, {\n    className: classNames(classPrefix, `${classPrefix}-right`),\n    style: {\n      opacity: rightMaskOpacity\n    }\n  }));\n};", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "classNames", "animated", "useSpring", "useThrottleFn", "classPrefix", "ScrollMask", "props", "maskRef", "leftMaskOpacity", "rightMaskOpacity", "api", "config", "clamp", "run", "updateMask", "immediate", "mask", "current", "scrollEl", "scrollTrackRef", "scrollLeft", "showLeftMask", "showRightMask", "offsetWidth", "scrollWidth", "start", "wait", "trailing", "leading", "addEventListener", "removeEventListener", "createElement", "Fragment", "div", "ref", "className", "style", "opacity"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/scroll-mask/scroll-mask.js"], "sourcesContent": ["import React, { useRef, useEffect } from 'react';\nimport classNames from 'classnames';\nimport { animated, useSpring } from '@react-spring/web';\nimport { useThrottleFn } from 'ahooks';\nconst classPrefix = `adm-scroll-mask`;\nexport const ScrollMask = props => {\n  const maskRef = useRef(null);\n  const [{\n    leftMaskOpacity,\n    rightMaskOpacity\n  }, api] = useSpring(() => ({\n    leftMaskOpacity: 0,\n    rightMaskOpacity: 0,\n    config: {\n      clamp: true\n    }\n  }));\n  const {\n    run: updateMask\n  } = useThrottleFn((immediate = false) => {\n    const mask = maskRef.current;\n    if (!mask) return;\n    const scrollEl = props.scrollTrackRef.current;\n    if (!scrollEl) return;\n    const scrollLeft = scrollEl.scrollLeft;\n    const showLeftMask = scrollLeft > 0;\n    const showRightMask = scrollLeft + scrollEl.offsetWidth < scrollEl.scrollWidth;\n    api.start({\n      leftMaskOpacity: showLeftMask ? 1 : 0,\n      rightMaskOpacity: showRightMask ? 1 : 0,\n      immediate\n    });\n  }, {\n    wait: 100,\n    trailing: true,\n    leading: true\n  });\n  useEffect(() => {\n    updateMask(true);\n  }, []);\n  useEffect(() => {\n    const scrollEl = props.scrollTrackRef.current;\n    if (!scrollEl) return;\n    scrollEl.addEventListener('scroll', updateMask);\n    return () => scrollEl.removeEventListener('scroll', updateMask);\n  }, []);\n  return React.createElement(React.Fragment, null, React.createElement(animated.div, {\n    ref: maskRef,\n    className: classNames(classPrefix, `${classPrefix}-left`),\n    style: {\n      opacity: leftMaskOpacity\n    }\n  }), React.createElement(animated.div, {\n    className: classNames(classPrefix, `${classPrefix}-right`),\n    style: {\n      opacity: rightMaskOpacity\n    }\n  }));\n};"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAChD,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,QAAQ,EAAEC,SAAS,QAAQ,mBAAmB;AACvD,SAASC,aAAa,QAAQ,QAAQ;AACtC,MAAMC,WAAW,GAAG,iBAAiB;AACrC,OAAO,MAAMC,UAAU,GAAGC,KAAK,IAAI;EACjC,MAAMC,OAAO,GAAGT,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAM,CAAC;IACLU,eAAe;IACfC;EACF,CAAC,EAAEC,GAAG,CAAC,GAAGR,SAAS,CAAC,OAAO;IACzBM,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBE,MAAM,EAAE;MACNC,KAAK,EAAE;IACT;EACF,CAAC,CAAC,CAAC;EACH,MAAM;IACJC,GAAG,EAAEC;EACP,CAAC,GAAGX,aAAa,CAAC,CAACY,SAAS,GAAG,KAAK,KAAK;IACvC,MAAMC,IAAI,GAAGT,OAAO,CAACU,OAAO;IAC5B,IAAI,CAACD,IAAI,EAAE;IACX,MAAME,QAAQ,GAAGZ,KAAK,CAACa,cAAc,CAACF,OAAO;IAC7C,IAAI,CAACC,QAAQ,EAAE;IACf,MAAME,UAAU,GAAGF,QAAQ,CAACE,UAAU;IACtC,MAAMC,YAAY,GAAGD,UAAU,GAAG,CAAC;IACnC,MAAME,aAAa,GAAGF,UAAU,GAAGF,QAAQ,CAACK,WAAW,GAAGL,QAAQ,CAACM,WAAW;IAC9Ed,GAAG,CAACe,KAAK,CAAC;MACRjB,eAAe,EAAEa,YAAY,GAAG,CAAC,GAAG,CAAC;MACrCZ,gBAAgB,EAAEa,aAAa,GAAG,CAAC,GAAG,CAAC;MACvCP;IACF,CAAC,CAAC;EACJ,CAAC,EAAE;IACDW,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE;EACX,CAAC,CAAC;EACF7B,SAAS,CAAC,MAAM;IACde,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EACNf,SAAS,CAAC,MAAM;IACd,MAAMmB,QAAQ,GAAGZ,KAAK,CAACa,cAAc,CAACF,OAAO;IAC7C,IAAI,CAACC,QAAQ,EAAE;IACfA,QAAQ,CAACW,gBAAgB,CAAC,QAAQ,EAAEf,UAAU,CAAC;IAC/C,OAAO,MAAMI,QAAQ,CAACY,mBAAmB,CAAC,QAAQ,EAAEhB,UAAU,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EACN,OAAOjB,KAAK,CAACkC,aAAa,CAAClC,KAAK,CAACmC,QAAQ,EAAE,IAAI,EAAEnC,KAAK,CAACkC,aAAa,CAAC9B,QAAQ,CAACgC,GAAG,EAAE;IACjFC,GAAG,EAAE3B,OAAO;IACZ4B,SAAS,EAAEnC,UAAU,CAACI,WAAW,EAAE,GAAGA,WAAW,OAAO,CAAC;IACzDgC,KAAK,EAAE;MACLC,OAAO,EAAE7B;IACX;EACF,CAAC,CAAC,EAAEX,KAAK,CAACkC,aAAa,CAAC9B,QAAQ,CAACgC,GAAG,EAAE;IACpCE,SAAS,EAAEnC,UAAU,CAACI,WAAW,EAAE,GAAGA,WAAW,QAAQ,CAAC;IAC1DgC,KAAK,EAAE;MACLC,OAAO,EAAE5B;IACX;EACF,CAAC,CAAC,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}