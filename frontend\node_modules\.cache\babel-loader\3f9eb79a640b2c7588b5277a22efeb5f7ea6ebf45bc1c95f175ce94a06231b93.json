{"ast": null, "code": "import * as React from \"react\";\nfunction ExclamationShieldOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ExclamationShieldOutline-ExclamationShieldOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ExclamationShieldOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ExclamationShieldOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M23.5828113,4.01694873 L39.8608414,7.3485224 C41.6942831,7.74819585 43,9.35541667 43,11.212546 L43,29.7608837 C43,32.637301 41.4221454,35.2872082 38.8789667,36.681899 L25.4394833,43.5037686 C24.2329985,44.1654105 22.7670015,44.1654105 21.5605167,43.5037686 L9.12103331,36.681899 C6.57785456,35.2872082 5,32.637301 5,29.7608837 L5,11.212546 C5,9.35541667 6.30571695,7.74819585 8.1391586,7.3485224 L23.4174116,4.01800352 C23.4718692,4.00613226 23.5282067,4.00577298 23.5828113,4.01694873 Z M23.417495,7.05577718 L8.78478965,10.2465401 C8.36462594,10.3381319 8.05532106,10.6833926 8.00668805,11.0979156 L8,11.212546 L8,29.7608837 C8,31.480481 9.90227033,33.0705522 11.3717744,33.9684245 L11.5756458,34.0865183 L23.0151292,40.9083879 C23.2790477,41.053122 23.5926385,41.0712138 23.8689752,40.9626632 L23.9848708,40.9083879 L37.4243542,34.0865183 C38.9447328,33.2527357 39.9131065,31.7011782 39.9944389,29.9945186 L40,29.7608837 L40,11.212546 C40,10.7869539 39.7257088,10.4138599 39.3269393,10.2776704 L39.2152103,10.2465401 L23.5827367,7.05467968 C23.5281801,7.04354024 23.4718988,7.04391405 23.417495,7.05577718 Z M25.1,30 C25.3209139,30 25.5,30.1790861 25.5,30.4 L25.5,32.6 C25.5,32.8209139 25.3209139,33 25.1,33 L22.9,33 C22.6790861,33 22.5,32.8209139 22.5,32.6 L22.5,30.4 C22.5,30.1790861 22.6790861,30 22.9,30 L25.1,30 Z M25.1,14 C25.3209139,14 25.5,14.1790861 25.5,14.4 L25.5,26.6 C25.5,26.8209139 25.3209139,27 25.1,27 L22.9,27 C22.6790861,27 22.5,26.8209139 22.5,26.6 L22.5,14.4 C22.5,14.1790861 22.6790861,14 22.9,14 L25.1,14 Z\",\n    id: \"ExclamationShieldOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default ExclamationShieldOutline;", "map": {"version": 3, "names": ["React", "ExclamationShieldOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/ExclamationShieldOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction ExclamationShieldOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ExclamationShieldOutline-ExclamationShieldOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ExclamationShieldOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ExclamationShieldOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M23.5828113,4.01694873 L39.8608414,7.3485224 C41.6942831,7.74819585 43,9.35541667 43,11.212546 L43,29.7608837 C43,32.637301 41.4221454,35.2872082 38.8789667,36.681899 L25.4394833,43.5037686 C24.2329985,44.1654105 22.7670015,44.1654105 21.5605167,43.5037686 L9.12103331,36.681899 C6.57785456,35.2872082 5,32.637301 5,29.7608837 L5,11.212546 C5,9.35541667 6.30571695,7.74819585 8.1391586,7.3485224 L23.4174116,4.01800352 C23.4718692,4.00613226 23.5282067,4.00577298 23.5828113,4.01694873 Z M23.417495,7.05577718 L8.78478965,10.2465401 C8.36462594,10.3381319 8.05532106,10.6833926 8.00668805,11.0979156 L8,11.212546 L8,29.7608837 C8,31.480481 9.90227033,33.0705522 11.3717744,33.9684245 L11.5756458,34.0865183 L23.0151292,40.9083879 C23.2790477,41.053122 23.5926385,41.0712138 23.8689752,40.9626632 L23.9848708,40.9083879 L37.4243542,34.0865183 C38.9447328,33.2527357 39.9131065,31.7011782 39.9944389,29.9945186 L40,29.7608837 L40,11.212546 C40,10.7869539 39.7257088,10.4138599 39.3269393,10.2776704 L39.2152103,10.2465401 L23.5827367,7.05467968 C23.5281801,7.04354024 23.4718988,7.04391405 23.417495,7.05577718 Z M25.1,30 C25.3209139,30 25.5,30.1790861 25.5,30.4 L25.5,32.6 C25.5,32.8209139 25.3209139,33 25.1,33 L22.9,33 C22.6790861,33 22.5,32.8209139 22.5,32.6 L22.5,30.4 C22.5,30.1790861 22.6790861,30 22.9,30 L25.1,30 Z M25.1,14 C25.3209139,14 25.5,14.1790861 25.5,14.4 L25.5,26.6 C25.5,26.8209139 25.3209139,27 25.1,27 L22.9,27 C22.6790861,27 22.5,26.8209139 22.5,26.6 L22.5,14.4 C22.5,14.1790861 22.6790861,14 22.9,14 L25.1,14 Z\",\n    id: \"ExclamationShieldOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default ExclamationShieldOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,wBAAwBA,CAACC,KAAK,EAAE;EACvC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,mDAAmD;IACvDC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,uCAAuC;IAC3CG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,8/CAA8/C;IACjgDR,EAAE,EAAE,mDAAmD;IACvDG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}