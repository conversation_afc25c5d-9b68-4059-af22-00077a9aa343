{"ast": null, "code": "import { RightOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React from 'react';\nimport { isNodeWithContent } from '../../utils/is-node-with-content';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProp } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nconst classPrefix = `adm-list-item`;\nexport const ListItem = props => {\n  var _a, _b;\n  const {\n    arrow,\n    arrowIcon\n  } = props;\n  const {\n    list: componentConfig = {}\n  } = useConfig();\n  const clickable = (_a = props.clickable) !== null && _a !== void 0 ? _a : !!props.onClick;\n  const showArrow = (_b = arrow !== null && arrow !== void 0 ? arrow : arrowIcon) !== null && _b !== void 0 ? _b : clickable;\n  const mergedArrowIcon = mergeProp(componentConfig.arrowIcon, arrow !== true ? arrow : null, arrowIcon !== true ? arrowIcon : null);\n  const content = React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, isNodeWithContent(props.prefix) && React.createElement(\"div\", {\n    className: `${classPrefix}-content-prefix`\n  }, props.prefix), React.createElement(\"div\", {\n    className: `${classPrefix}-content-main`\n  }, isNodeWithContent(props.title) && React.createElement(\"div\", {\n    className: `${classPrefix}-title`\n  }, props.title), props.children, isNodeWithContent(props.description) && React.createElement(\"div\", {\n    className: `${classPrefix}-description`\n  }, props.description)), isNodeWithContent(props.extra) && React.createElement(\"div\", {\n    className: `${classPrefix}-content-extra`\n  }, props.extra), showArrow && React.createElement(\"div\", {\n    className: `${classPrefix}-content-arrow`\n  }, mergedArrowIcon || React.createElement(RightOutline, null)));\n  return withNativeProps(props, React.createElement(clickable ? 'a' : 'div', {\n    className: classNames(`${classPrefix}`, clickable ? ['adm-plain-anchor'] : [], props.disabled && `${classPrefix}-disabled`),\n    onClick: props.disabled ? undefined : props.onClick\n  }, content));\n};", "map": {"version": 3, "names": ["RightOutline", "classNames", "React", "isNodeWithContent", "withNativeProps", "mergeProp", "useConfig", "classPrefix", "ListItem", "props", "_a", "_b", "arrow", "arrowIcon", "list", "componentConfig", "clickable", "onClick", "showArrow", "mergedArrowIcon", "content", "createElement", "className", "prefix", "title", "children", "description", "extra", "disabled", "undefined"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/list/list-item.js"], "sourcesContent": ["import { RightOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React from 'react';\nimport { isNodeWithContent } from '../../utils/is-node-with-content';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProp } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nconst classPrefix = `adm-list-item`;\nexport const ListItem = props => {\n  var _a, _b;\n  const {\n    arrow,\n    arrowIcon\n  } = props;\n  const {\n    list: componentConfig = {}\n  } = useConfig();\n  const clickable = (_a = props.clickable) !== null && _a !== void 0 ? _a : !!props.onClick;\n  const showArrow = (_b = arrow !== null && arrow !== void 0 ? arrow : arrowIcon) !== null && _b !== void 0 ? _b : clickable;\n  const mergedArrowIcon = mergeProp(componentConfig.arrowIcon, arrow !== true ? arrow : null, arrowIcon !== true ? arrowIcon : null);\n  const content = React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, isNodeWithContent(props.prefix) && React.createElement(\"div\", {\n    className: `${classPrefix}-content-prefix`\n  }, props.prefix), React.createElement(\"div\", {\n    className: `${classPrefix}-content-main`\n  }, isNodeWithContent(props.title) && React.createElement(\"div\", {\n    className: `${classPrefix}-title`\n  }, props.title), props.children, isNodeWithContent(props.description) && React.createElement(\"div\", {\n    className: `${classPrefix}-description`\n  }, props.description)), isNodeWithContent(props.extra) && React.createElement(\"div\", {\n    className: `${classPrefix}-content-extra`\n  }, props.extra), showArrow && React.createElement(\"div\", {\n    className: `${classPrefix}-content-arrow`\n  }, mergedArrowIcon || React.createElement(RightOutline, null)));\n  return withNativeProps(props, React.createElement(clickable ? 'a' : 'div', {\n    className: classNames(`${classPrefix}`, clickable ? ['adm-plain-anchor'] : [], props.disabled && `${classPrefix}-disabled`),\n    onClick: props.disabled ? undefined : props.onClick\n  }, content));\n};"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,MAAMC,WAAW,GAAG,eAAe;AACnC,OAAO,MAAMC,QAAQ,GAAGC,KAAK,IAAI;EAC/B,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAM;IACJC,KAAK;IACLC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAM;IACJK,IAAI,EAAEC,eAAe,GAAG,CAAC;EAC3B,CAAC,GAAGT,SAAS,CAAC,CAAC;EACf,MAAMU,SAAS,GAAG,CAACN,EAAE,GAAGD,KAAK,CAACO,SAAS,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAACD,KAAK,CAACQ,OAAO;EACzF,MAAMC,SAAS,GAAG,CAACP,EAAE,GAAGC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGC,SAAS,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGK,SAAS;EAC1H,MAAMG,eAAe,GAAGd,SAAS,CAACU,eAAe,CAACF,SAAS,EAAED,KAAK,KAAK,IAAI,GAAGA,KAAK,GAAG,IAAI,EAAEC,SAAS,KAAK,IAAI,GAAGA,SAAS,GAAG,IAAI,CAAC;EAClI,MAAMO,OAAO,GAAGlB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACzCC,SAAS,EAAE,GAAGf,WAAW;EAC3B,CAAC,EAAEJ,iBAAiB,CAACM,KAAK,CAACc,MAAM,CAAC,IAAIrB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAC/DC,SAAS,EAAE,GAAGf,WAAW;EAC3B,CAAC,EAAEE,KAAK,CAACc,MAAM,CAAC,EAAErB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAC3CC,SAAS,EAAE,GAAGf,WAAW;EAC3B,CAAC,EAAEJ,iBAAiB,CAACM,KAAK,CAACe,KAAK,CAAC,IAAItB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAC9DC,SAAS,EAAE,GAAGf,WAAW;EAC3B,CAAC,EAAEE,KAAK,CAACe,KAAK,CAAC,EAAEf,KAAK,CAACgB,QAAQ,EAAEtB,iBAAiB,CAACM,KAAK,CAACiB,WAAW,CAAC,IAAIxB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAClGC,SAAS,EAAE,GAAGf,WAAW;EAC3B,CAAC,EAAEE,KAAK,CAACiB,WAAW,CAAC,CAAC,EAAEvB,iBAAiB,CAACM,KAAK,CAACkB,KAAK,CAAC,IAAIzB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACnFC,SAAS,EAAE,GAAGf,WAAW;EAC3B,CAAC,EAAEE,KAAK,CAACkB,KAAK,CAAC,EAAET,SAAS,IAAIhB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAE,GAAGf,WAAW;EAC3B,CAAC,EAAEY,eAAe,IAAIjB,KAAK,CAACmB,aAAa,CAACrB,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;EAC/D,OAAOI,eAAe,CAACK,KAAK,EAAEP,KAAK,CAACmB,aAAa,CAACL,SAAS,GAAG,GAAG,GAAG,KAAK,EAAE;IACzEM,SAAS,EAAErB,UAAU,CAAC,GAAGM,WAAW,EAAE,EAAES,SAAS,GAAG,CAAC,kBAAkB,CAAC,GAAG,EAAE,EAAEP,KAAK,CAACmB,QAAQ,IAAI,GAAGrB,WAAW,WAAW,CAAC;IAC3HU,OAAO,EAAER,KAAK,CAACmB,QAAQ,GAAGC,SAAS,GAAGpB,KAAK,CAACQ;EAC9C,CAAC,EAAEG,OAAO,CAAC,CAAC;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}