{"ast": null, "code": "import classNames from 'classnames';\nimport React, { useState, useRef } from 'react';\nimport { useIsomorphicLayoutEffect, useUnmountedRef } from 'ahooks';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Mask from '../mask';\nimport { useLockScroll } from '../../utils/use-lock-scroll';\nimport { renderToContainer } from '../../utils/render-to-container';\nimport { useSpring, animated } from '@react-spring/web';\nimport { withStopPropagation } from '../../utils/with-stop-propagation';\nimport { ShouldRender } from '../../utils/should-render';\nimport { defaultPopupBaseProps } from './popup-base-props';\nimport { useInnerVisible } from '../../utils/use-inner-visible';\nimport { useConfig } from '../config-provider';\nimport { useDrag } from '@use-gesture/react';\nconst classPrefix = `adm-popup`;\nconst defaultProps = Object.assign(Object.assign({}, defaultPopupBaseProps), {\n  closeOnSwipe: false,\n  position: 'bottom'\n});\nexport const Popup = p => {\n  const {\n    locale,\n    popup: componentConfig = {}\n  } = useConfig();\n  const props = mergeProps(defaultProps, componentConfig, p);\n  const bodyCls = classNames(`${classPrefix}-body`, props.bodyClassName, `${classPrefix}-body-position-${props.position}`);\n  const [active, setActive] = useState(props.visible);\n  const ref = useRef(null);\n  useLockScroll(ref, props.disableBodyScroll && active ? 'strict' : false);\n  useIsomorphicLayoutEffect(() => {\n    if (props.visible) {\n      setActive(true);\n    }\n  }, [props.visible]);\n  const unmountedRef = useUnmountedRef();\n  const {\n    percent\n  } = useSpring({\n    percent: props.visible ? 0 : 100,\n    config: {\n      precision: 0.1,\n      mass: 0.4,\n      tension: 300,\n      friction: 30\n    },\n    onRest: () => {\n      var _a, _b;\n      if (unmountedRef.current) return;\n      setActive(props.visible);\n      if (props.visible) {\n        (_a = props.afterShow) === null || _a === void 0 ? void 0 : _a.call(props);\n      } else {\n        (_b = props.afterClose) === null || _b === void 0 ? void 0 : _b.call(props);\n      }\n    }\n  });\n  const bind = useDrag(({\n    swipe: [, swipeY]\n  }) => {\n    var _a;\n    if (!props.closeOnSwipe) return;\n    if (swipeY === 1 && props.position === 'bottom' || swipeY === -1 && props.position === 'top') {\n      (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n    }\n  }, {\n    axis: 'y',\n    enabled: ['top', 'bottom'].includes(props.position)\n  });\n  const maskVisible = useInnerVisible(active && props.visible);\n  const node = withStopPropagation(props.stopPropagation, withNativeProps(props, React.createElement(\"div\", Object.assign({\n    className: classPrefix,\n    onClick: props.onClick,\n    style: {\n      display: active ? undefined : 'none',\n      touchAction: ['top', 'bottom'].includes(props.position) ? 'none' : 'auto'\n    }\n  }, bind()), props.mask && React.createElement(Mask, {\n    visible: maskVisible,\n    forceRender: props.forceRender,\n    destroyOnClose: props.destroyOnClose,\n    onMaskClick: e => {\n      var _a, _b;\n      (_a = props.onMaskClick) === null || _a === void 0 ? void 0 : _a.call(props, e);\n      if (props.closeOnMaskClick) {\n        (_b = props.onClose) === null || _b === void 0 ? void 0 : _b.call(props);\n      }\n    },\n    className: props.maskClassName,\n    style: props.maskStyle,\n    disableBodyScroll: false,\n    stopPropagation: props.stopPropagation\n  }), React.createElement(animated.div, {\n    className: bodyCls,\n    style: Object.assign(Object.assign({}, props.bodyStyle), {\n      pointerEvents: percent.to(v => v === 0 ? 'unset' : 'none'),\n      transform: percent.to(v => {\n        if (props.position === 'bottom') {\n          return `translate(0, ${v}%)`;\n        }\n        if (props.position === 'top') {\n          return `translate(0, -${v}%)`;\n        }\n        if (props.position === 'left') {\n          return `translate(-${v}%, 0)`;\n        }\n        if (props.position === 'right') {\n          return `translate(${v}%, 0)`;\n        }\n        return 'none';\n      })\n    }),\n    ref: ref\n  }, props.showCloseButton && React.createElement(\"a\", {\n    className: classNames(`${classPrefix}-close-icon`, 'adm-plain-anchor'),\n    onClick: () => {\n      var _a;\n      (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n    },\n    role: 'button',\n    \"aria-label\": locale.common.close\n  }, props.closeIcon), props.children))));\n  return React.createElement(ShouldRender, {\n    active: active,\n    forceRender: props.forceRender,\n    destroyOnClose: props.destroyOnClose\n  }, renderToContainer(props.getContainer, node));\n};", "map": {"version": 3, "names": ["classNames", "React", "useState", "useRef", "useIsomorphicLayoutEffect", "useUnmountedRef", "withNativeProps", "mergeProps", "Mask", "useLockScroll", "renderToContainer", "useSpring", "animated", "withStopPropagation", "ShouldRender", "defaultPopupBaseProps", "useInnerVisible", "useConfig", "useDrag", "classPrefix", "defaultProps", "Object", "assign", "closeOnSwipe", "position", "Popup", "p", "locale", "popup", "componentConfig", "props", "bodyCls", "bodyClassName", "active", "setActive", "visible", "ref", "disableBodyScroll", "unmountedRef", "percent", "config", "precision", "mass", "tension", "friction", "onRest", "_a", "_b", "current", "afterShow", "call", "afterClose", "bind", "swipe", "swipeY", "onClose", "axis", "enabled", "includes", "maskVisible", "node", "stopPropagation", "createElement", "className", "onClick", "style", "display", "undefined", "touchAction", "mask", "forceRender", "destroyOnClose", "onMaskClick", "e", "closeOnMaskClick", "maskClassName", "maskStyle", "div", "bodyStyle", "pointerEvents", "to", "v", "transform", "showCloseButton", "role", "common", "close", "closeIcon", "children", "getContainer"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/popup/popup.js"], "sourcesContent": ["import classNames from 'classnames';\nimport React, { useState, useRef } from 'react';\nimport { useIsomorphicLayoutEffect, useUnmountedRef } from 'ahooks';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Mask from '../mask';\nimport { useLockScroll } from '../../utils/use-lock-scroll';\nimport { renderToContainer } from '../../utils/render-to-container';\nimport { useSpring, animated } from '@react-spring/web';\nimport { withStopPropagation } from '../../utils/with-stop-propagation';\nimport { ShouldRender } from '../../utils/should-render';\nimport { defaultPopupBaseProps } from './popup-base-props';\nimport { useInnerVisible } from '../../utils/use-inner-visible';\nimport { useConfig } from '../config-provider';\nimport { useDrag } from '@use-gesture/react';\nconst classPrefix = `adm-popup`;\nconst defaultProps = Object.assign(Object.assign({}, defaultPopupBaseProps), {\n  closeOnSwipe: false,\n  position: 'bottom'\n});\nexport const Popup = p => {\n  const {\n    locale,\n    popup: componentConfig = {}\n  } = useConfig();\n  const props = mergeProps(defaultProps, componentConfig, p);\n  const bodyCls = classNames(`${classPrefix}-body`, props.bodyClassName, `${classPrefix}-body-position-${props.position}`);\n  const [active, setActive] = useState(props.visible);\n  const ref = useRef(null);\n  useLockScroll(ref, props.disableBodyScroll && active ? 'strict' : false);\n  useIsomorphicLayoutEffect(() => {\n    if (props.visible) {\n      setActive(true);\n    }\n  }, [props.visible]);\n  const unmountedRef = useUnmountedRef();\n  const {\n    percent\n  } = useSpring({\n    percent: props.visible ? 0 : 100,\n    config: {\n      precision: 0.1,\n      mass: 0.4,\n      tension: 300,\n      friction: 30\n    },\n    onRest: () => {\n      var _a, _b;\n      if (unmountedRef.current) return;\n      setActive(props.visible);\n      if (props.visible) {\n        (_a = props.afterShow) === null || _a === void 0 ? void 0 : _a.call(props);\n      } else {\n        (_b = props.afterClose) === null || _b === void 0 ? void 0 : _b.call(props);\n      }\n    }\n  });\n  const bind = useDrag(({\n    swipe: [, swipeY]\n  }) => {\n    var _a;\n    if (!props.closeOnSwipe) return;\n    if (swipeY === 1 && props.position === 'bottom' || swipeY === -1 && props.position === 'top') {\n      (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n    }\n  }, {\n    axis: 'y',\n    enabled: ['top', 'bottom'].includes(props.position)\n  });\n  const maskVisible = useInnerVisible(active && props.visible);\n  const node = withStopPropagation(props.stopPropagation, withNativeProps(props, React.createElement(\"div\", Object.assign({\n    className: classPrefix,\n    onClick: props.onClick,\n    style: {\n      display: active ? undefined : 'none',\n      touchAction: ['top', 'bottom'].includes(props.position) ? 'none' : 'auto'\n    }\n  }, bind()), props.mask && React.createElement(Mask, {\n    visible: maskVisible,\n    forceRender: props.forceRender,\n    destroyOnClose: props.destroyOnClose,\n    onMaskClick: e => {\n      var _a, _b;\n      (_a = props.onMaskClick) === null || _a === void 0 ? void 0 : _a.call(props, e);\n      if (props.closeOnMaskClick) {\n        (_b = props.onClose) === null || _b === void 0 ? void 0 : _b.call(props);\n      }\n    },\n    className: props.maskClassName,\n    style: props.maskStyle,\n    disableBodyScroll: false,\n    stopPropagation: props.stopPropagation\n  }), React.createElement(animated.div, {\n    className: bodyCls,\n    style: Object.assign(Object.assign({}, props.bodyStyle), {\n      pointerEvents: percent.to(v => v === 0 ? 'unset' : 'none'),\n      transform: percent.to(v => {\n        if (props.position === 'bottom') {\n          return `translate(0, ${v}%)`;\n        }\n        if (props.position === 'top') {\n          return `translate(0, -${v}%)`;\n        }\n        if (props.position === 'left') {\n          return `translate(-${v}%, 0)`;\n        }\n        if (props.position === 'right') {\n          return `translate(${v}%, 0)`;\n        }\n        return 'none';\n      })\n    }),\n    ref: ref\n  }, props.showCloseButton && React.createElement(\"a\", {\n    className: classNames(`${classPrefix}-close-icon`, 'adm-plain-anchor'),\n    onClick: () => {\n      var _a;\n      (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n    },\n    role: 'button',\n    \"aria-label\": locale.common.close\n  }, props.closeIcon), props.children))));\n  return React.createElement(ShouldRender, {\n    active: active,\n    forceRender: props.forceRender,\n    destroyOnClose: props.destroyOnClose\n  }, renderToContainer(props.getContainer, node));\n};"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,yBAAyB,EAAEC,eAAe,QAAQ,QAAQ;AACnE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,IAAI,MAAM,SAAS;AAC1B,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,SAAS,EAAEC,QAAQ,QAAQ,mBAAmB;AACvD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,qBAAqB,QAAQ,oBAAoB;AAC1D,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,MAAMC,WAAW,GAAG,WAAW;AAC/B,MAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,qBAAqB,CAAC,EAAE;EAC3EQ,YAAY,EAAE,KAAK;EACnBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,OAAO,MAAMC,KAAK,GAAGC,CAAC,IAAI;EACxB,MAAM;IACJC,MAAM;IACNC,KAAK,EAAEC,eAAe,GAAG,CAAC;EAC5B,CAAC,GAAGZ,SAAS,CAAC,CAAC;EACf,MAAMa,KAAK,GAAGvB,UAAU,CAACa,YAAY,EAAES,eAAe,EAAEH,CAAC,CAAC;EAC1D,MAAMK,OAAO,GAAG/B,UAAU,CAAC,GAAGmB,WAAW,OAAO,EAAEW,KAAK,CAACE,aAAa,EAAE,GAAGb,WAAW,kBAAkBW,KAAK,CAACN,QAAQ,EAAE,CAAC;EACxH,MAAM,CAACS,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAC4B,KAAK,CAACK,OAAO,CAAC;EACnD,MAAMC,GAAG,GAAGjC,MAAM,CAAC,IAAI,CAAC;EACxBM,aAAa,CAAC2B,GAAG,EAAEN,KAAK,CAACO,iBAAiB,IAAIJ,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;EACxE7B,yBAAyB,CAAC,MAAM;IAC9B,IAAI0B,KAAK,CAACK,OAAO,EAAE;MACjBD,SAAS,CAAC,IAAI,CAAC;IACjB;EACF,CAAC,EAAE,CAACJ,KAAK,CAACK,OAAO,CAAC,CAAC;EACnB,MAAMG,YAAY,GAAGjC,eAAe,CAAC,CAAC;EACtC,MAAM;IACJkC;EACF,CAAC,GAAG5B,SAAS,CAAC;IACZ4B,OAAO,EAAET,KAAK,CAACK,OAAO,GAAG,CAAC,GAAG,GAAG;IAChCK,MAAM,EAAE;MACNC,SAAS,EAAE,GAAG;MACdC,IAAI,EAAE,GAAG;MACTC,OAAO,EAAE,GAAG;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,MAAM,EAAEA,CAAA,KAAM;MACZ,IAAIC,EAAE,EAAEC,EAAE;MACV,IAAIT,YAAY,CAACU,OAAO,EAAE;MAC1Bd,SAAS,CAACJ,KAAK,CAACK,OAAO,CAAC;MACxB,IAAIL,KAAK,CAACK,OAAO,EAAE;QACjB,CAACW,EAAE,GAAGhB,KAAK,CAACmB,SAAS,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,IAAI,CAACpB,KAAK,CAAC;MAC5E,CAAC,MAAM;QACL,CAACiB,EAAE,GAAGjB,KAAK,CAACqB,UAAU,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACpB,KAAK,CAAC;MAC7E;IACF;EACF,CAAC,CAAC;EACF,MAAMsB,IAAI,GAAGlC,OAAO,CAAC,CAAC;IACpBmC,KAAK,EAAE,GAAGC,MAAM;EAClB,CAAC,KAAK;IACJ,IAAIR,EAAE;IACN,IAAI,CAAChB,KAAK,CAACP,YAAY,EAAE;IACzB,IAAI+B,MAAM,KAAK,CAAC,IAAIxB,KAAK,CAACN,QAAQ,KAAK,QAAQ,IAAI8B,MAAM,KAAK,CAAC,CAAC,IAAIxB,KAAK,CAACN,QAAQ,KAAK,KAAK,EAAE;MAC5F,CAACsB,EAAE,GAAGhB,KAAK,CAACyB,OAAO,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,IAAI,CAACpB,KAAK,CAAC;IAC1E;EACF,CAAC,EAAE;IACD0B,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAAC5B,KAAK,CAACN,QAAQ;EACpD,CAAC,CAAC;EACF,MAAMmC,WAAW,GAAG3C,eAAe,CAACiB,MAAM,IAAIH,KAAK,CAACK,OAAO,CAAC;EAC5D,MAAMyB,IAAI,GAAG/C,mBAAmB,CAACiB,KAAK,CAAC+B,eAAe,EAAEvD,eAAe,CAACwB,KAAK,EAAE7B,KAAK,CAAC6D,aAAa,CAAC,KAAK,EAAEzC,MAAM,CAACC,MAAM,CAAC;IACtHyC,SAAS,EAAE5C,WAAW;IACtB6C,OAAO,EAAElC,KAAK,CAACkC,OAAO;IACtBC,KAAK,EAAE;MACLC,OAAO,EAAEjC,MAAM,GAAGkC,SAAS,GAAG,MAAM;MACpCC,WAAW,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAACV,QAAQ,CAAC5B,KAAK,CAACN,QAAQ,CAAC,GAAG,MAAM,GAAG;IACrE;EACF,CAAC,EAAE4B,IAAI,CAAC,CAAC,CAAC,EAAEtB,KAAK,CAACuC,IAAI,IAAIpE,KAAK,CAAC6D,aAAa,CAACtD,IAAI,EAAE;IAClD2B,OAAO,EAAEwB,WAAW;IACpBW,WAAW,EAAExC,KAAK,CAACwC,WAAW;IAC9BC,cAAc,EAAEzC,KAAK,CAACyC,cAAc;IACpCC,WAAW,EAAEC,CAAC,IAAI;MAChB,IAAI3B,EAAE,EAAEC,EAAE;MACV,CAACD,EAAE,GAAGhB,KAAK,CAAC0C,WAAW,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,IAAI,CAACpB,KAAK,EAAE2C,CAAC,CAAC;MAC/E,IAAI3C,KAAK,CAAC4C,gBAAgB,EAAE;QAC1B,CAAC3B,EAAE,GAAGjB,KAAK,CAACyB,OAAO,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACpB,KAAK,CAAC;MAC1E;IACF,CAAC;IACDiC,SAAS,EAAEjC,KAAK,CAAC6C,aAAa;IAC9BV,KAAK,EAAEnC,KAAK,CAAC8C,SAAS;IACtBvC,iBAAiB,EAAE,KAAK;IACxBwB,eAAe,EAAE/B,KAAK,CAAC+B;EACzB,CAAC,CAAC,EAAE5D,KAAK,CAAC6D,aAAa,CAAClD,QAAQ,CAACiE,GAAG,EAAE;IACpCd,SAAS,EAAEhC,OAAO;IAClBkC,KAAK,EAAE5C,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEQ,KAAK,CAACgD,SAAS,CAAC,EAAE;MACvDC,aAAa,EAAExC,OAAO,CAACyC,EAAE,CAACC,CAAC,IAAIA,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,MAAM,CAAC;MAC1DC,SAAS,EAAE3C,OAAO,CAACyC,EAAE,CAACC,CAAC,IAAI;QACzB,IAAInD,KAAK,CAACN,QAAQ,KAAK,QAAQ,EAAE;UAC/B,OAAO,gBAAgByD,CAAC,IAAI;QAC9B;QACA,IAAInD,KAAK,CAACN,QAAQ,KAAK,KAAK,EAAE;UAC5B,OAAO,iBAAiByD,CAAC,IAAI;QAC/B;QACA,IAAInD,KAAK,CAACN,QAAQ,KAAK,MAAM,EAAE;UAC7B,OAAO,cAAcyD,CAAC,OAAO;QAC/B;QACA,IAAInD,KAAK,CAACN,QAAQ,KAAK,OAAO,EAAE;UAC9B,OAAO,aAAayD,CAAC,OAAO;QAC9B;QACA,OAAO,MAAM;MACf,CAAC;IACH,CAAC,CAAC;IACF7C,GAAG,EAAEA;EACP,CAAC,EAAEN,KAAK,CAACqD,eAAe,IAAIlF,KAAK,CAAC6D,aAAa,CAAC,GAAG,EAAE;IACnDC,SAAS,EAAE/D,UAAU,CAAC,GAAGmB,WAAW,aAAa,EAAE,kBAAkB,CAAC;IACtE6C,OAAO,EAAEA,CAAA,KAAM;MACb,IAAIlB,EAAE;MACN,CAACA,EAAE,GAAGhB,KAAK,CAACyB,OAAO,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,IAAI,CAACpB,KAAK,CAAC;IAC1E,CAAC;IACDsD,IAAI,EAAE,QAAQ;IACd,YAAY,EAAEzD,MAAM,CAAC0D,MAAM,CAACC;EAC9B,CAAC,EAAExD,KAAK,CAACyD,SAAS,CAAC,EAAEzD,KAAK,CAAC0D,QAAQ,CAAC,CAAC,CAAC,CAAC;EACvC,OAAOvF,KAAK,CAAC6D,aAAa,CAAChD,YAAY,EAAE;IACvCmB,MAAM,EAAEA,MAAM;IACdqC,WAAW,EAAExC,KAAK,CAACwC,WAAW;IAC9BC,cAAc,EAAEzC,KAAK,CAACyC;EACxB,CAAC,EAAE7D,iBAAiB,CAACoB,KAAK,CAAC2D,YAAY,EAAE7B,IAAI,CAAC,CAAC;AACjD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}