{"ast": null, "code": "import { CheckOutline, CloseOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { useMemo } from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport AutoCenter from '../auto-center';\nimport Mask from '../mask';\nimport SpinLoading from '../spin-loading';\nconst classPrefix = `adm-toast`;\nconst defaultProps = {\n  maskClickable: true,\n  stopPropagation: ['click']\n};\nexport const InternalToast = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    maskClickable,\n    content,\n    icon,\n    position\n  } = props;\n  const iconElement = useMemo(() => {\n    if (icon === null || icon === undefined) return null;\n    switch (icon) {\n      case 'success':\n        return React.createElement(CheckOutline, {\n          className: `${classPrefix}-icon-success`\n        });\n      case 'fail':\n        return React.createElement(CloseOutline, {\n          className: `${classPrefix}-icon-fail`\n        });\n      case 'loading':\n        return React.createElement(SpinLoading, {\n          color: 'white',\n          className: `${classPrefix}-loading`\n        });\n      default:\n        return icon;\n    }\n  }, [icon]);\n  const top = useMemo(() => {\n    switch (position) {\n      case 'top':\n        return '20%';\n      case 'bottom':\n        return '80%';\n      default:\n        return '50%';\n    }\n  }, [position]);\n  return React.createElement(Mask, {\n    visible: props.visible,\n    destroyOnClose: true,\n    opacity: 0,\n    disableBodyScroll: !maskClickable,\n    getContainer: props.getContainer,\n    afterClose: props.afterClose,\n    style: Object.assign({\n      pointerEvents: maskClickable ? 'none' : 'auto'\n    }, props.maskStyle),\n    className: classNames(`${classPrefix}-mask`, props.maskClassName),\n    stopPropagation: props.stopPropagation\n  }, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-wrap`)\n  }, React.createElement(\"div\", {\n    style: {\n      top\n    },\n    className: classNames(`${classPrefix}-main`, icon ? `${classPrefix}-main-icon` : `${classPrefix}-main-text`)\n  }, iconElement && React.createElement(\"div\", {\n    className: `${classPrefix}-icon`\n  }, iconElement), React.createElement(AutoCenter, null, content))));\n};", "map": {"version": 3, "names": ["CheckOutline", "CloseOutline", "classNames", "React", "useMemo", "mergeProps", "AutoCenter", "Mask", "SpinLoading", "classPrefix", "defaultProps", "maskClickable", "stopPropagation", "InternalToast", "p", "props", "content", "icon", "position", "iconElement", "undefined", "createElement", "className", "color", "top", "visible", "destroyOnClose", "opacity", "disableBodyScroll", "getContainer", "afterClose", "style", "Object", "assign", "pointerEvents", "maskStyle", "maskClassName"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/toast/toast.js"], "sourcesContent": ["import { CheckOutline, CloseOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { useMemo } from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport AutoCenter from '../auto-center';\nimport Mask from '../mask';\nimport SpinLoading from '../spin-loading';\nconst classPrefix = `adm-toast`;\nconst defaultProps = {\n  maskClickable: true,\n  stopPropagation: ['click']\n};\nexport const InternalToast = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    maskClickable,\n    content,\n    icon,\n    position\n  } = props;\n  const iconElement = useMemo(() => {\n    if (icon === null || icon === undefined) return null;\n    switch (icon) {\n      case 'success':\n        return React.createElement(CheckOutline, {\n          className: `${classPrefix}-icon-success`\n        });\n      case 'fail':\n        return React.createElement(CloseOutline, {\n          className: `${classPrefix}-icon-fail`\n        });\n      case 'loading':\n        return React.createElement(SpinLoading, {\n          color: 'white',\n          className: `${classPrefix}-loading`\n        });\n      default:\n        return icon;\n    }\n  }, [icon]);\n  const top = useMemo(() => {\n    switch (position) {\n      case 'top':\n        return '20%';\n      case 'bottom':\n        return '80%';\n      default:\n        return '50%';\n    }\n  }, [position]);\n  return React.createElement(Mask, {\n    visible: props.visible,\n    destroyOnClose: true,\n    opacity: 0,\n    disableBodyScroll: !maskClickable,\n    getContainer: props.getContainer,\n    afterClose: props.afterClose,\n    style: Object.assign({\n      pointerEvents: maskClickable ? 'none' : 'auto'\n    }, props.maskStyle),\n    className: classNames(`${classPrefix}-mask`, props.maskClassName),\n    stopPropagation: props.stopPropagation\n  }, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-wrap`)\n  }, React.createElement(\"div\", {\n    style: {\n      top\n    },\n    className: classNames(`${classPrefix}-main`, icon ? `${classPrefix}-main-icon` : `${classPrefix}-main-text`)\n  }, iconElement && React.createElement(\"div\", {\n    className: `${classPrefix}-icon`\n  }, iconElement), React.createElement(AutoCenter, null, content))));\n};"], "mappings": "AAAA,SAASA,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,gBAAgB;AACvC,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,WAAW,MAAM,iBAAiB;AACzC,MAAMC,WAAW,GAAG,WAAW;AAC/B,MAAMC,YAAY,GAAG;EACnBC,aAAa,EAAE,IAAI;EACnBC,eAAe,EAAE,CAAC,OAAO;AAC3B,CAAC;AACD,OAAO,MAAMC,aAAa,GAAGC,CAAC,IAAI;EAChC,MAAMC,KAAK,GAAGV,UAAU,CAACK,YAAY,EAAEI,CAAC,CAAC;EACzC,MAAM;IACJH,aAAa;IACbK,OAAO;IACPC,IAAI;IACJC;EACF,CAAC,GAAGH,KAAK;EACT,MAAMI,WAAW,GAAGf,OAAO,CAAC,MAAM;IAChC,IAAIa,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKG,SAAS,EAAE,OAAO,IAAI;IACpD,QAAQH,IAAI;MACV,KAAK,SAAS;QACZ,OAAOd,KAAK,CAACkB,aAAa,CAACrB,YAAY,EAAE;UACvCsB,SAAS,EAAE,GAAGb,WAAW;QAC3B,CAAC,CAAC;MACJ,KAAK,MAAM;QACT,OAAON,KAAK,CAACkB,aAAa,CAACpB,YAAY,EAAE;UACvCqB,SAAS,EAAE,GAAGb,WAAW;QAC3B,CAAC,CAAC;MACJ,KAAK,SAAS;QACZ,OAAON,KAAK,CAACkB,aAAa,CAACb,WAAW,EAAE;UACtCe,KAAK,EAAE,OAAO;UACdD,SAAS,EAAE,GAAGb,WAAW;QAC3B,CAAC,CAAC;MACJ;QACE,OAAOQ,IAAI;IACf;EACF,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EACV,MAAMO,GAAG,GAAGpB,OAAO,CAAC,MAAM;IACxB,QAAQc,QAAQ;MACd,KAAK,KAAK;QACR,OAAO,KAAK;MACd,KAAK,QAAQ;QACX,OAAO,KAAK;MACd;QACE,OAAO,KAAK;IAChB;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACd,OAAOf,KAAK,CAACkB,aAAa,CAACd,IAAI,EAAE;IAC/BkB,OAAO,EAAEV,KAAK,CAACU,OAAO;IACtBC,cAAc,EAAE,IAAI;IACpBC,OAAO,EAAE,CAAC;IACVC,iBAAiB,EAAE,CAACjB,aAAa;IACjCkB,YAAY,EAAEd,KAAK,CAACc,YAAY;IAChCC,UAAU,EAAEf,KAAK,CAACe,UAAU;IAC5BC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;MACnBC,aAAa,EAAEvB,aAAa,GAAG,MAAM,GAAG;IAC1C,CAAC,EAAEI,KAAK,CAACoB,SAAS,CAAC;IACnBb,SAAS,EAAEpB,UAAU,CAAC,GAAGO,WAAW,OAAO,EAAEM,KAAK,CAACqB,aAAa,CAAC;IACjExB,eAAe,EAAEG,KAAK,CAACH;EACzB,CAAC,EAAET,KAAK,CAACkB,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAEpB,UAAU,CAAC,GAAGO,WAAW,OAAO;EAC7C,CAAC,EAAEN,KAAK,CAACkB,aAAa,CAAC,KAAK,EAAE;IAC5BU,KAAK,EAAE;MACLP;IACF,CAAC;IACDF,SAAS,EAAEpB,UAAU,CAAC,GAAGO,WAAW,OAAO,EAAEQ,IAAI,GAAG,GAAGR,WAAW,YAAY,GAAG,GAAGA,WAAW,YAAY;EAC7G,CAAC,EAAEU,WAAW,IAAIhB,KAAK,CAACkB,aAAa,CAAC,KAAK,EAAE;IAC3CC,SAAS,EAAE,GAAGb,WAAW;EAC3B,CAAC,EAAEU,WAAW,CAAC,EAAEhB,KAAK,CAACkB,aAAa,CAACf,UAAU,EAAE,IAAI,EAAEU,OAAO,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}