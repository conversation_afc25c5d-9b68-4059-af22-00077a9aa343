{"ast": null, "code": "const typeTemplate = '${label}不是一个有效的${type}';\nconst zhCN = {\n  locale: 'zh-CH',\n  common: {\n    confirm: '确定',\n    cancel: '取消',\n    loading: '加载中',\n    close: '关闭'\n  },\n  Calendar: {\n    title: '日期选择',\n    confirm: '确认',\n    start: '开始',\n    end: '结束',\n    today: '今日',\n    markItems: ['一', '二', '三', '四', '五', '六', '日'],\n    yearAndMonth: '${year}年${month}月'\n  },\n  Cascader: {\n    placeholder: '请选择'\n  },\n  Dialog: {\n    ok: '我知道了'\n  },\n  DatePicker: {\n    tillNow: '至今'\n  },\n  ErrorBlock: {\n    default: {\n      title: '页面遇到一些小问题',\n      description: '待会来试试'\n    },\n    busy: {\n      title: '前方拥堵',\n      description: '刷新试试'\n    },\n    disconnected: {\n      title: '网络有点忙',\n      description: '动动手指帮忙修复'\n    },\n    empty: {\n      title: '没有找到你需要的东西',\n      description: '找找其他的吧'\n    }\n  },\n  Form: {\n    required: '必填',\n    optional: '选填',\n    defaultValidateMessages: {\n      default: '字段验证错误${label}',\n      required: '请输入${label}',\n      enum: '${label}必须是其中一个[${enum}]',\n      whitespace: '${label}不能为空字符',\n      date: {\n        format: '${label}日期格式无效',\n        parse: '${label}不能转换为日期',\n        invalid: '${label}是一个无效日期'\n      },\n      types: {\n        string: typeTemplate,\n        method: typeTemplate,\n        array: typeTemplate,\n        object: typeTemplate,\n        number: typeTemplate,\n        date: typeTemplate,\n        boolean: typeTemplate,\n        integer: typeTemplate,\n        float: typeTemplate,\n        regexp: typeTemplate,\n        email: typeTemplate,\n        url: typeTemplate,\n        hex: typeTemplate\n      },\n      string: {\n        len: '${label}须为${len}个字符',\n        min: '${label}最少${min}个字符',\n        max: '${label}最多${max}个字符',\n        range: '${label}须在${min}-${max}字符之间'\n      },\n      number: {\n        len: '${label}必须等于${len}',\n        min: '${label}最小值为${min}',\n        max: '${label}最大值为${max}',\n        range: '${label}须在${min}-${max}之间'\n      },\n      array: {\n        len: '须为${len}个${label}',\n        min: '最少${min}个${label}',\n        max: '最多${max}个${label}',\n        range: '${label}数量须在${min}-${max}之间'\n      },\n      pattern: {\n        mismatch: '${label}与模式不匹配${pattern}'\n      }\n    }\n  },\n  ImageUploader: {\n    uploading: '上传中...',\n    upload: '上传'\n  },\n  InfiniteScroll: {\n    noMore: '没有更多了',\n    failedToLoad: '加载失败',\n    retry: '重新加载'\n  },\n  Input: {\n    clear: '清除'\n  },\n  Mask: {\n    name: '背景蒙层'\n  },\n  Modal: {\n    ok: '我知道了'\n  },\n  PasscodeInput: {\n    name: '密码输入框'\n  },\n  PullToRefresh: {\n    pulling: '下拉刷新',\n    canRelease: '释放立即刷新',\n    complete: '刷新成功'\n  },\n  SearchBar: {\n    name: '搜索框'\n  },\n  Slider: {\n    name: '滑动输入条'\n  },\n  Stepper: {\n    decrease: '减少',\n    increase: '增加'\n  },\n  Switch: {\n    name: '开关'\n  },\n  Selector: {\n    name: '选择组'\n  }\n};\nexport default zhCN;", "map": {"version": 3, "names": ["typeTemplate", "zhCN", "locale", "common", "confirm", "cancel", "loading", "close", "Calendar", "title", "start", "end", "today", "markItems", "yearAndMonth", "<PERSON>r", "placeholder", "Dialog", "ok", "DatePicker", "tillNow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default", "description", "busy", "disconnected", "empty", "Form", "required", "optional", "defaultValidateMessages", "enum", "whitespace", "date", "format", "parse", "invalid", "types", "string", "method", "array", "object", "number", "boolean", "integer", "float", "regexp", "email", "url", "hex", "len", "min", "max", "range", "pattern", "mismatch", "ImageUploader", "uploading", "upload", "InfiniteScroll", "noMore", "failedToLoad", "retry", "Input", "clear", "Mask", "name", "Modal", "PasscodeInput", "PullToRefresh", "pulling", "canRelease", "complete", "SearchBar", "Slide<PERSON>", "Stepper", "decrease", "increase", "Switch", "Selector"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/locales/zh-CN.js"], "sourcesContent": ["const typeTemplate = '${label}不是一个有效的${type}';\nconst zhCN = {\n  locale: 'zh-CH',\n  common: {\n    confirm: '确定',\n    cancel: '取消',\n    loading: '加载中',\n    close: '关闭'\n  },\n  Calendar: {\n    title: '日期选择',\n    confirm: '确认',\n    start: '开始',\n    end: '结束',\n    today: '今日',\n    markItems: ['一', '二', '三', '四', '五', '六', '日'],\n    yearAndMonth: '${year}年${month}月'\n  },\n  Cascader: {\n    placeholder: '请选择'\n  },\n  Dialog: {\n    ok: '我知道了'\n  },\n  DatePicker: {\n    tillNow: '至今'\n  },\n  ErrorBlock: {\n    default: {\n      title: '页面遇到一些小问题',\n      description: '待会来试试'\n    },\n    busy: {\n      title: '前方拥堵',\n      description: '刷新试试'\n    },\n    disconnected: {\n      title: '网络有点忙',\n      description: '动动手指帮忙修复'\n    },\n    empty: {\n      title: '没有找到你需要的东西',\n      description: '找找其他的吧'\n    }\n  },\n  Form: {\n    required: '必填',\n    optional: '选填',\n    defaultValidateMessages: {\n      default: '字段验证错误${label}',\n      required: '请输入${label}',\n      enum: '${label}必须是其中一个[${enum}]',\n      whitespace: '${label}不能为空字符',\n      date: {\n        format: '${label}日期格式无效',\n        parse: '${label}不能转换为日期',\n        invalid: '${label}是一个无效日期'\n      },\n      types: {\n        string: typeTemplate,\n        method: typeTemplate,\n        array: typeTemplate,\n        object: typeTemplate,\n        number: typeTemplate,\n        date: typeTemplate,\n        boolean: typeTemplate,\n        integer: typeTemplate,\n        float: typeTemplate,\n        regexp: typeTemplate,\n        email: typeTemplate,\n        url: typeTemplate,\n        hex: typeTemplate\n      },\n      string: {\n        len: '${label}须为${len}个字符',\n        min: '${label}最少${min}个字符',\n        max: '${label}最多${max}个字符',\n        range: '${label}须在${min}-${max}字符之间'\n      },\n      number: {\n        len: '${label}必须等于${len}',\n        min: '${label}最小值为${min}',\n        max: '${label}最大值为${max}',\n        range: '${label}须在${min}-${max}之间'\n      },\n      array: {\n        len: '须为${len}个${label}',\n        min: '最少${min}个${label}',\n        max: '最多${max}个${label}',\n        range: '${label}数量须在${min}-${max}之间'\n      },\n      pattern: {\n        mismatch: '${label}与模式不匹配${pattern}'\n      }\n    }\n  },\n  ImageUploader: {\n    uploading: '上传中...',\n    upload: '上传'\n  },\n  InfiniteScroll: {\n    noMore: '没有更多了',\n    failedToLoad: '加载失败',\n    retry: '重新加载'\n  },\n  Input: {\n    clear: '清除'\n  },\n  Mask: {\n    name: '背景蒙层'\n  },\n  Modal: {\n    ok: '我知道了'\n  },\n  PasscodeInput: {\n    name: '密码输入框'\n  },\n  PullToRefresh: {\n    pulling: '下拉刷新',\n    canRelease: '释放立即刷新',\n    complete: '刷新成功'\n  },\n  SearchBar: {\n    name: '搜索框'\n  },\n  Slider: {\n    name: '滑动输入条'\n  },\n  Stepper: {\n    decrease: '减少',\n    increase: '增加'\n  },\n  Switch: {\n    name: '开关'\n  },\n  Selector: {\n    name: '选择组'\n  }\n};\nexport default zhCN;"], "mappings": "AAAA,MAAMA,YAAY,GAAG,wBAAwB;AAC7C,MAAMC,IAAI,GAAG;EACXC,MAAM,EAAE,OAAO;EACfC,MAAM,EAAE;IACNC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRC,KAAK,EAAE,MAAM;IACbL,OAAO,EAAE,IAAI;IACbM,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC9CC,YAAY,EAAE;EAChB,CAAC;EACDC,QAAQ,EAAE;IACRC,WAAW,EAAE;EACf,CAAC;EACDC,MAAM,EAAE;IACNC,EAAE,EAAE;EACN,CAAC;EACDC,UAAU,EAAE;IACVC,OAAO,EAAE;EACX,CAAC;EACDC,UAAU,EAAE;IACVC,OAAO,EAAE;MACPb,KAAK,EAAE,WAAW;MAClBc,WAAW,EAAE;IACf,CAAC;IACDC,IAAI,EAAE;MACJf,KAAK,EAAE,MAAM;MACbc,WAAW,EAAE;IACf,CAAC;IACDE,YAAY,EAAE;MACZhB,KAAK,EAAE,OAAO;MACdc,WAAW,EAAE;IACf,CAAC;IACDG,KAAK,EAAE;MACLjB,KAAK,EAAE,YAAY;MACnBc,WAAW,EAAE;IACf;EACF,CAAC;EACDI,IAAI,EAAE;IACJC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,uBAAuB,EAAE;MACvBR,OAAO,EAAE,gBAAgB;MACzBM,QAAQ,EAAE,aAAa;MACvBG,IAAI,EAAE,0BAA0B;MAChCC,UAAU,EAAE,gBAAgB;MAC5BC,IAAI,EAAE;QACJC,MAAM,EAAE,gBAAgB;QACxBC,KAAK,EAAE,iBAAiB;QACxBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAEtC,YAAY;QACpBuC,MAAM,EAAEvC,YAAY;QACpBwC,KAAK,EAAExC,YAAY;QACnByC,MAAM,EAAEzC,YAAY;QACpB0C,MAAM,EAAE1C,YAAY;QACpBiC,IAAI,EAAEjC,YAAY;QAClB2C,OAAO,EAAE3C,YAAY;QACrB4C,OAAO,EAAE5C,YAAY;QACrB6C,KAAK,EAAE7C,YAAY;QACnB8C,MAAM,EAAE9C,YAAY;QACpB+C,KAAK,EAAE/C,YAAY;QACnBgD,GAAG,EAAEhD,YAAY;QACjBiD,GAAG,EAAEjD;MACP,CAAC;MACDsC,MAAM,EAAE;QACNY,GAAG,EAAE,qBAAqB;QAC1BC,GAAG,EAAE,qBAAqB;QAC1BC,GAAG,EAAE,qBAAqB;QAC1BC,KAAK,EAAE;MACT,CAAC;MACDX,MAAM,EAAE;QACNQ,GAAG,EAAE,oBAAoB;QACzBC,GAAG,EAAE,oBAAoB;QACzBC,GAAG,EAAE,oBAAoB;QACzBC,KAAK,EAAE;MACT,CAAC;MACDb,KAAK,EAAE;QACLU,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,mBAAmB;QACxBC,KAAK,EAAE;MACT,CAAC;MACDC,OAAO,EAAE;QACPC,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;EACDC,aAAa,EAAE;IACbC,SAAS,EAAE,QAAQ;IACnBC,MAAM,EAAE;EACV,CAAC;EACDC,cAAc,EAAE;IACdC,MAAM,EAAE,OAAO;IACfC,YAAY,EAAE,MAAM;IACpBC,KAAK,EAAE;EACT,CAAC;EACDC,KAAK,EAAE;IACLC,KAAK,EAAE;EACT,CAAC;EACDC,IAAI,EAAE;IACJC,IAAI,EAAE;EACR,CAAC;EACDC,KAAK,EAAE;IACLjD,EAAE,EAAE;EACN,CAAC;EACDkD,aAAa,EAAE;IACbF,IAAI,EAAE;EACR,CAAC;EACDG,aAAa,EAAE;IACbC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE;EACZ,CAAC;EACDC,SAAS,EAAE;IACTP,IAAI,EAAE;EACR,CAAC;EACDQ,MAAM,EAAE;IACNR,IAAI,EAAE;EACR,CAAC;EACDS,OAAO,EAAE;IACPC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC;EACDC,MAAM,EAAE;IACNZ,IAAI,EAAE;EACR,CAAC;EACDa,QAAQ,EAAE;IACRb,IAAI,EAAE;EACR;AACF,CAAC;AACD,eAAejE,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}