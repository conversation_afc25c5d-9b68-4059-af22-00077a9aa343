{"ast": null, "code": "import classNames from 'classnames';\nimport React, { useMemo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { getTreeDeep } from '../../utils/tree';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { useFieldNames } from '../../hooks';\nconst classPrefix = `adm-tree-select`;\nconst defaultProps = {\n  options: [],\n  fieldNames: {},\n  defaultValue: []\n};\nexport const TreeSelect = p => {\n  const props = mergeProps(defaultProps, p);\n  const [labelName, valueName, childrenName] = useFieldNames(props.fieldNames);\n  const [value, setValue] = usePropsValue({\n    value: props.value,\n    defaultValue: props.defaultValue\n  });\n  const [deep, optionsMap, optionsParentMap] = useMemo(() => {\n    const deep = getTreeDeep(props.options, childrenName);\n    const optionsMap = new Map();\n    const optionsParentMap = new Map();\n    function traverse(current, children) {\n      children.forEach(item => {\n        optionsParentMap.set(item[valueName], current);\n        optionsMap.set(item[valueName], item);\n        if (item[childrenName]) {\n          traverse(item, item[childrenName]);\n        }\n      });\n    }\n    traverse(undefined, props.options);\n    return [deep, optionsMap, optionsParentMap];\n  }, [props.options]);\n  const onItemSelect = node => {\n    var _a;\n    // 找到父级节点\n    const parentNodes = [];\n    let current = node;\n    while (current) {\n      parentNodes.push(current);\n      const next = optionsParentMap.get(current[valueName]);\n      current = next;\n    }\n    const values = parentNodes.reverse().map(i => i[valueName]);\n    setValue(values);\n    (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, values, {\n      options: parentNodes\n    });\n  };\n  const renderItems = (columnOptions = [], index) => {\n    return columnOptions.map(item => {\n      const isActive = item[valueName] === value[index];\n      return React.createElement(\"div\", {\n        key: item[valueName],\n        className: classNames(`${classPrefix}-item`, {\n          [`${classPrefix}-item-active`]: isActive\n        }),\n        onClick: () => {\n          if (!isActive) {\n            onItemSelect(item);\n          }\n        }\n      }, item[labelName]);\n    });\n  };\n  const renderColumns = () => {\n    var _a;\n    const columns = [];\n    for (let i = 0; i < deep; i++) {\n      let width = `${100 / deep}%`;\n      // 两列的第一列宽度为 33.33，两列的第二列为 66.67%\n      if (deep === 2 && i === 0) {\n        width = `33.33%`;\n      }\n      if (deep === 2 && i === 1) {\n        width = `66.67%`;\n      }\n      const column = React.createElement(\"div\", {\n        key: i,\n        className: classNames(`${classPrefix}-column`),\n        style: {\n          width\n        }\n      }, renderItems(i === 0 ? props.options : (_a = optionsMap.get(value[i - 1])) === null || _a === void 0 ? void 0 : _a[childrenName], i));\n      columns.push(column);\n    }\n    return columns;\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, renderColumns()));\n};", "map": {"version": 3, "names": ["classNames", "React", "useMemo", "withNativeProps", "getTreeDeep", "mergeProps", "usePropsValue", "useFieldNames", "classPrefix", "defaultProps", "options", "fieldNames", "defaultValue", "TreeSelect", "p", "props", "labelName", "valueName", "<PERSON><PERSON><PERSON>", "value", "setValue", "deep", "optionsMap", "optionsParentMap", "Map", "traverse", "current", "children", "for<PERSON>ach", "item", "set", "undefined", "onItemSelect", "node", "_a", "parentNodes", "push", "next", "get", "values", "reverse", "map", "i", "onChange", "call", "renderItems", "columnOptions", "index", "isActive", "createElement", "key", "className", "onClick", "renderColumns", "columns", "width", "column", "style"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/tree-select/tree-select.js"], "sourcesContent": ["import classNames from 'classnames';\nimport React, { useMemo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { getTreeDeep } from '../../utils/tree';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { useFieldNames } from '../../hooks';\nconst classPrefix = `adm-tree-select`;\nconst defaultProps = {\n  options: [],\n  fieldNames: {},\n  defaultValue: []\n};\nexport const TreeSelect = p => {\n  const props = mergeProps(defaultProps, p);\n  const [labelName, valueName, childrenName] = useFieldNames(props.fieldNames);\n  const [value, setValue] = usePropsValue({\n    value: props.value,\n    defaultValue: props.defaultValue\n  });\n  const [deep, optionsMap, optionsParentMap] = useMemo(() => {\n    const deep = getTreeDeep(props.options, childrenName);\n    const optionsMap = new Map();\n    const optionsParentMap = new Map();\n    function traverse(current, children) {\n      children.forEach(item => {\n        optionsParentMap.set(item[valueName], current);\n        optionsMap.set(item[valueName], item);\n        if (item[childrenName]) {\n          traverse(item, item[childrenName]);\n        }\n      });\n    }\n    traverse(undefined, props.options);\n    return [deep, optionsMap, optionsParentMap];\n  }, [props.options]);\n  const onItemSelect = node => {\n    var _a;\n    // 找到父级节点\n    const parentNodes = [];\n    let current = node;\n    while (current) {\n      parentNodes.push(current);\n      const next = optionsParentMap.get(current[valueName]);\n      current = next;\n    }\n    const values = parentNodes.reverse().map(i => i[valueName]);\n    setValue(values);\n    (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, values, {\n      options: parentNodes\n    });\n  };\n  const renderItems = (columnOptions = [], index) => {\n    return columnOptions.map(item => {\n      const isActive = item[valueName] === value[index];\n      return React.createElement(\"div\", {\n        key: item[valueName],\n        className: classNames(`${classPrefix}-item`, {\n          [`${classPrefix}-item-active`]: isActive\n        }),\n        onClick: () => {\n          if (!isActive) {\n            onItemSelect(item);\n          }\n        }\n      }, item[labelName]);\n    });\n  };\n  const renderColumns = () => {\n    var _a;\n    const columns = [];\n    for (let i = 0; i < deep; i++) {\n      let width = `${100 / deep}%`;\n      // 两列的第一列宽度为 33.33，两列的第二列为 66.67%\n      if (deep === 2 && i === 0) {\n        width = `33.33%`;\n      }\n      if (deep === 2 && i === 1) {\n        width = `66.67%`;\n      }\n      const column = React.createElement(\"div\", {\n        key: i,\n        className: classNames(`${classPrefix}-column`),\n        style: {\n          width\n        }\n      }, renderItems(i === 0 ? props.options : (_a = optionsMap.get(value[i - 1])) === null || _a === void 0 ? void 0 : _a[childrenName], i));\n      columns.push(column);\n    }\n    return columns;\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, renderColumns()));\n};"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,aAAa,QAAQ,aAAa;AAC3C,MAAMC,WAAW,GAAG,iBAAiB;AACrC,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,EAAE;EACXC,UAAU,EAAE,CAAC,CAAC;EACdC,YAAY,EAAE;AAChB,CAAC;AACD,OAAO,MAAMC,UAAU,GAAGC,CAAC,IAAI;EAC7B,MAAMC,KAAK,GAAGV,UAAU,CAACI,YAAY,EAAEK,CAAC,CAAC;EACzC,MAAM,CAACE,SAAS,EAAEC,SAAS,EAAEC,YAAY,CAAC,GAAGX,aAAa,CAACQ,KAAK,CAACJ,UAAU,CAAC;EAC5E,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC,GAAGd,aAAa,CAAC;IACtCa,KAAK,EAAEJ,KAAK,CAACI,KAAK;IAClBP,YAAY,EAAEG,KAAK,CAACH;EACtB,CAAC,CAAC;EACF,MAAM,CAACS,IAAI,EAAEC,UAAU,EAAEC,gBAAgB,CAAC,GAAGrB,OAAO,CAAC,MAAM;IACzD,MAAMmB,IAAI,GAAGjB,WAAW,CAACW,KAAK,CAACL,OAAO,EAAEQ,YAAY,CAAC;IACrD,MAAMI,UAAU,GAAG,IAAIE,GAAG,CAAC,CAAC;IAC5B,MAAMD,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAClC,SAASC,QAAQA,CAACC,OAAO,EAAEC,QAAQ,EAAE;MACnCA,QAAQ,CAACC,OAAO,CAACC,IAAI,IAAI;QACvBN,gBAAgB,CAACO,GAAG,CAACD,IAAI,CAACZ,SAAS,CAAC,EAAES,OAAO,CAAC;QAC9CJ,UAAU,CAACQ,GAAG,CAACD,IAAI,CAACZ,SAAS,CAAC,EAAEY,IAAI,CAAC;QACrC,IAAIA,IAAI,CAACX,YAAY,CAAC,EAAE;UACtBO,QAAQ,CAACI,IAAI,EAAEA,IAAI,CAACX,YAAY,CAAC,CAAC;QACpC;MACF,CAAC,CAAC;IACJ;IACAO,QAAQ,CAACM,SAAS,EAAEhB,KAAK,CAACL,OAAO,CAAC;IAClC,OAAO,CAACW,IAAI,EAAEC,UAAU,EAAEC,gBAAgB,CAAC;EAC7C,CAAC,EAAE,CAACR,KAAK,CAACL,OAAO,CAAC,CAAC;EACnB,MAAMsB,YAAY,GAAGC,IAAI,IAAI;IAC3B,IAAIC,EAAE;IACN;IACA,MAAMC,WAAW,GAAG,EAAE;IACtB,IAAIT,OAAO,GAAGO,IAAI;IAClB,OAAOP,OAAO,EAAE;MACdS,WAAW,CAACC,IAAI,CAACV,OAAO,CAAC;MACzB,MAAMW,IAAI,GAAGd,gBAAgB,CAACe,GAAG,CAACZ,OAAO,CAACT,SAAS,CAAC,CAAC;MACrDS,OAAO,GAAGW,IAAI;IAChB;IACA,MAAME,MAAM,GAAGJ,WAAW,CAACK,OAAO,CAAC,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzB,SAAS,CAAC,CAAC;IAC3DG,QAAQ,CAACmB,MAAM,CAAC;IAChB,CAACL,EAAE,GAAGnB,KAAK,CAAC4B,QAAQ,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,IAAI,CAAC7B,KAAK,EAAEwB,MAAM,EAAE;MAChF7B,OAAO,EAAEyB;IACX,CAAC,CAAC;EACJ,CAAC;EACD,MAAMU,WAAW,GAAGA,CAACC,aAAa,GAAG,EAAE,EAAEC,KAAK,KAAK;IACjD,OAAOD,aAAa,CAACL,GAAG,CAACZ,IAAI,IAAI;MAC/B,MAAMmB,QAAQ,GAAGnB,IAAI,CAACZ,SAAS,CAAC,KAAKE,KAAK,CAAC4B,KAAK,CAAC;MACjD,OAAO9C,KAAK,CAACgD,aAAa,CAAC,KAAK,EAAE;QAChCC,GAAG,EAAErB,IAAI,CAACZ,SAAS,CAAC;QACpBkC,SAAS,EAAEnD,UAAU,CAAC,GAAGQ,WAAW,OAAO,EAAE;UAC3C,CAAC,GAAGA,WAAW,cAAc,GAAGwC;QAClC,CAAC,CAAC;QACFI,OAAO,EAAEA,CAAA,KAAM;UACb,IAAI,CAACJ,QAAQ,EAAE;YACbhB,YAAY,CAACH,IAAI,CAAC;UACpB;QACF;MACF,CAAC,EAAEA,IAAI,CAACb,SAAS,CAAC,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMqC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAInB,EAAE;IACN,MAAMoB,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,IAAI,EAAEqB,CAAC,EAAE,EAAE;MAC7B,IAAIa,KAAK,GAAG,GAAG,GAAG,GAAGlC,IAAI,GAAG;MAC5B;MACA,IAAIA,IAAI,KAAK,CAAC,IAAIqB,CAAC,KAAK,CAAC,EAAE;QACzBa,KAAK,GAAG,QAAQ;MAClB;MACA,IAAIlC,IAAI,KAAK,CAAC,IAAIqB,CAAC,KAAK,CAAC,EAAE;QACzBa,KAAK,GAAG,QAAQ;MAClB;MACA,MAAMC,MAAM,GAAGvD,KAAK,CAACgD,aAAa,CAAC,KAAK,EAAE;QACxCC,GAAG,EAAER,CAAC;QACNS,SAAS,EAAEnD,UAAU,CAAC,GAAGQ,WAAW,SAAS,CAAC;QAC9CiD,KAAK,EAAE;UACLF;QACF;MACF,CAAC,EAAEV,WAAW,CAACH,CAAC,KAAK,CAAC,GAAG3B,KAAK,CAACL,OAAO,GAAG,CAACwB,EAAE,GAAGZ,UAAU,CAACgB,GAAG,CAACnB,KAAK,CAACuB,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChB,YAAY,CAAC,EAAEwB,CAAC,CAAC,CAAC;MACvIY,OAAO,CAAClB,IAAI,CAACoB,MAAM,CAAC;IACtB;IACA,OAAOF,OAAO;EAChB,CAAC;EACD,OAAOnD,eAAe,CAACY,KAAK,EAAEd,KAAK,CAACgD,aAAa,CAAC,KAAK,EAAE;IACvDE,SAAS,EAAE3C;EACb,CAAC,EAAE6C,aAAa,CAAC,CAAC,CAAC,CAAC;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}