{"ast": null, "code": "import * as React from \"react\";\nfunction ShrinkOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ShrinkOutline-ShrinkOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ShrinkOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ShrinkOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M36.8784602,25.1799542 L39.0001962,27.358974 C39.1543116,27.5172503 39.1509385,27.7704938 38.9926622,27.9246092 C38.9179796,27.9973285 38.8178601,28.0380219 38.713622,28.0380246 L29.4529821,28.0382654 L29.4529821,28.0382654 L40.7281795,39.6141111 C40.8794493,39.7694145 40.879462,40.0169571 40.7282081,40.1722761 L39.234866,41.705753 C39.0807411,41.8640201 38.8274974,41.867378 38.6692303,41.7132531 C38.6667045,41.7107935 38.6642114,41.7083006 38.6617516,41.705775 L27.4868107,30.2322654 L27.4868107,30.2322654 L27.4873666,39.0539752 C27.4873805,39.2748891 27.3083057,39.4539864 27.0873918,39.4540004 C26.9794723,39.4540072 26.8761291,39.4104065 26.8008287,39.333099 L24.6992399,37.1754929 C24.6264857,37.1007994 24.5857737,37.000649 24.5857778,36.8963787 L24.586125,28.0387593 L24.586125,28.0387593 C24.586125,26.3932302 25.8819766,25.0592654 27.4804905,25.0592654 L36.5918641,25.0590048 C36.699802,25.0590017 36.8031598,25.1026208 36.8784602,25.1799542 Z M9.33824861,6.29420909 L20.6786857,17.937 L20.6786857,17.937 L20.6786857,9.52918852 C20.6786857,9.30827462 20.8577718,9.12918852 21.0786857,9.12918852 C21.1866212,9.12918852 21.2899758,9.17280868 21.3652736,9.25014122 L23.4951193,11.4375408 C23.5678289,11.5122152 23.6085204,11.6123204 23.6085315,11.7165459 L23.6094116,20.0444491 L23.6094116,20.0444491 C23.6094116,21.7062131 22.3007751,23.0533389 20.6864901,23.0533389 L11.1157237,23.0530059 C11.007793,23.0530021 10.9044442,23.0093822 10.8291498,22.9320532 L8.67826623,20.7230473 C8.52415266,20.5647692 8.52752869,20.3115257 8.6858068,20.1574121 C8.76049167,20.0846923 8.86061399,20.044 8.9648541,20.044 L18.6270286,20.044 L18.6270286,20.044 L7.27181775,8.38588886 C7.12054922,8.23058525 7.12053713,7.98304372 7.27179048,7.82772532 L8.76514185,6.29423897 C8.91926673,6.13597187 9.17251046,6.13261394 9.33077755,6.28673881 C9.33330069,6.28919591 9.33579126,6.29168622 9.33824861,6.29420909 Z\",\n    id: \"ShrinkOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default ShrinkOutline;", "map": {"version": 3, "names": ["React", "ShrinkOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/ShrinkOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction ShrinkOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ShrinkOutline-ShrinkOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ShrinkOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ShrinkOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M36.8784602,25.1799542 L39.0001962,27.358974 C39.1543116,27.5172503 39.1509385,27.7704938 38.9926622,27.9246092 C38.9179796,27.9973285 38.8178601,28.0380219 38.713622,28.0380246 L29.4529821,28.0382654 L29.4529821,28.0382654 L40.7281795,39.6141111 C40.8794493,39.7694145 40.879462,40.0169571 40.7282081,40.1722761 L39.234866,41.705753 C39.0807411,41.8640201 38.8274974,41.867378 38.6692303,41.7132531 C38.6667045,41.7107935 38.6642114,41.7083006 38.6617516,41.705775 L27.4868107,30.2322654 L27.4868107,30.2322654 L27.4873666,39.0539752 C27.4873805,39.2748891 27.3083057,39.4539864 27.0873918,39.4540004 C26.9794723,39.4540072 26.8761291,39.4104065 26.8008287,39.333099 L24.6992399,37.1754929 C24.6264857,37.1007994 24.5857737,37.000649 24.5857778,36.8963787 L24.586125,28.0387593 L24.586125,28.0387593 C24.586125,26.3932302 25.8819766,25.0592654 27.4804905,25.0592654 L36.5918641,25.0590048 C36.699802,25.0590017 36.8031598,25.1026208 36.8784602,25.1799542 Z M9.33824861,6.29420909 L20.6786857,17.937 L20.6786857,17.937 L20.6786857,9.52918852 C20.6786857,9.30827462 20.8577718,9.12918852 21.0786857,9.12918852 C21.1866212,9.12918852 21.2899758,9.17280868 21.3652736,9.25014122 L23.4951193,11.4375408 C23.5678289,11.5122152 23.6085204,11.6123204 23.6085315,11.7165459 L23.6094116,20.0444491 L23.6094116,20.0444491 C23.6094116,21.7062131 22.3007751,23.0533389 20.6864901,23.0533389 L11.1157237,23.0530059 C11.007793,23.0530021 10.9044442,23.0093822 10.8291498,22.9320532 L8.67826623,20.7230473 C8.52415266,20.5647692 8.52752869,20.3115257 8.6858068,20.1574121 C8.76049167,20.0846923 8.86061399,20.044 8.9648541,20.044 L18.6270286,20.044 L18.6270286,20.044 L7.27181775,8.38588886 C7.12054922,8.23058525 7.12053713,7.98304372 7.27179048,7.82772532 L8.76514185,6.29423897 C8.91926673,6.13597187 9.17251046,6.13261394 9.33077755,6.28673881 C9.33330069,6.28919591 9.33579126,6.29168622 9.33824861,6.29420909 Z\",\n    id: \"ShrinkOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default ShrinkOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,6BAA6B;IACjCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,+2DAA+2D;IACl3DR,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}