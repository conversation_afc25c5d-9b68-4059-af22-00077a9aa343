{"ast": null, "code": "import React from 'react';\nimport { renderImperatively } from '../../utils/render-imperatively';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { InternalToast } from './toast';\nlet currentHandler = null;\nlet currentTimeout = null;\nconst defaultProps = {\n  duration: 2000,\n  position: 'center',\n  maskClickable: true\n};\nconst ToastInner = props => React.createElement(InternalToast, Object.assign({}, props));\nexport function show(p) {\n  var _a;\n  const props = mergeProps(defaultProps, typeof p === 'string' ? {\n    content: p\n  } : p);\n  const element = React.createElement(ToastInner, Object.assign({}, props, {\n    onClose: () => {\n      currentHandler = null;\n    }\n  }));\n  if (currentHandler) {\n    if ((_a = currentHandler.isRendered) === null || _a === void 0 ? void 0 : _a.call(currentHandler)) {\n      currentHandler.replace(element);\n    } else {\n      currentHandler.close();\n      currentHandler = renderImperatively(element);\n    }\n  } else {\n    currentHandler = renderImperatively(element);\n  }\n  if (currentTimeout) {\n    window.clearTimeout(currentTimeout);\n  }\n  if (props.duration !== 0) {\n    currentTimeout = window.setTimeout(() => {\n      clear();\n    }, props.duration);\n  }\n  return currentHandler;\n}\nexport function clear() {\n  currentHandler === null || currentHandler === void 0 ? void 0 : currentHandler.close();\n  currentHandler = null;\n}\nexport function config(val) {\n  if (val.duration !== undefined) {\n    defaultProps.duration = val.duration;\n  }\n  if (val.position !== undefined) {\n    defaultProps.position = val.position;\n  }\n  if (val.maskClickable !== undefined) {\n    defaultProps.maskClickable = val.maskClickable;\n  }\n}", "map": {"version": 3, "names": ["React", "renderImperatively", "mergeProps", "InternalToast", "<PERSON><PERSON><PERSON><PERSON>", "currentTimeout", "defaultProps", "duration", "position", "maskClickable", "ToastInner", "props", "createElement", "Object", "assign", "show", "p", "_a", "content", "element", "onClose", "isRendered", "call", "replace", "close", "window", "clearTimeout", "setTimeout", "clear", "config", "val", "undefined"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/toast/methods.js"], "sourcesContent": ["import React from 'react';\nimport { renderImperatively } from '../../utils/render-imperatively';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { InternalToast } from './toast';\nlet currentHandler = null;\nlet currentTimeout = null;\nconst defaultProps = {\n  duration: 2000,\n  position: 'center',\n  maskClickable: true\n};\nconst ToastInner = props => React.createElement(InternalToast, Object.assign({}, props));\nexport function show(p) {\n  var _a;\n  const props = mergeProps(defaultProps, typeof p === 'string' ? {\n    content: p\n  } : p);\n  const element = React.createElement(ToastInner, Object.assign({}, props, {\n    onClose: () => {\n      currentHandler = null;\n    }\n  }));\n  if (currentHandler) {\n    if ((_a = currentHandler.isRendered) === null || _a === void 0 ? void 0 : _a.call(currentHandler)) {\n      currentHandler.replace(element);\n    } else {\n      currentHandler.close();\n      currentHandler = renderImperatively(element);\n    }\n  } else {\n    currentHandler = renderImperatively(element);\n  }\n  if (currentTimeout) {\n    window.clearTimeout(currentTimeout);\n  }\n  if (props.duration !== 0) {\n    currentTimeout = window.setTimeout(() => {\n      clear();\n    }, props.duration);\n  }\n  return currentHandler;\n}\nexport function clear() {\n  currentHandler === null || currentHandler === void 0 ? void 0 : currentHandler.close();\n  currentHandler = null;\n}\nexport function config(val) {\n  if (val.duration !== undefined) {\n    defaultProps.duration = val.duration;\n  }\n  if (val.position !== undefined) {\n    defaultProps.position = val.position;\n  }\n  if (val.maskClickable !== undefined) {\n    defaultProps.maskClickable = val.maskClickable;\n  }\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,aAAa,QAAQ,SAAS;AACvC,IAAIC,cAAc,GAAG,IAAI;AACzB,IAAIC,cAAc,GAAG,IAAI;AACzB,MAAMC,YAAY,GAAG;EACnBC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,QAAQ;EAClBC,aAAa,EAAE;AACjB,CAAC;AACD,MAAMC,UAAU,GAAGC,KAAK,IAAIX,KAAK,CAACY,aAAa,CAACT,aAAa,EAAEU,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,KAAK,CAAC,CAAC;AACxF,OAAO,SAASI,IAAIA,CAACC,CAAC,EAAE;EACtB,IAAIC,EAAE;EACN,MAAMN,KAAK,GAAGT,UAAU,CAACI,YAAY,EAAE,OAAOU,CAAC,KAAK,QAAQ,GAAG;IAC7DE,OAAO,EAAEF;EACX,CAAC,GAAGA,CAAC,CAAC;EACN,MAAMG,OAAO,GAAGnB,KAAK,CAACY,aAAa,CAACF,UAAU,EAAEG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,KAAK,EAAE;IACvES,OAAO,EAAEA,CAAA,KAAM;MACbhB,cAAc,GAAG,IAAI;IACvB;EACF,CAAC,CAAC,CAAC;EACH,IAAIA,cAAc,EAAE;IAClB,IAAI,CAACa,EAAE,GAAGb,cAAc,CAACiB,UAAU,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,IAAI,CAAClB,cAAc,CAAC,EAAE;MACjGA,cAAc,CAACmB,OAAO,CAACJ,OAAO,CAAC;IACjC,CAAC,MAAM;MACLf,cAAc,CAACoB,KAAK,CAAC,CAAC;MACtBpB,cAAc,GAAGH,kBAAkB,CAACkB,OAAO,CAAC;IAC9C;EACF,CAAC,MAAM;IACLf,cAAc,GAAGH,kBAAkB,CAACkB,OAAO,CAAC;EAC9C;EACA,IAAId,cAAc,EAAE;IAClBoB,MAAM,CAACC,YAAY,CAACrB,cAAc,CAAC;EACrC;EACA,IAAIM,KAAK,CAACJ,QAAQ,KAAK,CAAC,EAAE;IACxBF,cAAc,GAAGoB,MAAM,CAACE,UAAU,CAAC,MAAM;MACvCC,KAAK,CAAC,CAAC;IACT,CAAC,EAAEjB,KAAK,CAACJ,QAAQ,CAAC;EACpB;EACA,OAAOH,cAAc;AACvB;AACA,OAAO,SAASwB,KAAKA,CAAA,EAAG;EACtBxB,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACoB,KAAK,CAAC,CAAC;EACtFpB,cAAc,GAAG,IAAI;AACvB;AACA,OAAO,SAASyB,MAAMA,CAACC,GAAG,EAAE;EAC1B,IAAIA,GAAG,CAACvB,QAAQ,KAAKwB,SAAS,EAAE;IAC9BzB,YAAY,CAACC,QAAQ,GAAGuB,GAAG,CAACvB,QAAQ;EACtC;EACA,IAAIuB,GAAG,CAACtB,QAAQ,KAAKuB,SAAS,EAAE;IAC9BzB,YAAY,CAACE,QAAQ,GAAGsB,GAAG,CAACtB,QAAQ;EACtC;EACA,IAAIsB,GAAG,CAACrB,aAAa,KAAKsB,SAAS,EAAE;IACnCzB,YAAY,CAACG,aAAa,GAAGqB,GAAG,CAACrB,aAAa;EAChD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}