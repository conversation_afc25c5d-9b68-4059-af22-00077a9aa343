{"ast": null, "code": "import * as React from \"react\";\nfunction PicturesOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PicturesOutline-PicturesOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PicturesOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"PicturesOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M32,10 C35.3137085,10 38,12.6862915 38,16 L38,38 C38,41.3137085 35.3137085,44 32,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,16 C4,12.6862915 6.6862915,10 10,10 L32,10 Z M32,13 L10,13 C8.40231912,13 7.09633912,14.24892 7.00509269,15.8237272 L7,16 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 C13.1111111,41 15.4444444,41 17,41 C20.3333333,41 25.3333333,41 32,41 C33.5976809,41 34.9036609,39.75108 34.9949073,38.1762728 L35,38 L35,33.332 L30.7808252,29.1126218 C30.0818755,28.4136721 28.9683937,28.3819016 28.2317592,29.0173105 L28.1291748,29.1126218 L16.8091025,40.4319451 L16.242,40.9990101 L12,40.9990101 L26.6265729,26.372583 C28.18867,24.8104858 30.72133,24.8104858 32.2834271,26.372583 L35,29.089 L35,16 C35,14.4023191 33.75108,13.0963391 32.1762728,13.0050927 L32,13 Z M38,4 C41.3137085,4 44,6.6862915 44,10 L44,34 L41,34 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L14,7 L14,4 L38,4 Z M14.5,16 C16.9852814,16 19,18.0147186 19,20.5 C19,22.9852814 16.9852814,25 14.5,25 C12.0147186,25 10,22.9852814 10,20.5 C10,18.0147186 12.0147186,16 14.5,16 Z M14.5,19 C13.6715729,19 13,19.6715729 13,20.5 C13,21.3284271 13.6715729,22 14.5,22 C15.3284271,22 16,21.3284271 16,20.5 C16,19.6715729 15.3284271,19 14.5,19 Z\",\n    id: \"PicturesOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default PicturesOutline;", "map": {"version": 3, "names": ["React", "PicturesOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/PicturesOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction PicturesOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PicturesOutline-PicturesOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PicturesOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"PicturesOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M32,10 C35.3137085,10 38,12.6862915 38,16 L38,38 C38,41.3137085 35.3137085,44 32,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,16 C4,12.6862915 6.6862915,10 10,10 L32,10 Z M32,13 L10,13 C8.40231912,13 7.09633912,14.24892 7.00509269,15.8237272 L7,16 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 C13.1111111,41 15.4444444,41 17,41 C20.3333333,41 25.3333333,41 32,41 C33.5976809,41 34.9036609,39.75108 34.9949073,38.1762728 L35,38 L35,33.332 L30.7808252,29.1126218 C30.0818755,28.4136721 28.9683937,28.3819016 28.2317592,29.0173105 L28.1291748,29.1126218 L16.8091025,40.4319451 L16.242,40.9990101 L12,40.9990101 L26.6265729,26.372583 C28.18867,24.8104858 30.72133,24.8104858 32.2834271,26.372583 L35,29.089 L35,16 C35,14.4023191 33.75108,13.0963391 32.1762728,13.0050927 L32,13 Z M38,4 C41.3137085,4 44,6.6862915 44,10 L44,34 L41,34 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L14,7 L14,4 L38,4 Z M14.5,16 C16.9852814,16 19,18.0147186 19,20.5 C19,22.9852814 16.9852814,25 14.5,25 C12.0147186,25 10,22.9852814 10,20.5 C10,18.0147186 12.0147186,16 14.5,16 Z M14.5,19 C13.6715729,19 13,19.6715729 13,20.5 C13,21.3284271 13.6715729,22 14.5,22 C15.3284271,22 16,21.3284271 16,20.5 C16,19.6715729 15.3284271,19 14.5,19 Z\",\n    id: \"PicturesOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default PicturesOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,iCAAiC;IACrCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,gvCAAgvC;IACnvCR,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}