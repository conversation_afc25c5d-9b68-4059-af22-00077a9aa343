{"ast": null, "code": "import { render, unmount as reactUnmount } from './render';\nexport function renderToBody(element) {\n  const container = document.createElement('div');\n  document.body.appendChild(container);\n  function unmount() {\n    const unmountResult = reactUnmount(container);\n    if (unmountResult && container.parentNode) {\n      container.parentNode.removeChild(container);\n    }\n  }\n  render(element, container);\n  return unmount;\n}", "map": {"version": 3, "names": ["render", "unmount", "reactUnmount", "renderToBody", "element", "container", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "unmountResult", "parentNode", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/render-to-body.js"], "sourcesContent": ["import { render, unmount as reactUnmount } from './render';\nexport function renderToBody(element) {\n  const container = document.createElement('div');\n  document.body.appendChild(container);\n  function unmount() {\n    const unmountResult = reactUnmount(container);\n    if (unmountResult && container.parentNode) {\n      container.parentNode.removeChild(container);\n    }\n  }\n  render(element, container);\n  return unmount;\n}"], "mappings": "AAAA,SAASA,MAAM,EAAEC,OAAO,IAAIC,YAAY,QAAQ,UAAU;AAC1D,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAE;EACpC,MAAMC,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC/CD,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,SAAS,CAAC;EACpC,SAASJ,OAAOA,CAAA,EAAG;IACjB,MAAMS,aAAa,GAAGR,YAAY,CAACG,SAAS,CAAC;IAC7C,IAAIK,aAAa,IAAIL,SAAS,CAACM,UAAU,EAAE;MACzCN,SAAS,CAACM,UAAU,CAACC,WAAW,CAACP,SAAS,CAAC;IAC7C;EACF;EACAL,MAAM,CAACI,OAAO,EAAEC,SAAS,CAAC;EAC1B,OAAOJ,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}