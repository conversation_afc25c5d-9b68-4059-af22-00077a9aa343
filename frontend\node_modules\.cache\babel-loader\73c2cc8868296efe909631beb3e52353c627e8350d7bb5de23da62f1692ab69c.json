{"ast": null, "code": "import React, { memo, useRef } from 'react';\nimport { useSpring, animated } from '@react-spring/web';\nimport { useDrag, useWheel } from '@use-gesture/react';\nimport { rubberbandIfOutOfBounds } from '../../utils/rubberband';\nimport { bound } from '../../utils/bound';\nimport isEqual from 'react-fast-compare';\nimport { useIsomorphicLayoutEffect } from 'ahooks';\nimport { measureCSSLength } from '../../utils/measure-css-length';\nimport { supportsPassive } from '../../utils/supports-passive';\nimport classNames from 'classnames';\nconst classPrefix = `adm-picker-view`;\nexport const Wheel = memo(props => {\n  const {\n    value,\n    column,\n    renderLabel\n  } = props;\n  function onSelect(val) {\n    props.onSelect(val, props.index);\n  }\n  const [{\n    y\n  }, api] = useSpring(() => ({\n    from: {\n      y: 0\n    },\n    config: {\n      tension: 400,\n      mass: 0.8\n    }\n  }));\n  const draggingRef = useRef(false);\n  const rootRef = useRef(null);\n  const itemHeightMeasureRef = useRef(null);\n  const itemHeight = useRef(34);\n  useIsomorphicLayoutEffect(() => {\n    const itemHeightMeasure = itemHeightMeasureRef.current;\n    if (!itemHeightMeasure) return;\n    itemHeight.current = measureCSSLength(window.getComputedStyle(itemHeightMeasure).getPropertyValue('height'));\n  });\n  useIsomorphicLayoutEffect(() => {\n    if (draggingRef.current) return;\n    if (value === null) return;\n    const targetIndex = column.findIndex(item => item.value === value);\n    if (targetIndex < 0) return;\n    const finalPosition = targetIndex * -itemHeight.current;\n    api.start({\n      y: finalPosition,\n      immediate: y.goal !== finalPosition\n    });\n  }, [value, column]);\n  useIsomorphicLayoutEffect(() => {\n    if (column.length === 0) {\n      if (value !== null) {\n        onSelect(null);\n      }\n    } else {\n      if (!column.some(item => item.value === value)) {\n        const firstItem = column[0];\n        onSelect(firstItem.value);\n      }\n    }\n  }, [column, value]);\n  function scrollSelect(index) {\n    const finalPosition = index * -itemHeight.current;\n    api.start({\n      y: finalPosition\n    });\n    const item = column[index];\n    if (!item) return;\n    onSelect(item.value);\n  }\n  const handleGestureState = state => {\n    const {\n      direction: [, direction],\n      distance: [, distance],\n      velocity: [, velocity],\n      offset: [, offset],\n      last\n    } = state;\n    return {\n      direction,\n      distance,\n      velocity,\n      offset,\n      last\n    };\n  };\n  const handleDrag = state => {\n    draggingRef.current = true;\n    const min = -((column.length - 1) * itemHeight.current);\n    const max = 0;\n    const {\n      direction,\n      last,\n      velocity,\n      offset\n    } = handleGestureState(state);\n    if (last) {\n      draggingRef.current = false;\n      const position = offset + velocity * direction * 50;\n      const boundNum = bound(position, min, max);\n      const targetIndex = -Math.round(boundNum / itemHeight.current);\n      scrollSelect(targetIndex);\n    } else {\n      const position = offset;\n      api.start({\n        y: rubberbandIfOutOfBounds(position, min, max, itemHeight.current * 50, 0.2)\n      });\n    }\n  };\n  const handleWheel = state => {\n    draggingRef.current = true;\n    const min = -((column.length - 1) * itemHeight.current);\n    const max = 0;\n    const {\n      direction,\n      last,\n      velocity,\n      distance\n    } = handleGestureState(state);\n    const whellDir = -direction; // 取反\n    const scrollY = y.get();\n    if (last) {\n      draggingRef.current = false;\n      const speed = velocity * whellDir * 50;\n      const position = scrollY + distance * whellDir + speed;\n      const boundNum = bound(position, min, max);\n      const targetIndex = -Math.round(boundNum / itemHeight.current);\n      scrollSelect(targetIndex);\n    } else {\n      const position = scrollY + distance * whellDir;\n      api.start({\n        y: rubberbandIfOutOfBounds(position, min, max, itemHeight.current * 50, 0.2)\n      });\n    }\n  };\n  useDrag(state => {\n    state.event.stopPropagation();\n    handleDrag(state);\n  }, {\n    axis: 'y',\n    from: () => [0, y.get()],\n    filterTaps: true,\n    pointer: {\n      touch: true\n    },\n    target: rootRef\n  });\n  useWheel(state => {\n    state.event.stopPropagation();\n    handleWheel(state);\n  }, {\n    target: props.mouseWheel ? rootRef : undefined,\n    axis: 'y',\n    from: () => [0, y.get()],\n    preventDefault: true,\n    eventOptions: supportsPassive ? {\n      passive: false\n    } : undefined\n  });\n  let selectedIndex = null;\n  function renderAccessible() {\n    if (selectedIndex === null) {\n      return null;\n    }\n    const current = column[selectedIndex];\n    const previousIndex = selectedIndex - 1;\n    const nextIndex = selectedIndex + 1;\n    const previous = column[previousIndex];\n    const next = column[nextIndex];\n    return React.createElement(\"div\", {\n      className: `${classPrefix}-column-accessible`\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-column-accessible-current`,\n      role: 'button',\n      \"aria-label\": current ? `当前选择的是：${current.label}` : '当前未选择'\n    }, \"-\"), React.createElement(\"div\", {\n      className: `${classPrefix}-column-accessible-button`,\n      onClick: () => {\n        if (!previous) return;\n        scrollSelect(previousIndex);\n      },\n      role: previous ? 'button' : 'text',\n      \"aria-label\": !previous ? '没有上一项' : `选择上一项：${previous.label}`\n    }, \"-\"), React.createElement(\"div\", {\n      className: `${classPrefix}-column-accessible-button`,\n      onClick: () => {\n        if (!next) return;\n        scrollSelect(nextIndex);\n      },\n      role: next ? 'button' : 'text',\n      \"aria-label\": !next ? '没有下一项' : `选择下一项：${next.label}`\n    }, \"-\"));\n  }\n  return React.createElement(\"div\", {\n    className: `${classPrefix}-column`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-item-height-measure`,\n    ref: itemHeightMeasureRef\n  }), React.createElement(animated.div, {\n    ref: rootRef,\n    style: {\n      translateY: y\n    },\n    className: `${classPrefix}-column-wheel`,\n    \"aria-hidden\": true\n  }, column.map((item, index) => {\n    var _a;\n    const selected = props.value === item.value;\n    if (selected) selectedIndex = index;\n    function handleClick() {\n      draggingRef.current = false;\n      scrollSelect(index);\n    }\n    return React.createElement(\"div\", {\n      key: (_a = item.key) !== null && _a !== void 0 ? _a : item.value,\n      \"data-selected\": selected,\n      className: classNames(`${classPrefix}-column-item`, {\n        [`${classPrefix}-column-item-active`]: selected\n      }),\n      onClick: handleClick,\n      \"aria-hidden\": !selected,\n      \"aria-label\": selected ? 'active' : ''\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-column-item-label`\n    }, renderLabel(item)));\n  })), renderAccessible());\n}, (prev, next) => {\n  if (prev.index !== next.index) return false;\n  if (prev.value !== next.value) return false;\n  if (prev.onSelect !== next.onSelect) return false;\n  if (prev.renderLabel !== next.renderLabel) return false;\n  if (prev.mouseWheel !== next.mouseWheel) return false;\n  if (!isEqual(prev.column, next.column)) return false;\n  return true;\n});\nWheel.displayName = 'Wheel';", "map": {"version": 3, "names": ["React", "memo", "useRef", "useSpring", "animated", "useDrag", "useWheel", "rubberbandIfOutOfBounds", "bound", "isEqual", "useIsomorphicLayoutEffect", "measureCSSLength", "supportsPassive", "classNames", "classPrefix", "Wheel", "props", "value", "column", "renderLabel", "onSelect", "val", "index", "y", "api", "from", "config", "tension", "mass", "draggingRef", "rootRef", "itemHeightMeasureRef", "itemHeight", "itemHeightMeasure", "current", "window", "getComputedStyle", "getPropertyValue", "targetIndex", "findIndex", "item", "finalPosition", "start", "immediate", "goal", "length", "some", "firstItem", "scrollSelect", "handleGestureState", "state", "direction", "distance", "velocity", "offset", "last", "handleDrag", "min", "max", "position", "boundNum", "Math", "round", "handleWheel", "whellDir", "scrollY", "get", "speed", "event", "stopPropagation", "axis", "filterTaps", "pointer", "touch", "target", "mouseWheel", "undefined", "preventDefault", "eventOptions", "passive", "selectedIndex", "renderAccessible", "previousIndex", "nextIndex", "previous", "next", "createElement", "className", "role", "label", "onClick", "ref", "div", "style", "translateY", "map", "_a", "selected", "handleClick", "key", "prev", "displayName"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/picker-view/wheel.js"], "sourcesContent": ["import React, { memo, useRef } from 'react';\nimport { useSpring, animated } from '@react-spring/web';\nimport { useDrag, useWheel } from '@use-gesture/react';\nimport { rubberbandIfOutOfBounds } from '../../utils/rubberband';\nimport { bound } from '../../utils/bound';\nimport isEqual from 'react-fast-compare';\nimport { useIsomorphicLayoutEffect } from 'ahooks';\nimport { measureCSSLength } from '../../utils/measure-css-length';\nimport { supportsPassive } from '../../utils/supports-passive';\nimport classNames from 'classnames';\nconst classPrefix = `adm-picker-view`;\nexport const Wheel = memo(props => {\n  const {\n    value,\n    column,\n    renderLabel\n  } = props;\n  function onSelect(val) {\n    props.onSelect(val, props.index);\n  }\n  const [{\n    y\n  }, api] = useSpring(() => ({\n    from: {\n      y: 0\n    },\n    config: {\n      tension: 400,\n      mass: 0.8\n    }\n  }));\n  const draggingRef = useRef(false);\n  const rootRef = useRef(null);\n  const itemHeightMeasureRef = useRef(null);\n  const itemHeight = useRef(34);\n  useIsomorphicLayoutEffect(() => {\n    const itemHeightMeasure = itemHeightMeasureRef.current;\n    if (!itemHeightMeasure) return;\n    itemHeight.current = measureCSSLength(window.getComputedStyle(itemHeightMeasure).getPropertyValue('height'));\n  });\n  useIsomorphicLayoutEffect(() => {\n    if (draggingRef.current) return;\n    if (value === null) return;\n    const targetIndex = column.findIndex(item => item.value === value);\n    if (targetIndex < 0) return;\n    const finalPosition = targetIndex * -itemHeight.current;\n    api.start({\n      y: finalPosition,\n      immediate: y.goal !== finalPosition\n    });\n  }, [value, column]);\n  useIsomorphicLayoutEffect(() => {\n    if (column.length === 0) {\n      if (value !== null) {\n        onSelect(null);\n      }\n    } else {\n      if (!column.some(item => item.value === value)) {\n        const firstItem = column[0];\n        onSelect(firstItem.value);\n      }\n    }\n  }, [column, value]);\n  function scrollSelect(index) {\n    const finalPosition = index * -itemHeight.current;\n    api.start({\n      y: finalPosition\n    });\n    const item = column[index];\n    if (!item) return;\n    onSelect(item.value);\n  }\n  const handleGestureState = state => {\n    const {\n      direction: [, direction],\n      distance: [, distance],\n      velocity: [, velocity],\n      offset: [, offset],\n      last\n    } = state;\n    return {\n      direction,\n      distance,\n      velocity,\n      offset,\n      last\n    };\n  };\n  const handleDrag = state => {\n    draggingRef.current = true;\n    const min = -((column.length - 1) * itemHeight.current);\n    const max = 0;\n    const {\n      direction,\n      last,\n      velocity,\n      offset\n    } = handleGestureState(state);\n    if (last) {\n      draggingRef.current = false;\n      const position = offset + velocity * direction * 50;\n      const boundNum = bound(position, min, max);\n      const targetIndex = -Math.round(boundNum / itemHeight.current);\n      scrollSelect(targetIndex);\n    } else {\n      const position = offset;\n      api.start({\n        y: rubberbandIfOutOfBounds(position, min, max, itemHeight.current * 50, 0.2)\n      });\n    }\n  };\n  const handleWheel = state => {\n    draggingRef.current = true;\n    const min = -((column.length - 1) * itemHeight.current);\n    const max = 0;\n    const {\n      direction,\n      last,\n      velocity,\n      distance\n    } = handleGestureState(state);\n    const whellDir = -direction; // 取反\n    const scrollY = y.get();\n    if (last) {\n      draggingRef.current = false;\n      const speed = velocity * whellDir * 50;\n      const position = scrollY + distance * whellDir + speed;\n      const boundNum = bound(position, min, max);\n      const targetIndex = -Math.round(boundNum / itemHeight.current);\n      scrollSelect(targetIndex);\n    } else {\n      const position = scrollY + distance * whellDir;\n      api.start({\n        y: rubberbandIfOutOfBounds(position, min, max, itemHeight.current * 50, 0.2)\n      });\n    }\n  };\n  useDrag(state => {\n    state.event.stopPropagation();\n    handleDrag(state);\n  }, {\n    axis: 'y',\n    from: () => [0, y.get()],\n    filterTaps: true,\n    pointer: {\n      touch: true\n    },\n    target: rootRef\n  });\n  useWheel(state => {\n    state.event.stopPropagation();\n    handleWheel(state);\n  }, {\n    target: props.mouseWheel ? rootRef : undefined,\n    axis: 'y',\n    from: () => [0, y.get()],\n    preventDefault: true,\n    eventOptions: supportsPassive ? {\n      passive: false\n    } : undefined\n  });\n  let selectedIndex = null;\n  function renderAccessible() {\n    if (selectedIndex === null) {\n      return null;\n    }\n    const current = column[selectedIndex];\n    const previousIndex = selectedIndex - 1;\n    const nextIndex = selectedIndex + 1;\n    const previous = column[previousIndex];\n    const next = column[nextIndex];\n    return React.createElement(\"div\", {\n      className: `${classPrefix}-column-accessible`\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-column-accessible-current`,\n      role: 'button',\n      \"aria-label\": current ? `当前选择的是：${current.label}` : '当前未选择'\n    }, \"-\"), React.createElement(\"div\", {\n      className: `${classPrefix}-column-accessible-button`,\n      onClick: () => {\n        if (!previous) return;\n        scrollSelect(previousIndex);\n      },\n      role: previous ? 'button' : 'text',\n      \"aria-label\": !previous ? '没有上一项' : `选择上一项：${previous.label}`\n    }, \"-\"), React.createElement(\"div\", {\n      className: `${classPrefix}-column-accessible-button`,\n      onClick: () => {\n        if (!next) return;\n        scrollSelect(nextIndex);\n      },\n      role: next ? 'button' : 'text',\n      \"aria-label\": !next ? '没有下一项' : `选择下一项：${next.label}`\n    }, \"-\"));\n  }\n  return React.createElement(\"div\", {\n    className: `${classPrefix}-column`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-item-height-measure`,\n    ref: itemHeightMeasureRef\n  }), React.createElement(animated.div, {\n    ref: rootRef,\n    style: {\n      translateY: y\n    },\n    className: `${classPrefix}-column-wheel`,\n    \"aria-hidden\": true\n  }, column.map((item, index) => {\n    var _a;\n    const selected = props.value === item.value;\n    if (selected) selectedIndex = index;\n    function handleClick() {\n      draggingRef.current = false;\n      scrollSelect(index);\n    }\n    return React.createElement(\"div\", {\n      key: (_a = item.key) !== null && _a !== void 0 ? _a : item.value,\n      \"data-selected\": selected,\n      className: classNames(`${classPrefix}-column-item`, {\n        [`${classPrefix}-column-item-active`]: selected\n      }),\n      onClick: handleClick,\n      \"aria-hidden\": !selected,\n      \"aria-label\": selected ? 'active' : ''\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-column-item-label`\n    }, renderLabel(item)));\n  })), renderAccessible());\n}, (prev, next) => {\n  if (prev.index !== next.index) return false;\n  if (prev.value !== next.value) return false;\n  if (prev.onSelect !== next.onSelect) return false;\n  if (prev.renderLabel !== next.renderLabel) return false;\n  if (prev.mouseWheel !== next.mouseWheel) return false;\n  if (!isEqual(prev.column, next.column)) return false;\n  return true;\n});\nWheel.displayName = 'Wheel';"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,IAAI,EAAEC,MAAM,QAAQ,OAAO;AAC3C,SAASC,SAAS,EAAEC,QAAQ,QAAQ,mBAAmB;AACvD,SAASC,OAAO,EAAEC,QAAQ,QAAQ,oBAAoB;AACtD,SAASC,uBAAuB,QAAQ,wBAAwB;AAChE,SAASC,KAAK,QAAQ,mBAAmB;AACzC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,yBAAyB,QAAQ,QAAQ;AAClD,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,OAAOC,UAAU,MAAM,YAAY;AACnC,MAAMC,WAAW,GAAG,iBAAiB;AACrC,OAAO,MAAMC,KAAK,GAAGd,IAAI,CAACe,KAAK,IAAI;EACjC,MAAM;IACJC,KAAK;IACLC,MAAM;IACNC;EACF,CAAC,GAAGH,KAAK;EACT,SAASI,QAAQA,CAACC,GAAG,EAAE;IACrBL,KAAK,CAACI,QAAQ,CAACC,GAAG,EAAEL,KAAK,CAACM,KAAK,CAAC;EAClC;EACA,MAAM,CAAC;IACLC;EACF,CAAC,EAAEC,GAAG,CAAC,GAAGrB,SAAS,CAAC,OAAO;IACzBsB,IAAI,EAAE;MACJF,CAAC,EAAE;IACL,CAAC;IACDG,MAAM,EAAE;MACNC,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR;EACF,CAAC,CAAC,CAAC;EACH,MAAMC,WAAW,GAAG3B,MAAM,CAAC,KAAK,CAAC;EACjC,MAAM4B,OAAO,GAAG5B,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAM6B,oBAAoB,GAAG7B,MAAM,CAAC,IAAI,CAAC;EACzC,MAAM8B,UAAU,GAAG9B,MAAM,CAAC,EAAE,CAAC;EAC7BQ,yBAAyB,CAAC,MAAM;IAC9B,MAAMuB,iBAAiB,GAAGF,oBAAoB,CAACG,OAAO;IACtD,IAAI,CAACD,iBAAiB,EAAE;IACxBD,UAAU,CAACE,OAAO,GAAGvB,gBAAgB,CAACwB,MAAM,CAACC,gBAAgB,CAACH,iBAAiB,CAAC,CAACI,gBAAgB,CAAC,QAAQ,CAAC,CAAC;EAC9G,CAAC,CAAC;EACF3B,yBAAyB,CAAC,MAAM;IAC9B,IAAImB,WAAW,CAACK,OAAO,EAAE;IACzB,IAAIjB,KAAK,KAAK,IAAI,EAAE;IACpB,MAAMqB,WAAW,GAAGpB,MAAM,CAACqB,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACvB,KAAK,KAAKA,KAAK,CAAC;IAClE,IAAIqB,WAAW,GAAG,CAAC,EAAE;IACrB,MAAMG,aAAa,GAAGH,WAAW,GAAG,CAACN,UAAU,CAACE,OAAO;IACvDV,GAAG,CAACkB,KAAK,CAAC;MACRnB,CAAC,EAAEkB,aAAa;MAChBE,SAAS,EAAEpB,CAAC,CAACqB,IAAI,KAAKH;IACxB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACxB,KAAK,EAAEC,MAAM,CAAC,CAAC;EACnBR,yBAAyB,CAAC,MAAM;IAC9B,IAAIQ,MAAM,CAAC2B,MAAM,KAAK,CAAC,EAAE;MACvB,IAAI5B,KAAK,KAAK,IAAI,EAAE;QAClBG,QAAQ,CAAC,IAAI,CAAC;MAChB;IACF,CAAC,MAAM;MACL,IAAI,CAACF,MAAM,CAAC4B,IAAI,CAACN,IAAI,IAAIA,IAAI,CAACvB,KAAK,KAAKA,KAAK,CAAC,EAAE;QAC9C,MAAM8B,SAAS,GAAG7B,MAAM,CAAC,CAAC,CAAC;QAC3BE,QAAQ,CAAC2B,SAAS,CAAC9B,KAAK,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAACC,MAAM,EAAED,KAAK,CAAC,CAAC;EACnB,SAAS+B,YAAYA,CAAC1B,KAAK,EAAE;IAC3B,MAAMmB,aAAa,GAAGnB,KAAK,GAAG,CAACU,UAAU,CAACE,OAAO;IACjDV,GAAG,CAACkB,KAAK,CAAC;MACRnB,CAAC,EAAEkB;IACL,CAAC,CAAC;IACF,MAAMD,IAAI,GAAGtB,MAAM,CAACI,KAAK,CAAC;IAC1B,IAAI,CAACkB,IAAI,EAAE;IACXpB,QAAQ,CAACoB,IAAI,CAACvB,KAAK,CAAC;EACtB;EACA,MAAMgC,kBAAkB,GAAGC,KAAK,IAAI;IAClC,MAAM;MACJC,SAAS,EAAE,GAAGA,SAAS,CAAC;MACxBC,QAAQ,EAAE,GAAGA,QAAQ,CAAC;MACtBC,QAAQ,EAAE,GAAGA,QAAQ,CAAC;MACtBC,MAAM,EAAE,GAAGA,MAAM,CAAC;MAClBC;IACF,CAAC,GAAGL,KAAK;IACT,OAAO;MACLC,SAAS;MACTC,QAAQ;MACRC,QAAQ;MACRC,MAAM;MACNC;IACF,CAAC;EACH,CAAC;EACD,MAAMC,UAAU,GAAGN,KAAK,IAAI;IAC1BrB,WAAW,CAACK,OAAO,GAAG,IAAI;IAC1B,MAAMuB,GAAG,GAAG,EAAE,CAACvC,MAAM,CAAC2B,MAAM,GAAG,CAAC,IAAIb,UAAU,CAACE,OAAO,CAAC;IACvD,MAAMwB,GAAG,GAAG,CAAC;IACb,MAAM;MACJP,SAAS;MACTI,IAAI;MACJF,QAAQ;MACRC;IACF,CAAC,GAAGL,kBAAkB,CAACC,KAAK,CAAC;IAC7B,IAAIK,IAAI,EAAE;MACR1B,WAAW,CAACK,OAAO,GAAG,KAAK;MAC3B,MAAMyB,QAAQ,GAAGL,MAAM,GAAGD,QAAQ,GAAGF,SAAS,GAAG,EAAE;MACnD,MAAMS,QAAQ,GAAGpD,KAAK,CAACmD,QAAQ,EAAEF,GAAG,EAAEC,GAAG,CAAC;MAC1C,MAAMpB,WAAW,GAAG,CAACuB,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG5B,UAAU,CAACE,OAAO,CAAC;MAC9Dc,YAAY,CAACV,WAAW,CAAC;IAC3B,CAAC,MAAM;MACL,MAAMqB,QAAQ,GAAGL,MAAM;MACvB9B,GAAG,CAACkB,KAAK,CAAC;QACRnB,CAAC,EAAEhB,uBAAuB,CAACoD,QAAQ,EAAEF,GAAG,EAAEC,GAAG,EAAE1B,UAAU,CAACE,OAAO,GAAG,EAAE,EAAE,GAAG;MAC7E,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAM6B,WAAW,GAAGb,KAAK,IAAI;IAC3BrB,WAAW,CAACK,OAAO,GAAG,IAAI;IAC1B,MAAMuB,GAAG,GAAG,EAAE,CAACvC,MAAM,CAAC2B,MAAM,GAAG,CAAC,IAAIb,UAAU,CAACE,OAAO,CAAC;IACvD,MAAMwB,GAAG,GAAG,CAAC;IACb,MAAM;MACJP,SAAS;MACTI,IAAI;MACJF,QAAQ;MACRD;IACF,CAAC,GAAGH,kBAAkB,CAACC,KAAK,CAAC;IAC7B,MAAMc,QAAQ,GAAG,CAACb,SAAS,CAAC,CAAC;IAC7B,MAAMc,OAAO,GAAG1C,CAAC,CAAC2C,GAAG,CAAC,CAAC;IACvB,IAAIX,IAAI,EAAE;MACR1B,WAAW,CAACK,OAAO,GAAG,KAAK;MAC3B,MAAMiC,KAAK,GAAGd,QAAQ,GAAGW,QAAQ,GAAG,EAAE;MACtC,MAAML,QAAQ,GAAGM,OAAO,GAAGb,QAAQ,GAAGY,QAAQ,GAAGG,KAAK;MACtD,MAAMP,QAAQ,GAAGpD,KAAK,CAACmD,QAAQ,EAAEF,GAAG,EAAEC,GAAG,CAAC;MAC1C,MAAMpB,WAAW,GAAG,CAACuB,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG5B,UAAU,CAACE,OAAO,CAAC;MAC9Dc,YAAY,CAACV,WAAW,CAAC;IAC3B,CAAC,MAAM;MACL,MAAMqB,QAAQ,GAAGM,OAAO,GAAGb,QAAQ,GAAGY,QAAQ;MAC9CxC,GAAG,CAACkB,KAAK,CAAC;QACRnB,CAAC,EAAEhB,uBAAuB,CAACoD,QAAQ,EAAEF,GAAG,EAAEC,GAAG,EAAE1B,UAAU,CAACE,OAAO,GAAG,EAAE,EAAE,GAAG;MAC7E,CAAC,CAAC;IACJ;EACF,CAAC;EACD7B,OAAO,CAAC6C,KAAK,IAAI;IACfA,KAAK,CAACkB,KAAK,CAACC,eAAe,CAAC,CAAC;IAC7Bb,UAAU,CAACN,KAAK,CAAC;EACnB,CAAC,EAAE;IACDoB,IAAI,EAAE,GAAG;IACT7C,IAAI,EAAEA,CAAA,KAAM,CAAC,CAAC,EAAEF,CAAC,CAAC2C,GAAG,CAAC,CAAC,CAAC;IACxBK,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAE5C;EACV,CAAC,CAAC;EACFxB,QAAQ,CAAC4C,KAAK,IAAI;IAChBA,KAAK,CAACkB,KAAK,CAACC,eAAe,CAAC,CAAC;IAC7BN,WAAW,CAACb,KAAK,CAAC;EACpB,CAAC,EAAE;IACDwB,MAAM,EAAE1D,KAAK,CAAC2D,UAAU,GAAG7C,OAAO,GAAG8C,SAAS;IAC9CN,IAAI,EAAE,GAAG;IACT7C,IAAI,EAAEA,CAAA,KAAM,CAAC,CAAC,EAAEF,CAAC,CAAC2C,GAAG,CAAC,CAAC,CAAC;IACxBW,cAAc,EAAE,IAAI;IACpBC,YAAY,EAAElE,eAAe,GAAG;MAC9BmE,OAAO,EAAE;IACX,CAAC,GAAGH;EACN,CAAC,CAAC;EACF,IAAII,aAAa,GAAG,IAAI;EACxB,SAASC,gBAAgBA,CAAA,EAAG;IAC1B,IAAID,aAAa,KAAK,IAAI,EAAE;MAC1B,OAAO,IAAI;IACb;IACA,MAAM9C,OAAO,GAAGhB,MAAM,CAAC8D,aAAa,CAAC;IACrC,MAAME,aAAa,GAAGF,aAAa,GAAG,CAAC;IACvC,MAAMG,SAAS,GAAGH,aAAa,GAAG,CAAC;IACnC,MAAMI,QAAQ,GAAGlE,MAAM,CAACgE,aAAa,CAAC;IACtC,MAAMG,IAAI,GAAGnE,MAAM,CAACiE,SAAS,CAAC;IAC9B,OAAOnF,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;MAChCC,SAAS,EAAE,GAAGzE,WAAW;IAC3B,CAAC,EAAEd,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;MAC5BC,SAAS,EAAE,GAAGzE,WAAW,4BAA4B;MACrD0E,IAAI,EAAE,QAAQ;MACd,YAAY,EAAEtD,OAAO,GAAG,UAAUA,OAAO,CAACuD,KAAK,EAAE,GAAG;IACtD,CAAC,EAAE,GAAG,CAAC,EAAEzF,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;MAClCC,SAAS,EAAE,GAAGzE,WAAW,2BAA2B;MACpD4E,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,CAACN,QAAQ,EAAE;QACfpC,YAAY,CAACkC,aAAa,CAAC;MAC7B,CAAC;MACDM,IAAI,EAAEJ,QAAQ,GAAG,QAAQ,GAAG,MAAM;MAClC,YAAY,EAAE,CAACA,QAAQ,GAAG,OAAO,GAAG,SAASA,QAAQ,CAACK,KAAK;IAC7D,CAAC,EAAE,GAAG,CAAC,EAAEzF,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;MAClCC,SAAS,EAAE,GAAGzE,WAAW,2BAA2B;MACpD4E,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,CAACL,IAAI,EAAE;QACXrC,YAAY,CAACmC,SAAS,CAAC;MACzB,CAAC;MACDK,IAAI,EAAEH,IAAI,GAAG,QAAQ,GAAG,MAAM;MAC9B,YAAY,EAAE,CAACA,IAAI,GAAG,OAAO,GAAG,SAASA,IAAI,CAACI,KAAK;IACrD,CAAC,EAAE,GAAG,CAAC,CAAC;EACV;EACA,OAAOzF,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;IAChCC,SAAS,EAAE,GAAGzE,WAAW;EAC3B,CAAC,EAAEd,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGzE,WAAW,sBAAsB;IAC/C6E,GAAG,EAAE5D;EACP,CAAC,CAAC,EAAE/B,KAAK,CAACsF,aAAa,CAAClF,QAAQ,CAACwF,GAAG,EAAE;IACpCD,GAAG,EAAE7D,OAAO;IACZ+D,KAAK,EAAE;MACLC,UAAU,EAAEvE;IACd,CAAC;IACDgE,SAAS,EAAE,GAAGzE,WAAW,eAAe;IACxC,aAAa,EAAE;EACjB,CAAC,EAAEI,MAAM,CAAC6E,GAAG,CAAC,CAACvD,IAAI,EAAElB,KAAK,KAAK;IAC7B,IAAI0E,EAAE;IACN,MAAMC,QAAQ,GAAGjF,KAAK,CAACC,KAAK,KAAKuB,IAAI,CAACvB,KAAK;IAC3C,IAAIgF,QAAQ,EAAEjB,aAAa,GAAG1D,KAAK;IACnC,SAAS4E,WAAWA,CAAA,EAAG;MACrBrE,WAAW,CAACK,OAAO,GAAG,KAAK;MAC3Bc,YAAY,CAAC1B,KAAK,CAAC;IACrB;IACA,OAAOtB,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;MAChCa,GAAG,EAAE,CAACH,EAAE,GAAGxD,IAAI,CAAC2D,GAAG,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGxD,IAAI,CAACvB,KAAK;MAChE,eAAe,EAAEgF,QAAQ;MACzBV,SAAS,EAAE1E,UAAU,CAAC,GAAGC,WAAW,cAAc,EAAE;QAClD,CAAC,GAAGA,WAAW,qBAAqB,GAAGmF;MACzC,CAAC,CAAC;MACFP,OAAO,EAAEQ,WAAW;MACpB,aAAa,EAAE,CAACD,QAAQ;MACxB,YAAY,EAAEA,QAAQ,GAAG,QAAQ,GAAG;IACtC,CAAC,EAAEjG,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;MAC5BC,SAAS,EAAE,GAAGzE,WAAW;IAC3B,CAAC,EAAEK,WAAW,CAACqB,IAAI,CAAC,CAAC,CAAC;EACxB,CAAC,CAAC,CAAC,EAAEyC,gBAAgB,CAAC,CAAC,CAAC;AAC1B,CAAC,EAAE,CAACmB,IAAI,EAAEf,IAAI,KAAK;EACjB,IAAIe,IAAI,CAAC9E,KAAK,KAAK+D,IAAI,CAAC/D,KAAK,EAAE,OAAO,KAAK;EAC3C,IAAI8E,IAAI,CAACnF,KAAK,KAAKoE,IAAI,CAACpE,KAAK,EAAE,OAAO,KAAK;EAC3C,IAAImF,IAAI,CAAChF,QAAQ,KAAKiE,IAAI,CAACjE,QAAQ,EAAE,OAAO,KAAK;EACjD,IAAIgF,IAAI,CAACjF,WAAW,KAAKkE,IAAI,CAAClE,WAAW,EAAE,OAAO,KAAK;EACvD,IAAIiF,IAAI,CAACzB,UAAU,KAAKU,IAAI,CAACV,UAAU,EAAE,OAAO,KAAK;EACrD,IAAI,CAAClE,OAAO,CAAC2F,IAAI,CAAClF,MAAM,EAAEmE,IAAI,CAACnE,MAAM,CAAC,EAAE,OAAO,KAAK;EACpD,OAAO,IAAI;AACb,CAAC,CAAC;AACFH,KAAK,CAACsF,WAAW,GAAG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}