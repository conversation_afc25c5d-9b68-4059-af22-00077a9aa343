{"ast": null, "code": "import React, { forwardRef, useImperativeHandle, useRef } from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = `adm-list`;\nconst defaultProps = {\n  mode: 'default'\n};\nexport const List = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const nativeElementRef = useRef(null);\n  useImperativeHandle(ref, () => ({\n    get nativeElement() {\n      return nativeElementRef.current;\n    }\n  }));\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, `${classPrefix}-${props.mode}`),\n    ref: nativeElementRef\n  }, props.header && React.createElement(\"div\", {\n    className: `${classPrefix}-header`\n  }, props.header), React.createElement(\"div\", {\n    className: `${classPrefix}-body`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-body-inner`\n  }, props.children))));\n});", "map": {"version": 3, "names": ["React", "forwardRef", "useImperativeHandle", "useRef", "classNames", "withNativeProps", "mergeProps", "classPrefix", "defaultProps", "mode", "List", "p", "ref", "props", "nativeElementRef", "nativeElement", "current", "createElement", "className", "header", "children"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/list/list.js"], "sourcesContent": ["import React, { forwardRef, useImperativeHandle, useRef } from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = `adm-list`;\nconst defaultProps = {\n  mode: 'default'\n};\nexport const List = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const nativeElementRef = useRef(null);\n  useImperativeHandle(ref, () => ({\n    get nativeElement() {\n      return nativeElementRef.current;\n    }\n  }));\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, `${classPrefix}-${props.mode}`),\n    ref: nativeElementRef\n  }, props.header && React.createElement(\"div\", {\n    className: `${classPrefix}-header`\n  }, props.header), React.createElement(\"div\", {\n    className: `${classPrefix}-body`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-body-inner`\n  }, props.children))));\n});"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,mBAAmB,EAAEC,MAAM,QAAQ,OAAO;AACtE,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,MAAMC,WAAW,GAAG,UAAU;AAC9B,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE;AACR,CAAC;AACD,OAAO,MAAMC,IAAI,GAAGT,UAAU,CAAC,CAACU,CAAC,EAAEC,GAAG,KAAK;EACzC,MAAMC,KAAK,GAAGP,UAAU,CAACE,YAAY,EAAEG,CAAC,CAAC;EACzC,MAAMG,gBAAgB,GAAGX,MAAM,CAAC,IAAI,CAAC;EACrCD,mBAAmB,CAACU,GAAG,EAAE,OAAO;IAC9B,IAAIG,aAAaA,CAAA,EAAG;MAClB,OAAOD,gBAAgB,CAACE,OAAO;IACjC;EACF,CAAC,CAAC,CAAC;EACH,OAAOX,eAAe,CAACQ,KAAK,EAAEb,KAAK,CAACiB,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEd,UAAU,CAACG,WAAW,EAAE,GAAGA,WAAW,IAAIM,KAAK,CAACJ,IAAI,EAAE,CAAC;IAClEG,GAAG,EAAEE;EACP,CAAC,EAAED,KAAK,CAACM,MAAM,IAAInB,KAAK,CAACiB,aAAa,CAAC,KAAK,EAAE;IAC5CC,SAAS,EAAE,GAAGX,WAAW;EAC3B,CAAC,EAAEM,KAAK,CAACM,MAAM,CAAC,EAAEnB,KAAK,CAACiB,aAAa,CAAC,KAAK,EAAE;IAC3CC,SAAS,EAAE,GAAGX,WAAW;EAC3B,CAAC,EAAEP,KAAK,CAACiB,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGX,WAAW;EAC3B,CAAC,EAAEM,KAAK,CAACO,QAAQ,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}