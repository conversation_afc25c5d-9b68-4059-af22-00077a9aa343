{"ast": null, "code": "import * as React from \"react\";\nfunction FingerdownOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FingerdownOutline-FingerdownOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FingerdownOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"FingerdownOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37,16 L36.9761903,15.9220279 C36.9448377,15.6019116 36.8826531,15.2854091 36.7904169,14.9769518 L36.6989671,14.7006845 L35.128,10.384 L33,4 L36,4 L39.5183474,13.6754553 C39.8369955,14.5517376 40,15.4769797 40,16.4093998 L40,25.5 C40,27.9852814 37.9852814,30 35.5,30 C34.9740145,30 34.4691063,29.9097576 33.9999135,29.7439108 L34,39.5 C34,41.9852814 31.9852814,44 29.5,44 C27.0147186,44 25,41.9852814 25,39.5 L25.0010919,35.7435553 C24.531615,35.9096286 24.0263612,36 23.5,36 C22.0603912,36 20.7786726,35.3239913 19.9549664,34.272096 C19.2496449,34.732199 18.4060966,35 17.5,35 C15.8064788,35 14.3314557,34.0644984 13.5635115,32.6820758 C13.0841403,32.8868523 12.5552837,33 12,33 C9.790861,33 8,31.209139 8,29 L8,15.3520722 C8,14.5879654 8.10947076,13.8277994 8.32507599,13.0947416 L11,4 L14,4 L12.383,9.927 L11.2031725,13.9412406 C11.1083707,14.2635666 11.0464005,14.5942783 11.0179878,14.9283337 L11,15 L11,29 C11,29.5522847 11.4477153,30 12,30 C12.5128358,30 12.9355072,29.6139598 12.9932723,29.1166211 L13,29 L13,28.4 C13,28.1790861 13.1790861,28 13.4,28 L15.6,28 C15.8209139,28 16,28.1790861 16,28.4 L16,30.5 L16,30.5 C16,31.3284271 16.6715729,32 17.5,32 C18.2796961,32 18.9204487,31.4051119 18.9931334,30.64446 L19,30.5 L19,28.4 C19,28.1790861 19.1790861,28 19.4,28 L21.6,28 C21.8209139,28 22,28.1790861 22,28.4 L22,31.5 L22,31.5 C22,32.3284271 22.6715729,33 23.5,33 C24.2796961,33 24.9204487,32.4051119 24.9931334,31.64446 L25,31.5 L25,28.4 C25,28.1790861 25.1790861,28 25.4,28 L27.6,28 C27.8209139,28 28,28.1790861 28,28.4 L28,39.5 L28,39.5 C28,40.2796961 28.5948881,40.9204487 29.35554,40.9931334 L29.5,41 C30.2796961,41 30.9204487,40.4051119 30.9931334,39.64446 L31,39.5 L31,20.4 C31,20.1790861 31.1790861,20 31.4,20 L33.6,20 C33.8209139,20 34,20.1790861 34,20.4 L34,25.5 L34,25.5 C34,26.3284271 34.6715729,27 35.5,27 C36.2796961,27 36.9204487,26.4051119 36.9931334,25.64446 L37,25.5 L37,16 Z\",\n    id: \"FingerdownOutline-\\u8DEF\\u5F84\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default FingerdownOutline;", "map": {"version": 3, "names": ["React", "FingerdownOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/FingerdownOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction FingerdownOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FingerdownOutline-FingerdownOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FingerdownOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"FingerdownOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37,16 L36.9761903,15.9220279 C36.9448377,15.6019116 36.8826531,15.2854091 36.7904169,14.9769518 L36.6989671,14.7006845 L35.128,10.384 L33,4 L36,4 L39.5183474,13.6754553 C39.8369955,14.5517376 40,15.4769797 40,16.4093998 L40,25.5 C40,27.9852814 37.9852814,30 35.5,30 C34.9740145,30 34.4691063,29.9097576 33.9999135,29.7439108 L34,39.5 C34,41.9852814 31.9852814,44 29.5,44 C27.0147186,44 25,41.9852814 25,39.5 L25.0010919,35.7435553 C24.531615,35.9096286 24.0263612,36 23.5,36 C22.0603912,36 20.7786726,35.3239913 19.9549664,34.272096 C19.2496449,34.732199 18.4060966,35 17.5,35 C15.8064788,35 14.3314557,34.0644984 13.5635115,32.6820758 C13.0841403,32.8868523 12.5552837,33 12,33 C9.790861,33 8,31.209139 8,29 L8,15.3520722 C8,14.5879654 8.10947076,13.8277994 8.32507599,13.0947416 L11,4 L14,4 L12.383,9.927 L11.2031725,13.9412406 C11.1083707,14.2635666 11.0464005,14.5942783 11.0179878,14.9283337 L11,15 L11,29 C11,29.5522847 11.4477153,30 12,30 C12.5128358,30 12.9355072,29.6139598 12.9932723,29.1166211 L13,29 L13,28.4 C13,28.1790861 13.1790861,28 13.4,28 L15.6,28 C15.8209139,28 16,28.1790861 16,28.4 L16,30.5 L16,30.5 C16,31.3284271 16.6715729,32 17.5,32 C18.2796961,32 18.9204487,31.4051119 18.9931334,30.64446 L19,30.5 L19,28.4 C19,28.1790861 19.1790861,28 19.4,28 L21.6,28 C21.8209139,28 22,28.1790861 22,28.4 L22,31.5 L22,31.5 C22,32.3284271 22.6715729,33 23.5,33 C24.2796961,33 24.9204487,32.4051119 24.9931334,31.64446 L25,31.5 L25,28.4 C25,28.1790861 25.1790861,28 25.4,28 L27.6,28 C27.8209139,28 28,28.1790861 28,28.4 L28,39.5 L28,39.5 C28,40.2796961 28.5948881,40.9204487 29.35554,40.9931334 L29.5,41 C30.2796961,41 30.9204487,40.4051119 30.9931334,39.64446 L31,39.5 L31,20.4 C31,20.1790861 31.1790861,20 31.4,20 L33.6,20 C33.8209139,20 34,20.1790861 34,20.4 L34,25.5 L34,25.5 C34,26.3284271 34.6715729,27 35.5,27 C36.2796961,27 36.9204487,26.4051119 36.9931334,25.64446 L37,25.5 L37,16 Z\",\n    id: \"FingerdownOutline-\\u8DEF\\u5F84\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default FingerdownOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EAChC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,qCAAqC;IACzCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,gCAAgC;IACpCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,g3DAAg3D;IACn3DR,EAAE,EAAE,gCAAgC;IACpCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}