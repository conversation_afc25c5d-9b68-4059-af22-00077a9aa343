{"ast": null, "code": "import * as dateUtils from './date-picker-date-utils';\nimport * as quarterUtils from './date-picker-quarter-utils';\nimport * as weekUtils from './date-picker-week-utils';\nimport { TILL_NOW } from './util';\nconst precisionLengthRecord = {\n  year: 1,\n  month: 2,\n  day: 3,\n  hour: 4,\n  minute: 5,\n  second: 6\n};\nexport const convertDateToStringArray = (date, precision) => {\n  if (precision.includes('week')) {\n    return weekUtils.convertDateToStringArray(date);\n  } else if (precision.includes('quarter')) {\n    return quarterUtils.convertDateToStringArray(date);\n  } else {\n    const datePrecision = precision;\n    const stringArray = dateUtils.convertDateToStringArray(date);\n    return stringArray.slice(0, precisionLengthRecord[datePrecision]);\n  }\n};\nexport const convertStringArrayToDate = (value, precision) => {\n  // Special case for DATE_NOW\n  if ((value === null || value === void 0 ? void 0 : value[0]) === TILL_NOW) {\n    const now = new Date();\n    now.tillNow = true;\n    return now;\n  }\n  if (precision.includes('week')) {\n    return weekUtils.convertStringArrayToDate(value);\n  } else if (precision.includes('quarter')) {\n    return quarterUtils.convertStringArrayToDate(value);\n  } else {\n    return dateUtils.convertStringArrayToDate(value);\n  }\n};\nexport const generateDatePickerColumns = (selected, min, max, precision, renderLabel, filter, tillNow) => {\n  if (precision.startsWith('week')) {\n    return weekUtils.generateDatePickerColumns(selected, min, max, precision, renderLabel, filter);\n  } else if (precision.startsWith('quarter')) {\n    return quarterUtils.generateDatePickerColumns(selected, min, max, precision, renderLabel, filter);\n  } else {\n    return dateUtils.generateDatePickerColumns(selected, min, max, precision, renderLabel, filter, tillNow);\n  }\n};", "map": {"version": 3, "names": ["dateUtils", "quarterUtils", "weekUtils", "TILL_NOW", "precisionLengthRecord", "year", "month", "day", "hour", "minute", "second", "convertDateToStringArray", "date", "precision", "includes", "datePrecision", "stringArray", "slice", "convertStringArrayToDate", "value", "now", "Date", "tillNow", "generateDatePickerColumns", "selected", "min", "max", "renderLabel", "filter", "startsWith"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/date-picker/date-picker-utils.js"], "sourcesContent": ["import * as dateUtils from './date-picker-date-utils';\nimport * as quarterUtils from './date-picker-quarter-utils';\nimport * as weekUtils from './date-picker-week-utils';\nimport { TILL_NOW } from './util';\nconst precisionLengthRecord = {\n  year: 1,\n  month: 2,\n  day: 3,\n  hour: 4,\n  minute: 5,\n  second: 6\n};\nexport const convertDateToStringArray = (date, precision) => {\n  if (precision.includes('week')) {\n    return weekUtils.convertDateToStringArray(date);\n  } else if (precision.includes('quarter')) {\n    return quarterUtils.convertDateToStringArray(date);\n  } else {\n    const datePrecision = precision;\n    const stringArray = dateUtils.convertDateToStringArray(date);\n    return stringArray.slice(0, precisionLengthRecord[datePrecision]);\n  }\n};\nexport const convertStringArrayToDate = (value, precision) => {\n  // Special case for DATE_NOW\n  if ((value === null || value === void 0 ? void 0 : value[0]) === TILL_NOW) {\n    const now = new Date();\n    now.tillNow = true;\n    return now;\n  }\n  if (precision.includes('week')) {\n    return weekUtils.convertStringArrayToDate(value);\n  } else if (precision.includes('quarter')) {\n    return quarterUtils.convertStringArrayToDate(value);\n  } else {\n    return dateUtils.convertStringArrayToDate(value);\n  }\n};\nexport const generateDatePickerColumns = (selected, min, max, precision, renderLabel, filter, tillNow) => {\n  if (precision.startsWith('week')) {\n    return weekUtils.generateDatePickerColumns(selected, min, max, precision, renderLabel, filter);\n  } else if (precision.startsWith('quarter')) {\n    return quarterUtils.generateDatePickerColumns(selected, min, max, precision, renderLabel, filter);\n  } else {\n    return dateUtils.generateDatePickerColumns(selected, min, max, precision, renderLabel, filter, tillNow);\n  }\n};"], "mappings": "AAAA,OAAO,KAAKA,SAAS,MAAM,0BAA0B;AACrD,OAAO,KAAKC,YAAY,MAAM,6BAA6B;AAC3D,OAAO,KAAKC,SAAS,MAAM,0BAA0B;AACrD,SAASC,QAAQ,QAAQ,QAAQ;AACjC,MAAMC,qBAAqB,GAAG;EAC5BC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE;AACV,CAAC;AACD,OAAO,MAAMC,wBAAwB,GAAGA,CAACC,IAAI,EAAEC,SAAS,KAAK;EAC3D,IAAIA,SAAS,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC9B,OAAOZ,SAAS,CAACS,wBAAwB,CAACC,IAAI,CAAC;EACjD,CAAC,MAAM,IAAIC,SAAS,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;IACxC,OAAOb,YAAY,CAACU,wBAAwB,CAACC,IAAI,CAAC;EACpD,CAAC,MAAM;IACL,MAAMG,aAAa,GAAGF,SAAS;IAC/B,MAAMG,WAAW,GAAGhB,SAAS,CAACW,wBAAwB,CAACC,IAAI,CAAC;IAC5D,OAAOI,WAAW,CAACC,KAAK,CAAC,CAAC,EAAEb,qBAAqB,CAACW,aAAa,CAAC,CAAC;EACnE;AACF,CAAC;AACD,OAAO,MAAMG,wBAAwB,GAAGA,CAACC,KAAK,EAAEN,SAAS,KAAK;EAC5D;EACA,IAAI,CAACM,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,MAAMhB,QAAQ,EAAE;IACzE,MAAMiB,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtBD,GAAG,CAACE,OAAO,GAAG,IAAI;IAClB,OAAOF,GAAG;EACZ;EACA,IAAIP,SAAS,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC9B,OAAOZ,SAAS,CAACgB,wBAAwB,CAACC,KAAK,CAAC;EAClD,CAAC,MAAM,IAAIN,SAAS,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;IACxC,OAAOb,YAAY,CAACiB,wBAAwB,CAACC,KAAK,CAAC;EACrD,CAAC,MAAM;IACL,OAAOnB,SAAS,CAACkB,wBAAwB,CAACC,KAAK,CAAC;EAClD;AACF,CAAC;AACD,OAAO,MAAMI,yBAAyB,GAAGA,CAACC,QAAQ,EAAEC,GAAG,EAAEC,GAAG,EAAEb,SAAS,EAAEc,WAAW,EAAEC,MAAM,EAAEN,OAAO,KAAK;EACxG,IAAIT,SAAS,CAACgB,UAAU,CAAC,MAAM,CAAC,EAAE;IAChC,OAAO3B,SAAS,CAACqB,yBAAyB,CAACC,QAAQ,EAAEC,GAAG,EAAEC,GAAG,EAAEb,SAAS,EAAEc,WAAW,EAAEC,MAAM,CAAC;EAChG,CAAC,MAAM,IAAIf,SAAS,CAACgB,UAAU,CAAC,SAAS,CAAC,EAAE;IAC1C,OAAO5B,YAAY,CAACsB,yBAAyB,CAACC,QAAQ,EAAEC,GAAG,EAAEC,GAAG,EAAEb,SAAS,EAAEc,WAAW,EAAEC,MAAM,CAAC;EACnG,CAAC,MAAM;IACL,OAAO5B,SAAS,CAACuB,yBAAyB,CAACC,QAAQ,EAAEC,GAAG,EAAEC,GAAG,EAAEb,SAAS,EAAEc,WAAW,EAAEC,MAAM,EAAEN,OAAO,CAAC;EACzG;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}