{"ast": null, "code": "import React, { isValidElement, useRef } from 'react';\nimport classNames from 'classnames';\nimport { animated } from '@react-spring/web';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { useResizeEffect } from '../../utils/use-resize-effect';\nimport { useTabListScroll } from '../../utils/use-tab-list-scroll';\nimport ScrollMask from '../scroll-mask';\nimport { ShouldRender } from '../../utils/should-render';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\nconst classPrefix = `adm-jumbo-tabs`;\nexport const JumboTab = () => {\n  return null;\n};\nexport const JumboTabs = props => {\n  var _a;\n  const tabListContainerRef = useRef(null);\n  const rootRef = useRef(null);\n  const keyToIndexRecord = {};\n  let firstActiveKey = null;\n  const panes = [];\n  traverseReactNode(props.children, (child, index) => {\n    if (!isValidElement(child)) return;\n    const key = child.key;\n    if (typeof key !== 'string') return;\n    if (index === 0) {\n      firstActiveKey = key;\n    }\n    const length = panes.push(child);\n    keyToIndexRecord[key] = length - 1;\n  });\n  const [activeKey, setActiveKey] = usePropsValue({\n    value: props.activeKey,\n    defaultValue: (_a = props.defaultActiveKey) !== null && _a !== void 0 ? _a : firstActiveKey,\n    onChange: v => {\n      var _a;\n      if (v === null) return;\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, v);\n    }\n  });\n  const {\n    scrollLeft,\n    animate\n  } = useTabListScroll(tabListContainerRef, keyToIndexRecord[activeKey]);\n  useResizeEffect(() => {\n    animate(true);\n  }, rootRef);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix,\n    ref: rootRef\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-header`\n  }, React.createElement(ScrollMask, {\n    scrollTrackRef: tabListContainerRef\n  }), React.createElement(animated.div, {\n    className: `${classPrefix}-tab-list`,\n    ref: tabListContainerRef,\n    scrollLeft: scrollLeft\n  }, panes.map(pane => withNativeProps(pane.props, React.createElement(\"div\", {\n    key: pane.key,\n    className: `${classPrefix}-tab-wrapper`\n  }, React.createElement(\"div\", {\n    onClick: () => {\n      const {\n        key\n      } = pane;\n      if (pane.props.disabled) return;\n      if (key === undefined || key === null) {\n        return;\n      }\n      setActiveKey(key.toString());\n    },\n    className: classNames(`${classPrefix}-tab`, {\n      [`${classPrefix}-tab-active`]: pane.key === activeKey,\n      [`${classPrefix}-tab-disabled`]: pane.props.disabled\n    })\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-tab-title`\n  }, pane.props.title), React.createElement(\"div\", {\n    className: `${classPrefix}-tab-description`\n  }, pane.props.description))))))), panes.map(pane => {\n    if (pane.props.children === undefined) {\n      return null;\n    }\n    const active = pane.key === activeKey;\n    return React.createElement(ShouldRender, {\n      key: pane.key,\n      active: active,\n      forceRender: pane.props.forceRender,\n      destroyOnClose: pane.props.destroyOnClose\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-content`,\n      style: {\n        display: active ? 'block' : 'none'\n      }\n    }, pane.props.children));\n  })));\n};", "map": {"version": 3, "names": ["React", "isValidElement", "useRef", "classNames", "animated", "withNativeProps", "usePropsValue", "useResizeEffect", "useTabListScroll", "ScrollMask", "ShouldRender", "traverseReactNode", "classPrefix", "JumboTab", "JumboTabs", "props", "_a", "tabListContainerRef", "rootRef", "keyToIndexRecord", "firstActiveKey", "panes", "children", "child", "index", "key", "length", "push", "active<PERSON><PERSON>", "setActiveKey", "value", "defaultValue", "defaultActiveKey", "onChange", "v", "call", "scrollLeft", "animate", "createElement", "className", "ref", "scrollTrackRef", "div", "map", "pane", "onClick", "disabled", "undefined", "toString", "title", "description", "active", "forceRender", "destroyOnClose", "style", "display"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/jumbo-tabs/jumbo-tabs.js"], "sourcesContent": ["import React, { isValidElement, useRef } from 'react';\nimport classNames from 'classnames';\nimport { animated } from '@react-spring/web';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { useResizeEffect } from '../../utils/use-resize-effect';\nimport { useTabListScroll } from '../../utils/use-tab-list-scroll';\nimport ScrollMask from '../scroll-mask';\nimport { ShouldRender } from '../../utils/should-render';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\nconst classPrefix = `adm-jumbo-tabs`;\nexport const JumboTab = () => {\n  return null;\n};\nexport const JumboTabs = props => {\n  var _a;\n  const tabListContainerRef = useRef(null);\n  const rootRef = useRef(null);\n  const keyToIndexRecord = {};\n  let firstActiveKey = null;\n  const panes = [];\n  traverseReactNode(props.children, (child, index) => {\n    if (!isValidElement(child)) return;\n    const key = child.key;\n    if (typeof key !== 'string') return;\n    if (index === 0) {\n      firstActiveKey = key;\n    }\n    const length = panes.push(child);\n    keyToIndexRecord[key] = length - 1;\n  });\n  const [activeKey, setActiveKey] = usePropsValue({\n    value: props.activeKey,\n    defaultValue: (_a = props.defaultActiveKey) !== null && _a !== void 0 ? _a : firstActiveKey,\n    onChange: v => {\n      var _a;\n      if (v === null) return;\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, v);\n    }\n  });\n  const {\n    scrollLeft,\n    animate\n  } = useTabListScroll(tabListContainerRef, keyToIndexRecord[activeKey]);\n  useResizeEffect(() => {\n    animate(true);\n  }, rootRef);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix,\n    ref: rootRef\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-header`\n  }, React.createElement(ScrollMask, {\n    scrollTrackRef: tabListContainerRef\n  }), React.createElement(animated.div, {\n    className: `${classPrefix}-tab-list`,\n    ref: tabListContainerRef,\n    scrollLeft: scrollLeft\n  }, panes.map(pane => withNativeProps(pane.props, React.createElement(\"div\", {\n    key: pane.key,\n    className: `${classPrefix}-tab-wrapper`\n  }, React.createElement(\"div\", {\n    onClick: () => {\n      const {\n        key\n      } = pane;\n      if (pane.props.disabled) return;\n      if (key === undefined || key === null) {\n        return;\n      }\n      setActiveKey(key.toString());\n    },\n    className: classNames(`${classPrefix}-tab`, {\n      [`${classPrefix}-tab-active`]: pane.key === activeKey,\n      [`${classPrefix}-tab-disabled`]: pane.props.disabled\n    })\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-tab-title`\n  }, pane.props.title), React.createElement(\"div\", {\n    className: `${classPrefix}-tab-description`\n  }, pane.props.description))))))), panes.map(pane => {\n    if (pane.props.children === undefined) {\n      return null;\n    }\n    const active = pane.key === activeKey;\n    return React.createElement(ShouldRender, {\n      key: pane.key,\n      active: active,\n      forceRender: pane.props.forceRender,\n      destroyOnClose: pane.props.destroyOnClose\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-content`,\n      style: {\n        display: active ? 'block' : 'none'\n      }\n    }, pane.props.children));\n  })));\n};"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,cAAc,EAAEC,MAAM,QAAQ,OAAO;AACrD,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,OAAOC,UAAU,MAAM,gBAAgB;AACvC,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,MAAMC,WAAW,GAAG,gBAAgB;AACpC,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAC5B,OAAO,IAAI;AACb,CAAC;AACD,OAAO,MAAMC,SAAS,GAAGC,KAAK,IAAI;EAChC,IAAIC,EAAE;EACN,MAAMC,mBAAmB,GAAGf,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMgB,OAAO,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMiB,gBAAgB,GAAG,CAAC,CAAC;EAC3B,IAAIC,cAAc,GAAG,IAAI;EACzB,MAAMC,KAAK,GAAG,EAAE;EAChBV,iBAAiB,CAACI,KAAK,CAACO,QAAQ,EAAE,CAACC,KAAK,EAAEC,KAAK,KAAK;IAClD,IAAI,CAACvB,cAAc,CAACsB,KAAK,CAAC,EAAE;IAC5B,MAAME,GAAG,GAAGF,KAAK,CAACE,GAAG;IACrB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC7B,IAAID,KAAK,KAAK,CAAC,EAAE;MACfJ,cAAc,GAAGK,GAAG;IACtB;IACA,MAAMC,MAAM,GAAGL,KAAK,CAACM,IAAI,CAACJ,KAAK,CAAC;IAChCJ,gBAAgB,CAACM,GAAG,CAAC,GAAGC,MAAM,GAAG,CAAC;EACpC,CAAC,CAAC;EACF,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAGvB,aAAa,CAAC;IAC9CwB,KAAK,EAAEf,KAAK,CAACa,SAAS;IACtBG,YAAY,EAAE,CAACf,EAAE,GAAGD,KAAK,CAACiB,gBAAgB,MAAM,IAAI,IAAIhB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGI,cAAc;IAC3Fa,QAAQ,EAAEC,CAAC,IAAI;MACb,IAAIlB,EAAE;MACN,IAAIkB,CAAC,KAAK,IAAI,EAAE;MAChB,CAAClB,EAAE,GAAGD,KAAK,CAACkB,QAAQ,MAAM,IAAI,IAAIjB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,IAAI,CAACpB,KAAK,EAAEmB,CAAC,CAAC;IAC9E;EACF,CAAC,CAAC;EACF,MAAM;IACJE,UAAU;IACVC;EACF,CAAC,GAAG7B,gBAAgB,CAACS,mBAAmB,EAAEE,gBAAgB,CAACS,SAAS,CAAC,CAAC;EACtErB,eAAe,CAAC,MAAM;IACpB8B,OAAO,CAAC,IAAI,CAAC;EACf,CAAC,EAAEnB,OAAO,CAAC;EACX,OAAOb,eAAe,CAACU,KAAK,EAAEf,KAAK,CAACsC,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAE3B,WAAW;IACtB4B,GAAG,EAAEtB;EACP,CAAC,EAAElB,KAAK,CAACsC,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAG3B,WAAW;EAC3B,CAAC,EAAEZ,KAAK,CAACsC,aAAa,CAAC7B,UAAU,EAAE;IACjCgC,cAAc,EAAExB;EAClB,CAAC,CAAC,EAAEjB,KAAK,CAACsC,aAAa,CAAClC,QAAQ,CAACsC,GAAG,EAAE;IACpCH,SAAS,EAAE,GAAG3B,WAAW,WAAW;IACpC4B,GAAG,EAAEvB,mBAAmB;IACxBmB,UAAU,EAAEA;EACd,CAAC,EAAEf,KAAK,CAACsB,GAAG,CAACC,IAAI,IAAIvC,eAAe,CAACuC,IAAI,CAAC7B,KAAK,EAAEf,KAAK,CAACsC,aAAa,CAAC,KAAK,EAAE;IAC1Eb,GAAG,EAAEmB,IAAI,CAACnB,GAAG;IACbc,SAAS,EAAE,GAAG3B,WAAW;EAC3B,CAAC,EAAEZ,KAAK,CAACsC,aAAa,CAAC,KAAK,EAAE;IAC5BO,OAAO,EAAEA,CAAA,KAAM;MACb,MAAM;QACJpB;MACF,CAAC,GAAGmB,IAAI;MACR,IAAIA,IAAI,CAAC7B,KAAK,CAAC+B,QAAQ,EAAE;MACzB,IAAIrB,GAAG,KAAKsB,SAAS,IAAItB,GAAG,KAAK,IAAI,EAAE;QACrC;MACF;MACAI,YAAY,CAACJ,GAAG,CAACuB,QAAQ,CAAC,CAAC,CAAC;IAC9B,CAAC;IACDT,SAAS,EAAEpC,UAAU,CAAC,GAAGS,WAAW,MAAM,EAAE;MAC1C,CAAC,GAAGA,WAAW,aAAa,GAAGgC,IAAI,CAACnB,GAAG,KAAKG,SAAS;MACrD,CAAC,GAAGhB,WAAW,eAAe,GAAGgC,IAAI,CAAC7B,KAAK,CAAC+B;IAC9C,CAAC;EACH,CAAC,EAAE9C,KAAK,CAACsC,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAG3B,WAAW;EAC3B,CAAC,EAAEgC,IAAI,CAAC7B,KAAK,CAACkC,KAAK,CAAC,EAAEjD,KAAK,CAACsC,aAAa,CAAC,KAAK,EAAE;IAC/CC,SAAS,EAAE,GAAG3B,WAAW;EAC3B,CAAC,EAAEgC,IAAI,CAAC7B,KAAK,CAACmC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE7B,KAAK,CAACsB,GAAG,CAACC,IAAI,IAAI;IAClD,IAAIA,IAAI,CAAC7B,KAAK,CAACO,QAAQ,KAAKyB,SAAS,EAAE;MACrC,OAAO,IAAI;IACb;IACA,MAAMI,MAAM,GAAGP,IAAI,CAACnB,GAAG,KAAKG,SAAS;IACrC,OAAO5B,KAAK,CAACsC,aAAa,CAAC5B,YAAY,EAAE;MACvCe,GAAG,EAAEmB,IAAI,CAACnB,GAAG;MACb0B,MAAM,EAAEA,MAAM;MACdC,WAAW,EAAER,IAAI,CAAC7B,KAAK,CAACqC,WAAW;MACnCC,cAAc,EAAET,IAAI,CAAC7B,KAAK,CAACsC;IAC7B,CAAC,EAAErD,KAAK,CAACsC,aAAa,CAAC,KAAK,EAAE;MAC5BC,SAAS,EAAE,GAAG3B,WAAW,UAAU;MACnC0C,KAAK,EAAE;QACLC,OAAO,EAAEJ,MAAM,GAAG,OAAO,GAAG;MAC9B;IACF,CAAC,EAAEP,IAAI,CAAC7B,KAAK,CAACO,QAAQ,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}