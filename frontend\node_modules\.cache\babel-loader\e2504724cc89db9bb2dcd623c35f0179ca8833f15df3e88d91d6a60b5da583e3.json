{"ast": null, "code": "import classNames from 'classnames';\nimport React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nconst classPrefix = `adm-badge`;\nexport const dot = React.createElement(React.Fragment, null);\nexport const Badge = props => {\n  const {\n    content,\n    color,\n    children\n  } = props;\n  const isDot = content === dot;\n  const badgeClass = classNames(classPrefix, {\n    [`${classPrefix}-fixed`]: !!children,\n    [`${classPrefix}-dot`]: isDot,\n    [`${classPrefix}-bordered`]: props.bordered\n  });\n  const element = content || content === 0 ? withNativeProps(props, React.createElement(\"div\", {\n    className: badgeClass,\n    style: {\n      '--color': color\n    }\n  }, !isDot && React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, content))) : null;\n  return children ? React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-wrapper`, props.wrapperClassName),\n    style: props.wrapperStyle\n  }, children, element) : element;\n};", "map": {"version": 3, "names": ["classNames", "React", "withNativeProps", "classPrefix", "dot", "createElement", "Fragment", "Badge", "props", "content", "color", "children", "isDot", "badgeClass", "bordered", "element", "className", "style", "wrapperClassName", "wrapperStyle"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/badge/badge.js"], "sourcesContent": ["import classNames from 'classnames';\nimport React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nconst classPrefix = `adm-badge`;\nexport const dot = React.createElement(React.Fragment, null);\nexport const Badge = props => {\n  const {\n    content,\n    color,\n    children\n  } = props;\n  const isDot = content === dot;\n  const badgeClass = classNames(classPrefix, {\n    [`${classPrefix}-fixed`]: !!children,\n    [`${classPrefix}-dot`]: isDot,\n    [`${classPrefix}-bordered`]: props.bordered\n  });\n  const element = content || content === 0 ? withNativeProps(props, React.createElement(\"div\", {\n    className: badgeClass,\n    style: {\n      '--color': color\n    }\n  }, !isDot && React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, content))) : null;\n  return children ? React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-wrapper`, props.wrapperClassName),\n    style: props.wrapperStyle\n  }, children, element) : element;\n};"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,MAAMC,WAAW,GAAG,WAAW;AAC/B,OAAO,MAAMC,GAAG,GAAGH,KAAK,CAACI,aAAa,CAACJ,KAAK,CAACK,QAAQ,EAAE,IAAI,CAAC;AAC5D,OAAO,MAAMC,KAAK,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,OAAO;IACPC,KAAK;IACLC;EACF,CAAC,GAAGH,KAAK;EACT,MAAMI,KAAK,GAAGH,OAAO,KAAKL,GAAG;EAC7B,MAAMS,UAAU,GAAGb,UAAU,CAACG,WAAW,EAAE;IACzC,CAAC,GAAGA,WAAW,QAAQ,GAAG,CAAC,CAACQ,QAAQ;IACpC,CAAC,GAAGR,WAAW,MAAM,GAAGS,KAAK;IAC7B,CAAC,GAAGT,WAAW,WAAW,GAAGK,KAAK,CAACM;EACrC,CAAC,CAAC;EACF,MAAMC,OAAO,GAAGN,OAAO,IAAIA,OAAO,KAAK,CAAC,GAAGP,eAAe,CAACM,KAAK,EAAEP,KAAK,CAACI,aAAa,CAAC,KAAK,EAAE;IAC3FW,SAAS,EAAEH,UAAU;IACrBI,KAAK,EAAE;MACL,SAAS,EAAEP;IACb;EACF,CAAC,EAAE,CAACE,KAAK,IAAIX,KAAK,CAACI,aAAa,CAAC,KAAK,EAAE;IACtCW,SAAS,EAAE,GAAGb,WAAW;EAC3B,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;EACpB,OAAOE,QAAQ,GAAGV,KAAK,CAACI,aAAa,CAAC,KAAK,EAAE;IAC3CW,SAAS,EAAEhB,UAAU,CAAC,GAAGG,WAAW,UAAU,EAAEK,KAAK,CAACU,gBAAgB,CAAC;IACvED,KAAK,EAAET,KAAK,CAACW;EACf,CAAC,EAAER,QAAQ,EAAEI,OAAO,CAAC,GAAGA,OAAO;AACjC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}