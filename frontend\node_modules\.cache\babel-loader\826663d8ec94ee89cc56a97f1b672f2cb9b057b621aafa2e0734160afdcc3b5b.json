{"ast": null, "code": "import React, { memo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nexport const IndeterminateIcon = memo(props => {\n  return withNativeProps(props, React.createElement(\"svg\", {\n    viewBox: '0 0 40 40'\n  }, React.createElement(\"path\", {\n    d: 'M20,9 C26.0752953,9 31,13.9247047 31,20 C31,26.0752953 26.0752953,31 20,31 C13.9247047,31 9,26.0752953 9,20 C9,13.9247047 13.9247047,9 20,9 Z',\n    fill: 'currentColor'\n  })));\n});", "map": {"version": 3, "names": ["React", "memo", "withNativeProps", "IndeterminateIcon", "props", "createElement", "viewBox", "d", "fill"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/checkbox/indeterminate-icon.js"], "sourcesContent": ["import React, { memo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nexport const IndeterminateIcon = memo(props => {\n  return withNativeProps(props, React.createElement(\"svg\", {\n    viewBox: '0 0 40 40'\n  }, React.createElement(\"path\", {\n    d: 'M20,9 C26.0752953,9 31,13.9247047 31,20 C31,26.0752953 26.0752953,31 20,31 C13.9247047,31 9,26.0752953 9,20 C9,13.9247047 13.9247047,9 20,9 Z',\n    fill: 'currentColor'\n  })));\n});"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,IAAI,QAAQ,OAAO;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,iBAAiB,GAAGF,IAAI,CAACG,KAAK,IAAI;EAC7C,OAAOF,eAAe,CAACE,KAAK,EAAEJ,KAAK,CAACK,aAAa,CAAC,KAAK,EAAE;IACvDC,OAAO,EAAE;EACX,CAAC,EAAEN,KAAK,CAACK,aAAa,CAAC,MAAM,EAAE;IAC7BE,CAAC,EAAE,+IAA+I;IAClJC,IAAI,EAAE;EACR,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}