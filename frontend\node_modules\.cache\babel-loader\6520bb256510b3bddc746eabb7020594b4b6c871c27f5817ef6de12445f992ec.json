{"ast": null, "code": "import { __rest } from \"tslib\";\nimport classNames from 'classnames';\nimport RcSegmented from 'rc-segmented';\nimport * as React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nfunction isSegmentedLabeledOptionWithIcon(option) {\n  return typeof option === 'object' && !!(option === null || option === void 0 ? void 0 : option.icon);\n}\nconst classPrefix = `adm-segmented`;\nconst Segmented = React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      block,\n      options = []\n    } = props,\n    restProps = __rest(props\n    // syntactic sugar to support `icon` for Segmented Item\n    , [\"prefixCls\", \"className\", \"block\", \"options\"]);\n  // syntactic sugar to support `icon` for Segmented Item\n  const extendedOptions = React.useMemo(() => options.map(option => {\n    if (isSegmentedLabeledOptionWithIcon(option)) {\n      const {\n          icon,\n          label\n        } = option,\n        restOption = __rest(option, [\"icon\", \"label\"]);\n      return Object.assign(Object.assign({}, restOption), {\n        label: React.createElement(React.Fragment, null, React.createElement(\"span\", {\n          className: `${classPrefix}-item-icon`\n        }, icon), label && React.createElement(\"span\", null, label))\n      });\n    }\n    return option;\n  }), [options, classPrefix]);\n  return withNativeProps(props, React.createElement(RcSegmented, Object.assign({}, restProps, {\n    className: classNames(className, {\n      [`${classPrefix}-block`]: block\n    }),\n    options: extendedOptions,\n    ref: ref,\n    prefixCls: classPrefix\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Segmented.displayName = 'Segmented';\n}\nexport { Segmented };", "map": {"version": 3, "names": ["__rest", "classNames", "RcSegmented", "React", "withNativeProps", "isSegmentedLabeledOptionWithIcon", "option", "icon", "classPrefix", "Segmented", "forwardRef", "props", "ref", "prefixCls", "customizePrefixCls", "className", "block", "options", "restProps", "extendedOptions", "useMemo", "map", "label", "restOption", "Object", "assign", "createElement", "Fragment", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/segmented/segmented.js"], "sourcesContent": ["import { __rest } from \"tslib\";\nimport classNames from 'classnames';\nimport RcSegmented from 'rc-segmented';\nimport * as React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nfunction isSegmentedLabeledOptionWithIcon(option) {\n  return typeof option === 'object' && !!(option === null || option === void 0 ? void 0 : option.icon);\n}\nconst classPrefix = `adm-segmented`;\nconst Segmented = React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      block,\n      options = []\n    } = props,\n    restProps = __rest(props\n    // syntactic sugar to support `icon` for Segmented Item\n    , [\"prefixCls\", \"className\", \"block\", \"options\"]);\n  // syntactic sugar to support `icon` for Segmented Item\n  const extendedOptions = React.useMemo(() => options.map(option => {\n    if (isSegmentedLabeledOptionWithIcon(option)) {\n      const {\n          icon,\n          label\n        } = option,\n        restOption = __rest(option, [\"icon\", \"label\"]);\n      return Object.assign(Object.assign({}, restOption), {\n        label: React.createElement(React.Fragment, null, React.createElement(\"span\", {\n          className: `${classPrefix}-item-icon`\n        }, icon), label && React.createElement(\"span\", null, label))\n      });\n    }\n    return option;\n  }), [options, classPrefix]);\n  return withNativeProps(props, React.createElement(RcSegmented, Object.assign({}, restProps, {\n    className: classNames(className, {\n      [`${classPrefix}-block`]: block\n    }),\n    options: extendedOptions,\n    ref: ref,\n    prefixCls: classPrefix\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Segmented.displayName = 'Segmented';\n}\nexport { Segmented };"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gCAAgCA,CAACC,MAAM,EAAE;EAChD,OAAO,OAAOA,MAAM,KAAK,QAAQ,IAAI,CAAC,EAAEA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,IAAI,CAAC;AACtG;AACA,MAAMC,WAAW,GAAG,eAAe;AACnC,MAAMC,SAAS,GAAGN,KAAK,CAACO,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACjD,MAAM;MACFC,SAAS,EAAEC,kBAAkB;MAC7BC,SAAS;MACTC,KAAK;MACLC,OAAO,GAAG;IACZ,CAAC,GAAGN,KAAK;IACTO,SAAS,GAAGlB,MAAM,CAACW;IACnB;IAAA,EACE,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;EACnD;EACA,MAAMQ,eAAe,GAAGhB,KAAK,CAACiB,OAAO,CAAC,MAAMH,OAAO,CAACI,GAAG,CAACf,MAAM,IAAI;IAChE,IAAID,gCAAgC,CAACC,MAAM,CAAC,EAAE;MAC5C,MAAM;UACFC,IAAI;UACJe;QACF,CAAC,GAAGhB,MAAM;QACViB,UAAU,GAAGvB,MAAM,CAACM,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;MAChD,OAAOkB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,UAAU,CAAC,EAAE;QAClDD,KAAK,EAAEnB,KAAK,CAACuB,aAAa,CAACvB,KAAK,CAACwB,QAAQ,EAAE,IAAI,EAAExB,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAE;UAC3EX,SAAS,EAAE,GAAGP,WAAW;QAC3B,CAAC,EAAED,IAAI,CAAC,EAAEe,KAAK,IAAInB,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEJ,KAAK,CAAC;MAC7D,CAAC,CAAC;IACJ;IACA,OAAOhB,MAAM;EACf,CAAC,CAAC,EAAE,CAACW,OAAO,EAAET,WAAW,CAAC,CAAC;EAC3B,OAAOJ,eAAe,CAACO,KAAK,EAAER,KAAK,CAACuB,aAAa,CAACxB,WAAW,EAAEsB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,SAAS,EAAE;IAC1FH,SAAS,EAAEd,UAAU,CAACc,SAAS,EAAE;MAC/B,CAAC,GAAGP,WAAW,QAAQ,GAAGQ;IAC5B,CAAC,CAAC;IACFC,OAAO,EAAEE,eAAe;IACxBP,GAAG,EAAEA,GAAG;IACRC,SAAS,EAAEL;EACb,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF,IAAIoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCrB,SAAS,CAACsB,WAAW,GAAG,WAAW;AACrC;AACA,SAAStB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}