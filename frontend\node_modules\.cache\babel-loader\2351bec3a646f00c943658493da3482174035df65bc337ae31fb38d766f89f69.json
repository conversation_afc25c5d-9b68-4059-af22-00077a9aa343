{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_isoWeek = t();\n}(this, function () {\n  \"use strict\";\n\n  var e = \"day\";\n  return function (t, i, s) {\n    var a = function (t) {\n        return t.add(4 - t.isoWeekday(), e);\n      },\n      d = i.prototype;\n    d.isoWeekYear = function () {\n      return a(this).year();\n    }, d.isoWeek = function (t) {\n      if (!this.$utils().u(t)) return this.add(7 * (t - this.isoWeek()), e);\n      var i,\n        d,\n        n,\n        o,\n        r = a(this),\n        u = (i = this.isoWeekYear(), d = this.$u, n = (d ? s.utc : s)().year(i).startOf(\"year\"), o = 4 - n.isoWeekday(), n.isoWeekday() > 4 && (o += 7), n.add(o, e));\n      return r.diff(u, \"week\") + 1;\n    }, d.isoWeekday = function (e) {\n      return this.$utils().u(e) ? this.day() || 7 : this.day(this.day() % 7 ? e : e - 7);\n    };\n    var n = d.startOf;\n    d.startOf = function (e, t) {\n      var i = this.$utils(),\n        s = !!i.u(t) || t;\n      return \"isoweek\" === i.p(e) ? s ? this.date(this.date() - (this.isoWeekday() - 1)).startOf(\"day\") : this.date(this.date() - 1 - (this.isoWeekday() - 1) + 7).endOf(\"day\") : n.bind(this)(e, t);\n    };\n  };\n});", "map": {"version": 3, "names": ["e", "t", "exports", "module", "define", "amd", "globalThis", "self", "dayjs_plugin_isoWeek", "i", "s", "a", "add", "isoWeekday", "d", "prototype", "isoWeekYear", "year", "isoWeek", "$utils", "u", "n", "o", "r", "$u", "utc", "startOf", "diff", "day", "p", "date", "endOf", "bind"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/dayjs/plugin/isoWeek.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_isoWeek=t()}(this,(function(){\"use strict\";var e=\"day\";return function(t,i,s){var a=function(t){return t.add(4-t.isoWeekday(),e)},d=i.prototype;d.isoWeekYear=function(){return a(this).year()},d.isoWeek=function(t){if(!this.$utils().u(t))return this.add(7*(t-this.isoWeek()),e);var i,d,n,o,r=a(this),u=(i=this.isoWeekYear(),d=this.$u,n=(d?s.utc:s)().year(i).startOf(\"year\"),o=4-n.isoWeekday(),n.isoWeekday()>4&&(o+=7),n.add(o,e));return r.diff(u,\"week\")+1},d.isoWeekday=function(e){return this.$utils().u(e)?this.day()||7:this.day(this.day()%7?e:e-7)};var n=d.startOf;d.startOf=function(e,t){var i=this.$utils(),s=!!i.u(t)||t;return\"isoweek\"===i.p(e)?s?this.date(this.date()-(this.isoWeekday()-1)).startOf(\"day\"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf(\"day\"):n.bind(this)(e,t)}}}));"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,WAAW,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOG,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAACH,CAAC,CAAC,GAAC,CAACD,CAAC,GAAC,WAAW,IAAE,OAAOM,UAAU,GAACA,UAAU,GAACN,CAAC,IAAEO,IAAI,EAAEC,oBAAoB,GAACP,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC,IAAI,EAAE,YAAU;EAAC,YAAY;;EAAC,IAAID,CAAC,GAAC,KAAK;EAAC,OAAO,UAASC,CAAC,EAACQ,CAAC,EAACC,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,SAAAA,CAASV,CAAC,EAAC;QAAC,OAAOA,CAAC,CAACW,GAAG,CAAC,CAAC,GAACX,CAAC,CAACY,UAAU,CAAC,CAAC,EAACb,CAAC,CAAC;MAAA,CAAC;MAACc,CAAC,GAACL,CAAC,CAACM,SAAS;IAACD,CAAC,CAACE,WAAW,GAAC,YAAU;MAAC,OAAOL,CAAC,CAAC,IAAI,CAAC,CAACM,IAAI,CAAC,CAAC;IAAA,CAAC,EAACH,CAAC,CAACI,OAAO,GAAC,UAASjB,CAAC,EAAC;MAAC,IAAG,CAAC,IAAI,CAACkB,MAAM,CAAC,CAAC,CAACC,CAAC,CAACnB,CAAC,CAAC,EAAC,OAAO,IAAI,CAACW,GAAG,CAAC,CAAC,IAAEX,CAAC,GAAC,IAAI,CAACiB,OAAO,CAAC,CAAC,CAAC,EAAClB,CAAC,CAAC;MAAC,IAAIS,CAAC;QAACK,CAAC;QAACO,CAAC;QAACC,CAAC;QAACC,CAAC,GAACZ,CAAC,CAAC,IAAI,CAAC;QAACS,CAAC,IAAEX,CAAC,GAAC,IAAI,CAACO,WAAW,CAAC,CAAC,EAACF,CAAC,GAAC,IAAI,CAACU,EAAE,EAACH,CAAC,GAAC,CAACP,CAAC,GAACJ,CAAC,CAACe,GAAG,GAACf,CAAC,EAAE,CAAC,CAACO,IAAI,CAACR,CAAC,CAAC,CAACiB,OAAO,CAAC,MAAM,CAAC,EAACJ,CAAC,GAAC,CAAC,GAACD,CAAC,CAACR,UAAU,CAAC,CAAC,EAACQ,CAAC,CAACR,UAAU,CAAC,CAAC,GAAC,CAAC,KAAGS,CAAC,IAAE,CAAC,CAAC,EAACD,CAAC,CAACT,GAAG,CAACU,CAAC,EAACtB,CAAC,CAAC,CAAC;MAAC,OAAOuB,CAAC,CAACI,IAAI,CAACP,CAAC,EAAC,MAAM,CAAC,GAAC,CAAC;IAAA,CAAC,EAACN,CAAC,CAACD,UAAU,GAAC,UAASb,CAAC,EAAC;MAAC,OAAO,IAAI,CAACmB,MAAM,CAAC,CAAC,CAACC,CAAC,CAACpB,CAAC,CAAC,GAAC,IAAI,CAAC4B,GAAG,CAAC,CAAC,IAAE,CAAC,GAAC,IAAI,CAACA,GAAG,CAAC,IAAI,CAACA,GAAG,CAAC,CAAC,GAAC,CAAC,GAAC5B,CAAC,GAACA,CAAC,GAAC,CAAC,CAAC;IAAA,CAAC;IAAC,IAAIqB,CAAC,GAACP,CAAC,CAACY,OAAO;IAACZ,CAAC,CAACY,OAAO,GAAC,UAAS1B,CAAC,EAACC,CAAC,EAAC;MAAC,IAAIQ,CAAC,GAAC,IAAI,CAACU,MAAM,CAAC,CAAC;QAACT,CAAC,GAAC,CAAC,CAACD,CAAC,CAACW,CAAC,CAACnB,CAAC,CAAC,IAAEA,CAAC;MAAC,OAAM,SAAS,KAAGQ,CAAC,CAACoB,CAAC,CAAC7B,CAAC,CAAC,GAACU,CAAC,GAAC,IAAI,CAACoB,IAAI,CAAC,IAAI,CAACA,IAAI,CAAC,CAAC,IAAE,IAAI,CAACjB,UAAU,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,KAAK,CAAC,GAAC,IAAI,CAACI,IAAI,CAAC,IAAI,CAACA,IAAI,CAAC,CAAC,GAAC,CAAC,IAAE,IAAI,CAACjB,UAAU,CAAC,CAAC,GAAC,CAAC,CAAC,GAAC,CAAC,CAAC,CAACkB,KAAK,CAAC,KAAK,CAAC,GAACV,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC,CAAChC,CAAC,EAACC,CAAC,CAAC;IAAA,CAAC;EAAA,CAAC;AAAA,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}