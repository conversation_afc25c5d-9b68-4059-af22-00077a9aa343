{"ast": null, "code": "import classNames from 'classnames';\nimport React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nconst classPrefix = `adm-card`;\nexport const Card = props => {\n  const renderHeader = () => {\n    if (!(props.title || props.extra)) {\n      return null;\n    }\n    return React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-header`, props.headerClassName),\n      style: props.headerStyle,\n      onClick: props.onHeaderClick\n    }, props.icon && React.createElement(\"div\", {\n      className: `${classPrefix}-header-icon`\n    }, props.icon), React.createElement(\"div\", {\n      className: `${classPrefix}-header-title`\n    }, props.title), props.extra && React.createElement(\"div\", {\n      className: `${classPrefix}-header-extra`\n    }, props.extra));\n  };\n  const renderBody = () => {\n    if (!props.children) {\n      return null;\n    }\n    return React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-body`, props.bodyClassName),\n      style: props.bodyStyle,\n      onClick: props.onBodyClick\n    }, props.children);\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix,\n    onClick: props.onClick\n  }, renderHeader(), renderBody()));\n};", "map": {"version": 3, "names": ["classNames", "React", "withNativeProps", "classPrefix", "Card", "props", "renderHeader", "title", "extra", "createElement", "className", "headerClassName", "style", "headerStyle", "onClick", "onHeaderClick", "icon", "renderBody", "children", "bodyClassName", "bodyStyle", "onBodyClick"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/card/card.js"], "sourcesContent": ["import classNames from 'classnames';\nimport React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nconst classPrefix = `adm-card`;\nexport const Card = props => {\n  const renderHeader = () => {\n    if (!(props.title || props.extra)) {\n      return null;\n    }\n    return React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-header`, props.headerClassName),\n      style: props.headerStyle,\n      onClick: props.onHeaderClick\n    }, props.icon && React.createElement(\"div\", {\n      className: `${classPrefix}-header-icon`\n    }, props.icon), React.createElement(\"div\", {\n      className: `${classPrefix}-header-title`\n    }, props.title), props.extra && React.createElement(\"div\", {\n      className: `${classPrefix}-header-extra`\n    }, props.extra));\n  };\n  const renderBody = () => {\n    if (!props.children) {\n      return null;\n    }\n    return React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-body`, props.bodyClassName),\n      style: props.bodyStyle,\n      onClick: props.onBodyClick\n    }, props.children);\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix,\n    onClick: props.onClick\n  }, renderHeader(), renderBody()));\n};"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,MAAMC,WAAW,GAAG,UAAU;AAC9B,OAAO,MAAMC,IAAI,GAAGC,KAAK,IAAI;EAC3B,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,EAAED,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACG,KAAK,CAAC,EAAE;MACjC,OAAO,IAAI;IACb;IACA,OAAOP,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAE;MAChCC,SAAS,EAAEV,UAAU,CAAC,GAAGG,WAAW,SAAS,EAAEE,KAAK,CAACM,eAAe,CAAC;MACrEC,KAAK,EAAEP,KAAK,CAACQ,WAAW;MACxBC,OAAO,EAAET,KAAK,CAACU;IACjB,CAAC,EAAEV,KAAK,CAACW,IAAI,IAAIf,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAE;MAC1CC,SAAS,EAAE,GAAGP,WAAW;IAC3B,CAAC,EAAEE,KAAK,CAACW,IAAI,CAAC,EAAEf,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAE;MACzCC,SAAS,EAAE,GAAGP,WAAW;IAC3B,CAAC,EAAEE,KAAK,CAACE,KAAK,CAAC,EAAEF,KAAK,CAACG,KAAK,IAAIP,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAE;MACzDC,SAAS,EAAE,GAAGP,WAAW;IAC3B,CAAC,EAAEE,KAAK,CAACG,KAAK,CAAC,CAAC;EAClB,CAAC;EACD,MAAMS,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACZ,KAAK,CAACa,QAAQ,EAAE;MACnB,OAAO,IAAI;IACb;IACA,OAAOjB,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAE;MAChCC,SAAS,EAAEV,UAAU,CAAC,GAAGG,WAAW,OAAO,EAAEE,KAAK,CAACc,aAAa,CAAC;MACjEP,KAAK,EAAEP,KAAK,CAACe,SAAS;MACtBN,OAAO,EAAET,KAAK,CAACgB;IACjB,CAAC,EAAEhB,KAAK,CAACa,QAAQ,CAAC;EACpB,CAAC;EACD,OAAOhB,eAAe,CAACG,KAAK,EAAEJ,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEP,WAAW;IACtBW,OAAO,EAAET,KAAK,CAACS;EACjB,CAAC,EAAER,YAAY,CAAC,CAAC,EAAEW,UAAU,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}