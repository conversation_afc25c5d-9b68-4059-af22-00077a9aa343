{"ast": null, "code": "import * as React from \"react\";\nfunction EditSFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EditSFill-EditSFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EditSFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"EditSFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M41.5581882,38.9405779 C41.8382044,38.9405779 41.9782122,39.0765521 41.9782122,39.3485009 L41.9782122,41.592077 C41.9782122,41.8640258 41.8382044,42 41.5581882,42 L24.5472085,42 C24.2671923,42 24.1271844,41.8640258 24.1271844,41.592077 L24.1271844,39.3485009 C24.1271844,39.0765521 24.2671923,38.9405779 24.5472085,38.9405779 L41.5581882,38.9405779 Z M27.5325522,13.8391031 L34.9564907,21.0501177 C35.3664155,21.4483524 35.3664155,22.0938928 34.9564908,22.4921247 L15.217454,41.6645312 C15.0204038,41.8554216 14.7533811,41.9623153 14.4750596,41.9623153 L8.10012595,41.9623153 C6.9402636,41.9623153 6.00000168,41.0491486 6.00000168,39.9226974 L6.00000168,33.7324869 C5.99950341,33.4618321 6.1098059,33.2020832 6.30661496,33.010458 L26.0477683,13.8391032 C26.4578166,13.4409884 27.1225068,13.4409884 27.5325522,13.8391031 Z M41.5581882,32.8217298 C41.8382044,32.8217298 41.9782122,32.957704 41.9782122,33.2296528 L41.9782122,35.4732289 C41.9782122,35.7451777 41.8382044,35.8811519 41.5581882,35.8811519 L32.9477087,35.8811519 C32.6676925,35.8811519 32.5276846,35.7451777 32.5276846,35.4732289 L32.5276846,33.2296528 C32.5276846,32.957704 32.6676925,32.8217298 32.9477087,32.8217298 L41.5581882,32.8217298 Z M40.1542565,8.79202516 L40.15429,8.7910055 C42.614941,11.1803631 42.6152806,15.0546026 40.1550449,17.4443474 C40.1547933,17.4445917 40.1545418,17.444836 40.1542903,17.4450802 L38.6695064,18.8870871 C38.2911871,19.2549161 37.6955751,19.2835165 37.2834679,18.9726724 L37.1844961,18.8878895 C37.1842206,18.8876222 37.1839453,18.8873548 37.1836701,18.8870872 L29.7597261,11.6750209 C29.3498014,11.2767862 29.3498014,10.6312458 29.759726,10.233014 L31.2445098,8.79202539 C33.7049196,6.40265824 37.6938664,6.40265824 40.1542565,8.79202516 Z\",\n    id: \"EditSFill-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default EditSFill;", "map": {"version": 3, "names": ["React", "EditSFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/EditSFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction EditSFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EditSFill-EditSFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EditSFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"EditSFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M41.5581882,38.9405779 C41.8382044,38.9405779 41.9782122,39.0765521 41.9782122,39.3485009 L41.9782122,41.592077 C41.9782122,41.8640258 41.8382044,42 41.5581882,42 L24.5472085,42 C24.2671923,42 24.1271844,41.8640258 24.1271844,41.592077 L24.1271844,39.3485009 C24.1271844,39.0765521 24.2671923,38.9405779 24.5472085,38.9405779 L41.5581882,38.9405779 Z M27.5325522,13.8391031 L34.9564907,21.0501177 C35.3664155,21.4483524 35.3664155,22.0938928 34.9564908,22.4921247 L15.217454,41.6645312 C15.0204038,41.8554216 14.7533811,41.9623153 14.4750596,41.9623153 L8.10012595,41.9623153 C6.9402636,41.9623153 6.00000168,41.0491486 6.00000168,39.9226974 L6.00000168,33.7324869 C5.99950341,33.4618321 6.1098059,33.2020832 6.30661496,33.010458 L26.0477683,13.8391032 C26.4578166,13.4409884 27.1225068,13.4409884 27.5325522,13.8391031 Z M41.5581882,32.8217298 C41.8382044,32.8217298 41.9782122,32.957704 41.9782122,33.2296528 L41.9782122,35.4732289 C41.9782122,35.7451777 41.8382044,35.8811519 41.5581882,35.8811519 L32.9477087,35.8811519 C32.6676925,35.8811519 32.5276846,35.7451777 32.5276846,35.4732289 L32.5276846,33.2296528 C32.5276846,32.957704 32.6676925,32.8217298 32.9477087,32.8217298 L41.5581882,32.8217298 Z M40.1542565,8.79202516 L40.15429,8.7910055 C42.614941,11.1803631 42.6152806,15.0546026 40.1550449,17.4443474 C40.1547933,17.4445917 40.1545418,17.444836 40.1542903,17.4450802 L38.6695064,18.8870871 C38.2911871,19.2549161 37.6955751,19.2835165 37.2834679,18.9726724 L37.1844961,18.8878895 C37.1842206,18.8876222 37.1839453,18.8873548 37.1836701,18.8870872 L29.7597261,11.6750209 C29.3498014,11.2767862 29.3498014,10.6312458 29.759726,10.233014 L31.2445098,8.79202539 C33.7049196,6.40265824 37.6938664,6.40265824 40.1542565,8.79202516 Z\",\n    id: \"EditSFill-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default EditSFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,qBAAqB;IACzBC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,wBAAwB;IAC5BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,6sDAA6sD;IAChtDR,EAAE,EAAE,oCAAoC;IACxCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}