{"ast": null, "code": "import { __awaiter } from \"tslib\";\nimport React, { useEffect, useImperativeHandle, useRef, useState } from 'react';\nimport { renderToBody } from './render-to-body';\nexport function renderImperatively(element) {\n  const Wrapper = React.forwardRef((_, ref) => {\n    const [visible, setVisible] = useState(false);\n    const closedRef = useRef(false);\n    const [elementToRender, setElementToRender] = useState(element);\n    const keyRef = useRef(0);\n    useEffect(() => {\n      if (!closedRef.current) {\n        setVisible(true);\n      } else {\n        afterClose();\n      }\n    }, []);\n    function onClose() {\n      var _a, _b;\n      closedRef.current = true;\n      setVisible(false);\n      (_b = (_a = elementToRender.props).onClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n    }\n    function afterClose() {\n      var _a, _b;\n      unmount();\n      (_b = (_a = elementToRender.props).afterClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n    }\n    useImperativeHandle(ref, () => ({\n      close: onClose,\n      replace: element => {\n        var _a, _b;\n        keyRef.current++;\n        (_b = (_a = elementToRender.props).afterClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n        setElementToRender(element);\n      }\n    }));\n    return React.cloneElement(elementToRender, Object.assign(Object.assign({}, elementToRender.props), {\n      key: keyRef.current,\n      visible,\n      onClose,\n      afterClose\n    }));\n  });\n  const wrapperRef = React.createRef();\n  const unmount = renderToBody(React.createElement(Wrapper, {\n    ref: wrapperRef\n  }));\n  return {\n    close: () => __awaiter(this, void 0, void 0, function* () {\n      var _a, _b, _c;\n      if (!wrapperRef.current) {\n        // it means the wrapper is not mounted yet, call `unmount` directly\n        unmount();\n        // call `afterClose` to make sure the callback is called\n        (_b = (_a = element.props).afterClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n      } else {\n        (_c = wrapperRef.current) === null || _c === void 0 ? void 0 : _c.close();\n      }\n    }),\n    replace: element => {\n      var _a;\n      (_a = wrapperRef.current) === null || _a === void 0 ? void 0 : _a.replace(element);\n    },\n    isRendered: () => !!wrapperRef.current\n  };\n}", "map": {"version": 3, "names": ["__awaiter", "React", "useEffect", "useImperativeHandle", "useRef", "useState", "renderToBody", "renderImperatively", "element", "Wrapper", "forwardRef", "_", "ref", "visible", "setVisible", "closedRef", "elementToRender", "setElementToRender", "keyRef", "current", "afterClose", "onClose", "_a", "_b", "props", "call", "unmount", "close", "replace", "cloneElement", "Object", "assign", "key", "wrapperRef", "createRef", "createElement", "_c", "isRendered"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/render-imperatively.js"], "sourcesContent": ["import { __awaiter } from \"tslib\";\nimport React, { useEffect, useImperativeHandle, useRef, useState } from 'react';\nimport { renderToBody } from './render-to-body';\nexport function renderImperatively(element) {\n  const Wrapper = React.forwardRef((_, ref) => {\n    const [visible, setVisible] = useState(false);\n    const closedRef = useRef(false);\n    const [elementToRender, setElementToRender] = useState(element);\n    const keyRef = useRef(0);\n    useEffect(() => {\n      if (!closedRef.current) {\n        setVisible(true);\n      } else {\n        afterClose();\n      }\n    }, []);\n    function onClose() {\n      var _a, _b;\n      closedRef.current = true;\n      setVisible(false);\n      (_b = (_a = elementToRender.props).onClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n    }\n    function afterClose() {\n      var _a, _b;\n      unmount();\n      (_b = (_a = elementToRender.props).afterClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n    }\n    useImperativeHandle(ref, () => ({\n      close: onClose,\n      replace: element => {\n        var _a, _b;\n        keyRef.current++;\n        (_b = (_a = elementToRender.props).afterClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n        setElementToRender(element);\n      }\n    }));\n    return React.cloneElement(elementToRender, Object.assign(Object.assign({}, elementToRender.props), {\n      key: keyRef.current,\n      visible,\n      onClose,\n      afterClose\n    }));\n  });\n  const wrapperRef = React.createRef();\n  const unmount = renderToBody(React.createElement(Wrapper, {\n    ref: wrapperRef\n  }));\n  return {\n    close: () => __awaiter(this, void 0, void 0, function* () {\n      var _a, _b, _c;\n      if (!wrapperRef.current) {\n        // it means the wrapper is not mounted yet, call `unmount` directly\n        unmount();\n        // call `afterClose` to make sure the callback is called\n        (_b = (_a = element.props).afterClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n      } else {\n        (_c = wrapperRef.current) === null || _c === void 0 ? void 0 : _c.close();\n      }\n    }),\n    replace: element => {\n      var _a;\n      (_a = wrapperRef.current) === null || _a === void 0 ? void 0 : _a.replace(element);\n    },\n    isRendered: () => !!wrapperRef.current\n  };\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,KAAK,IAAIC,SAAS,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC/E,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,OAAO,SAASC,kBAAkBA,CAACC,OAAO,EAAE;EAC1C,MAAMC,OAAO,GAAGR,KAAK,CAACS,UAAU,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;IAC3C,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;IAC7C,MAAMU,SAAS,GAAGX,MAAM,CAAC,KAAK,CAAC;IAC/B,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAGZ,QAAQ,CAACG,OAAO,CAAC;IAC/D,MAAMU,MAAM,GAAGd,MAAM,CAAC,CAAC,CAAC;IACxBF,SAAS,CAAC,MAAM;MACd,IAAI,CAACa,SAAS,CAACI,OAAO,EAAE;QACtBL,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,MAAM;QACLM,UAAU,CAAC,CAAC;MACd;IACF,CAAC,EAAE,EAAE,CAAC;IACN,SAASC,OAAOA,CAAA,EAAG;MACjB,IAAIC,EAAE,EAAEC,EAAE;MACVR,SAAS,CAACI,OAAO,GAAG,IAAI;MACxBL,UAAU,CAAC,KAAK,CAAC;MACjB,CAACS,EAAE,GAAG,CAACD,EAAE,GAAGN,eAAe,CAACQ,KAAK,EAAEH,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACH,EAAE,CAAC;IAC9F;IACA,SAASF,UAAUA,CAAA,EAAG;MACpB,IAAIE,EAAE,EAAEC,EAAE;MACVG,OAAO,CAAC,CAAC;MACT,CAACH,EAAE,GAAG,CAACD,EAAE,GAAGN,eAAe,CAACQ,KAAK,EAAEJ,UAAU,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACH,EAAE,CAAC;IACjG;IACAnB,mBAAmB,CAACS,GAAG,EAAE,OAAO;MAC9Be,KAAK,EAAEN,OAAO;MACdO,OAAO,EAAEpB,OAAO,IAAI;QAClB,IAAIc,EAAE,EAAEC,EAAE;QACVL,MAAM,CAACC,OAAO,EAAE;QAChB,CAACI,EAAE,GAAG,CAACD,EAAE,GAAGN,eAAe,CAACQ,KAAK,EAAEJ,UAAU,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACH,EAAE,CAAC;QAC/FL,kBAAkB,CAACT,OAAO,CAAC;MAC7B;IACF,CAAC,CAAC,CAAC;IACH,OAAOP,KAAK,CAAC4B,YAAY,CAACb,eAAe,EAAEc,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEf,eAAe,CAACQ,KAAK,CAAC,EAAE;MACjGQ,GAAG,EAAEd,MAAM,CAACC,OAAO;MACnBN,OAAO;MACPQ,OAAO;MACPD;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,MAAMa,UAAU,GAAGhC,KAAK,CAACiC,SAAS,CAAC,CAAC;EACpC,MAAMR,OAAO,GAAGpB,YAAY,CAACL,KAAK,CAACkC,aAAa,CAAC1B,OAAO,EAAE;IACxDG,GAAG,EAAEqB;EACP,CAAC,CAAC,CAAC;EACH,OAAO;IACLN,KAAK,EAAEA,CAAA,KAAM3B,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MACxD,IAAIsB,EAAE,EAAEC,EAAE,EAAEa,EAAE;MACd,IAAI,CAACH,UAAU,CAACd,OAAO,EAAE;QACvB;QACAO,OAAO,CAAC,CAAC;QACT;QACA,CAACH,EAAE,GAAG,CAACD,EAAE,GAAGd,OAAO,CAACgB,KAAK,EAAEJ,UAAU,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACH,EAAE,CAAC;MACzF,CAAC,MAAM;QACL,CAACc,EAAE,GAAGH,UAAU,CAACd,OAAO,MAAM,IAAI,IAAIiB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACT,KAAK,CAAC,CAAC;MAC3E;IACF,CAAC,CAAC;IACFC,OAAO,EAAEpB,OAAO,IAAI;MAClB,IAAIc,EAAE;MACN,CAACA,EAAE,GAAGW,UAAU,CAACd,OAAO,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,OAAO,CAACpB,OAAO,CAAC;IACpF,CAAC;IACD6B,UAAU,EAAEA,CAAA,KAAM,CAAC,CAACJ,UAAU,CAACd;EACjC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}