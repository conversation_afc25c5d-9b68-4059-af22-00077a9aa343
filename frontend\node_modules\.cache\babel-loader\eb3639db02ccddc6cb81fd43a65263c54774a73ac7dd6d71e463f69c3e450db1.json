{"ast": null, "code": "import React from 'react';\nimport { List as RCList } from 'rc-field-form';\nimport List from '../list';\nexport const FormArray = props => {\n  return React.createElement(RCList, {\n    name: props.name,\n    initialValue: props.initialValue\n  }, (rcFields, operation) => {\n    const fields = rcFields.map(field => ({\n      index: field.name,\n      key: field.key\n    }));\n    const children = props.children(fields, operation).map((child, index) => {\n      var _a;\n      return React.createElement(List, {\n        key: fields[index].key,\n        mode: 'card',\n        header: (_a = props.renderHeader) === null || _a === void 0 ? void 0 : _a.call(props, fields[index], operation)\n      }, child);\n    });\n    if (props.renderAdd) {\n      children.push(React.createElement(List, {\n        key: 'add',\n        mode: 'card'\n      }, React.createElement(List.Item, {\n        className: 'adm-form-list-operation',\n        onClick: () => {\n          props.onAdd ? props.onAdd(operation) : operation.add();\n        },\n        arrow: false\n      }, props.renderAdd())));\n    }\n    return React.createElement(React.Fragment, null, children);\n  });\n};", "map": {"version": 3, "names": ["React", "List", "RCList", "FormArray", "props", "createElement", "name", "initialValue", "rc<PERSON>ields", "operation", "fields", "map", "field", "index", "key", "children", "child", "_a", "mode", "header", "renderHeader", "call", "renderAdd", "push", "<PERSON><PERSON>", "className", "onClick", "onAdd", "add", "arrow", "Fragment"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/form/form-array.js"], "sourcesContent": ["import React from 'react';\nimport { List as RCList } from 'rc-field-form';\nimport List from '../list';\nexport const FormArray = props => {\n  return React.createElement(RCList, {\n    name: props.name,\n    initialValue: props.initialValue\n  }, (rcFields, operation) => {\n    const fields = rcFields.map(field => ({\n      index: field.name,\n      key: field.key\n    }));\n    const children = props.children(fields, operation).map((child, index) => {\n      var _a;\n      return React.createElement(List, {\n        key: fields[index].key,\n        mode: 'card',\n        header: (_a = props.renderHeader) === null || _a === void 0 ? void 0 : _a.call(props, fields[index], operation)\n      }, child);\n    });\n    if (props.renderAdd) {\n      children.push(React.createElement(List, {\n        key: 'add',\n        mode: 'card'\n      }, React.createElement(List.Item, {\n        className: 'adm-form-list-operation',\n        onClick: () => {\n          props.onAdd ? props.onAdd(operation) : operation.add();\n        },\n        arrow: false\n      }, props.renderAdd())));\n    }\n    return React.createElement(React.Fragment, null, children);\n  });\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,IAAIC,MAAM,QAAQ,eAAe;AAC9C,OAAOD,IAAI,MAAM,SAAS;AAC1B,OAAO,MAAME,SAAS,GAAGC,KAAK,IAAI;EAChC,OAAOJ,KAAK,CAACK,aAAa,CAACH,MAAM,EAAE;IACjCI,IAAI,EAAEF,KAAK,CAACE,IAAI;IAChBC,YAAY,EAAEH,KAAK,CAACG;EACtB,CAAC,EAAE,CAACC,QAAQ,EAAEC,SAAS,KAAK;IAC1B,MAAMC,MAAM,GAAGF,QAAQ,CAACG,GAAG,CAACC,KAAK,KAAK;MACpCC,KAAK,EAAED,KAAK,CAACN,IAAI;MACjBQ,GAAG,EAAEF,KAAK,CAACE;IACb,CAAC,CAAC,CAAC;IACH,MAAMC,QAAQ,GAAGX,KAAK,CAACW,QAAQ,CAACL,MAAM,EAAED,SAAS,CAAC,CAACE,GAAG,CAAC,CAACK,KAAK,EAAEH,KAAK,KAAK;MACvE,IAAII,EAAE;MACN,OAAOjB,KAAK,CAACK,aAAa,CAACJ,IAAI,EAAE;QAC/Ba,GAAG,EAAEJ,MAAM,CAACG,KAAK,CAAC,CAACC,GAAG;QACtBI,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,CAACF,EAAE,GAAGb,KAAK,CAACgB,YAAY,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,IAAI,CAACjB,KAAK,EAAEM,MAAM,CAACG,KAAK,CAAC,EAAEJ,SAAS;MAChH,CAAC,EAAEO,KAAK,CAAC;IACX,CAAC,CAAC;IACF,IAAIZ,KAAK,CAACkB,SAAS,EAAE;MACnBP,QAAQ,CAACQ,IAAI,CAACvB,KAAK,CAACK,aAAa,CAACJ,IAAI,EAAE;QACtCa,GAAG,EAAE,KAAK;QACVI,IAAI,EAAE;MACR,CAAC,EAAElB,KAAK,CAACK,aAAa,CAACJ,IAAI,CAACuB,IAAI,EAAE;QAChCC,SAAS,EAAE,yBAAyB;QACpCC,OAAO,EAAEA,CAAA,KAAM;UACbtB,KAAK,CAACuB,KAAK,GAAGvB,KAAK,CAACuB,KAAK,CAAClB,SAAS,CAAC,GAAGA,SAAS,CAACmB,GAAG,CAAC,CAAC;QACxD,CAAC;QACDC,KAAK,EAAE;MACT,CAAC,EAAEzB,KAAK,CAACkB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB;IACA,OAAOtB,KAAK,CAACK,aAAa,CAACL,KAAK,CAAC8B,QAAQ,EAAE,IAAI,EAAEf,QAAQ,CAAC;EAC5D,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}