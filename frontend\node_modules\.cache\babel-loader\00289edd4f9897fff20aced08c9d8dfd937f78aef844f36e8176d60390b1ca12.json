{"ast": null, "code": "import React, { useState } from 'react';\nimport classNames from 'classnames';\nconst classPrefix = `adm-index-bar`;\nexport const Sidebar = props => {\n  const [interacting, setInteracting] = useState(false);\n  return React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-sidebar`, {\n      [`${classPrefix}-sidebar-interacting`]: interacting\n    }),\n    onMouseDown: () => {\n      setInteracting(true);\n    },\n    onMouseUp: () => {\n      setInteracting(false);\n    },\n    onTouchStart: () => {\n      setInteracting(true);\n    },\n    onTouchEnd: () => {\n      setInteracting(false);\n    },\n    onTouchMove: e => {\n      if (!interacting) return;\n      const {\n        clientX,\n        clientY\n      } = e.touches[0];\n      const target = document.elementFromPoint(clientX, clientY);\n      if (!target) return;\n      const index = target.dataset['index'];\n      if (index) {\n        props.onActive(index);\n      }\n    }\n  }, props.indexItems.map(({\n    index,\n    brief\n  }) => {\n    const active = index === props.activeIndex;\n    return React.createElement(\"div\", {\n      className: `${classPrefix}-sidebar-row`,\n      onMouseDown: () => {\n        props.onActive(index);\n      },\n      onTouchStart: () => {\n        props.onActive(index);\n      },\n      onMouseEnter: () => {\n        if (interacting) {\n          props.onActive(index);\n        }\n      },\n      \"data-index\": index,\n      key: index\n    }, interacting && active && React.createElement(\"div\", {\n      className: `${classPrefix}-sidebar-bubble`\n    }, brief), React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-sidebar-item`, {\n        [`${classPrefix}-sidebar-item-active`]: active\n      }),\n      \"data-index\": index\n    }, React.createElement(\"div\", null, brief)));\n  }));\n};", "map": {"version": 3, "names": ["React", "useState", "classNames", "classPrefix", "Sidebar", "props", "interacting", "setInteracting", "createElement", "className", "onMouseDown", "onMouseUp", "onTouchStart", "onTouchEnd", "onTouchMove", "e", "clientX", "clientY", "touches", "target", "document", "elementFromPoint", "index", "dataset", "onActive", "indexItems", "map", "brief", "active", "activeIndex", "onMouseEnter", "key"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/index-bar/sidebar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport classNames from 'classnames';\nconst classPrefix = `adm-index-bar`;\nexport const Sidebar = props => {\n  const [interacting, setInteracting] = useState(false);\n  return React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-sidebar`, {\n      [`${classPrefix}-sidebar-interacting`]: interacting\n    }),\n    onMouseDown: () => {\n      setInteracting(true);\n    },\n    onMouseUp: () => {\n      setInteracting(false);\n    },\n    onTouchStart: () => {\n      setInteracting(true);\n    },\n    onTouchEnd: () => {\n      setInteracting(false);\n    },\n    onTouchMove: e => {\n      if (!interacting) return;\n      const {\n        clientX,\n        clientY\n      } = e.touches[0];\n      const target = document.elementFromPoint(clientX, clientY);\n      if (!target) return;\n      const index = target.dataset['index'];\n      if (index) {\n        props.onActive(index);\n      }\n    }\n  }, props.indexItems.map(({\n    index,\n    brief\n  }) => {\n    const active = index === props.activeIndex;\n    return React.createElement(\"div\", {\n      className: `${classPrefix}-sidebar-row`,\n      onMouseDown: () => {\n        props.onActive(index);\n      },\n      onTouchStart: () => {\n        props.onActive(index);\n      },\n      onMouseEnter: () => {\n        if (interacting) {\n          props.onActive(index);\n        }\n      },\n      \"data-index\": index,\n      key: index\n    }, interacting && active && React.createElement(\"div\", {\n      className: `${classPrefix}-sidebar-bubble`\n    }, brief), React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-sidebar-item`, {\n        [`${classPrefix}-sidebar-item-active`]: active\n      }),\n      \"data-index\": index\n    }, React.createElement(\"div\", null, brief)));\n  }));\n};"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,YAAY;AACnC,MAAMC,WAAW,GAAG,eAAe;AACnC,OAAO,MAAMC,OAAO,GAAGC,KAAK,IAAI;EAC9B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGN,QAAQ,CAAC,KAAK,CAAC;EACrD,OAAOD,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAE;IAChCC,SAAS,EAAEP,UAAU,CAAC,GAAGC,WAAW,UAAU,EAAE;MAC9C,CAAC,GAAGA,WAAW,sBAAsB,GAAGG;IAC1C,CAAC,CAAC;IACFI,WAAW,EAAEA,CAAA,KAAM;MACjBH,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC;IACDI,SAAS,EAAEA,CAAA,KAAM;MACfJ,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC;IACDK,YAAY,EAAEA,CAAA,KAAM;MAClBL,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC;IACDM,UAAU,EAAEA,CAAA,KAAM;MAChBN,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC;IACDO,WAAW,EAAEC,CAAC,IAAI;MAChB,IAAI,CAACT,WAAW,EAAE;MAClB,MAAM;QACJU,OAAO;QACPC;MACF,CAAC,GAAGF,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC;MAChB,MAAMC,MAAM,GAAGC,QAAQ,CAACC,gBAAgB,CAACL,OAAO,EAAEC,OAAO,CAAC;MAC1D,IAAI,CAACE,MAAM,EAAE;MACb,MAAMG,KAAK,GAAGH,MAAM,CAACI,OAAO,CAAC,OAAO,CAAC;MACrC,IAAID,KAAK,EAAE;QACTjB,KAAK,CAACmB,QAAQ,CAACF,KAAK,CAAC;MACvB;IACF;EACF,CAAC,EAAEjB,KAAK,CAACoB,UAAU,CAACC,GAAG,CAAC,CAAC;IACvBJ,KAAK;IACLK;EACF,CAAC,KAAK;IACJ,MAAMC,MAAM,GAAGN,KAAK,KAAKjB,KAAK,CAACwB,WAAW;IAC1C,OAAO7B,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAE;MAChCC,SAAS,EAAE,GAAGN,WAAW,cAAc;MACvCO,WAAW,EAAEA,CAAA,KAAM;QACjBL,KAAK,CAACmB,QAAQ,CAACF,KAAK,CAAC;MACvB,CAAC;MACDV,YAAY,EAAEA,CAAA,KAAM;QAClBP,KAAK,CAACmB,QAAQ,CAACF,KAAK,CAAC;MACvB,CAAC;MACDQ,YAAY,EAAEA,CAAA,KAAM;QAClB,IAAIxB,WAAW,EAAE;UACfD,KAAK,CAACmB,QAAQ,CAACF,KAAK,CAAC;QACvB;MACF,CAAC;MACD,YAAY,EAAEA,KAAK;MACnBS,GAAG,EAAET;IACP,CAAC,EAAEhB,WAAW,IAAIsB,MAAM,IAAI5B,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAE;MACrDC,SAAS,EAAE,GAAGN,WAAW;IAC3B,CAAC,EAAEwB,KAAK,CAAC,EAAE3B,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAE;MACpCC,SAAS,EAAEP,UAAU,CAAC,GAAGC,WAAW,eAAe,EAAE;QACnD,CAAC,GAAGA,WAAW,sBAAsB,GAAGyB;MAC1C,CAAC,CAAC;MACF,YAAY,EAAEN;IAChB,CAAC,EAAEtB,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAE,IAAI,EAAEmB,KAAK,CAAC,CAAC,CAAC;EAC9C,CAAC,CAAC,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}