{"ast": null, "code": "import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nconst classPrefix = 'adm-auto-center';\nexport const AutoCenter = props => withNativeProps(props, React.createElement(\"div\", {\n  className: classPrefix\n}, React.createElement(\"div\", {\n  className: `${classPrefix}-content`\n}, props.children)));", "map": {"version": 3, "names": ["React", "withNativeProps", "classPrefix", "AutoCenter", "props", "createElement", "className", "children"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/auto-center/auto-center.js"], "sourcesContent": ["import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nconst classPrefix = 'adm-auto-center';\nexport const AutoCenter = props => withNativeProps(props, React.createElement(\"div\", {\n  className: classPrefix\n}, React.createElement(\"div\", {\n  className: `${classPrefix}-content`\n}, props.children)));"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,MAAMC,WAAW,GAAG,iBAAiB;AACrC,OAAO,MAAMC,UAAU,GAAGC,KAAK,IAAIH,eAAe,CAACG,KAAK,EAAEJ,KAAK,CAACK,aAAa,CAAC,KAAK,EAAE;EACnFC,SAAS,EAAEJ;AACb,CAAC,EAAEF,KAAK,CAACK,aAAa,CAAC,KAAK,EAAE;EAC5BC,SAAS,EAAE,GAAGJ,WAAW;AAC3B,CAAC,EAAEE,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}