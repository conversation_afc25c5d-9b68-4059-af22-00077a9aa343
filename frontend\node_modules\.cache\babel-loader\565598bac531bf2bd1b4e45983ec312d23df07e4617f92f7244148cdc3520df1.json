{"ast": null, "code": "import { __rest } from \"tslib\";\nimport React, { forwardRef, useMemo } from 'react';\nimport classNames from 'classnames';\nimport List from '../list';\nimport RcForm from 'rc-field-form';\nimport { defaultFormContext, FormContext } from './context';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { Header } from './header';\nimport { useConfig } from '../config-provider';\nimport merge from 'deepmerge';\nimport { FormArray } from './form-array';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\nconst classPrefix = 'adm-form';\nconst defaultProps = defaultFormContext;\nexport const Form = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const {\n      className,\n      style,\n      hasFeedback,\n      children,\n      layout,\n      footer,\n      mode,\n      disabled,\n      requiredMarkStyle\n    } = props,\n    formProps = __rest(props, [\"className\", \"style\", \"hasFeedback\", \"children\", \"layout\", \"footer\", \"mode\", \"disabled\", \"requiredMarkStyle\"]);\n  const {\n    locale\n  } = useConfig();\n  const validateMessages = useMemo(() => merge(locale.Form.defaultValidateMessages, formProps.validateMessages || {}), [locale.Form.defaultValidateMessages, formProps.validateMessages]);\n  const lists = [];\n  let currentHeader = null;\n  let items = [];\n  let count = 0;\n  function collect() {\n    if (items.length === 0) return;\n    count += 1;\n    lists.push(React.createElement(List, {\n      header: currentHeader,\n      key: count,\n      mode: mode\n    }, items));\n    items = [];\n  }\n  traverseReactNode(props.children, child => {\n    if (React.isValidElement(child)) {\n      if (child.type === Header) {\n        collect();\n        currentHeader = child.props.children;\n        return;\n      }\n      if (child.type === FormArray) {\n        collect();\n        lists.push(child);\n        return;\n      }\n    }\n    items.push(child);\n  });\n  collect();\n  return React.createElement(RcForm, Object.assign({\n    className: classNames(classPrefix, className),\n    style: style,\n    ref: ref\n  }, formProps, {\n    validateMessages: validateMessages\n  }), React.createElement(FormContext.Provider, {\n    value: {\n      name: formProps.name,\n      hasFeedback,\n      layout,\n      requiredMarkStyle,\n      disabled\n    }\n  }, lists), footer && React.createElement(\"div\", {\n    className: `${classPrefix}-footer`\n  }, footer));\n});", "map": {"version": 3, "names": ["__rest", "React", "forwardRef", "useMemo", "classNames", "List", "RcForm", "defaultFormContext", "FormContext", "mergeProps", "Header", "useConfig", "merge", "FormArray", "traverseReactNode", "classPrefix", "defaultProps", "Form", "p", "ref", "props", "className", "style", "hasFeedback", "children", "layout", "footer", "mode", "disabled", "requiredMarkStyle", "formProps", "locale", "validateMessages", "defaultValidateMessages", "lists", "<PERSON><PERSON><PERSON><PERSON>", "items", "count", "collect", "length", "push", "createElement", "header", "key", "child", "isValidElement", "type", "Object", "assign", "Provider", "value", "name"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/form/form.js"], "sourcesContent": ["import { __rest } from \"tslib\";\nimport React, { forwardRef, useMemo } from 'react';\nimport classNames from 'classnames';\nimport List from '../list';\nimport RcForm from 'rc-field-form';\nimport { defaultFormContext, FormContext } from './context';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { Header } from './header';\nimport { useConfig } from '../config-provider';\nimport merge from 'deepmerge';\nimport { FormArray } from './form-array';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\nconst classPrefix = 'adm-form';\nconst defaultProps = defaultFormContext;\nexport const Form = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const {\n      className,\n      style,\n      hasFeedback,\n      children,\n      layout,\n      footer,\n      mode,\n      disabled,\n      requiredMarkStyle\n    } = props,\n    formProps = __rest(props, [\"className\", \"style\", \"hasFeedback\", \"children\", \"layout\", \"footer\", \"mode\", \"disabled\", \"requiredMarkStyle\"]);\n  const {\n    locale\n  } = useConfig();\n  const validateMessages = useMemo(() => merge(locale.Form.defaultValidateMessages, formProps.validateMessages || {}), [locale.Form.defaultValidateMessages, formProps.validateMessages]);\n  const lists = [];\n  let currentHeader = null;\n  let items = [];\n  let count = 0;\n  function collect() {\n    if (items.length === 0) return;\n    count += 1;\n    lists.push(React.createElement(List, {\n      header: currentHeader,\n      key: count,\n      mode: mode\n    }, items));\n    items = [];\n  }\n  traverseReactNode(props.children, child => {\n    if (React.isValidElement(child)) {\n      if (child.type === Header) {\n        collect();\n        currentHeader = child.props.children;\n        return;\n      }\n      if (child.type === FormArray) {\n        collect();\n        lists.push(child);\n        return;\n      }\n    }\n    items.push(child);\n  });\n  collect();\n  return React.createElement(RcForm, Object.assign({\n    className: classNames(classPrefix, className),\n    style: style,\n    ref: ref\n  }, formProps, {\n    validateMessages: validateMessages\n  }), React.createElement(FormContext.Provider, {\n    value: {\n      name: formProps.name,\n      hasFeedback,\n      layout,\n      requiredMarkStyle,\n      disabled\n    }\n  }, lists), footer && React.createElement(\"div\", {\n    className: `${classPrefix}-footer`\n  }, footer));\n});"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,KAAK,IAAIC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,MAAM,MAAM,eAAe;AAClC,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,WAAW;AAC3D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,KAAK,MAAM,WAAW;AAC7B,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,MAAMC,WAAW,GAAG,UAAU;AAC9B,MAAMC,YAAY,GAAGT,kBAAkB;AACvC,OAAO,MAAMU,IAAI,GAAGf,UAAU,CAAC,CAACgB,CAAC,EAAEC,GAAG,KAAK;EACzC,MAAMC,KAAK,GAAGX,UAAU,CAACO,YAAY,EAAEE,CAAC,CAAC;EACzC,MAAM;MACFG,SAAS;MACTC,KAAK;MACLC,WAAW;MACXC,QAAQ;MACRC,MAAM;MACNC,MAAM;MACNC,IAAI;MACJC,QAAQ;MACRC;IACF,CAAC,GAAGT,KAAK;IACTU,SAAS,GAAG9B,MAAM,CAACoB,KAAK,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,mBAAmB,CAAC,CAAC;EAC3I,MAAM;IACJW;EACF,CAAC,GAAGpB,SAAS,CAAC,CAAC;EACf,MAAMqB,gBAAgB,GAAG7B,OAAO,CAAC,MAAMS,KAAK,CAACmB,MAAM,CAACd,IAAI,CAACgB,uBAAuB,EAAEH,SAAS,CAACE,gBAAgB,IAAI,CAAC,CAAC,CAAC,EAAE,CAACD,MAAM,CAACd,IAAI,CAACgB,uBAAuB,EAAEH,SAAS,CAACE,gBAAgB,CAAC,CAAC;EACvL,MAAME,KAAK,GAAG,EAAE;EAChB,IAAIC,aAAa,GAAG,IAAI;EACxB,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,KAAK,GAAG,CAAC;EACb,SAASC,OAAOA,CAAA,EAAG;IACjB,IAAIF,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;IACxBF,KAAK,IAAI,CAAC;IACVH,KAAK,CAACM,IAAI,CAACvC,KAAK,CAACwC,aAAa,CAACpC,IAAI,EAAE;MACnCqC,MAAM,EAAEP,aAAa;MACrBQ,GAAG,EAAEN,KAAK;MACVV,IAAI,EAAEA;IACR,CAAC,EAAES,KAAK,CAAC,CAAC;IACVA,KAAK,GAAG,EAAE;EACZ;EACAtB,iBAAiB,CAACM,KAAK,CAACI,QAAQ,EAAEoB,KAAK,IAAI;IACzC,IAAI3C,KAAK,CAAC4C,cAAc,CAACD,KAAK,CAAC,EAAE;MAC/B,IAAIA,KAAK,CAACE,IAAI,KAAKpC,MAAM,EAAE;QACzB4B,OAAO,CAAC,CAAC;QACTH,aAAa,GAAGS,KAAK,CAACxB,KAAK,CAACI,QAAQ;QACpC;MACF;MACA,IAAIoB,KAAK,CAACE,IAAI,KAAKjC,SAAS,EAAE;QAC5ByB,OAAO,CAAC,CAAC;QACTJ,KAAK,CAACM,IAAI,CAACI,KAAK,CAAC;QACjB;MACF;IACF;IACAR,KAAK,CAACI,IAAI,CAACI,KAAK,CAAC;EACnB,CAAC,CAAC;EACFN,OAAO,CAAC,CAAC;EACT,OAAOrC,KAAK,CAACwC,aAAa,CAACnC,MAAM,EAAEyC,MAAM,CAACC,MAAM,CAAC;IAC/C3B,SAAS,EAAEjB,UAAU,CAACW,WAAW,EAAEM,SAAS,CAAC;IAC7CC,KAAK,EAAEA,KAAK;IACZH,GAAG,EAAEA;EACP,CAAC,EAAEW,SAAS,EAAE;IACZE,gBAAgB,EAAEA;EACpB,CAAC,CAAC,EAAE/B,KAAK,CAACwC,aAAa,CAACjC,WAAW,CAACyC,QAAQ,EAAE;IAC5CC,KAAK,EAAE;MACLC,IAAI,EAAErB,SAAS,CAACqB,IAAI;MACpB5B,WAAW;MACXE,MAAM;MACNI,iBAAiB;MACjBD;IACF;EACF,CAAC,EAAEM,KAAK,CAAC,EAAER,MAAM,IAAIzB,KAAK,CAACwC,aAAa,CAAC,KAAK,EAAE;IAC9CpB,SAAS,EAAE,GAAGN,WAAW;EAC3B,CAAC,EAAEW,MAAM,CAAC,CAAC;AACb,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}