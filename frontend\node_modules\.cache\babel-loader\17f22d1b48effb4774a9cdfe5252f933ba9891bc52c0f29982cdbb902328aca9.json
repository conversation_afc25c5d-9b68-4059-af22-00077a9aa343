{"ast": null, "code": "import * as React from \"react\";\nfunction SmileOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"SmileOutline-SmileOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"SmileOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"SmileOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M14.0458046,26.5174634 C14.1287685,26.3971688 14.2935421,26.3669063 14.4138242,26.4498883 C14.4436571,26.4704632 14.4708829,26.4890663 14.4955018,26.5056975 C17.208648,28.3385613 20.4793419,29.408785 24,29.408785 C27.5197511,29.408785 30.7896669,28.3391126 33.5024013,26.5071138 L33.5024013,26.5071138 L33.5581039,26.4770566 C33.5969468,26.4608325 33.6388099,26.4523573 33.6813205,26.4523573 C33.8578812,26.4523573 34.0010118,26.595488 34.0009735,26.7720486 L34.0009735,26.7720486 L34.0006746,29.4995669 C34.0006492,29.6440904 33.9226882,29.7773677 33.7966693,29.8481229 C33.6527935,29.9290566 33.537965,29.9925674 33.4521837,30.0386552 C30.6375036,31.5509024 27.4189364,32.408785 24,32.408785 C20.5458697,32.408785 17.296255,31.5331498 14.4610235,29.991747 C14.3953412,29.9560382 14.3093075,29.9081912 14.2029223,29.848206 C14.0772269,29.7773329 13.9994637,29.6442379 13.9993289,29.4999385 L13.9993289,29.4999385 L13.9990265,26.6676828 L13.9990265,26.6676828 L14.0043844,26.614705 C14.0114748,26.5800114 14.0254953,26.5469111 14.0458046,26.5174634 Z M16,18 C17.1045695,18 18,18.8954305 18,20 C18,21.1045695 17.1045695,22 16,22 C14.8954305,22 14,21.1045695 14,20 C14,18.8954305 14.8954305,18 16,18 Z M32,18 C33.1045695,18 34,18.8954305 34,20 C34,21.1045695 33.1045695,22 32,22 C30.8954305,22 30,21.1045695 30,20 C30,18.8954305 30.8954305,18 32,18 Z\",\n    id: \"SmileOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default SmileOutline;", "map": {"version": 3, "names": ["React", "SmileOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/SmileOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction SmileOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"SmileOutline-SmileOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"SmileOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"SmileOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M14.0458046,26.5174634 C14.1287685,26.3971688 14.2935421,26.3669063 14.4138242,26.4498883 C14.4436571,26.4704632 14.4708829,26.4890663 14.4955018,26.5056975 C17.208648,28.3385613 20.4793419,29.408785 24,29.408785 C27.5197511,29.408785 30.7896669,28.3391126 33.5024013,26.5071138 L33.5024013,26.5071138 L33.5581039,26.4770566 C33.5969468,26.4608325 33.6388099,26.4523573 33.6813205,26.4523573 C33.8578812,26.4523573 34.0010118,26.595488 34.0009735,26.7720486 L34.0009735,26.7720486 L34.0006746,29.4995669 C34.0006492,29.6440904 33.9226882,29.7773677 33.7966693,29.8481229 C33.6527935,29.9290566 33.537965,29.9925674 33.4521837,30.0386552 C30.6375036,31.5509024 27.4189364,32.408785 24,32.408785 C20.5458697,32.408785 17.296255,31.5331498 14.4610235,29.991747 C14.3953412,29.9560382 14.3093075,29.9081912 14.2029223,29.848206 C14.0772269,29.7773329 13.9994637,29.6442379 13.9993289,29.4999385 L13.9993289,29.4999385 L13.9990265,26.6676828 L13.9990265,26.6676828 L14.0043844,26.614705 C14.0114748,26.5800114 14.0254953,26.5469111 14.0458046,26.5174634 Z M16,18 C17.1045695,18 18,18.8954305 18,20 C18,21.1045695 17.1045695,22 16,22 C14.8954305,22 14,21.1045695 14,20 C14,18.8954305 14.8954305,18 16,18 Z M32,18 C33.1045695,18 34,18.8954305 34,20 C34,21.1045695 33.1045695,22 32,22 C30.8954305,22 30,21.1045695 30,20 C30,18.8954305 30.8954305,18 32,18 Z\",\n    id: \"SmileOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default SmileOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,2BAA2B;IAC/BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,2BAA2B;IAC/BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,kmDAAkmD;IACrmDR,EAAE,EAAE,uCAAuC;IAC3CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}