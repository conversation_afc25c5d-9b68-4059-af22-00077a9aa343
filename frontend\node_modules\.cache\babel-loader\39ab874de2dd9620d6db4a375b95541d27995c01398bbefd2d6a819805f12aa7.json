{"ast": null, "code": "import { __rest } from \"tslib\";\nimport React, { useContext } from 'react';\nimport zhCN from '../../locales/zh-CN';\nexport const defaultConfigRef = {\n  current: {\n    locale: zhCN\n  }\n};\nexport function setDefaultConfig(config) {\n  defaultConfigRef.current = config;\n}\nexport function getDefaultConfig() {\n  return defaultConfigRef.current;\n}\nconst ConfigContext = React.createContext(null);\nexport const ConfigProvider = props => {\n  const {\n      children\n    } = props,\n    config = __rest(props, [\"children\"]);\n  const parentConfig = useConfig();\n  return React.createElement(ConfigContext.Provider, {\n    value: Object.assign(Object.assign({}, parentConfig), config)\n  }, children);\n};\nexport function useConfig() {\n  var _a;\n  return (_a = useContext(ConfigContext)) !== null && _a !== void 0 ? _a : getDefaultConfig();\n}", "map": {"version": 3, "names": ["__rest", "React", "useContext", "zhCN", "defaultConfigRef", "current", "locale", "setDefaultConfig", "config", "getDefaultConfig", "ConfigContext", "createContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "children", "parentConfig", "useConfig", "createElement", "Provider", "value", "Object", "assign", "_a"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/config-provider/config-provider.js"], "sourcesContent": ["import { __rest } from \"tslib\";\nimport React, { useContext } from 'react';\nimport zhCN from '../../locales/zh-CN';\nexport const defaultConfigRef = {\n  current: {\n    locale: zhCN\n  }\n};\nexport function setDefaultConfig(config) {\n  defaultConfigRef.current = config;\n}\nexport function getDefaultConfig() {\n  return defaultConfigRef.current;\n}\nconst ConfigContext = React.createContext(null);\nexport const ConfigProvider = props => {\n  const {\n      children\n    } = props,\n    config = __rest(props, [\"children\"]);\n  const parentConfig = useConfig();\n  return React.createElement(ConfigContext.Provider, {\n    value: Object.assign(Object.assign({}, parentConfig), config)\n  }, children);\n};\nexport function useConfig() {\n  var _a;\n  return (_a = useContext(ConfigContext)) !== null && _a !== void 0 ? _a : getDefaultConfig();\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAOC,IAAI,MAAM,qBAAqB;AACtC,OAAO,MAAMC,gBAAgB,GAAG;EAC9BC,OAAO,EAAE;IACPC,MAAM,EAAEH;EACV;AACF,CAAC;AACD,OAAO,SAASI,gBAAgBA,CAACC,MAAM,EAAE;EACvCJ,gBAAgB,CAACC,OAAO,GAAGG,MAAM;AACnC;AACA,OAAO,SAASC,gBAAgBA,CAAA,EAAG;EACjC,OAAOL,gBAAgB,CAACC,OAAO;AACjC;AACA,MAAMK,aAAa,GAAGT,KAAK,CAACU,aAAa,CAAC,IAAI,CAAC;AAC/C,OAAO,MAAMC,cAAc,GAAGC,KAAK,IAAI;EACrC,MAAM;MACFC;IACF,CAAC,GAAGD,KAAK;IACTL,MAAM,GAAGR,MAAM,CAACa,KAAK,EAAE,CAAC,UAAU,CAAC,CAAC;EACtC,MAAME,YAAY,GAAGC,SAAS,CAAC,CAAC;EAChC,OAAOf,KAAK,CAACgB,aAAa,CAACP,aAAa,CAACQ,QAAQ,EAAE;IACjDC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,YAAY,CAAC,EAAEP,MAAM;EAC9D,CAAC,EAAEM,QAAQ,CAAC;AACd,CAAC;AACD,OAAO,SAASE,SAASA,CAAA,EAAG;EAC1B,IAAIM,EAAE;EACN,OAAO,CAACA,EAAE,GAAGpB,UAAU,CAACQ,aAAa,CAAC,MAAM,IAAI,IAAIY,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGb,gBAAgB,CAAC,CAAC;AAC7F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}