{"ast": null, "code": "import { useEvent } from 'rc-util';\nimport React from 'react';\nimport { unstable_batchedUpdates } from 'react-dom';\nimport runes from 'runes2';\nconst ELLIPSIS_TEXT = '...';\nconst measureStyle = {\n  visibility: 'hidden',\n  whiteSpace: 'inherit',\n  lineHeight: 'inherit',\n  fontSize: 'inherit'\n};\nexport default function useMeasure(containerRef, content, rows, direction, expanded, expandNode, collapseNode) {\n  const contentChars = React.useMemo(() => runes(content), [content]);\n  const [maxHeight, setMaxHeight] = React.useState(0);\n  const [walkingIndexes, setWalkingIndexes] = React.useState([0, 0]);\n  const midIndex = Math.ceil((walkingIndexes[0] + walkingIndexes[1]) / 2);\n  const [status, setStatus] = React.useState(100 /* STABLE_NO_ELLIPSIS */);\n  // ============================ Refs ============================\n  const singleRowMeasureRef = React.useRef(null);\n  const fullMeasureRef = React.useRef(null);\n  const midMeasureRef = React.useRef(null);\n  const startMeasure = useEvent(() => {\n    // use batch update to avoid async update trigger 2 render\n    unstable_batchedUpdates(() => {\n      setStatus(1 /* PREPARE */);\n      setWalkingIndexes([0, direction === 'middle' ? Math.ceil(contentChars.length / 2) : contentChars.length]);\n    });\n  });\n  // Initialize\n  React.useLayoutEffect(() => {\n    startMeasure();\n  }, [contentChars, rows]);\n  // Measure element height\n  React.useLayoutEffect(() => {\n    var _a, _b;\n    if (status === 1 /* PREPARE */) {\n      const fullMeasureHeight = ((_a = fullMeasureRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0;\n      const singleRowMeasureHeight = ((_b = singleRowMeasureRef.current) === null || _b === void 0 ? void 0 : _b.offsetHeight) || 0;\n      const rowMeasureHeight = singleRowMeasureHeight * (rows + 0.5);\n      if (fullMeasureHeight <= rowMeasureHeight) {\n        setStatus(100 /* STABLE_NO_ELLIPSIS */);\n      } else {\n        setMaxHeight(rowMeasureHeight);\n        setStatus(2 /* MEASURE_WALKING */);\n      }\n    }\n  }, [status]);\n  // Walking measure\n  React.useLayoutEffect(() => {\n    var _a;\n    if (status === 2 /* MEASURE_WALKING */) {\n      const diff = walkingIndexes[1] - walkingIndexes[0];\n      const midHeight = ((_a = midMeasureRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0;\n      if (diff > 1) {\n        if (midHeight > maxHeight) {\n          setWalkingIndexes([walkingIndexes[0], midIndex]);\n        } else {\n          setWalkingIndexes([midIndex, walkingIndexes[1]]);\n        }\n      } else {\n        if (midHeight > maxHeight) {\n          setWalkingIndexes([walkingIndexes[0], walkingIndexes[0]]);\n        } else {\n          setWalkingIndexes([walkingIndexes[1], walkingIndexes[1]]);\n        }\n        setStatus(99 /* STABLE_ELLIPSIS */);\n      }\n    }\n  }, [status, walkingIndexes]);\n  // =========================== Render ===========================\n  /** Render by cut index */\n  const renderContent = index => {\n    const prefixContent = contentChars.slice(0, index);\n    const suffixContent = contentChars.slice(contentChars.length - index);\n    return React.createElement(React.Fragment, null, direction === 'start' && React.createElement(React.Fragment, null, expandNode, ELLIPSIS_TEXT), direction !== 'start' && prefixContent.join(''), direction === 'middle' && React.createElement(React.Fragment, null, ELLIPSIS_TEXT, expandNode, ELLIPSIS_TEXT), direction !== 'end' && suffixContent.join(''), direction === 'end' && React.createElement(React.Fragment, null, ELLIPSIS_TEXT, expandNode));\n  };\n  const finalContent = React.useMemo(() => {\n    if (expanded || status === 100 /* STABLE_NO_ELLIPSIS */) {\n      return React.createElement(React.Fragment, {\n        key: 'display'\n      }, content, status === 99 /* STABLE_ELLIPSIS */ && collapseNode);\n    }\n    if (status === 99 /* STABLE_ELLIPSIS */) {\n      return renderContent(midIndex);\n    }\n    return null;\n  }, [expanded, status, content, collapseNode, midIndex]);\n  const allNodes = React.createElement(React.Fragment, null, status === 1 /* PREPARE */ && React.createElement(\"div\", {\n    key: 'full',\n    \"aria-hidden\": true,\n    ref: fullMeasureRef,\n    style: measureStyle\n  }, content, expandNode), status === 1 /* PREPARE */ && React.createElement(\"div\", {\n    key: 'stable',\n    \"aria-hidden\": true,\n    ref: singleRowMeasureRef,\n    style: measureStyle\n  }, '\\u00A0'), status === 2 /* MEASURE_WALKING */ && React.createElement(\"div\", {\n    key: 'walking-mid',\n    \"aria-hidden\": true,\n    ref: midMeasureRef,\n    style: measureStyle\n  }, renderContent(midIndex)), finalContent);\n  return [allNodes, startMeasure];\n}", "map": {"version": 3, "names": ["useEvent", "React", "unstable_batchedUpdates", "runes", "ELLIPSIS_TEXT", "measureStyle", "visibility", "whiteSpace", "lineHeight", "fontSize", "useMeasure", "containerRef", "content", "rows", "direction", "expanded", "expandNode", "collapseNode", "contentChars", "useMemo", "maxHeight", "setMaxHeight", "useState", "walkingIndexes", "setWalkingIndexes", "midIndex", "Math", "ceil", "status", "setStatus", "singleRowMeasureRef", "useRef", "fullMeasureRef", "midMeasureRef", "startMeasure", "length", "useLayoutEffect", "_a", "_b", "fullMeasureHeight", "current", "offsetHeight", "singleRowMeasureHeight", "rowMeasureHeight", "diff", "midHeight", "renderContent", "index", "prefixContent", "slice", "suffixContent", "createElement", "Fragment", "join", "finalContent", "key", "allNodes", "ref", "style"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/ellipsis/useMeasure.js"], "sourcesContent": ["import { useEvent } from 'rc-util';\nimport React from 'react';\nimport { unstable_batchedUpdates } from 'react-dom';\nimport runes from 'runes2';\nconst ELLIPSIS_TEXT = '...';\nconst measureStyle = {\n  visibility: 'hidden',\n  whiteSpace: 'inherit',\n  lineHeight: 'inherit',\n  fontSize: 'inherit'\n};\nexport default function useMeasure(containerRef, content, rows, direction, expanded, expandNode, collapseNode) {\n  const contentChars = React.useMemo(() => runes(content), [content]);\n  const [maxHeight, setMaxHeight] = React.useState(0);\n  const [walkingIndexes, setWalkingIndexes] = React.useState([0, 0]);\n  const midIndex = Math.ceil((walkingIndexes[0] + walkingIndexes[1]) / 2);\n  const [status, setStatus] = React.useState(100 /* STABLE_NO_ELLIPSIS */);\n  // ============================ Refs ============================\n  const singleRowMeasureRef = React.useRef(null);\n  const fullMeasureRef = React.useRef(null);\n  const midMeasureRef = React.useRef(null);\n  const startMeasure = useEvent(() => {\n    // use batch update to avoid async update trigger 2 render\n    unstable_batchedUpdates(() => {\n      setStatus(1 /* PREPARE */);\n      setWalkingIndexes([0, direction === 'middle' ? Math.ceil(contentChars.length / 2) : contentChars.length]);\n    });\n  });\n  // Initialize\n  React.useLayoutEffect(() => {\n    startMeasure();\n  }, [contentChars, rows]);\n  // Measure element height\n  React.useLayoutEffect(() => {\n    var _a, _b;\n    if (status === 1 /* PREPARE */) {\n      const fullMeasureHeight = ((_a = fullMeasureRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0;\n      const singleRowMeasureHeight = ((_b = singleRowMeasureRef.current) === null || _b === void 0 ? void 0 : _b.offsetHeight) || 0;\n      const rowMeasureHeight = singleRowMeasureHeight * (rows + 0.5);\n      if (fullMeasureHeight <= rowMeasureHeight) {\n        setStatus(100 /* STABLE_NO_ELLIPSIS */);\n      } else {\n        setMaxHeight(rowMeasureHeight);\n        setStatus(2 /* MEASURE_WALKING */);\n      }\n    }\n  }, [status]);\n  // Walking measure\n  React.useLayoutEffect(() => {\n    var _a;\n    if (status === 2 /* MEASURE_WALKING */) {\n      const diff = walkingIndexes[1] - walkingIndexes[0];\n      const midHeight = ((_a = midMeasureRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0;\n      if (diff > 1) {\n        if (midHeight > maxHeight) {\n          setWalkingIndexes([walkingIndexes[0], midIndex]);\n        } else {\n          setWalkingIndexes([midIndex, walkingIndexes[1]]);\n        }\n      } else {\n        if (midHeight > maxHeight) {\n          setWalkingIndexes([walkingIndexes[0], walkingIndexes[0]]);\n        } else {\n          setWalkingIndexes([walkingIndexes[1], walkingIndexes[1]]);\n        }\n        setStatus(99 /* STABLE_ELLIPSIS */);\n      }\n    }\n  }, [status, walkingIndexes]);\n  // =========================== Render ===========================\n  /** Render by cut index */\n  const renderContent = index => {\n    const prefixContent = contentChars.slice(0, index);\n    const suffixContent = contentChars.slice(contentChars.length - index);\n    return React.createElement(React.Fragment, null, direction === 'start' && React.createElement(React.Fragment, null, expandNode, ELLIPSIS_TEXT), direction !== 'start' && prefixContent.join(''), direction === 'middle' && React.createElement(React.Fragment, null, ELLIPSIS_TEXT, expandNode, ELLIPSIS_TEXT), direction !== 'end' && suffixContent.join(''), direction === 'end' && React.createElement(React.Fragment, null, ELLIPSIS_TEXT, expandNode));\n  };\n  const finalContent = React.useMemo(() => {\n    if (expanded || status === 100 /* STABLE_NO_ELLIPSIS */) {\n      return React.createElement(React.Fragment, {\n        key: 'display'\n      }, content, status === 99 /* STABLE_ELLIPSIS */ && collapseNode);\n    }\n    if (status === 99 /* STABLE_ELLIPSIS */) {\n      return renderContent(midIndex);\n    }\n    return null;\n  }, [expanded, status, content, collapseNode, midIndex]);\n  const allNodes = React.createElement(React.Fragment, null, status === 1 /* PREPARE */ && React.createElement(\"div\", {\n    key: 'full',\n    \"aria-hidden\": true,\n    ref: fullMeasureRef,\n    style: measureStyle\n  }, content, expandNode), status === 1 /* PREPARE */ && React.createElement(\"div\", {\n    key: 'stable',\n    \"aria-hidden\": true,\n    ref: singleRowMeasureRef,\n    style: measureStyle\n  }, '\\u00A0'), status === 2 /* MEASURE_WALKING */ && React.createElement(\"div\", {\n    key: 'walking-mid',\n    \"aria-hidden\": true,\n    ref: midMeasureRef,\n    style: measureStyle\n  }, renderContent(midIndex)), finalContent);\n  return [allNodes, startMeasure];\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,SAAS;AAClC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,uBAAuB,QAAQ,WAAW;AACnD,OAAOC,KAAK,MAAM,QAAQ;AAC1B,MAAMC,aAAa,GAAG,KAAK;AAC3B,MAAMC,YAAY,GAAG;EACnBC,UAAU,EAAE,QAAQ;EACpBC,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrBC,QAAQ,EAAE;AACZ,CAAC;AACD,eAAe,SAASC,UAAUA,CAACC,YAAY,EAAEC,OAAO,EAAEC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,YAAY,EAAE;EAC7G,MAAMC,YAAY,GAAGjB,KAAK,CAACkB,OAAO,CAAC,MAAMhB,KAAK,CAACS,OAAO,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACnE,MAAM,CAACQ,SAAS,EAAEC,YAAY,CAAC,GAAGpB,KAAK,CAACqB,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvB,KAAK,CAACqB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClE,MAAMG,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACJ,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACvE,MAAM,CAACK,MAAM,EAAEC,SAAS,CAAC,GAAG5B,KAAK,CAACqB,QAAQ,CAAC,GAAG,CAAC,wBAAwB,CAAC;EACxE;EACA,MAAMQ,mBAAmB,GAAG7B,KAAK,CAAC8B,MAAM,CAAC,IAAI,CAAC;EAC9C,MAAMC,cAAc,GAAG/B,KAAK,CAAC8B,MAAM,CAAC,IAAI,CAAC;EACzC,MAAME,aAAa,GAAGhC,KAAK,CAAC8B,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMG,YAAY,GAAGlC,QAAQ,CAAC,MAAM;IAClC;IACAE,uBAAuB,CAAC,MAAM;MAC5B2B,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC;MAC1BL,iBAAiB,CAAC,CAAC,CAAC,EAAEV,SAAS,KAAK,QAAQ,GAAGY,IAAI,CAACC,IAAI,CAACT,YAAY,CAACiB,MAAM,GAAG,CAAC,CAAC,GAAGjB,YAAY,CAACiB,MAAM,CAAC,CAAC;IAC3G,CAAC,CAAC;EACJ,CAAC,CAAC;EACF;EACAlC,KAAK,CAACmC,eAAe,CAAC,MAAM;IAC1BF,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAChB,YAAY,EAAEL,IAAI,CAAC,CAAC;EACxB;EACAZ,KAAK,CAACmC,eAAe,CAAC,MAAM;IAC1B,IAAIC,EAAE,EAAEC,EAAE;IACV,IAAIV,MAAM,KAAK,CAAC,CAAC,eAAe;MAC9B,MAAMW,iBAAiB,GAAG,CAAC,CAACF,EAAE,GAAGL,cAAc,CAACQ,OAAO,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,YAAY,KAAK,CAAC;MACnH,MAAMC,sBAAsB,GAAG,CAAC,CAACJ,EAAE,GAAGR,mBAAmB,CAACU,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,YAAY,KAAK,CAAC;MAC7H,MAAME,gBAAgB,GAAGD,sBAAsB,IAAI7B,IAAI,GAAG,GAAG,CAAC;MAC9D,IAAI0B,iBAAiB,IAAII,gBAAgB,EAAE;QACzCd,SAAS,CAAC,GAAG,CAAC,wBAAwB,CAAC;MACzC,CAAC,MAAM;QACLR,YAAY,CAACsB,gBAAgB,CAAC;QAC9Bd,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC;MACpC;IACF;EACF,CAAC,EAAE,CAACD,MAAM,CAAC,CAAC;EACZ;EACA3B,KAAK,CAACmC,eAAe,CAAC,MAAM;IAC1B,IAAIC,EAAE;IACN,IAAIT,MAAM,KAAK,CAAC,CAAC,uBAAuB;MACtC,MAAMgB,IAAI,GAAGrB,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc,CAAC,CAAC,CAAC;MAClD,MAAMsB,SAAS,GAAG,CAAC,CAACR,EAAE,GAAGJ,aAAa,CAACO,OAAO,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,YAAY,KAAK,CAAC;MAC1G,IAAIG,IAAI,GAAG,CAAC,EAAE;QACZ,IAAIC,SAAS,GAAGzB,SAAS,EAAE;UACzBI,iBAAiB,CAAC,CAACD,cAAc,CAAC,CAAC,CAAC,EAAEE,QAAQ,CAAC,CAAC;QAClD,CAAC,MAAM;UACLD,iBAAiB,CAAC,CAACC,QAAQ,EAAEF,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD;MACF,CAAC,MAAM;QACL,IAAIsB,SAAS,GAAGzB,SAAS,EAAE;UACzBI,iBAAiB,CAAC,CAACD,cAAc,CAAC,CAAC,CAAC,EAAEA,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,MAAM;UACLC,iBAAiB,CAAC,CAACD,cAAc,CAAC,CAAC,CAAC,EAAEA,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D;QACAM,SAAS,CAAC,EAAE,CAAC,qBAAqB,CAAC;MACrC;IACF;EACF,CAAC,EAAE,CAACD,MAAM,EAAEL,cAAc,CAAC,CAAC;EAC5B;EACA;EACA,MAAMuB,aAAa,GAAGC,KAAK,IAAI;IAC7B,MAAMC,aAAa,GAAG9B,YAAY,CAAC+B,KAAK,CAAC,CAAC,EAAEF,KAAK,CAAC;IAClD,MAAMG,aAAa,GAAGhC,YAAY,CAAC+B,KAAK,CAAC/B,YAAY,CAACiB,MAAM,GAAGY,KAAK,CAAC;IACrE,OAAO9C,KAAK,CAACkD,aAAa,CAAClD,KAAK,CAACmD,QAAQ,EAAE,IAAI,EAAEtC,SAAS,KAAK,OAAO,IAAIb,KAAK,CAACkD,aAAa,CAAClD,KAAK,CAACmD,QAAQ,EAAE,IAAI,EAAEpC,UAAU,EAAEZ,aAAa,CAAC,EAAEU,SAAS,KAAK,OAAO,IAAIkC,aAAa,CAACK,IAAI,CAAC,EAAE,CAAC,EAAEvC,SAAS,KAAK,QAAQ,IAAIb,KAAK,CAACkD,aAAa,CAAClD,KAAK,CAACmD,QAAQ,EAAE,IAAI,EAAEhD,aAAa,EAAEY,UAAU,EAAEZ,aAAa,CAAC,EAAEU,SAAS,KAAK,KAAK,IAAIoC,aAAa,CAACG,IAAI,CAAC,EAAE,CAAC,EAAEvC,SAAS,KAAK,KAAK,IAAIb,KAAK,CAACkD,aAAa,CAAClD,KAAK,CAACmD,QAAQ,EAAE,IAAI,EAAEhD,aAAa,EAAEY,UAAU,CAAC,CAAC;EAC7b,CAAC;EACD,MAAMsC,YAAY,GAAGrD,KAAK,CAACkB,OAAO,CAAC,MAAM;IACvC,IAAIJ,QAAQ,IAAIa,MAAM,KAAK,GAAG,CAAC,0BAA0B;MACvD,OAAO3B,KAAK,CAACkD,aAAa,CAAClD,KAAK,CAACmD,QAAQ,EAAE;QACzCG,GAAG,EAAE;MACP,CAAC,EAAE3C,OAAO,EAAEgB,MAAM,KAAK,EAAE,CAAC,yBAAyBX,YAAY,CAAC;IAClE;IACA,IAAIW,MAAM,KAAK,EAAE,CAAC,uBAAuB;MACvC,OAAOkB,aAAa,CAACrB,QAAQ,CAAC;IAChC;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAACV,QAAQ,EAAEa,MAAM,EAAEhB,OAAO,EAAEK,YAAY,EAAEQ,QAAQ,CAAC,CAAC;EACvD,MAAM+B,QAAQ,GAAGvD,KAAK,CAACkD,aAAa,CAAClD,KAAK,CAACmD,QAAQ,EAAE,IAAI,EAAExB,MAAM,KAAK,CAAC,CAAC,iBAAiB3B,KAAK,CAACkD,aAAa,CAAC,KAAK,EAAE;IAClHI,GAAG,EAAE,MAAM;IACX,aAAa,EAAE,IAAI;IACnBE,GAAG,EAAEzB,cAAc;IACnB0B,KAAK,EAAErD;EACT,CAAC,EAAEO,OAAO,EAAEI,UAAU,CAAC,EAAEY,MAAM,KAAK,CAAC,CAAC,iBAAiB3B,KAAK,CAACkD,aAAa,CAAC,KAAK,EAAE;IAChFI,GAAG,EAAE,QAAQ;IACb,aAAa,EAAE,IAAI;IACnBE,GAAG,EAAE3B,mBAAmB;IACxB4B,KAAK,EAAErD;EACT,CAAC,EAAE,QAAQ,CAAC,EAAEuB,MAAM,KAAK,CAAC,CAAC,yBAAyB3B,KAAK,CAACkD,aAAa,CAAC,KAAK,EAAE;IAC7EI,GAAG,EAAE,aAAa;IAClB,aAAa,EAAE,IAAI;IACnBE,GAAG,EAAExB,aAAa;IAClByB,KAAK,EAAErD;EACT,CAAC,EAAEyC,aAAa,CAACrB,QAAQ,CAAC,CAAC,EAAE6B,YAAY,CAAC;EAC1C,OAAO,CAACE,QAAQ,EAAEtB,YAAY,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}