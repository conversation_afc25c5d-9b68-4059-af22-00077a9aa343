{"ast": null, "code": "import { __awaiter } from \"tslib\";\nimport classNames from 'classnames';\nimport React, { useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { SpinIcon } from './spin-icon';\nimport { useConfig } from '../config-provider';\nimport { isPromise } from '../../utils/validate';\nconst classPrefix = `adm-switch`;\nconst defaultProps = {\n  defaultChecked: false\n};\nexport const Switch = p => {\n  const props = mergeProps(defaultProps, p);\n  const disabled = props.disabled || props.loading || false;\n  const [changing, setChanging] = useState(false);\n  const {\n    locale\n  } = useConfig();\n  const [checked, setChecked] = usePropsValue({\n    value: props.checked,\n    defaultValue: props.defaultChecked,\n    onChange: props.onChange\n  });\n  function onClick() {\n    return __awaiter(this, void 0, void 0, function* () {\n      if (disabled || props.loading || changing) {\n        return;\n      }\n      const nextChecked = !checked;\n      if (props.beforeChange) {\n        setChanging(true);\n        try {\n          yield props.beforeChange(nextChecked);\n          setChanging(false);\n        } catch (e) {\n          setChanging(false);\n          throw e;\n        }\n      }\n      const result = setChecked(nextChecked);\n      if (isPromise(result)) {\n        setChanging(true);\n        try {\n          yield result;\n          setChanging(false);\n        } catch (e) {\n          setChanging(false);\n          throw e;\n        }\n      }\n    });\n  }\n  return withNativeProps(props, React.createElement(\"div\", {\n    onClick: onClick,\n    className: classNames(classPrefix, {\n      [`${classPrefix}-checked`]: checked,\n      [`${classPrefix}-disabled`]: disabled || changing\n    }),\n    role: 'switch',\n    \"aria-label\": locale.Switch.name,\n    \"aria-checked\": checked,\n    \"aria-disabled\": disabled\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-checkbox`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-handle`\n  }, (props.loading || changing) && React.createElement(SpinIcon, {\n    className: `${classPrefix}-spin-icon`\n  })), React.createElement(\"div\", {\n    className: `${classPrefix}-inner`\n  }, checked ? props.checkedText : props.uncheckedText))));\n};", "map": {"version": 3, "names": ["__awaiter", "classNames", "React", "useState", "withNativeProps", "usePropsValue", "mergeProps", "SpinIcon", "useConfig", "isPromise", "classPrefix", "defaultProps", "defaultChecked", "Switch", "p", "props", "disabled", "loading", "changing", "setChanging", "locale", "checked", "setChecked", "value", "defaultValue", "onChange", "onClick", "nextChecked", "beforeChange", "e", "result", "createElement", "className", "role", "name", "checkedText", "uncheckedText"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/switch/switch.js"], "sourcesContent": ["import { __awaiter } from \"tslib\";\nimport classNames from 'classnames';\nimport React, { useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { SpinIcon } from './spin-icon';\nimport { useConfig } from '../config-provider';\nimport { isPromise } from '../../utils/validate';\nconst classPrefix = `adm-switch`;\nconst defaultProps = {\n  defaultChecked: false\n};\nexport const Switch = p => {\n  const props = mergeProps(defaultProps, p);\n  const disabled = props.disabled || props.loading || false;\n  const [changing, setChanging] = useState(false);\n  const {\n    locale\n  } = useConfig();\n  const [checked, setChecked] = usePropsValue({\n    value: props.checked,\n    defaultValue: props.defaultChecked,\n    onChange: props.onChange\n  });\n  function onClick() {\n    return __awaiter(this, void 0, void 0, function* () {\n      if (disabled || props.loading || changing) {\n        return;\n      }\n      const nextChecked = !checked;\n      if (props.beforeChange) {\n        setChanging(true);\n        try {\n          yield props.beforeChange(nextChecked);\n          setChanging(false);\n        } catch (e) {\n          setChanging(false);\n          throw e;\n        }\n      }\n      const result = setChecked(nextChecked);\n      if (isPromise(result)) {\n        setChanging(true);\n        try {\n          yield result;\n          setChanging(false);\n        } catch (e) {\n          setChanging(false);\n          throw e;\n        }\n      }\n    });\n  }\n  return withNativeProps(props, React.createElement(\"div\", {\n    onClick: onClick,\n    className: classNames(classPrefix, {\n      [`${classPrefix}-checked`]: checked,\n      [`${classPrefix}-disabled`]: disabled || changing\n    }),\n    role: 'switch',\n    \"aria-label\": locale.Switch.name,\n    \"aria-checked\": checked,\n    \"aria-disabled\": disabled\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-checkbox`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-handle`\n  }, (props.loading || changing) && React.createElement(SpinIcon, {\n    className: `${classPrefix}-spin-icon`\n  })), React.createElement(\"div\", {\n    className: `${classPrefix}-inner`\n  }, checked ? props.checkedText : props.uncheckedText))));\n};"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,MAAMC,WAAW,GAAG,YAAY;AAChC,MAAMC,YAAY,GAAG;EACnBC,cAAc,EAAE;AAClB,CAAC;AACD,OAAO,MAAMC,MAAM,GAAGC,CAAC,IAAI;EACzB,MAAMC,KAAK,GAAGT,UAAU,CAACK,YAAY,EAAEG,CAAC,CAAC;EACzC,MAAME,QAAQ,GAAGD,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACE,OAAO,IAAI,KAAK;EACzD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM;IACJiB;EACF,CAAC,GAAGZ,SAAS,CAAC,CAAC;EACf,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGjB,aAAa,CAAC;IAC1CkB,KAAK,EAAER,KAAK,CAACM,OAAO;IACpBG,YAAY,EAAET,KAAK,CAACH,cAAc;IAClCa,QAAQ,EAAEV,KAAK,CAACU;EAClB,CAAC,CAAC;EACF,SAASC,OAAOA,CAAA,EAAG;IACjB,OAAO1B,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAClD,IAAIgB,QAAQ,IAAID,KAAK,CAACE,OAAO,IAAIC,QAAQ,EAAE;QACzC;MACF;MACA,MAAMS,WAAW,GAAG,CAACN,OAAO;MAC5B,IAAIN,KAAK,CAACa,YAAY,EAAE;QACtBT,WAAW,CAAC,IAAI,CAAC;QACjB,IAAI;UACF,MAAMJ,KAAK,CAACa,YAAY,CAACD,WAAW,CAAC;UACrCR,WAAW,CAAC,KAAK,CAAC;QACpB,CAAC,CAAC,OAAOU,CAAC,EAAE;UACVV,WAAW,CAAC,KAAK,CAAC;UAClB,MAAMU,CAAC;QACT;MACF;MACA,MAAMC,MAAM,GAAGR,UAAU,CAACK,WAAW,CAAC;MACtC,IAAIlB,SAAS,CAACqB,MAAM,CAAC,EAAE;QACrBX,WAAW,CAAC,IAAI,CAAC;QACjB,IAAI;UACF,MAAMW,MAAM;UACZX,WAAW,CAAC,KAAK,CAAC;QACpB,CAAC,CAAC,OAAOU,CAAC,EAAE;UACVV,WAAW,CAAC,KAAK,CAAC;UAClB,MAAMU,CAAC;QACT;MACF;IACF,CAAC,CAAC;EACJ;EACA,OAAOzB,eAAe,CAACW,KAAK,EAAEb,KAAK,CAAC6B,aAAa,CAAC,KAAK,EAAE;IACvDL,OAAO,EAAEA,OAAO;IAChBM,SAAS,EAAE/B,UAAU,CAACS,WAAW,EAAE;MACjC,CAAC,GAAGA,WAAW,UAAU,GAAGW,OAAO;MACnC,CAAC,GAAGX,WAAW,WAAW,GAAGM,QAAQ,IAAIE;IAC3C,CAAC,CAAC;IACFe,IAAI,EAAE,QAAQ;IACd,YAAY,EAAEb,MAAM,CAACP,MAAM,CAACqB,IAAI;IAChC,cAAc,EAAEb,OAAO;IACvB,eAAe,EAAEL;EACnB,CAAC,EAAEd,KAAK,CAAC6B,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGtB,WAAW;EAC3B,CAAC,EAAER,KAAK,CAAC6B,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGtB,WAAW;EAC3B,CAAC,EAAE,CAACK,KAAK,CAACE,OAAO,IAAIC,QAAQ,KAAKhB,KAAK,CAAC6B,aAAa,CAACxB,QAAQ,EAAE;IAC9DyB,SAAS,EAAE,GAAGtB,WAAW;EAC3B,CAAC,CAAC,CAAC,EAAER,KAAK,CAAC6B,aAAa,CAAC,KAAK,EAAE;IAC9BC,SAAS,EAAE,GAAGtB,WAAW;EAC3B,CAAC,EAAEW,OAAO,GAAGN,KAAK,CAACoB,WAAW,GAAGpB,KAAK,CAACqB,aAAa,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}