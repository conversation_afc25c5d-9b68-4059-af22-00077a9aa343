{"ast": null, "code": "import { canUseDom } from './can-use-dom';\nexport let supportsPassive = false;\nif (canUseDom) {\n  try {\n    const opts = {};\n    Object.defineProperty(opts, 'passive', {\n      get() {\n        supportsPassive = true;\n      }\n    });\n    window.addEventListener('test-passive', null, opts);\n  } catch (e) {}\n}", "map": {"version": 3, "names": ["canUseDom", "supportsPassive", "opts", "Object", "defineProperty", "get", "window", "addEventListener", "e"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/supports-passive.js"], "sourcesContent": ["import { canUseDom } from './can-use-dom';\nexport let supportsPassive = false;\nif (canUseDom) {\n  try {\n    const opts = {};\n    Object.defineProperty(opts, 'passive', {\n      get() {\n        supportsPassive = true;\n      }\n    });\n    window.addEventListener('test-passive', null, opts);\n  } catch (e) {}\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,OAAO,IAAIC,eAAe,GAAG,KAAK;AAClC,IAAID,SAAS,EAAE;EACb,IAAI;IACF,MAAME,IAAI,GAAG,CAAC,CAAC;IACfC,MAAM,CAACC,cAAc,CAACF,IAAI,EAAE,SAAS,EAAE;MACrCG,GAAGA,CAAA,EAAG;QACJJ,eAAe,GAAG,IAAI;MACxB;IACF,CAAC,CAAC;IACFK,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAE,IAAI,EAAEL,IAAI,CAAC;EACrD,CAAC,CAAC,OAAOM,CAAC,EAAE,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}