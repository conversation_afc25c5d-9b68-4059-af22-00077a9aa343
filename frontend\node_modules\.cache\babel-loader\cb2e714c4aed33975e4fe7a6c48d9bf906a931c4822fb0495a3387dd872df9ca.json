{"ast": null, "code": "import React, { useRef, useState } from 'react';\nimport { useDrag } from '@use-gesture/react';\nimport { ThumbIcon } from './thumb-icon';\nimport Popover from '../popover';\nimport { useConfig } from '../config-provider';\nconst classPrefix = `adm-slider`;\nconst Thumb = props => {\n  const {\n    value,\n    min,\n    max,\n    disabled,\n    icon,\n    residentPopover,\n    onDrag\n  } = props;\n  const prevValue = useRef(value);\n  const {\n    locale\n  } = useConfig();\n  const currentPosition = () => {\n    return {\n      left: `${(value - min) / (max - min) * 100}%`,\n      right: 'auto'\n    };\n  };\n  const [dragging, setDragging] = useState(false);\n  const bind = useDrag(state => {\n    var _a;\n    if (disabled) return;\n    if (state.first) {\n      prevValue.current = value;\n    }\n    const x = state.xy[0] - state.initial[0];\n    const sliderOffsetWith = (_a = props.trackRef.current) === null || _a === void 0 ? void 0 : _a.offsetWidth;\n    if (!sliderOffsetWith) return;\n    const diff = x / Math.ceil(sliderOffsetWith) * (max - min);\n    onDrag(prevValue.current + diff, state.first, state.last);\n    setDragging(!state.last);\n  }, {\n    axis: 'x',\n    pointer: {\n      touch: true\n    }\n  });\n  const renderPopoverContent = typeof props.popover === 'function' ? props.popover : props.popover ? value => value.toString() : null;\n  const thumbElement = React.createElement(\"div\", {\n    className: `${classPrefix}-thumb`\n  }, icon ? icon : React.createElement(ThumbIcon, {\n    className: `${classPrefix}-thumb-icon`\n  }));\n  return React.createElement(\"div\", Object.assign({\n    className: `${classPrefix}-thumb-container`,\n    style: currentPosition()\n  }, bind(), {\n    role: 'slider',\n    \"aria-label\": props['aria-label'] || locale.Slider.name,\n    \"aria-valuemax\": max,\n    \"aria-valuemin\": min,\n    \"aria-valuenow\": value,\n    \"aria-disabled\": disabled\n  }), renderPopoverContent ? React.createElement(Popover, {\n    content: renderPopoverContent(value),\n    placement: 'top',\n    visible: residentPopover || dragging,\n    getContainer: null,\n    mode: 'dark'\n  }, thumbElement) : thumbElement);\n};\nexport default Thumb;", "map": {"version": 3, "names": ["React", "useRef", "useState", "useDrag", "ThumbIcon", "Popover", "useConfig", "classPrefix", "Thumb", "props", "value", "min", "max", "disabled", "icon", "residentPop<PERSON>", "onDrag", "prevValue", "locale", "currentPosition", "left", "right", "dragging", "setDragging", "bind", "state", "_a", "first", "current", "x", "xy", "initial", "sliderOffsetWith", "trackRef", "offsetWidth", "diff", "Math", "ceil", "last", "axis", "pointer", "touch", "renderPopoverContent", "popover", "toString", "thumbElement", "createElement", "className", "Object", "assign", "style", "role", "Slide<PERSON>", "name", "content", "placement", "visible", "getContainer", "mode"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/slider/thumb.js"], "sourcesContent": ["import React, { useRef, useState } from 'react';\nimport { useDrag } from '@use-gesture/react';\nimport { ThumbIcon } from './thumb-icon';\nimport Popover from '../popover';\nimport { useConfig } from '../config-provider';\nconst classPrefix = `adm-slider`;\nconst Thumb = props => {\n  const {\n    value,\n    min,\n    max,\n    disabled,\n    icon,\n    residentPopover,\n    onDrag\n  } = props;\n  const prevValue = useRef(value);\n  const {\n    locale\n  } = useConfig();\n  const currentPosition = () => {\n    return {\n      left: `${(value - min) / (max - min) * 100}%`,\n      right: 'auto'\n    };\n  };\n  const [dragging, setDragging] = useState(false);\n  const bind = useDrag(state => {\n    var _a;\n    if (disabled) return;\n    if (state.first) {\n      prevValue.current = value;\n    }\n    const x = state.xy[0] - state.initial[0];\n    const sliderOffsetWith = (_a = props.trackRef.current) === null || _a === void 0 ? void 0 : _a.offsetWidth;\n    if (!sliderOffsetWith) return;\n    const diff = x / Math.ceil(sliderOffsetWith) * (max - min);\n    onDrag(prevValue.current + diff, state.first, state.last);\n    setDragging(!state.last);\n  }, {\n    axis: 'x',\n    pointer: {\n      touch: true\n    }\n  });\n  const renderPopoverContent = typeof props.popover === 'function' ? props.popover : props.popover ? value => value.toString() : null;\n  const thumbElement = React.createElement(\"div\", {\n    className: `${classPrefix}-thumb`\n  }, icon ? icon : React.createElement(ThumbIcon, {\n    className: `${classPrefix}-thumb-icon`\n  }));\n  return React.createElement(\"div\", Object.assign({\n    className: `${classPrefix}-thumb-container`,\n    style: currentPosition()\n  }, bind(), {\n    role: 'slider',\n    \"aria-label\": props['aria-label'] || locale.Slider.name,\n    \"aria-valuemax\": max,\n    \"aria-valuemin\": min,\n    \"aria-valuenow\": value,\n    \"aria-disabled\": disabled\n  }), renderPopoverContent ? React.createElement(Popover, {\n    content: renderPopoverContent(value),\n    placement: 'top',\n    visible: residentPopover || dragging,\n    getContainer: null,\n    mode: 'dark'\n  }, thumbElement) : thumbElement);\n};\nexport default Thumb;"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC/C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,OAAO,MAAM,YAAY;AAChC,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,MAAMC,WAAW,GAAG,YAAY;AAChC,MAAMC,KAAK,GAAGC,KAAK,IAAI;EACrB,MAAM;IACJC,KAAK;IACLC,GAAG;IACHC,GAAG;IACHC,QAAQ;IACRC,IAAI;IACJC,eAAe;IACfC;EACF,CAAC,GAAGP,KAAK;EACT,MAAMQ,SAAS,GAAGhB,MAAM,CAACS,KAAK,CAAC;EAC/B,MAAM;IACJQ;EACF,CAAC,GAAGZ,SAAS,CAAC,CAAC;EACf,MAAMa,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO;MACLC,IAAI,EAAE,GAAG,CAACV,KAAK,GAAGC,GAAG,KAAKC,GAAG,GAAGD,GAAG,CAAC,GAAG,GAAG,GAAG;MAC7CU,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMsB,IAAI,GAAGrB,OAAO,CAACsB,KAAK,IAAI;IAC5B,IAAIC,EAAE;IACN,IAAIb,QAAQ,EAAE;IACd,IAAIY,KAAK,CAACE,KAAK,EAAE;MACfV,SAAS,CAACW,OAAO,GAAGlB,KAAK;IAC3B;IACA,MAAMmB,CAAC,GAAGJ,KAAK,CAACK,EAAE,CAAC,CAAC,CAAC,GAAGL,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;IACxC,MAAMC,gBAAgB,GAAG,CAACN,EAAE,GAAGjB,KAAK,CAACwB,QAAQ,CAACL,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,WAAW;IAC1G,IAAI,CAACF,gBAAgB,EAAE;IACvB,MAAMG,IAAI,GAAGN,CAAC,GAAGO,IAAI,CAACC,IAAI,CAACL,gBAAgB,CAAC,IAAIpB,GAAG,GAAGD,GAAG,CAAC;IAC1DK,MAAM,CAACC,SAAS,CAACW,OAAO,GAAGO,IAAI,EAAEV,KAAK,CAACE,KAAK,EAAEF,KAAK,CAACa,IAAI,CAAC;IACzDf,WAAW,CAAC,CAACE,KAAK,CAACa,IAAI,CAAC;EAC1B,CAAC,EAAE;IACDC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE;MACPC,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACF,MAAMC,oBAAoB,GAAG,OAAOjC,KAAK,CAACkC,OAAO,KAAK,UAAU,GAAGlC,KAAK,CAACkC,OAAO,GAAGlC,KAAK,CAACkC,OAAO,GAAGjC,KAAK,IAAIA,KAAK,CAACkC,QAAQ,CAAC,CAAC,GAAG,IAAI;EACnI,MAAMC,YAAY,GAAG7C,KAAK,CAAC8C,aAAa,CAAC,KAAK,EAAE;IAC9CC,SAAS,EAAE,GAAGxC,WAAW;EAC3B,CAAC,EAAEO,IAAI,GAAGA,IAAI,GAAGd,KAAK,CAAC8C,aAAa,CAAC1C,SAAS,EAAE;IAC9C2C,SAAS,EAAE,GAAGxC,WAAW;EAC3B,CAAC,CAAC,CAAC;EACH,OAAOP,KAAK,CAAC8C,aAAa,CAAC,KAAK,EAAEE,MAAM,CAACC,MAAM,CAAC;IAC9CF,SAAS,EAAE,GAAGxC,WAAW,kBAAkB;IAC3C2C,KAAK,EAAE/B,eAAe,CAAC;EACzB,CAAC,EAAEK,IAAI,CAAC,CAAC,EAAE;IACT2B,IAAI,EAAE,QAAQ;IACd,YAAY,EAAE1C,KAAK,CAAC,YAAY,CAAC,IAAIS,MAAM,CAACkC,MAAM,CAACC,IAAI;IACvD,eAAe,EAAEzC,GAAG;IACpB,eAAe,EAAED,GAAG;IACpB,eAAe,EAAED,KAAK;IACtB,eAAe,EAAEG;EACnB,CAAC,CAAC,EAAE6B,oBAAoB,GAAG1C,KAAK,CAAC8C,aAAa,CAACzC,OAAO,EAAE;IACtDiD,OAAO,EAAEZ,oBAAoB,CAAChC,KAAK,CAAC;IACpC6C,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAEzC,eAAe,IAAIO,QAAQ;IACpCmC,YAAY,EAAE,IAAI;IAClBC,IAAI,EAAE;EACR,CAAC,EAAEb,YAAY,CAAC,GAAGA,YAAY,CAAC;AAClC,CAAC;AACD,eAAerC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}