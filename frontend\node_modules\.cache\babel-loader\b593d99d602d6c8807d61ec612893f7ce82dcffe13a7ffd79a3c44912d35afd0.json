{"ast": null, "code": "import React, { useState } from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { isNodeWithContent } from '../../utils/is-node-with-content';\nimport Button from '../button';\nimport { useResultIcon } from '../result/use-result-icon';\nconst classPrefix = `adm-result-page`;\nconst defaultProps = {\n  status: 'info',\n  details: []\n};\nexport const ResultPage = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    status,\n    title,\n    description,\n    details,\n    icon,\n    primaryButtonText,\n    secondaryButtonText,\n    onPrimaryButtonClick,\n    onSecondaryButtonClick\n  } = props;\n  const fallbackIcon = useResultIcon(status);\n  const [collapse, setCollapse] = useState(true);\n  const showSecondaryButton = isNodeWithContent(secondaryButtonText);\n  const showPrimaryButton = isNodeWithContent(primaryButtonText);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-header`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-icon`\n  }, icon || fallbackIcon), React.createElement(\"div\", {\n    className: `${classPrefix}-title`\n  }, title), isNodeWithContent(description) ? React.createElement(\"div\", {\n    className: `${classPrefix}-description`\n  }, description) : null, (details === null || details === void 0 ? void 0 : details.length) ? React.createElement(\"div\", {\n    className: `${classPrefix}-details`\n  }, (collapse ? details.slice(0, 3) : details).map((detail, index) => {\n    return React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-detail`, detail.bold && `${classPrefix}-detail-bold`),\n      key: index\n    }, React.createElement(\"span\", null, detail.label), React.createElement(\"span\", null, detail.value));\n  }), details.length > 3 && React.createElement(\"div\", {\n    onClick: () => setCollapse(prev => !prev)\n  }, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-collapse`, !collapse && `${classPrefix}-collapse-active`)\n  }))) : null, React.createElement(\"div\", {\n    className: `${classPrefix}-bgWrapper`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-bg`\n  }))), React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, props.children), (showPrimaryButton || showSecondaryButton) && React.createElement(\"div\", {\n    className: `${classPrefix}-footer`\n  }, showSecondaryButton && React.createElement(Button, {\n    block: true,\n    color: 'default',\n    fill: 'solid',\n    size: 'large',\n    onClick: onSecondaryButtonClick,\n    className: `${classPrefix}-footer-btn`\n  }, secondaryButtonText), showPrimaryButton && showSecondaryButton && React.createElement(\"div\", {\n    className: `${classPrefix}-footer-space`\n  }), showPrimaryButton && React.createElement(Button, {\n    block: true,\n    color: 'primary',\n    fill: 'solid',\n    size: 'large',\n    onClick: onPrimaryButtonClick,\n    className: `${classPrefix}-footer-btn`\n  }, primaryButtonText))));\n};", "map": {"version": 3, "names": ["React", "useState", "classNames", "withNativeProps", "mergeProps", "isNodeWithContent", "<PERSON><PERSON>", "useResultIcon", "classPrefix", "defaultProps", "status", "details", "ResultPage", "p", "props", "title", "description", "icon", "primaryButtonText", "secondaryButtonText", "onPrimaryButtonClick", "onSecondaryButtonClick", "fallbackIcon", "collapse", "setCollapse", "showSecondaryButton", "showPrimaryButton", "createElement", "className", "length", "slice", "map", "detail", "index", "bold", "key", "label", "value", "onClick", "prev", "children", "block", "color", "fill", "size"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/result-page/result-page.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { isNodeWithContent } from '../../utils/is-node-with-content';\nimport Button from '../button';\nimport { useResultIcon } from '../result/use-result-icon';\nconst classPrefix = `adm-result-page`;\nconst defaultProps = {\n  status: 'info',\n  details: []\n};\nexport const ResultPage = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    status,\n    title,\n    description,\n    details,\n    icon,\n    primaryButtonText,\n    secondaryButtonText,\n    onPrimaryButtonClick,\n    onSecondaryButtonClick\n  } = props;\n  const fallbackIcon = useResultIcon(status);\n  const [collapse, setCollapse] = useState(true);\n  const showSecondaryButton = isNodeWithContent(secondaryButtonText);\n  const showPrimaryButton = isNodeWithContent(primaryButtonText);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-header`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-icon`\n  }, icon || fallbackIcon), React.createElement(\"div\", {\n    className: `${classPrefix}-title`\n  }, title), isNodeWithContent(description) ? React.createElement(\"div\", {\n    className: `${classPrefix}-description`\n  }, description) : null, (details === null || details === void 0 ? void 0 : details.length) ? React.createElement(\"div\", {\n    className: `${classPrefix}-details`\n  }, (collapse ? details.slice(0, 3) : details).map((detail, index) => {\n    return React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-detail`, detail.bold && `${classPrefix}-detail-bold`),\n      key: index\n    }, React.createElement(\"span\", null, detail.label), React.createElement(\"span\", null, detail.value));\n  }), details.length > 3 && React.createElement(\"div\", {\n    onClick: () => setCollapse(prev => !prev)\n  }, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-collapse`, !collapse && `${classPrefix}-collapse-active`)\n  }))) : null, React.createElement(\"div\", {\n    className: `${classPrefix}-bgWrapper`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-bg`\n  }))), React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, props.children), (showPrimaryButton || showSecondaryButton) && React.createElement(\"div\", {\n    className: `${classPrefix}-footer`\n  }, showSecondaryButton && React.createElement(Button, {\n    block: true,\n    color: 'default',\n    fill: 'solid',\n    size: 'large',\n    onClick: onSecondaryButtonClick,\n    className: `${classPrefix}-footer-btn`\n  }, secondaryButtonText), showPrimaryButton && showSecondaryButton && React.createElement(\"div\", {\n    className: `${classPrefix}-footer-space`\n  }), showPrimaryButton && React.createElement(Button, {\n    block: true,\n    color: 'primary',\n    fill: 'solid',\n    size: 'large',\n    onClick: onPrimaryButtonClick,\n    className: `${classPrefix}-footer-btn`\n  }, primaryButtonText))));\n};"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,aAAa,QAAQ,2BAA2B;AACzD,MAAMC,WAAW,GAAG,iBAAiB;AACrC,MAAMC,YAAY,GAAG;EACnBC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,MAAMC,UAAU,GAAGC,CAAC,IAAI;EAC7B,MAAMC,KAAK,GAAGV,UAAU,CAACK,YAAY,EAAEI,CAAC,CAAC;EACzC,MAAM;IACJH,MAAM;IACNK,KAAK;IACLC,WAAW;IACXL,OAAO;IACPM,IAAI;IACJC,iBAAiB;IACjBC,mBAAmB;IACnBC,oBAAoB;IACpBC;EACF,CAAC,GAAGP,KAAK;EACT,MAAMQ,YAAY,GAAGf,aAAa,CAACG,MAAM,CAAC;EAC1C,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAMwB,mBAAmB,GAAGpB,iBAAiB,CAACc,mBAAmB,CAAC;EAClE,MAAMO,iBAAiB,GAAGrB,iBAAiB,CAACa,iBAAiB,CAAC;EAC9D,OAAOf,eAAe,CAACW,KAAK,EAAEd,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEpB;EACb,CAAC,EAAER,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,EAAER,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,EAAES,IAAI,IAAIK,YAAY,CAAC,EAAEtB,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IACnDC,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,EAAEO,KAAK,CAAC,EAAEV,iBAAiB,CAACW,WAAW,CAAC,GAAGhB,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IACrEC,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,EAAEQ,WAAW,CAAC,GAAG,IAAI,EAAE,CAACL,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACkB,MAAM,IAAI7B,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IACtHC,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,EAAE,CAACe,QAAQ,GAAGZ,OAAO,CAACmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGnB,OAAO,EAAEoB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;IACnE,OAAOjC,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;MAChCC,SAAS,EAAE1B,UAAU,CAAC,GAAGM,WAAW,SAAS,EAAEwB,MAAM,CAACE,IAAI,IAAI,GAAG1B,WAAW,cAAc,CAAC;MAC3F2B,GAAG,EAAEF;IACP,CAAC,EAAEjC,KAAK,CAAC2B,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEK,MAAM,CAACI,KAAK,CAAC,EAAEpC,KAAK,CAAC2B,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEK,MAAM,CAACK,KAAK,CAAC,CAAC;EACtG,CAAC,CAAC,EAAE1B,OAAO,CAACkB,MAAM,GAAG,CAAC,IAAI7B,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IACnDW,OAAO,EAAEA,CAAA,KAAMd,WAAW,CAACe,IAAI,IAAI,CAACA,IAAI;EAC1C,CAAC,EAAEvC,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE1B,UAAU,CAAC,GAAGM,WAAW,WAAW,EAAE,CAACe,QAAQ,IAAI,GAAGf,WAAW,kBAAkB;EAChG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAER,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IACtCC,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,EAAER,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,CAAC,CAAC,CAAC,EAAER,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IAC/BC,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,EAAEM,KAAK,CAAC0B,QAAQ,CAAC,EAAE,CAACd,iBAAiB,IAAID,mBAAmB,KAAKzB,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IAC3FC,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,EAAEiB,mBAAmB,IAAIzB,KAAK,CAAC2B,aAAa,CAACrB,MAAM,EAAE;IACpDmC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,OAAO;IACbN,OAAO,EAAEjB,sBAAsB;IAC/BO,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,EAAEW,mBAAmB,CAAC,EAAEO,iBAAiB,IAAID,mBAAmB,IAAIzB,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IAC9FC,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,CAAC,EAAEkB,iBAAiB,IAAI1B,KAAK,CAAC2B,aAAa,CAACrB,MAAM,EAAE;IACnDmC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,OAAO;IACbN,OAAO,EAAElB,oBAAoB;IAC7BQ,SAAS,EAAE,GAAGpB,WAAW;EAC3B,CAAC,EAAEU,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}