{"ast": null, "code": "import * as React from \"react\";\nfunction CheckCircleOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CheckCircleOutline-CheckCircleOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CheckCircleOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CheckCircleOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M35.9397591,17.682013 L23.3431458,30.2781746 L23.3431458,30.2781746 C22.7573593,30.863961 21.8076118,30.863961 21.2218254,30.2781746 L21.1448618,30.1962736 C21.0355278,30.1325525 20.932526,30.0538463 20.8388348,29.9601551 L13.5613926,22.681994 C13.4051906,22.5257765 13.4052031,22.2725106 13.5614206,22.1163086 C13.636434,22.0413025 13.7381696,21.9991652 13.8442493,21.9991652 L16.9549543,21.9991652 C17.0610359,21.9991652 17.1627731,22.041304 17.2377869,22.1163124 L22.25,27.1281652 L22.25,27.1281652 L32.2624756,17.1163137 C32.3374894,17.0413045 32.4392272,16.9991652 32.5453095,16.9991652 L35.6569214,16.9991652 C35.8778353,16.9991652 36.0569214,17.1782513 36.0569214,17.3991652 C36.0569214,17.5052543 36.0147767,17.6069981 35.9397591,17.682013 Z\",\n    id: \"CheckCircleOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\"\n  }))));\n}\nexport default CheckCircleOutline;", "map": {"version": 3, "names": ["React", "CheckCircleOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/CheckCircleOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction CheckCircleOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CheckCircleOutline-CheckCircleOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CheckCircleOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CheckCircleOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M35.9397591,17.682013 L23.3431458,30.2781746 L23.3431458,30.2781746 C22.7573593,30.863961 21.8076118,30.863961 21.2218254,30.2781746 L21.1448618,30.1962736 C21.0355278,30.1325525 20.932526,30.0538463 20.8388348,29.9601551 L13.5613926,22.681994 C13.4051906,22.5257765 13.4052031,22.2725106 13.5614206,22.1163086 C13.636434,22.0413025 13.7381696,21.9991652 13.8442493,21.9991652 L16.9549543,21.9991652 C17.0610359,21.9991652 17.1627731,22.041304 17.2377869,22.1163124 L22.25,27.1281652 L22.25,27.1281652 L32.2624756,17.1163137 C32.3374894,17.0413045 32.4392272,16.9991652 32.5453095,16.9991652 L35.6569214,16.9991652 C35.8778353,16.9991652 36.0569214,17.1782513 36.0569214,17.3991652 C36.0569214,17.5052543 36.0147767,17.6069981 35.9397591,17.682013 Z\",\n    id: \"CheckCircleOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\"\n  }))));\n}\n\nexport default CheckCircleOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACjC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,uCAAuC;IAC3CC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,iCAAiC;IACrCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,2gCAA2gC;IAC9gCR,EAAE,EAAE,iCAAiC;IACrCG,IAAI,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAenB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}