{"ast": null, "code": "import * as React from \"react\";\nfunction GlobalOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"GlobalOutline-GlobalOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"GlobalOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"GlobalOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M22.5000466,42.4403346 L22.5000466,24 L14.3913043,24 C14.3913043,33.4649419 18.2446927,40.9747273 22.5000466,42.4403346 Z M25.5,24 L25.500952,42.4399905 C29.7559102,40.9735523 33.6086957,33.4642014 33.6086957,24 L25.5,24 Z M5.3,24 C5.3,31.2731078 9.45216137,37.5764263 15.5153578,40.6688293 C12.7504509,36.634678 11,30.6632803 11,24 L5.3,24 Z M37,24 C37,30.6632803 35.2495491,36.634678 32.4843951,40.6690813 C38.5478386,37.5764263 42.7,31.2731078 42.7,24 L37,24 Z M15.5156049,7.33091875 L15.2946208,7.44561447 C10.196287,10.1321925 6.48991277,15.1049882 5.5393343,21.0002511 L11.1198383,21.0002511 C11.5582739,15.5581426 13.1727089,10.749241 15.5156049,7.33091875 Z M14.5224111,21 L22.5000466,21 L22.5000466,5.5596654 C18.7053742,6.8666071 15.2303454,12.9798886 14.5224111,21 Z M25.500952,5.56000947 L25.5,21 L33.4775889,21 C32.7697167,12.9805921 29.2952355,6.86767971 25.500952,5.56000947 Z M32.4846422,7.33117073 L32.5441633,7.4186565 C34.8547353,10.8312895 36.4454642,15.6045426 36.8801617,21.0002511 L42.4606657,21.0002511 C41.4964219,15.0202402 37.6965792,9.98940714 32.4846422,7.33117073 Z\",\n    id: \"GlobalOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default GlobalOutline;", "map": {"version": 3, "names": ["React", "GlobalOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/GlobalOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction GlobalOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"GlobalOutline-GlobalOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"GlobalOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"GlobalOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M22.5000466,42.4403346 L22.5000466,24 L14.3913043,24 C14.3913043,33.4649419 18.2446927,40.9747273 22.5000466,42.4403346 Z M25.5,24 L25.500952,42.4399905 C29.7559102,40.9735523 33.6086957,33.4642014 33.6086957,24 L25.5,24 Z M5.3,24 C5.3,31.2731078 9.45216137,37.5764263 15.5153578,40.6688293 C12.7504509,36.634678 11,30.6632803 11,24 L5.3,24 Z M37,24 C37,30.6632803 35.2495491,36.634678 32.4843951,40.6690813 C38.5478386,37.5764263 42.7,31.2731078 42.7,24 L37,24 Z M15.5156049,7.33091875 L15.2946208,7.44561447 C10.196287,10.1321925 6.48991277,15.1049882 5.5393343,21.0002511 L11.1198383,21.0002511 C11.5582739,15.5581426 13.1727089,10.749241 15.5156049,7.33091875 Z M14.5224111,21 L22.5000466,21 L22.5000466,5.5596654 C18.7053742,6.8666071 15.2303454,12.9798886 14.5224111,21 Z M25.500952,5.56000947 L25.5,21 L33.4775889,21 C32.7697167,12.9805921 29.2952355,6.86767971 25.500952,5.56000947 Z M32.4846422,7.33117073 L32.5441633,7.4186565 C34.8547353,10.8312895 36.4454642,15.6045426 36.8801617,21.0002511 L42.4606657,21.0002511 C41.4964219,15.0202402 37.6965792,9.98940714 32.4846422,7.33117073 Z\",\n    id: \"GlobalOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default GlobalOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,6BAA6B;IACjCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,utCAAutC;IAC1tCR,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}