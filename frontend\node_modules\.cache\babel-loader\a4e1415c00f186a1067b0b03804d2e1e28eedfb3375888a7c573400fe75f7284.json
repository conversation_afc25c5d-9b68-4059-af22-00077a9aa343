{"ast": null, "code": "import * as React from \"react\";\nfunction LocationOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LocationOutline-LocationOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LocationOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"LocationOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M27.0587149,6.32924614 L41.6238014,38.2117577 C42.5499509,40.2390694 41.7072446,42.6568629 39.7415642,43.6120506 C38.660319,44.1374626 37.4061217,44.1274672 36.3328783,43.5848849 L24.3654078,37.5346835 C23.8192056,37.2585489 23.1798659,37.2585489 22.6336638,37.5346835 L10.6661932,43.5848849 C8.71506002,44.5712879 6.35802583,43.7396271 5.40161002,41.7273186 C4.87552246,40.6204251 4.86583092,39.3269039 5.37527013,38.2117577 L19.9403567,6.32924614 C20.8665061,4.3019344 23.2107974,3.43280614 25.1764777,4.38799383 C26.0035209,4.78988091 26.6690454,5.47627202 27.0587149,6.32924614 Z M24.3893305,7.62638637 C24.2919132,7.41314284 24.1255321,7.24154507 23.9187712,7.1410733 C23.4624526,6.9193333 22.9245201,7.09084508 22.664619,7.5222729 L22.609741,7.62638637 L8.04465446,39.5088979 C7.91729465,39.7876845 7.91971754,40.1110648 8.05123943,40.3877882 C8.27326453,40.8549312 8.79720864,41.0675724 9.26147164,40.8981239 L9.36738523,40.8521797 L21.3348558,34.8019784 C22.6244997,34.1499939 24.1218347,34.1137725 25.4345554,34.6933143 L25.6642158,34.8019784 L37.6316863,40.8521797 C37.8999971,40.9878253 38.2135465,40.9903242 38.4838578,40.8589712 C38.9401764,40.6372312 39.1544254,40.1002101 38.9973225,39.6188293 L38.9544171,39.5088979 L24.3893305,7.62638637 Z\",\n    id: \"LocationOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default LocationOutline;", "map": {"version": 3, "names": ["React", "LocationOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/LocationOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction LocationOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LocationOutline-LocationOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LocationOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"LocationOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M27.0587149,6.32924614 L41.6238014,38.2117577 C42.5499509,40.2390694 41.7072446,42.6568629 39.7415642,43.6120506 C38.660319,44.1374626 37.4061217,44.1274672 36.3328783,43.5848849 L24.3654078,37.5346835 C23.8192056,37.2585489 23.1798659,37.2585489 22.6336638,37.5346835 L10.6661932,43.5848849 C8.71506002,44.5712879 6.35802583,43.7396271 5.40161002,41.7273186 C4.87552246,40.6204251 4.86583092,39.3269039 5.37527013,38.2117577 L19.9403567,6.32924614 C20.8665061,4.3019344 23.2107974,3.43280614 25.1764777,4.38799383 C26.0035209,4.78988091 26.6690454,5.47627202 27.0587149,6.32924614 Z M24.3893305,7.62638637 C24.2919132,7.41314284 24.1255321,7.24154507 23.9187712,7.1410733 C23.4624526,6.9193333 22.9245201,7.09084508 22.664619,7.5222729 L22.609741,7.62638637 L8.04465446,39.5088979 C7.91729465,39.7876845 7.91971754,40.1110648 8.05123943,40.3877882 C8.27326453,40.8549312 8.79720864,41.0675724 9.26147164,40.8981239 L9.36738523,40.8521797 L21.3348558,34.8019784 C22.6244997,34.1499939 24.1218347,34.1137725 25.4345554,34.6933143 L25.6642158,34.8019784 L37.6316863,40.8521797 C37.8999971,40.9878253 38.2135465,40.9903242 38.4838578,40.8589712 C38.9401764,40.6372312 39.1544254,40.1002101 38.9973225,39.6188293 L38.9544171,39.5088979 L24.3893305,7.62638637 Z\",\n    id: \"LocationOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default LocationOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,iCAAiC;IACrCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,0uCAA0uC;IAC7uCR,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}