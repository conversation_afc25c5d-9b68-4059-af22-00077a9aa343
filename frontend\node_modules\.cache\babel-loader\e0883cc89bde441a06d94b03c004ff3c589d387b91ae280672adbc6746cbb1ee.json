{"ast": null, "code": "import React, { useEffect, useRef } from 'react';\nimport { useInViewport } from 'ahooks';\nexport const LazyDetector = props => {\n  const ref = useRef(null);\n  const [inViewport] = useInViewport(ref);\n  useEffect(() => {\n    if (inViewport) {\n      props.onActive();\n    }\n  }, [inViewport]);\n  return React.createElement(\"div\", {\n    ref: ref\n  });\n};", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useInViewport", "LazyDetector", "props", "ref", "inViewport", "onActive", "createElement"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/image/lazy-detector.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { useInViewport } from 'ahooks';\nexport const LazyDetector = props => {\n  const ref = useRef(null);\n  const [inViewport] = useInViewport(ref);\n  useEffect(() => {\n    if (inViewport) {\n      props.onActive();\n    }\n  }, [inViewport]);\n  return React.createElement(\"div\", {\n    ref: ref\n  });\n};"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,aAAa,QAAQ,QAAQ;AACtC,OAAO,MAAMC,YAAY,GAAGC,KAAK,IAAI;EACnC,MAAMC,GAAG,GAAGJ,MAAM,CAAC,IAAI,CAAC;EACxB,MAAM,CAACK,UAAU,CAAC,GAAGJ,aAAa,CAACG,GAAG,CAAC;EACvCL,SAAS,CAAC,MAAM;IACd,IAAIM,UAAU,EAAE;MACdF,KAAK,CAACG,QAAQ,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACD,UAAU,CAAC,CAAC;EAChB,OAAOP,KAAK,CAACS,aAAa,CAAC,KAAK,EAAE;IAChCH,GAAG,EAAEA;EACP,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}