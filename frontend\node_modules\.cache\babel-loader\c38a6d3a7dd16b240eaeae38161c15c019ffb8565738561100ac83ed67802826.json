{"ast": null, "code": "import React from 'react';\nexport const BrokenImageIcon = () => React.createElement(\"svg\", {\n  viewBox: '0 0 48 48',\n  xmlns: 'http://www.w3.org/2000/svg'\n}, React.createElement(\"path\", {\n  d: 'M19.233 6.233 17.42 9.08l-10.817.001a.665.665 0 0 0-.647.562l-.007.096V34.9l5.989-8.707a2.373 2.373 0 0 1 1.801-1.005 2.415 2.415 0 0 1 1.807.625l.126.127 4.182 4.525 2.267-3.292 5.461 7.841-4.065 7.375H6.604c-1.86 0-3.382-1.47-3.482-3.317l-.005-.192V9.744c0-1.872 1.461-3.405 3.296-3.505l.19-.005h12.63Zm22.163 0c1.86 0 3.382 1.472 3.482 3.314l.005.192v29.14a3.507 3.507 0 0 1-3.3 3.505l-.191.006H27.789l3.63-6.587.06-.119a1.87 1.87 0 0 0-.163-1.853l-6.928-9.949 3.047-4.422a2.374 2.374 0 0 1 1.96-1.01 2.4 2.4 0 0 1 1.86.87l.106.14L42.05 34.89V9.74a.664.664 0 0 0-.654-.658H21.855l1.812-2.848h17.73Zm-28.305 5.611c.794 0 1.52.298 2.07.788l-.843 1.325-.067.114a1.87 1.87 0 0 0 .11 1.959l.848 1.217c-.556.515-1.3.83-2.118.83a3.122 3.122 0 0 1-3.117-3.116 3.119 3.119 0 0 1 3.117-3.117Z',\n  fill: '#DBDBDB',\n  fillRule: 'nonzero'\n}));", "map": {"version": 3, "names": ["React", "BrokenImageIcon", "createElement", "viewBox", "xmlns", "d", "fill", "fillRule"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/image/broken-image-icon.js"], "sourcesContent": ["import React from 'react';\nexport const BrokenImageIcon = () => React.createElement(\"svg\", {\n  viewBox: '0 0 48 48',\n  xmlns: 'http://www.w3.org/2000/svg'\n}, React.createElement(\"path\", {\n  d: 'M19.233 6.233 17.42 9.08l-10.817.001a.665.665 0 0 0-.647.562l-.007.096V34.9l5.989-8.707a2.373 2.373 0 0 1 1.801-1.005 2.415 2.415 0 0 1 1.807.625l.126.127 4.182 4.525 2.267-3.292 5.461 7.841-4.065 7.375H6.604c-1.86 0-3.382-1.47-3.482-3.317l-.005-.192V9.744c0-1.872 1.461-3.405 3.296-3.505l.19-.005h12.63Zm22.163 0c1.86 0 3.382 1.472 3.482 3.314l.005.192v29.14a3.507 3.507 0 0 1-3.3 3.505l-.191.006H27.789l3.63-6.587.06-.119a1.87 1.87 0 0 0-.163-1.853l-6.928-9.949 3.047-4.422a2.374 2.374 0 0 1 1.96-1.01 2.4 2.4 0 0 1 1.86.87l.106.14L42.05 34.89V9.74a.664.664 0 0 0-.654-.658H21.855l1.812-2.848h17.73Zm-28.305 5.611c.794 0 1.52.298 2.07.788l-.843 1.325-.067.114a1.87 1.87 0 0 0 .11 1.959l.848 1.217c-.556.515-1.3.83-2.118.83a3.122 3.122 0 0 1-3.117-3.116 3.119 3.119 0 0 1 3.117-3.117Z',\n  fill: '#DBDBDB',\n  fillRule: 'nonzero'\n}));"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,MAAMC,eAAe,GAAGA,CAAA,KAAMD,KAAK,CAACE,aAAa,CAAC,KAAK,EAAE;EAC9DC,OAAO,EAAE,WAAW;EACpBC,KAAK,EAAE;AACT,CAAC,EAAEJ,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC7BG,CAAC,EAAE,mxBAAmxB;EACtxBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE;AACZ,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}