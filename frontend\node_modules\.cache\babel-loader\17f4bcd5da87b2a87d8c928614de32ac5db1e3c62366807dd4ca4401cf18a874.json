{"ast": null, "code": "import * as React from \"react\";\nfunction AddressBookFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AddressBookFill-AddressBookFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AddressBookFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"AddressBookFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37.7926856,4 L37.7926853,4 C40.9447461,4 43.5,6.55837054 43.5,9.71428571 L43.5,38.2857143 L43.5,38.2857143 C43.5,41.441625 40.9447461,44 37.7926853,44 L13.0609736,44 L13.0609734,44 C9.90891267,44 7.35365872,41.441625 7.35365872,38.2857143 C7.35365872,38.2857143 7.35365872,38.2857143 7.35365872,38.2857143 L7.35365872,36.3809509 L4.88048755,36.3809509 L4.88048753,36.3809509 C4.67035,36.3809509 4.5,36.2103929 4.5,36 L4.5,33.9047621 C4.5,33.6952384 4.67121947,33.5238098 4.88048753,33.5238098 L7.3536587,33.5238098 L7.3536587,24.9523812 L4.88048753,24.9523812 L4.88048751,24.9523812 C4.67034998,24.9523812 4.5,24.7818232 4.5,24.571429 L4.5,22.4761924 C4.5,22.2666687 4.67121946,22.0952402 4.88048751,22.0952402 L7.35365868,22.0952402 L7.35365868,13.5238116 L4.88048751,13.5238116 L4.8804875,13.5238116 C4.67034997,13.5238116 4.5,13.3532536 4.5,13.1428594 C4.5,13.1428594 4.5,13.1428594 4.5,13.1428594 L4.5,11.0476228 C4.5,10.8380991 4.67121944,10.6666705 4.8804875,10.6666705 L7.35365867,10.6666705 L7.35365867,9.71429107 L7.35365867,9.71429193 C7.35365819,6.55838122 9.90890816,4 13.0609733,4 L37.792685,4 L37.7926856,4 Z M25.2548004,11 L24.7451838,11 L24.534607,11.0038401 L24.5346069,11.0038401 C21.6614191,11.1162429 19.3902591,13.4745812 19.3903943,16.3455051 L19.3903943,19.3456245 L19.397125,19.609635 L19.3971247,19.6096279 C19.464146,21.0163877 20.063484,22.3453034 21.0740407,23.327854 L22.4173107,24.6344669 L22.5038491,24.7295106 C22.6673107,24.9282384 22.7567337,25.1778484 22.7567337,25.4370599 L22.7567337,25.6194669 L22.7480799,25.7509923 L22.7480799,25.7509922 C22.7043637,26.102785 22.4890497,26.4101511 22.1730817,26.5718245 L13.7221102,30.886234 L13.5759563,30.9678371 L13.5759564,30.967837 C12.9081097,31.3741616 12.5004192,32.09843 12.5,32.8792782 L12.5,33.2536929 L12.5057692,33.3976985 C12.578846,34.2943723 13.3307687,35 14.2480759,35 L35.7519241,35 L35.7519241,35 C36.7169851,35 37.4994681,34.2191653 37.5,33.2556099 L37.5,32.8811952 C37.5,32.0392436 37.0288479,31.2683318 36.2778838,30.8862353 L27.8269124,26.5718258 L27.8269123,26.5718257 C27.4683579,26.3888481 27.2428771,26.0205541 27.2432603,25.6185082 L27.2432603,25.4361012 C27.2432603,25.1346492 27.3663373,24.845678 27.5826833,24.6344712 L28.9259532,23.3278584 L28.9259533,23.3278583 C30.0019232,22.2814366 30.6087513,20.8453222 30.6086431,19.3456203 L30.6086431,16.3455009 L30.6086431,16.3455011 C30.6086431,13.393268 28.2116464,11 25.2548002,11 L25.2548004,11 Z\",\n    id: \"AddressBookFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default AddressBookFill;", "map": {"version": 3, "names": ["React", "AddressBookFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/AddressBookFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction AddressBookFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AddressBookFill-AddressBookFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AddressBookFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"AddressBookFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37.7926856,4 L37.7926853,4 C40.9447461,4 43.5,6.55837054 43.5,9.71428571 L43.5,38.2857143 L43.5,38.2857143 C43.5,41.441625 40.9447461,44 37.7926853,44 L13.0609736,44 L13.0609734,44 C9.90891267,44 7.35365872,41.441625 7.35365872,38.2857143 C7.35365872,38.2857143 7.35365872,38.2857143 7.35365872,38.2857143 L7.35365872,36.3809509 L4.88048755,36.3809509 L4.88048753,36.3809509 C4.67035,36.3809509 4.5,36.2103929 4.5,36 L4.5,33.9047621 C4.5,33.6952384 4.67121947,33.5238098 4.88048753,33.5238098 L7.3536587,33.5238098 L7.3536587,24.9523812 L4.88048753,24.9523812 L4.88048751,24.9523812 C4.67034998,24.9523812 4.5,24.7818232 4.5,24.571429 L4.5,22.4761924 C4.5,22.2666687 4.67121946,22.0952402 4.88048751,22.0952402 L7.35365868,22.0952402 L7.35365868,13.5238116 L4.88048751,13.5238116 L4.8804875,13.5238116 C4.67034997,13.5238116 4.5,13.3532536 4.5,13.1428594 C4.5,13.1428594 4.5,13.1428594 4.5,13.1428594 L4.5,11.0476228 C4.5,10.8380991 4.67121944,10.6666705 4.8804875,10.6666705 L7.35365867,10.6666705 L7.35365867,9.71429107 L7.35365867,9.71429193 C7.35365819,6.55838122 9.90890816,4 13.0609733,4 L37.792685,4 L37.7926856,4 Z M25.2548004,11 L24.7451838,11 L24.534607,11.0038401 L24.5346069,11.0038401 C21.6614191,11.1162429 19.3902591,13.4745812 19.3903943,16.3455051 L19.3903943,19.3456245 L19.397125,19.609635 L19.3971247,19.6096279 C19.464146,21.0163877 20.063484,22.3453034 21.0740407,23.327854 L22.4173107,24.6344669 L22.5038491,24.7295106 C22.6673107,24.9282384 22.7567337,25.1778484 22.7567337,25.4370599 L22.7567337,25.6194669 L22.7480799,25.7509923 L22.7480799,25.7509922 C22.7043637,26.102785 22.4890497,26.4101511 22.1730817,26.5718245 L13.7221102,30.886234 L13.5759563,30.9678371 L13.5759564,30.967837 C12.9081097,31.3741616 12.5004192,32.09843 12.5,32.8792782 L12.5,33.2536929 L12.5057692,33.3976985 C12.578846,34.2943723 13.3307687,35 14.2480759,35 L35.7519241,35 L35.7519241,35 C36.7169851,35 37.4994681,34.2191653 37.5,33.2556099 L37.5,32.8811952 C37.5,32.0392436 37.0288479,31.2683318 36.2778838,30.8862353 L27.8269124,26.5718258 L27.8269123,26.5718257 C27.4683579,26.3888481 27.2428771,26.0205541 27.2432603,25.6185082 L27.2432603,25.4361012 C27.2432603,25.1346492 27.3663373,24.845678 27.5826833,24.6344712 L28.9259532,23.3278584 L28.9259533,23.3278583 C30.0019232,22.2814366 30.6087513,20.8453222 30.6086431,19.3456203 L30.6086431,16.3455009 L30.6086431,16.3455011 C30.6086431,13.393268 28.2116464,11 25.2548002,11 L25.2548004,11 Z\",\n    id: \"AddressBookFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default AddressBookFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,iCAAiC;IACrCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,m5EAAm5E;IACt5ER,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}