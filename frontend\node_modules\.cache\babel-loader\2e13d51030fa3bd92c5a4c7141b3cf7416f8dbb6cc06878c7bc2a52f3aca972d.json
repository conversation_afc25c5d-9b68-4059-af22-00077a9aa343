{"ast": null, "code": "import React from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nconst classPrefix = `adm-tag`;\nconst colorRecord = {\n  default: 'var(--adm-color-text-secondary, #666666)',\n  primary: 'var(--adm-color-primary, #1677ff)',\n  success: 'var(--adm-color-success, #00b578)',\n  warning: 'var(--adm-color-warning, #ff8f1f)',\n  danger: 'var(--adm-color-danger, #ff3141)'\n};\nconst defaultProps = {\n  color: 'default',\n  fill: 'solid',\n  round: false\n};\nexport const Tag = p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  const color = (_a = colorRecord[props.color]) !== null && _a !== void 0 ? _a : props.color;\n  const style = {\n    '--border-color': color,\n    '--text-color': props.fill === 'outline' ? color : '#ffffff',\n    '--background-color': props.fill === 'outline' ? 'transparent' : color\n  };\n  return withNativeProps(props, React.createElement(\"span\", {\n    style: style,\n    onClick: props.onClick,\n    className: classNames(classPrefix, {\n      [`${classPrefix}-round`]: props.round\n    })\n  }, props.children));\n};", "map": {"version": 3, "names": ["React", "mergeProps", "withNativeProps", "classNames", "classPrefix", "colorRecord", "default", "primary", "success", "warning", "danger", "defaultProps", "color", "fill", "round", "Tag", "p", "_a", "props", "style", "createElement", "onClick", "className", "children"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/tag/tag.js"], "sourcesContent": ["import React from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nconst classPrefix = `adm-tag`;\nconst colorRecord = {\n  default: 'var(--adm-color-text-secondary, #666666)',\n  primary: 'var(--adm-color-primary, #1677ff)',\n  success: 'var(--adm-color-success, #00b578)',\n  warning: 'var(--adm-color-warning, #ff8f1f)',\n  danger: 'var(--adm-color-danger, #ff3141)'\n};\nconst defaultProps = {\n  color: 'default',\n  fill: 'solid',\n  round: false\n};\nexport const Tag = p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  const color = (_a = colorRecord[props.color]) !== null && _a !== void 0 ? _a : props.color;\n  const style = {\n    '--border-color': color,\n    '--text-color': props.fill === 'outline' ? color : '#ffffff',\n    '--background-color': props.fill === 'outline' ? 'transparent' : color\n  };\n  return withNativeProps(props, React.createElement(\"span\", {\n    style: style,\n    onClick: props.onClick,\n    className: classNames(classPrefix, {\n      [`${classPrefix}-round`]: props.round\n    })\n  }, props.children));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,UAAU,MAAM,YAAY;AACnC,MAAMC,WAAW,GAAG,SAAS;AAC7B,MAAMC,WAAW,GAAG;EAClBC,OAAO,EAAE,0CAA0C;EACnDC,OAAO,EAAE,mCAAmC;EAC5CC,OAAO,EAAE,mCAAmC;EAC5CC,OAAO,EAAE,mCAAmC;EAC5CC,MAAM,EAAE;AACV,CAAC;AACD,MAAMC,YAAY,GAAG;EACnBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE;AACT,CAAC;AACD,OAAO,MAAMC,GAAG,GAAGC,CAAC,IAAI;EACtB,IAAIC,EAAE;EACN,MAAMC,KAAK,GAAGjB,UAAU,CAACU,YAAY,EAAEK,CAAC,CAAC;EACzC,MAAMJ,KAAK,GAAG,CAACK,EAAE,GAAGZ,WAAW,CAACa,KAAK,CAACN,KAAK,CAAC,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGC,KAAK,CAACN,KAAK;EAC1F,MAAMO,KAAK,GAAG;IACZ,gBAAgB,EAAEP,KAAK;IACvB,cAAc,EAAEM,KAAK,CAACL,IAAI,KAAK,SAAS,GAAGD,KAAK,GAAG,SAAS;IAC5D,oBAAoB,EAAEM,KAAK,CAACL,IAAI,KAAK,SAAS,GAAG,aAAa,GAAGD;EACnE,CAAC;EACD,OAAOV,eAAe,CAACgB,KAAK,EAAElB,KAAK,CAACoB,aAAa,CAAC,MAAM,EAAE;IACxDD,KAAK,EAAEA,KAAK;IACZE,OAAO,EAAEH,KAAK,CAACG,OAAO;IACtBC,SAAS,EAAEnB,UAAU,CAACC,WAAW,EAAE;MACjC,CAAC,GAAGA,WAAW,QAAQ,GAAGc,KAAK,CAACJ;IAClC,CAAC;EACH,CAAC,EAAEI,KAAK,CAACK,QAAQ,CAAC,CAAC;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}