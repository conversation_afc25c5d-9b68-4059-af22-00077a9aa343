{"ast": null, "code": "export { default as useEvent } from \"./hooks/useEvent\";\nexport { default as useMergedState } from \"./hooks/useMergedState\";\nexport { supportNodeRef, supportRef, useComposeRef } from \"./ref\";\nexport { default as get } from \"./utils/get\";\nexport { default as set } from \"./utils/set\";\nexport { default as warning } from \"./warning\";", "map": {"version": 3, "names": ["default", "useEvent", "useMergedState", "supportNodeRef", "supportRef", "useComposeRef", "get", "set", "warning"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/rc-util/es/index.js"], "sourcesContent": ["export { default as useEvent } from \"./hooks/useEvent\";\nexport { default as useMergedState } from \"./hooks/useMergedState\";\nexport { supportNodeRef, supportRef, useComposeRef } from \"./ref\";\nexport { default as get } from \"./utils/get\";\nexport { default as set } from \"./utils/set\";\nexport { default as warning } from \"./warning\";"], "mappings": "AAAA,SAASA,OAAO,IAAIC,QAAQ,QAAQ,kBAAkB;AACtD,SAASD,OAAO,IAAIE,cAAc,QAAQ,wBAAwB;AAClE,SAASC,cAAc,EAAEC,UAAU,EAAEC,aAAa,QAAQ,OAAO;AACjE,SAASL,OAAO,IAAIM,GAAG,QAAQ,aAAa;AAC5C,SAASN,OAAO,IAAIO,GAAG,QAAQ,aAAa;AAC5C,SAASP,OAAO,IAAIQ,OAAO,QAAQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}