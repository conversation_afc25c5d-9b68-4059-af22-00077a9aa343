{"ast": null, "code": "import * as React from \"react\";\nfunction ReceiptOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ReceiptOutline-ReceiptOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ReceiptOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ReceiptOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M36,4 C39.3137085,4 42,6.6862915 42,10 L42,35.714 L45,35 L45,38 L24,43 L3,38 L3,35 L6,35.714 L6,10 C6,6.6862915 8.6862915,4 12,4 L36,4 Z M36,7 L12,7 C10.4023191,7 9.09633912,8.24891996 9.00509269,9.82372721 L9,10 L9,36.429 L24,40 L39,36.429 L39,10 C39,8.40231912 37.75108,7.09633912 36.1762728,7.00509269 L36,7 Z M29.481684,11.6536905 L29.5554769,11.6868007 L31.4514212,12.7775476 C31.4517209,12.77772 31.4520203,12.7778928 31.4523195,12.778066 L31.5180198,12.8254803 C31.6372256,12.9314105 31.682089,13.0995545 31.6314989,13.2507889 L31.5981516,13.3246307 L26.5546134,22.0374511 L31.1,22.0381798 C31.3209139,22.0381798 31.5,22.2172659 31.5,22.4381798 L31.5,24.6305439 C31.5,24.8514578 31.3209139,25.0305439 31.1,25.0305439 L25.4996134,25.0304511 L25.4996134,27.0254511 L31.1,27.0254532 C31.3209139,27.0254532 31.5,27.2045393 31.5,27.4254532 L31.5,29.6178173 C31.5,29.8387312 31.3209139,30.0178173 31.1,30.0178173 L25.4996134,30.0174511 L25.5,34.6 C25.5,34.8209139 25.3209139,35 25.1,35 L22.9,35 C22.6790861,35 22.5,34.8209139 22.5,34.6 L22.4996134,30.0174511 L16.9,30.0178173 C16.6790861,30.0178173 16.5,29.8387312 16.5,29.6178173 L16.5,27.4254532 C16.5,27.2045393 16.6790861,27.0254532 16.9,27.0254532 L22.4996134,27.0254511 L22.4996134,25.0304511 L16.9,25.0305439 C16.6790861,25.0305439 16.5,24.8514578 16.5,24.6305439 L16.5,22.4381798 C16.5,22.2172659 16.6790861,22.0381798 16.9,22.0381798 L21.3286134,22.0374511 L16.2864814,13.3246307 C16.1758221,13.1334306 16.2411134,12.8887252 16.4323135,12.778066 L16.4327625,12.7778065 L18.329156,11.6868007 C18.5202914,11.5768395 18.764366,11.6423015 18.8748225,11.8331512 L23.9416134,20.5884511 L29.0098105,11.8331512 C29.1064599,11.6661577 29.3054107,11.5951642 29.481684,11.6536905 Z\",\n    id: \"ReceiptOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\"\n  }))));\n}\nexport default ReceiptOutline;", "map": {"version": 3, "names": ["React", "ReceiptOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile-icons/es/ReceiptOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction ReceiptOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ReceiptOutline-ReceiptOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ReceiptOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ReceiptOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M36,4 C39.3137085,4 42,6.6862915 42,10 L42,35.714 L45,35 L45,38 L24,43 L3,38 L3,35 L6,35.714 L6,10 C6,6.6862915 8.6862915,4 12,4 L36,4 Z M36,7 L12,7 C10.4023191,7 9.09633912,8.24891996 9.00509269,9.82372721 L9,10 L9,36.429 L24,40 L39,36.429 L39,10 C39,8.40231912 37.75108,7.09633912 36.1762728,7.00509269 L36,7 Z M29.481684,11.6536905 L29.5554769,11.6868007 L31.4514212,12.7775476 C31.4517209,12.77772 31.4520203,12.7778928 31.4523195,12.778066 L31.5180198,12.8254803 C31.6372256,12.9314105 31.682089,13.0995545 31.6314989,13.2507889 L31.5981516,13.3246307 L26.5546134,22.0374511 L31.1,22.0381798 C31.3209139,22.0381798 31.5,22.2172659 31.5,22.4381798 L31.5,24.6305439 C31.5,24.8514578 31.3209139,25.0305439 31.1,25.0305439 L25.4996134,25.0304511 L25.4996134,27.0254511 L31.1,27.0254532 C31.3209139,27.0254532 31.5,27.2045393 31.5,27.4254532 L31.5,29.6178173 C31.5,29.8387312 31.3209139,30.0178173 31.1,30.0178173 L25.4996134,30.0174511 L25.5,34.6 C25.5,34.8209139 25.3209139,35 25.1,35 L22.9,35 C22.6790861,35 22.5,34.8209139 22.5,34.6 L22.4996134,30.0174511 L16.9,30.0178173 C16.6790861,30.0178173 16.5,29.8387312 16.5,29.6178173 L16.5,27.4254532 C16.5,27.2045393 16.6790861,27.0254532 16.9,27.0254532 L22.4996134,27.0254511 L22.4996134,25.0304511 L16.9,25.0305439 C16.6790861,25.0305439 16.5,24.8514578 16.5,24.6305439 L16.5,22.4381798 C16.5,22.2172659 16.6790861,22.0381798 16.9,22.0381798 L21.3286134,22.0374511 L16.2864814,13.3246307 C16.1758221,13.1334306 16.2411134,12.8887252 16.4323135,12.778066 L16.4327625,12.7778065 L18.329156,11.6868007 C18.5202914,11.5768395 18.764366,11.6423015 18.8748225,11.8331512 L23.9416134,20.5884511 L29.0098105,11.8331512 C29.1064599,11.6661577 29.3054107,11.5951642 29.481684,11.6536905 Z\",\n    id: \"ReceiptOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\"\n  }))));\n}\n\nexport default ReceiptOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,+BAA+B;IACnCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,6BAA6B;IACjCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,osDAAosD;IACvsDR,EAAE,EAAE,yCAAyC;IAC7CG,IAAI,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAenB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}