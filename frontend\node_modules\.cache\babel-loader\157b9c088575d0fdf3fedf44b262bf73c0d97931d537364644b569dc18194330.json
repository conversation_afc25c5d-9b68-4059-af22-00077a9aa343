{"ast": null, "code": "import { useMemo } from 'react';\nimport { withCache } from '../../utils/with-cache';\nexport function generateColumnsExtend(rawColumns, val) {\n  const columns = withCache(() => {\n    const c = typeof rawColumns === 'function' ? rawColumns(val) : rawColumns;\n    return c.map(column => column.map(item => typeof item === 'string' ? {\n      label: item,\n      value: item\n    } : item));\n  });\n  const items = withCache(() => {\n    return val.map((v, index) => {\n      var _a;\n      const column = columns()[index];\n      if (!column) return null;\n      return (_a = column.find(item => item.value === v)) !== null && _a !== void 0 ? _a : null;\n    });\n  });\n  const extend = {\n    get columns() {\n      return columns();\n    },\n    get items() {\n      return items();\n    }\n  };\n  return extend;\n}\nexport function useColumnsExtend(rawColumns, value) {\n  return useMemo(() => generateColumnsExtend(rawColumns, value), [rawColumns, value]);\n}", "map": {"version": 3, "names": ["useMemo", "with<PERSON><PERSON>", "generateColumnsExtend", "rawColumns", "val", "columns", "c", "map", "column", "item", "label", "value", "items", "v", "index", "_a", "find", "extend", "useColumnsExtend"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/picker-view/columns-extend.js"], "sourcesContent": ["import { useMemo } from 'react';\nimport { withCache } from '../../utils/with-cache';\nexport function generateColumnsExtend(rawColumns, val) {\n  const columns = withCache(() => {\n    const c = typeof rawColumns === 'function' ? rawColumns(val) : rawColumns;\n    return c.map(column => column.map(item => typeof item === 'string' ? {\n      label: item,\n      value: item\n    } : item));\n  });\n  const items = withCache(() => {\n    return val.map((v, index) => {\n      var _a;\n      const column = columns()[index];\n      if (!column) return null;\n      return (_a = column.find(item => item.value === v)) !== null && _a !== void 0 ? _a : null;\n    });\n  });\n  const extend = {\n    get columns() {\n      return columns();\n    },\n    get items() {\n      return items();\n    }\n  };\n  return extend;\n}\nexport function useColumnsExtend(rawColumns, value) {\n  return useMemo(() => generateColumnsExtend(rawColumns, value), [rawColumns, value]);\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B,SAASC,SAAS,QAAQ,wBAAwB;AAClD,OAAO,SAASC,qBAAqBA,CAACC,UAAU,EAAEC,GAAG,EAAE;EACrD,MAAMC,OAAO,GAAGJ,SAAS,CAAC,MAAM;IAC9B,MAAMK,CAAC,GAAG,OAAOH,UAAU,KAAK,UAAU,GAAGA,UAAU,CAACC,GAAG,CAAC,GAAGD,UAAU;IACzE,OAAOG,CAAC,CAACC,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACD,GAAG,CAACE,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,GAAG;MACnEC,KAAK,EAAED,IAAI;MACXE,KAAK,EAAEF;IACT,CAAC,GAAGA,IAAI,CAAC,CAAC;EACZ,CAAC,CAAC;EACF,MAAMG,KAAK,GAAGX,SAAS,CAAC,MAAM;IAC5B,OAAOG,GAAG,CAACG,GAAG,CAAC,CAACM,CAAC,EAAEC,KAAK,KAAK;MAC3B,IAAIC,EAAE;MACN,MAAMP,MAAM,GAAGH,OAAO,CAAC,CAAC,CAACS,KAAK,CAAC;MAC/B,IAAI,CAACN,MAAM,EAAE,OAAO,IAAI;MACxB,OAAO,CAACO,EAAE,GAAGP,MAAM,CAACQ,IAAI,CAACP,IAAI,IAAIA,IAAI,CAACE,KAAK,KAAKE,CAAC,CAAC,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;IAC3F,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAME,MAAM,GAAG;IACb,IAAIZ,OAAOA,CAAA,EAAG;MACZ,OAAOA,OAAO,CAAC,CAAC;IAClB,CAAC;IACD,IAAIO,KAAKA,CAAA,EAAG;MACV,OAAOA,KAAK,CAAC,CAAC;IAChB;EACF,CAAC;EACD,OAAOK,MAAM;AACf;AACA,OAAO,SAASC,gBAAgBA,CAACf,UAAU,EAAEQ,KAAK,EAAE;EAClD,OAAOX,OAAO,CAAC,MAAME,qBAAqB,CAACC,UAAU,EAAEQ,KAAK,CAAC,EAAE,CAACR,UAAU,EAAEQ,KAAK,CAAC,CAAC;AACrF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}