{"ast": null, "code": "import { useTimeout } from 'ahooks';\nimport { CloseOutline, SoundOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { memo, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { useMutationEffect } from '../../utils/use-mutation-effect';\nimport { useResizeEffect } from '../../utils/use-resize-effect';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nconst classPrefix = `adm-notice-bar`;\nconst defaultProps = {\n  color: 'default',\n  delay: 2000,\n  speed: 50,\n  icon: React.createElement(SoundOutline, null),\n  wrap: false,\n  shape: 'rectangular',\n  bordered: 'block'\n};\nexport const NoticeBar = memo(props => {\n  const {\n    noticeBar: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, props);\n  const closeIcon = mergeProp(React.createElement(CloseOutline, {\n    className: `${classPrefix}-close-icon`\n  }), componentConfig.closeIcon, props.closeIcon);\n  const containerRef = useRef(null);\n  const textRef = useRef(null);\n  const [visible, setVisible] = useState(true);\n  const speed = mergedProps.speed;\n  const delayLockRef = useRef(true);\n  const animatingRef = useRef(false);\n  function start() {\n    if (delayLockRef.current || mergedProps.wrap) return;\n    const container = containerRef.current;\n    const text = textRef.current;\n    if (!container || !text) return;\n    if (container.offsetWidth >= text.offsetWidth) {\n      animatingRef.current = false;\n      text.style.removeProperty('transition-duration');\n      text.style.removeProperty('transform');\n      return;\n    }\n    if (animatingRef.current) return;\n    const initial = !text.style.transform;\n    text.style.transitionDuration = '0s';\n    if (initial) {\n      text.style.transform = 'translateX(0)';\n    } else {\n      text.style.transform = `translateX(${container.offsetWidth}px)`;\n    }\n    const distance = initial ? text.offsetWidth : container.offsetWidth + text.offsetWidth;\n    animatingRef.current = true;\n    text.style.transitionDuration = `${Math.round(distance / speed)}s`;\n    text.style.transform = `translateX(-${text.offsetWidth}px)`;\n  }\n  useTimeout(() => {\n    delayLockRef.current = false;\n    start();\n  }, mergedProps.delay);\n  useResizeEffect(() => {\n    start();\n  }, containerRef);\n  useMutationEffect(() => {\n    start();\n  }, textRef, {\n    subtree: true,\n    childList: true,\n    characterData: true\n  });\n  if (!visible) return null;\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    className: classNames(classPrefix, `${classPrefix}-${mergedProps.color}`, `${classPrefix}-${mergedProps.shape}`, {\n      [`${classPrefix}-wrap`]: mergedProps.wrap,\n      [`${classPrefix}-bordered`]: mergedProps.bordered === true,\n      [`${classPrefix}-without-border`]: mergedProps.bordered === false\n    }),\n    onClick: mergedProps.onClick\n  }, mergedProps.icon && React.createElement(\"span\", {\n    className: `${classPrefix}-left`\n  }, mergedProps.icon), React.createElement(\"span\", {\n    ref: containerRef,\n    className: `${classPrefix}-content`\n  }, React.createElement(\"span\", {\n    onTransitionEnd: () => {\n      animatingRef.current = false;\n      start();\n    },\n    ref: textRef,\n    className: `${classPrefix}-content-inner`\n  }, mergedProps.content)), (mergedProps.closeable || mergedProps.extra) && React.createElement(\"span\", {\n    className: `${classPrefix}-right`\n  }, mergedProps.extra, mergedProps.closeable && React.createElement(\"div\", {\n    className: `${classPrefix}-close`,\n    onClick: () => {\n      var _a;\n      setVisible(false);\n      (_a = mergedProps.onClose) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n    }\n  }, closeIcon))));\n});", "map": {"version": 3, "names": ["useTimeout", "CloseOutline", "SoundOutline", "classNames", "React", "memo", "useRef", "useState", "withNativeProps", "useMutationEffect", "useResizeEffect", "mergeProp", "mergeProps", "useConfig", "classPrefix", "defaultProps", "color", "delay", "speed", "icon", "createElement", "wrap", "shape", "bordered", "NoticeBar", "props", "noticeBar", "componentConfig", "mergedProps", "closeIcon", "className", "containerRef", "textRef", "visible", "setVisible", "delayLockRef", "animatingRef", "start", "current", "container", "text", "offsetWidth", "style", "removeProperty", "initial", "transform", "transitionDuration", "distance", "Math", "round", "subtree", "childList", "characterData", "onClick", "ref", "onTransitionEnd", "content", "closeable", "extra", "_a", "onClose", "call"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/notice-bar/notice-bar.js"], "sourcesContent": ["import { useTimeout } from 'ahooks';\nimport { CloseOutline, SoundOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { memo, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { useMutationEffect } from '../../utils/use-mutation-effect';\nimport { useResizeEffect } from '../../utils/use-resize-effect';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nconst classPrefix = `adm-notice-bar`;\nconst defaultProps = {\n  color: 'default',\n  delay: 2000,\n  speed: 50,\n  icon: React.createElement(SoundOutline, null),\n  wrap: false,\n  shape: 'rectangular',\n  bordered: 'block'\n};\nexport const NoticeBar = memo(props => {\n  const {\n    noticeBar: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, props);\n  const closeIcon = mergeProp(React.createElement(CloseOutline, {\n    className: `${classPrefix}-close-icon`\n  }), componentConfig.closeIcon, props.closeIcon);\n  const containerRef = useRef(null);\n  const textRef = useRef(null);\n  const [visible, setVisible] = useState(true);\n  const speed = mergedProps.speed;\n  const delayLockRef = useRef(true);\n  const animatingRef = useRef(false);\n  function start() {\n    if (delayLockRef.current || mergedProps.wrap) return;\n    const container = containerRef.current;\n    const text = textRef.current;\n    if (!container || !text) return;\n    if (container.offsetWidth >= text.offsetWidth) {\n      animatingRef.current = false;\n      text.style.removeProperty('transition-duration');\n      text.style.removeProperty('transform');\n      return;\n    }\n    if (animatingRef.current) return;\n    const initial = !text.style.transform;\n    text.style.transitionDuration = '0s';\n    if (initial) {\n      text.style.transform = 'translateX(0)';\n    } else {\n      text.style.transform = `translateX(${container.offsetWidth}px)`;\n    }\n    const distance = initial ? text.offsetWidth : container.offsetWidth + text.offsetWidth;\n    animatingRef.current = true;\n    text.style.transitionDuration = `${Math.round(distance / speed)}s`;\n    text.style.transform = `translateX(-${text.offsetWidth}px)`;\n  }\n  useTimeout(() => {\n    delayLockRef.current = false;\n    start();\n  }, mergedProps.delay);\n  useResizeEffect(() => {\n    start();\n  }, containerRef);\n  useMutationEffect(() => {\n    start();\n  }, textRef, {\n    subtree: true,\n    childList: true,\n    characterData: true\n  });\n  if (!visible) return null;\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    className: classNames(classPrefix, `${classPrefix}-${mergedProps.color}`, `${classPrefix}-${mergedProps.shape}`, {\n      [`${classPrefix}-wrap`]: mergedProps.wrap,\n      [`${classPrefix}-bordered`]: mergedProps.bordered === true,\n      [`${classPrefix}-without-border`]: mergedProps.bordered === false\n    }),\n    onClick: mergedProps.onClick\n  }, mergedProps.icon && React.createElement(\"span\", {\n    className: `${classPrefix}-left`\n  }, mergedProps.icon), React.createElement(\"span\", {\n    ref: containerRef,\n    className: `${classPrefix}-content`\n  }, React.createElement(\"span\", {\n    onTransitionEnd: () => {\n      animatingRef.current = false;\n      start();\n    },\n    ref: textRef,\n    className: `${classPrefix}-content-inner`\n  }, mergedProps.content)), (mergedProps.closeable || mergedProps.extra) && React.createElement(\"span\", {\n    className: `${classPrefix}-right`\n  }, mergedProps.extra, mergedProps.closeable && React.createElement(\"div\", {\n    className: `${classPrefix}-close`,\n    onClick: () => {\n      var _a;\n      setVisible(false);\n      (_a = mergedProps.onClose) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n    }\n  }, closeIcon))));\n});"], "mappings": "AAAA,SAASA,UAAU,QAAQ,QAAQ;AACnC,SAASC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACrD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,SAAS,EAAEC,UAAU,QAAQ,gCAAgC;AACtE,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,MAAMC,WAAW,GAAG,gBAAgB;AACpC,MAAMC,YAAY,GAAG;EACnBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE,EAAE;EACTC,IAAI,EAAEf,KAAK,CAACgB,aAAa,CAAClB,YAAY,EAAE,IAAI,CAAC;EAC7CmB,IAAI,EAAE,KAAK;EACXC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE;AACZ,CAAC;AACD,OAAO,MAAMC,SAAS,GAAGnB,IAAI,CAACoB,KAAK,IAAI;EACrC,MAAM;IACJC,SAAS,EAAEC,eAAe,GAAG,CAAC;EAChC,CAAC,GAAGd,SAAS,CAAC,CAAC;EACf,MAAMe,WAAW,GAAGhB,UAAU,CAACG,YAAY,EAAEY,eAAe,EAAEF,KAAK,CAAC;EACpE,MAAMI,SAAS,GAAGlB,SAAS,CAACP,KAAK,CAACgB,aAAa,CAACnB,YAAY,EAAE;IAC5D6B,SAAS,EAAE,GAAGhB,WAAW;EAC3B,CAAC,CAAC,EAAEa,eAAe,CAACE,SAAS,EAAEJ,KAAK,CAACI,SAAS,CAAC;EAC/C,MAAME,YAAY,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM0B,OAAO,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMW,KAAK,GAAGU,WAAW,CAACV,KAAK;EAC/B,MAAMiB,YAAY,GAAG7B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM8B,YAAY,GAAG9B,MAAM,CAAC,KAAK,CAAC;EAClC,SAAS+B,KAAKA,CAAA,EAAG;IACf,IAAIF,YAAY,CAACG,OAAO,IAAIV,WAAW,CAACP,IAAI,EAAE;IAC9C,MAAMkB,SAAS,GAAGR,YAAY,CAACO,OAAO;IACtC,MAAME,IAAI,GAAGR,OAAO,CAACM,OAAO;IAC5B,IAAI,CAACC,SAAS,IAAI,CAACC,IAAI,EAAE;IACzB,IAAID,SAAS,CAACE,WAAW,IAAID,IAAI,CAACC,WAAW,EAAE;MAC7CL,YAAY,CAACE,OAAO,GAAG,KAAK;MAC5BE,IAAI,CAACE,KAAK,CAACC,cAAc,CAAC,qBAAqB,CAAC;MAChDH,IAAI,CAACE,KAAK,CAACC,cAAc,CAAC,WAAW,CAAC;MACtC;IACF;IACA,IAAIP,YAAY,CAACE,OAAO,EAAE;IAC1B,MAAMM,OAAO,GAAG,CAACJ,IAAI,CAACE,KAAK,CAACG,SAAS;IACrCL,IAAI,CAACE,KAAK,CAACI,kBAAkB,GAAG,IAAI;IACpC,IAAIF,OAAO,EAAE;MACXJ,IAAI,CAACE,KAAK,CAACG,SAAS,GAAG,eAAe;IACxC,CAAC,MAAM;MACLL,IAAI,CAACE,KAAK,CAACG,SAAS,GAAG,cAAcN,SAAS,CAACE,WAAW,KAAK;IACjE;IACA,MAAMM,QAAQ,GAAGH,OAAO,GAAGJ,IAAI,CAACC,WAAW,GAAGF,SAAS,CAACE,WAAW,GAAGD,IAAI,CAACC,WAAW;IACtFL,YAAY,CAACE,OAAO,GAAG,IAAI;IAC3BE,IAAI,CAACE,KAAK,CAACI,kBAAkB,GAAG,GAAGE,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG7B,KAAK,CAAC,GAAG;IAClEsB,IAAI,CAACE,KAAK,CAACG,SAAS,GAAG,eAAeL,IAAI,CAACC,WAAW,KAAK;EAC7D;EACAzC,UAAU,CAAC,MAAM;IACfmC,YAAY,CAACG,OAAO,GAAG,KAAK;IAC5BD,KAAK,CAAC,CAAC;EACT,CAAC,EAAET,WAAW,CAACX,KAAK,CAAC;EACrBP,eAAe,CAAC,MAAM;IACpB2B,KAAK,CAAC,CAAC;EACT,CAAC,EAAEN,YAAY,CAAC;EAChBtB,iBAAiB,CAAC,MAAM;IACtB4B,KAAK,CAAC,CAAC;EACT,CAAC,EAAEL,OAAO,EAAE;IACVkB,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,IAAI,CAACnB,OAAO,EAAE,OAAO,IAAI;EACzB,OAAOzB,eAAe,CAACoB,WAAW,EAAExB,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAC7DU,SAAS,EAAE3B,UAAU,CAACW,WAAW,EAAE,GAAGA,WAAW,IAAIc,WAAW,CAACZ,KAAK,EAAE,EAAE,GAAGF,WAAW,IAAIc,WAAW,CAACN,KAAK,EAAE,EAAE;MAC/G,CAAC,GAAGR,WAAW,OAAO,GAAGc,WAAW,CAACP,IAAI;MACzC,CAAC,GAAGP,WAAW,WAAW,GAAGc,WAAW,CAACL,QAAQ,KAAK,IAAI;MAC1D,CAAC,GAAGT,WAAW,iBAAiB,GAAGc,WAAW,CAACL,QAAQ,KAAK;IAC9D,CAAC,CAAC;IACF8B,OAAO,EAAEzB,WAAW,CAACyB;EACvB,CAAC,EAAEzB,WAAW,CAACT,IAAI,IAAIf,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAE;IACjDU,SAAS,EAAE,GAAGhB,WAAW;EAC3B,CAAC,EAAEc,WAAW,CAACT,IAAI,CAAC,EAAEf,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAE;IAChDkC,GAAG,EAAEvB,YAAY;IACjBD,SAAS,EAAE,GAAGhB,WAAW;EAC3B,CAAC,EAAEV,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAE;IAC7BmC,eAAe,EAAEA,CAAA,KAAM;MACrBnB,YAAY,CAACE,OAAO,GAAG,KAAK;MAC5BD,KAAK,CAAC,CAAC;IACT,CAAC;IACDiB,GAAG,EAAEtB,OAAO;IACZF,SAAS,EAAE,GAAGhB,WAAW;EAC3B,CAAC,EAAEc,WAAW,CAAC4B,OAAO,CAAC,CAAC,EAAE,CAAC5B,WAAW,CAAC6B,SAAS,IAAI7B,WAAW,CAAC8B,KAAK,KAAKtD,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAE;IACpGU,SAAS,EAAE,GAAGhB,WAAW;EAC3B,CAAC,EAAEc,WAAW,CAAC8B,KAAK,EAAE9B,WAAW,CAAC6B,SAAS,IAAIrD,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IACxEU,SAAS,EAAE,GAAGhB,WAAW,QAAQ;IACjCuC,OAAO,EAAEA,CAAA,KAAM;MACb,IAAIM,EAAE;MACNzB,UAAU,CAAC,KAAK,CAAC;MACjB,CAACyB,EAAE,GAAG/B,WAAW,CAACgC,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACjC,WAAW,CAAC;IACtF;EACF,CAAC,EAAEC,SAAS,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}